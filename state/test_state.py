from __future__ import annotations

import eth_utils

import common
import rawdb

from .database import newDatabase
from .statedb import StateDB


def testSnapshot():
    addr = b"\x33" * 20
    key = b"\x01" * 32
    value1 = b"\x42" * 32
    value2 = b"\x43" * 32
    db = rawdb.newMemorydb()
    sdb = newDatabase(db)
    state = StateDB(common.EmptyRootHash, sdb)

    # snapshot the genesis state
    genesis = state.snapshot()
    state.createContract(addr)

    # set initial state object value
    state.setState(addr, key, value1)
    snapshot = state.snapshot()

    # set a new state object value, revert it and ensure correct content
    state.setState(addr, key, value2)
    state.revertToSnapshot(snapshot)

    assert (v := state.getState(addr, key)) == value1, f"wrong storage value {v}, want {value1}"
    assert (v := state.getCommittedState(addr, key)) is None, f"wrong committed storage value {v}, want None"

    # revert up to the genesis state and ensure correct content
    state.revertToSnapshot(genesis)
    assert (v := state.getState(addr, key)) is None, f"wrong storage value {v}, want None"
    assert (v := state.getCommittedState(addr, key)) is None, f"wrong committed storage value {v}, want None"


def testNull():
    db = rawdb.newMemorydb()
    sdb = newDatabase(db)
    state = StateDB(common.EmptyRootHash, sdb)
    addr = common.hexToAddress("0x823140710bf13990e4500136726d8b55")
    state.createContract(addr)
    state.setState(addr, b"", None)
    state.commit(0)
    assert (v := state.getState(addr, b"")) is None, f"expected empty current value, got {v}"
    assert (v := state.getCommittedState(addr, b"")) is None, f"expected empty committed value, got {v}"


def testSnapshotEmpty():
    db = rawdb.newMemorydb()
    sdb = newDatabase(db)
    state = StateDB(common.EmptyRootHash, sdb)

    snapshot = state.snapshot()
    state.revertToSnapshot(snapshot)


def testCreateObjectRevert():
    state = StateDB(common.EmptyRootHash, newDatabase(rawdb.newMemorydb()))
    addr = b"\x33" * 20
    snap = state.snapshot()

    state.createContract(addr)
    s0 = state._getStateObject(addr)
    s0.setCode(eth_utils.keccak(b"42code"), b"42hash")
    s0.setState(b"42key", b"42value")
    state._setStateObject(s0)

    state.revertToSnapshot(snap)
    exist = state.exist(addr)
    assert exist is False, "Unexpected account after revert"
