from __future__ import annotations

import copy
from typing import Dict, List, Optional

import eth_utils
import rlp
import trie
from kivy import Logger

import tree
from common.address import EMPTY_ADDRESS
from common.encode import encodeRLP
from common.state_account import AccountAttributes, StateAccount

from . import journal, state_update, statedb


class Storage(Dict[bytes, Optional[bytes]]):
    def copy(self) -> Dict[bytes, Optional[bytes]]:
        return copy.deepcopy(self)


class StateObject:
    """
    stateObject represents a Vgraph account which is being modified.
    The usage pattern is as follows:
    - First you need to obtain a state object.
    - Account values as well as storages can be accessed and modified through the object.
    - Finally, call commit to return the changes of storage trie and update account data.
    """

    def __init__(self, db: statedb.StateDB, address: bytes, data: Optional[StateAccount]):
        self.db: statedb.StateDB = db
        self.address: bytes = address  # address of vgraph account
        self.addressHash: bytes = eth_utils.keccak(address)  # hash of vgraph address of the account
        self.origin: StateAccount = (
            data  # Account original data without any change applied, nil means it was not existent
        )
        data = copy.deepcopy(data)
        if data is None:
            root = trie.HexaryTrie.BLANK_NODE_HASH
            codeHash = eth_utils.keccak(b"None")
            attrs = AccountAttributes(
                deployer=EMPTY_ADDRESS,
                upgradable=False,
                contractSourceUrl="",
                gitCommitHash="",
                reproducibleBuild=False,
            )
            data = StateAccount(root=root, codeHash=codeHash, attrs=attrs)

        self.data: StateAccount = data  # Account data with all mutations applied in the scope of block

        # write cache
        self.trie: Optional[tree.TrieABC] = None  # storage trie, which becomes non-nil on first access
        self.code: bytes = b""  # contract bytecode, which gets set when code is loaded

        self.originStorage: Storage = Storage()  # Storage entries that have been accessed within the current block
        self.dirtyStorage: Storage = Storage()  # Storage entries that have been modified within the current transaction
        self.pendingStorage: Storage = Storage()  # Storage entries that have been modified within the current block

        # uncommittedStorage tracks a set of storage entries that have been modified
        # but not yet committed since the "last commit operation", along with their
        # original values before mutation.
        #
        # Specifically, the commit will be performed after each transaction before
        # the byzantium fork, therefore the map is already reset at the transaction
        # boundary; however post the byzantium fork, the commit will only be performed
        # at the end of block, this set essentially tracks all the modifications
        # made within the block.
        self.uncommittedStorage: Storage = Storage()

        self.dirtyCode: bool = False

        # TODO: self destruct flag
        # Flag whether the account was marked as self-destructed. The self-destructed
        # account is still accessible in the scope of same transaction.
        # self._selfDestructed: bool = False

        # The flag could be set either when the contract is just created
        # within the current transaction, or when the object was previously existent
        # and is being deployed as a contract within the current transaction.
        self.newContract: bool = False

    # TODO: self destruct flag
    # def markSelfDestructed(self):
    #     self._selfDestructed = True

    def getTrie(self) -> tree.TrieABC:
        """
        getTrie returns the associated storage trie. The trie will be opened if it's
        not loaded previously. An error will be returned if trie can't be loaded.

        If a new trie is opened, it will be cached within the state object to allow
        subsequent reads to expand the same trie instead of reloading from disk.
        """
        if self.trie is None:
            self.trie = self.db.db.openStorageTrie(self.db.originalRoot, self.address, self.data.root)
        return self.trie

    def getState(self, key: bytes) -> bytes:
        """
        GetState retrieves a value associated with the given storage key.
        """
        value, _ = self._getState(key)
        return value

    def _getState(self, key) -> (bytes, bytes):
        """
        getState retrieves a value associated with the given storage key, along with
        its original value.
        """
        origin = self.getCommittedState(key)
        value = self.dirtyStorage.get(key, None)
        dirty = value is not None
        if dirty:
            return value, origin
        return origin, origin

    def getCommittedState(self, key: bytes) -> Optional[bytes]:
        """
        GetCommittedState retrieves the value associated with the specific key
        without any mutations caused in the current execution.
        """
        # If we have a pending write or clean cached, return that
        if (value := self.pendingStorage.get(key)) is not None:
            return value
        if (value := self.originStorage.get(key)) is not None:
            return value
        # If the object was destructed in *this* block (and potentially resurrected),
        # the storage has been cleared out, and we should *not* consult the previous
        # database about any storage values. The only possible alternatives are:
        #   1) resurrect happened, and new slot values were set -- those should
        #      be in the pending storage.
        #   2) we don't have new values, and can deliver empty response back

        # TODO: state_objects_destruct
        # if self.address in self.db.stateObjectsDestruct.get():
        #     self.originStorage[key] = b""
        #     return b""

        # If no live objects are available, attempt to use snapshots
        value = None
        # TODO snapshots
        # if self._db.snap is None:

        try:
            tr = self.getTrie()
            value = tr.getStorage(self.address, key)
        except Exception as e:
            self.db._setError(e)
            return None

        self.originStorage[key] = value
        return value

    def setState(self, key: bytes, value: bytes):
        """
        SetState updates a value in account storage.
        """
        # If the new value is the same as old, don't set. Otherwise, track only the
        # dirty changes, supporting reverting all of it back to no change.
        if value == b"":
            value = None
        prev, origin = self._getState(key)
        if prev == value:
            return
        # New value is different, update and journal the change
        self.db.journal.append(
            journal.StorageChange(account=self.address, key=key, preValue=prev, originalValue=origin)
        )
        self._setState(key, value, origin)

    def _setState(self, key: bytes, value: bytes, origin: Optional[bytes]):
        """
        setState updates a value in account dirty storage. The dirtiness will be
        removed if the value being set equals to the original value.
        """
        if value == origin:
            del self.dirtyStorage[key]
            return
        self.dirtyStorage[key] = value

    def _finalise(self):
        """
        finalise moves all dirty storage slots into the pending area to be hashed or
        committed later. It is invoked at the end of every transaction.
        """
        for key, value in self.dirtyStorage.items():
            if (exist := key in self.uncommittedStorage) and self.uncommittedStorage[key] == value:
                # The slot is reverted to its original value, delete the entry
                # to avoid thrashing the data structures.
                del self.uncommittedStorage[key]
            elif exist:
                # The slot is modified to another value and the slot has been
                # tracked for commit, do nothing here.
                pass
            else:
                # The slot is different from its original value and hasn't been
                # tracked for commit yet.
                self.uncommittedStorage[key] = self.getCommittedState(key)
            # Aggregate the dirty storage slots into the pending area. It might
            # be possible that the value of tracked slot here is same with the
            # one in originStorage (e.g. the slot was modified in tx_a and then
            # modified back in tx_b). We can't blindly remove it from pending
            # map as the dirty slot might have been committed already (before the
            # byzantium fork) and entry is necessary to modify the value back.
            self.pendingStorage[key] = value

        if len(self.dirtyStorage) > 0:
            self.dirtyStorage.clear()

        # Revoke the flag at the end of the transaction. It finalizes the status of
        # the newly-created object as it's no longer eligible for self-destruct
        self.newContract = False

    def _updateTrie(self) -> Optional[tree.TrieABC]:
        """
        updateTrie is responsible for persisting cached storage changes into the
        object's storage trie. In case the storage trie is not yet loaded, this
        function will load the trie automatically. If any issues arise during the
        loading or updating of the trie, an error will be returned. Furthermore,
        this function will return the mutated storage trie, or nil if there is no
        storage change at all.

        It assumes all the dirty storage slots have been finalized before.
        """
        # Short circuit if nothing was accessed
        if len(self.pendingStorage) == 0:
            return self.trie

        tr = self.getTrie()

        # Perform trie updates before deletions. This prevents resolution of unnecessary trie nodes
        # in circumstances similar to the following:
        #
        # Consider nodes `A` and `B` who share the same full node parent `P` and have no other siblings.
        # During the execution of a block:
        # - `A` is deleted,
        # - `C` is created, and also shares the parent `P`.
        # If the deletion is handled first, then `P` would be left with only one child, thus collapsed
        # into a shortnode. This requires `B` to be resolved from disk.
        # Whereas if the created node is handled first, then the collapse is avoided, and `B` is not resolved.
        deletions: List[bytes] = []
        used: List[bytes] = []

        for key, origin in self.uncommittedStorage.items():
            # Skip noop changes, persist actual changes
            value, exist = self.pendingStorage.get(key), key in self.originStorage
            if value == origin:
                Logger.error(f"Storage update was noop, address: {self.address}, slot: {key}")
                continue
            if not exist:
                Logger.error(f"Storage slot is not found in pending area, address: {self.address}, slot: {key}")
                continue
            if value == b"" or value is None:
                deletions.append(key)
            else:
                tr.updateStorage(self.address, key, value)
            used.append(copy.deepcopy(key))
            for key in deletions:
                tr.deleteStorage(self.address, key)
        self.uncommittedStorage.clear()
        return tr

    def _updateRoot(self):
        """
        updateRoot flushes all cached storage mutations to trie, recalculating the new storage trie root.
        """
        trie = self._updateTrie()
        if trie is None:
            return
        self.data.root = trie.hash()

    def _commitStorage(self, op: state_update._AccountUpdate):
        """
        commitStorage overwrites the clean storage with the storage changes and
        fulfills the storage diffs into the given accountUpdate struct.
        """

        def encode(val: Optional[bytes]) -> Optional[bytes]:
            if val is None:
                return None
            return rlp.encode(val.rstrip(b"\x00"))

        for key, value in self.pendingStorage.items():
            # Skip the noop storage changes, it might be possible the value
            # of tracked slot is same in originStorage and pendingStorage
            # map, e.g. the storage slot is modified in tx_a and then reset
            # back in tx_b.
            if value == self.originStorage.get(key, None):
                continue
            hash = eth_utils.keccak(key)
            if op.storages is None:
                op.storages = {}
            op.storages[hash] = encode(value)

            if op.storagesOrigin is None:
                op.storagesOrigin = {}
            op.storagesOrigin[hash] = encode(self.originStorage[key])

            # Overwrite the clean value of storage slots
            self.originStorage[key] = value
        self.pendingStorage.clear()

    def _commit(self) -> (state_update._AccountUpdate, tree.TrieChanges):
        """
        commit obtains the account changes (metadata, storage slots, code) caused by
        state execution along with the dirty storage trie nodes.
        """
        # commit the account metadata changes
        op = state_update._AccountUpdate(
            address=self.address,
            data=encodeRLP(self.data),
            code=None,
            storages={},
            storagesOrigin={},
            origin=None,
        )
        if self.origin is not None:
            op.origin = rlp.encode(self.origin.toRLP())
        # commit the contract code if it's modified
        if self.dirtyCode:
            op.code = state_update._ContractCode(hash=eth_utils.keccak(self.code), blob=self.code)
            self.dirtyCode = False  # reset the dirty flag
        # commit storage changes and the associated storage trie
        self._commitStorage(op)
        if len(op.storages) == 0:
            # nothing changed, don't bother to commit the trie
            self.origin = self.data.copy()
            return op, None
        root, nodes = self.trie.commit()
        self.data.root = root
        self.origin = self.data.copy()
        return op, nodes

    def deepCopy(self, db: statedb.StateDB) -> StateObject:
        """
        deepCopy returns a deep copy of the state object.
        """
        obj = StateObject(db=db, address=self.address, data=self.data)
        obj.code = self.code
        obj.dirtyCode = self.dirtyCode
        obj.originStorage = self.originStorage.copy()
        obj.dirtyStorage = self.dirtyStorage.copy()
        obj.pendingStorage = self.pendingStorage.copy()
        obj.uncommittedStorage = self.uncommittedStorage.copy()
        if self.trie is not None:
            obj.trie = db.db.copyTrie(self.trie)
        return obj

    # Attribute accessors

    def getAddress(self) -> bytes:
        """
        Address returns the address of the contract
        """
        return self.address

    def getCode(self) -> Optional[bytes]:
        """
        Code returns the contract code associated with this object, if any.
        """
        if len(self.code) != 0:
            return self.code
        if self.codeHash() == eth_utils.keccak(b"None"):
            return None
        try:
            code = self.db.db.contractCode(self.address, self.codeHash())
        except Exception as e:
            Logger.error(f"can't load code hash: {self.codeHash()}, error: {e}")
            raise e
        self.code = code
        return code

    def codeSize(self) -> int:
        """
        CodeSize returns the size of the contract code associated with this object,
        or zero if none. This method is an almost mirror of Code, but uses a cache
        inside the database to avoid loading codes seen recently.
        """
        if len(self.code) != 0:
            return len(self.code)
        try:
            code = self.db.db.contractCodeSize(self.address, self.codeHash())
        except Exception as e:
            Logger.error(f"can't load code hash: {self.codeHash()}, error: {e}")
            raise e
        return code

    def codeHash(self) -> bytes:
        """
        codeHash returns the code hash associated with this object.
        """
        return self.data.codeHash

    def setCode(self, codeHash: bytes, code: bytes):
        """
        SetCode sets the contract code associated with this object.
        """
        prevCode = self.getCode()
        self.db.journal.append(journal.CodeChange(self.address, prevCode, self.codeHash()))
        self._setCode(codeHash, code)

    def _setCode(self, codeHash: bytes, code: bytes):
        self.data.codeHash = codeHash
        self.code = code
        self.dirtyCode = True

    def setAttributes(self, attrs: AccountAttributes):
        """
        SetAttributes sets attributes(e.g. deployer, upgradable...) associated with this object.
        """
        self.db.journal.append(journal.AttributesChange(self.address, self.data.attrs))
        self._setAttributes(attrs)

    def _setAttributes(self, attrs: AccountAttributes):
        self.data.attrs = attrs

    def root(self) -> bytes:
        """
        Root returns the root hash of the storage trie.
        """
        return self.data.root
