from __future__ import annotations

from enum import Enum
from typing import List, Optional, Tuple

from kivy import Logger

import vgraphdb
from common import AddressLength
from rawdb.accessors_data_indexes import deleteContractDataIndex, writeContractDataIndex
from rawdb.accessors_index_undo_log import (
    IndexUndoLogPrefix,
    decodeBlockNumber,
    deleteIndexUndoLog,
    getIndexUndoLogIterator,
    readIndexUndoLog,
    writeIndexUndoLog,
)


class IndexOperationType(Enum):
    """
    Enum representing the type of index operation.
    """

    CREATE = 0  # Index was created
    UPDATE = 1  # Index was updated
    DELETE = 2  # Index was deleted


class IndexUndoLogEntry:
    """
    Represents a single entry in the index undo log.
    """

    def __init__(
        self,
        contractAddress: bytes,
        index: bytes,
        operationType: IndexOperationType,
        previousValue: Optional[bytes] = None,
    ):
        """
        Initialize a new IndexUndoLogEntry.

        Args:
            contractAddress: The contract address
            index: The index key
            operationType: The type of operation (CREATE, UPDATE, DELETE)
            previousValue: The previous value of the index, or None if it was newly created
        """
        self.contractAddress: bytes = contractAddress.ljust(AddressLength, b"\x00")
        self.index = index
        self.operationType = operationType
        self.previousValue = previousValue

    def serialize(self) -> bytes:
        """
        Serialize the undo log entry to bytes.

        Returns:
            The serialized entry
        """
        # Format:
        # - 1 byte: operation type (0=CREATE, 1=UPDATE, 2=DELETE)
        # - 20 bytes: contract address
        # - 4 bytes: index length
        # - N bytes: index
        # - 4 bytes: previous value length (0 if None)
        # - M bytes: previous value (if any)

        opType = self.operationType.value.to_bytes(1, "big")
        addr = self.contractAddress
        indexLen = len(self.index).to_bytes(4, "big")
        index = self.index

        if self.previousValue is None:
            prevLen = (0).to_bytes(4, "big")
            prevVal = b""
        else:
            prevLen = len(self.previousValue).to_bytes(4, "big")
            prevVal = self.previousValue

        return opType + addr + indexLen + index + prevLen + prevVal

    @classmethod
    def deserialize(cls, data: bytes) -> Tuple[IndexUndoLogEntry, int]:
        """
        Deserialize an undo log entry from bytes.

        Args:
            data: The serialized entry

        Returns:
            A tuple of (deserialized entry, bytes consumed)
        """
        # Read operation type
        opType = IndexOperationType(int.from_bytes(data[0:1], "big"))

        # Read contract address
        addr = data[1 : AddressLength + 1]

        # Read index
        indexLen = int.from_bytes(data[AddressLength + 1 : AddressLength + 5], "big")
        index = data[AddressLength + 5 : AddressLength + 5 + indexLen]

        # Read previous value
        prevLenStart = AddressLength + 5 + indexLen
        prevLen = int.from_bytes(data[prevLenStart : prevLenStart + 4], "big")

        prevVal = None
        if prevLen > 0:
            prevVal = data[prevLenStart + 4 : prevLenStart + 4 + prevLen]

        # Calculate total bytes consumed
        bytesConsumed = 1 + AddressLength + 4 + indexLen + 4 + (prevLen if prevLen > 0 else 0)

        return cls(addr, index, opType, prevVal), bytesConsumed


class IndexUndoLog:
    """
    Represents a collection of index undo log entries for a block.
    """

    def __init__(self):
        """
        Initialize a new IndexUndoLog.
        """
        self.entries: List[IndexUndoLogEntry] = []

    def addEntry(self, entry: IndexUndoLogEntry):
        """
        Add an entry to the undo log.

        Args:
            entry: The entry to add
        """
        self.entries.append(entry)

    def serialize(self) -> bytes:
        """
        Serialize the undo log to bytes.

        Returns:
            The serialized undo log
        """
        # Format:
        # - 4 bytes: number of entries
        # - N bytes: serialized entries

        numEntries = len(self.entries).to_bytes(4, "big")
        serializedEntries = b"".join(entry.serialize() for entry in self.entries)

        return numEntries + serializedEntries

    @classmethod
    def deserialize(cls, data: bytes) -> IndexUndoLog:
        """
        Deserialize an undo log from bytes.

        Args:
            data: The serialized undo log

        Returns:
            The deserialized undo log
        """
        result = cls()

        # Read number of entries
        numEntries = int.from_bytes(data[0:4], "big")

        # Read entries
        offset = 4
        for _ in range(numEntries):
            entry, bytesConsumed = IndexUndoLogEntry.deserialize(data[offset:])
            result.addEntry(entry)
            offset += bytesConsumed

        return result


def writeBlockUndoLog(db: vgraphdb.KeyValueWriter, blockNumber: int, undoLog: IndexUndoLog):
    """
    Write an undo log for a block to the database.

    Args:
        db: The database writer
        blockNumber: The block number
        undoLog: The undo log to write
    """
    serialized = undoLog.serialize()
    writeIndexUndoLog(db, blockNumber, serialized)


def readBlockUndoLog(db: vgraphdb.KeyValueReader, blockNumber: int) -> Optional[IndexUndoLog]:
    """
    Read an undo log for a block from the database.

    Args:
        db: The database reader
        blockNumber: The block number

    Returns:
        The undo log, or None if not found
    """
    serialized = readIndexUndoLog(db, blockNumber)
    if serialized is None:
        return None

    return IndexUndoLog.deserialize(serialized)


def deleteBlockUndoLog(db: vgraphdb.KeyValueWriter, blockNumber: int):
    """
    Delete an undo log for a block from the database.

    Args:
        db: The database writer
        blockNumber: The block number
    """
    deleteIndexUndoLog(db, blockNumber)


def applyUndoLog(db: vgraphdb.KeyValueWriter, undoLog: IndexUndoLog):
    """
    Apply an undo log to revert changes.

    Args:
        db: The database writer
        undoLog: The undo log to apply
    """

    for entry in undoLog.entries:
        if entry.operationType in (IndexOperationType.UPDATE, IndexOperationType.DELETE):
            # Restore previous value
            writeContractDataIndex(db, entry.contractAddress, entry.index, entry.previousValue)
        elif entry.operationType == IndexOperationType.CREATE:
            # Delete created index
            deleteContractDataIndex(db, entry.contractAddress, entry.index)


def rollbackToBlock(db: vgraphdb.KeyValueStore, targetBlock: int) -> bool:
    """
    Rollback all index changes after the target block.

    This function applies all undo logs for blocks after the target block,
    in reverse order, to restore the database to its state at the target block.

    Args:
        db: The database writer
        targetBlock: The target block number to roll back to

    Returns:
        True if the rollback was successful, False otherwise
    """
    # Get an iterator over all undo logs after the target block, in reverse order
    # 18446744073709551615 is the maximum uint64 value, which is b'\xff' * 8
    iterator = getIndexUndoLogIterator(db, reverse=True, startBlock=18446744073709551615, endBlock=targetBlock + 1)

    try:
        # Apply each undo log in reverse order
        while iterator.next():
            key, value = iterator.key(), iterator.value()
            # Extract block number from key
            blockNumberBytes = key[len(IndexUndoLogPrefix) :]
            blockNumber = decodeBlockNumber(blockNumberBytes)

            # Skip blocks before or equal to target_block
            if blockNumber <= targetBlock:
                continue

            # Deserialize and apply the undo log
            undoLog = IndexUndoLog.deserialize(value)
            applyUndoLog(db, undoLog)

            # Delete the undo log after applying it
            deleteBlockUndoLog(db, blockNumber)

        return True
    except Exception as e:
        # Log the error
        Logger.error(f"Error rolling back to block {targetBlock}: {e}")
        return False
