from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Optional

import common
import vgraphdb


class StatedbABC(ABC):
    """
    StateDB is an VM database for full state querying.
    """

    @abstractmethod
    def createContract(self, address: bytes) -> None:
        raise NotImplementedError

    def getAccount(self, address: bytes) -> Optional[common.StateAccount]:
        raise NotImplementedError

    @abstractmethod
    def hasAccount(self, address: bytes) -> bool:
        raise NotImplementedError

    @abstractmethod
    def getCodeHash(self, address: bytes) -> bytes:
        raise NotImplementedError

    @abstractmethod
    def getCode(self, address: bytes) -> Optional[bytes]:
        raise NotImplementedError

    @abstractmethod
    def getCodeByHash(self, codeHash: bytes) -> Optional[bytes]:
        raise NotImplementedError

    @abstractmethod
    def setCode(self, address: bytes, code: bytes) -> None:
        raise NotImplementedError

    @abstractmethod
    def getCodeSize(self, address: bytes) -> int:
        raise NotImplementedError

    @abstractmethod
    def getCommittedState(self, address: bytes, key: bytes) -> Optional[bytes]:
        raise NotImplementedError

    @abstractmethod
    def getState(self, address: bytes, key: bytes) -> Optional[bytes]:
        raise NotImplementedError

    @abstractmethod
    def setState(self, address: bytes, key: bytes, value: bytes) -> None:
        raise NotImplementedError

    @abstractmethod
    def setContractAttributes(self, address: bytes, attrs: common.AccountAttributes) -> None:
        raise NotImplementedError

    @abstractmethod
    def getStorageRoot(self, address: bytes) -> Optional[bytes]:
        raise NotImplementedError

    # TODO: transient state for run in vm
    # @abstractmethod
    # def getTransientState(self, address: bytes, key: bytes) -> bytes:
    #     raise NotImplementedError
    #
    # @abstractmethod
    # def setTransientState(self, address: bytes, key: bytes, value: bytes) -> None:
    #     raise NotImplementedError

    @abstractmethod
    def revertToSnapshot(self, revId: int) -> None:
        raise NotImplementedError

    @abstractmethod
    def snapshot(self) -> int:
        raise NotImplementedError

    @abstractmethod
    def exist(self, addr: bytes) -> bool:
        """
        Exist reports whether the given account exists in state.
        """
        raise NotImplementedError

    @abstractmethod
    def setContractDataIndex(self, contractAddress: bytes, index: bytes, dataKey: bytes) -> None:
        """
        Set a contract data index and record the change in the journal.

        Args:
            contractAddress: The contract address
            index: The index key (must be less than 255 bytes)
            dataKey: The data key to store
        """
        raise NotImplementedError

    @abstractmethod
    def removeContractDataIndex(self, contractAddress: bytes, index: bytes) -> None:
        """
        Remove a contract data index and record the change in the journal.

        Args:
            contractAddress: The contract address
            index: The index key (must be less than 255 bytes)
        """
        raise NotImplementedError

    @abstractmethod
    def getContractDataIndex(self, contractAddress: bytes, index: bytes) -> Optional[bytes]:
        """
        Get a contract data index value.

        Args:
            contractAddress: The contract address
            index: The index key (must be less than 255 bytes)

        Returns:
            The data key or None if the index doesn't exist
        """
        raise NotImplementedError

    @abstractmethod
    def iterateContractDataIndexes(
        self,
        contractAddress: bytes,
        reverse: bool = False,
        start: bytes = b"",
        end: bytes = b"",
    ) -> vgraphdb.DBIterator:
        """
        Get an iterator over contract data indexes.

        Args:
            contractAddress: The contract address
            reverse: Whether to iterate forward (True) or backward (False)
            start: The starting index key (inclusive)
            end: The ending index key (exclusive)

        Returns:
            An iterator over (index, dataKey) pairs
        """
        raise NotImplementedError


# TODO: Add more methods here
