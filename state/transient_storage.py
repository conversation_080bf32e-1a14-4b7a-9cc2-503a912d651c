from __future__ import annotations

import copy
from typing import Dict

from . import state_object


class TransientStorage:
    """
    TransientStorage is a storage object that is not persisted to the database.
    """

    def __init__(self):
        self.data: Dict[bytes, state_object.Storage] = {}

    def set(self, address: bytes, key: bytes, value: bytes):
        """Set sets the transient-storage `value` for `key` at the given `addr`."""
        if value == b"":
            if address in self:
                del self.data[address][key]
                if len(self.data[address]) == 0:
                    del self.data[address]
        else:
            if address not in self:
                self.data[address] = state_object.Storage()
            self.data[address][key] = value

    def get(self, address: bytes, key: bytes) -> bytes:
        """Get retrieves the transient-storage `value` for `key` at the given `addr`."""
        return self.data.get(address, {}).get(key, b"")

    def copy(self):
        """Copy creates a deep copy of the transient storage."""
        return copy.deepcopy(self)

    def prettyPrint(self) -> str:
        """PrettyPrint prints the contents of the access list in a human-readable form"""
        out = ""
        sortedAddress = sorted(self.data.keys())
        for addr in sortedAddress:
            out += f"{addr}:"
            storage = self.data[addr]
            sortedKeys = sorted(storage.keys())
            for key in sortedKeys:
                out += f"  {key} : {storage[key]}\n"
        return out
