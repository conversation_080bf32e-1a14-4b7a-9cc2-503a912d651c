from __future__ import annotations

import dataclasses
from typing import Dict, Optional, Set

import trie

import tree


@dataclasses.dataclass
class _ContractCode:
    """
    contractCode represents a contract code with associated metadata.
    """

    hash: bytes  # hash is the cryptographic hash of the contract code.
    blob: bytes  # blob is the binary representation of the contract code.


@dataclasses.dataclass
class _AccountDelete:
    """
    accountDelete represents an operation for deleting a Vgraph account.
    """

    address: bytes  # address is the unique account identifier
    origin: bytes  # origin is the original value of account data in slim-RLP encoding.
    storageOrigin: Dict[
        bytes, bytes
    ]  # storagesOrigin stores the original values of mutated slots in prefix-zero-trimmed RLP format.


@dataclasses.dataclass
class _AccountUpdate:
    """
    accountUpdate represents an operation for updating a Vgraph account.
    """

    address: bytes  # address is the unique account identifier
    data: bytes  # data is the slim-RLP encoded account data.
    origin: Optional[bytes]  # origin is the original value of account data in slim-RLP encoding.
    code: Optional[_ContractCode]  # code represents mutated contract code; nil means it's not modified.
    storages: Dict[bytes, bytes]  # storages stores mutated storage slots in prefix-zero-trimmed RLP format.
    storagesOrigin: Dict[
        bytes, bytes
    ]  # storagesOrigin stores the original values of mutated slots in prefix-zero-trimmed RLP format.


class _StateUpdate:
    """
    stateUpdate represents the difference between two states resulting from state
    execution. It contains information about mutated contract codes, accounts, and
    storage slots, along with their original values.
    """

    def __init__(
        self,
        originRoot: bytes,
        root: bytes,  # deletes: Dict[bytes, _AccountDelete],
        updates: Dict[bytes, _AccountUpdate],
        trieChanges: tree.TrieChanges,
    ):
        """
        newStateUpdate constructs a state update object, representing the differences
        between two states by performing state execution. It aggregates the given account
        deletions and account updates to form a comprehensive state update.
        """
        destructs = set()
        accounts = {}
        accountsOrigin = {}
        storages = {}
        storagesOrigin = {}
        codes = {}

        # Due to the fact that some accounts could be destructed and resurrected
        # within the same block, the deletions must be aggregated first.
        # for addressHash, op in deletes.items():
        #     address = op.address
        #     destructs.add(addressHash)
        #     accountsOrigin[address] = op.origin
        #     if len(op.storageOrigin) > 0:
        #         storagesOrigin[address] = op.storageOrigin

        # Aggregate account updates then.
        for addressHash, op in updates.items():
            # Aggregate dirty contract codes if they are available.
            address = op.address
            if op.code is not None:
                codes[address] = op.code
            accounts[addressHash] = op.data

            # Aggregate the account changes.The original account value will only
            # be tracked if it's not present yet.
            if address in accountsOrigin:
                accountsOrigin[address] = op.origin

            # Aggregate the storage changes. The original storage slot value will
            # only be tracked if it's not present yet.
            if len(op.storages) > 0:
                storages[addressHash] = op.storages
            if len(op.storagesOrigin) > 0:
                origin = storagesOrigin.get(address, None)
                if origin is None:
                    storagesOrigin[address] = op.storagesOrigin
                    continue
                for key, slot in op.storagesOrigin.items():
                    if key not in origin:
                        origin[key] = slot
                storagesOrigin[address] = origin

        # hash of the state before applying mutation
        self.originRoot: bytes = _StateUpdate.__trieRootHash(originRoot)
        # hash of the state after applying mutation
        self.root: bytes = _StateUpdate.__trieRootHash(root)
        # destructs contains the list of destructed accounts
        self.destructs: Set[bytes] = destructs
        # accounts stores mutated accounts in 'slim RLP' encoding
        self.accounts: Dict[bytes, bytes] = accounts
        # accountsOrigin stores the original values of mutated accounts in 'slim RLP' encoding
        self.accountsOrigin: Dict[bytes, bytes] = accountsOrigin
        # storages stores mutated slots in 'prefix-zero-trimmed' RLP format
        self.storages: Dict[bytes, Dict[bytes, bytes]] = storages
        # storagesOrigin stores the original values of mutated slots in 'prefix-zero-trimmed' RLP format
        self.storagesOrigin: Dict[bytes, Dict[bytes, bytes]] = storagesOrigin
        # codes contains the set of dirty codes
        self.codes: Dict[bytes, _ContractCode] = codes
        # trie node change data, None is deletion
        self.trieChanges: tree.TrieChanges = trieChanges

    def empty(self) -> bool:
        # empty returns a flag indicating the state transition is empty or not.
        return self.originRoot == self.root

    @staticmethod
    def __trieRootHash(root: bytes) -> bytes:
        if root == b"":
            return trie.HexaryTrie.BLANK_NODE_HASH
        return root
