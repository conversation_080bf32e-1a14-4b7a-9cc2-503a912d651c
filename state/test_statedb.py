from __future__ import annotations

import pytest
import trie.exceptions

import common
import rawdb
import tree

from .database import newDatabase, newDatabaseWithTriedb
from .statedb import StateDB


def testUpdateLeaks():
    """
    Tests that updating a state trie does not leak any database writes prior to
    actually committing the state.
    """
    db = rawdb.newMemorydb()
    tdb = tree.TrieDB(db)
    state = StateDB(common.EmptyRootHash, newDatabaseWithTriedb(db, tdb), None)

    # update it with some accounts
    for i in range(255):
        addr = common.intToAddress(i)
        state.createContract(addr)
        state.setState(addr, bytes([i] * 3), bytes([i] * 4))
        state.setCode(addr, bytes([i] * 5))

    root = state.intermediateRoot()
    assert root != common.EmptyRootHash

    tdb.commit()

    # Ensure that no data was leaked into the database
    it = db.newIterator()
    while it.next():
        raise AssertionError(f"State leaked into database: {it.key()} -> {it.value()}")
    it.release()


def testSimpleUpdate():
    """
    Tests that updating a state trie and committing it works as expected.
    """
    db = rawdb.newMemorydb()
    tdb = tree.TrieDB(db)
    state = StateDB(common.EmptyRootHash, newDatabaseWithTriedb(db, tdb), None)
    address = common.intToAddress(42)
    state.createContract(address)
    state.setState(address, b"hello", b"world")
    state.intermediateRoot()
    state.setState(address, b"hello", b"wd")
    assert state.getState(address, b"hello") == b"wd"
    state.commit(0)
    assert state.getState(address, b"hello") == b"wd"

    state = StateDB(state.trie.hash(), newDatabaseWithTriedb(db, tdb), None)
    assert state.getState(address, b"hello") == b"wd"
    state.setState(address, b"a", b"b")
    assert state.getState(address, b"a") == b"b"
    state.setState(address, b"a", b"c")
    assert state.getState(address, b"a") == b"c"
    state.setState(address, b"a", b"d")
    assert state.getState(address, b"a") == b"d"
    state.commit(0)
    assert state.getState(address, b"a") == b"d"
    assert state.getState(address, b"hello") == b"wd"

    # test none value and empty value
    state = StateDB(state.trie.hash(), newDatabaseWithTriedb(db, tdb), None)
    state.setState(address, b"a", None)
    state.setState(address, b"hello", b"")
    state.commit(0)
    assert state.getState(address, b"a") is None
    assert state.getState(address, b"hello") is None


# TODO: use custom trie to fix intermediateLeaks test, which should have difflayer and disklayer structure
@pytest.mark.skip("miss custom trie, which is able to partial commit trie nodes")
def testIntermediateLeaks():
    """
    Tests that no intermediate state of an object is stored into the database,
    only the one right before the commit.
    """
    # Create two state databases, one transitioning to the final state, the other final from the beginning
    transDb = rawdb.newMemorydb()
    finalDb = rawdb.newMemorydb()
    transNdb = tree.TrieDB(transDb)
    finalNdb = tree.TrieDB(finalDb)
    transState = StateDB(common.EmptyRootHash, newDatabaseWithTriedb(transDb, transNdb), None)
    finalState = StateDB(common.EmptyRootHash, newDatabaseWithTriedb(finalDb, finalNdb), None)

    def modify(state: StateDB, addr: bytes, i: int, tweak: int):
        state.setState(addr, bytes([i, i, i, 0]), b"")
        state.setState(addr, bytes([i, i, i, tweak]), bytes([i, i, i, i, tweak]))
        state.setCode(addr, bytes([i, i, i, i, i, tweak]))

    # Modify the transient state.
    for i in range(255):
        modify(transState, common.intToAddress(i), i, 0)
        break

    # Write modifications to trie.
    transState.intermediateRoot()

    # Check that the transient state is as expected.
    assert transState.getState(common.intToAddress(0), bytes([0] * 4)) == bytes([0] * 5)
    assert transState.getState(common.intToAddress(254), bytes([254] * 3 + [0])) == bytes([254] * 4 + [0])
    assert transState.getState(common.intToAddress(124), bytes([124] * 3 + [0])) == bytes([124] * 4 + [0])

    # Overwrite all the data with new values in the transient database.
    for i in range(255):
        addr = common.intToAddress(i)
        modify(transState, addr, i, 99)
        modify(finalState, addr, i, 99)

    # transState.intermediateRoot()
    # finalState.intermediateRoot()

    # Commit and cross-check databases.
    transState.commit(0)
    finalState.commit(0)

    for kv in finalDb.newIterator():
        key, fvalue = kv.key(), kv.value()
        tvalue = transDb.get(key)
        assert tvalue == fvalue, (
            f"value mismatch at key {key}: {tvalue} in transition database, {fvalue} in final database"
        )

    for kv in transDb.newIterator():
        key, tvalue = kv.key(), kv.value()
        fvalue = finalDb.get(key)
        assert fvalue == tvalue, (
            f"value mismatch at key {key}: {tvalue} in transition database, {fvalue} in final database"
        )


def testCopy():
    """
    TestCopy tests that copying a StateDB object indeed makes the original and the copy independent of each other.
    """
    # Create a random state test to copy and modify "independently"
    orig = StateDB(common.EmptyRootHash, newDatabase(rawdb.newMemorydb()), None)
    for i in range(255):
        origObj = orig._getOrNewStateObject(common.bytesToAddress(bytes(i)))
        origObj.setState(b"key", b"value")
        orig._updateStateObject(origObj)

    orig.finalise()

    # Copy the state
    copy = orig.copy()

    # Copy the copy state
    ccopy = copy.copy()

    # modify all in memory
    for i in range(255):
        origObj = orig._getOrNewStateObject(common.intToAddress(i))
        copyObj = copy._getOrNewStateObject(common.intToAddress(i))
        ccopyObj = ccopy._getOrNewStateObject(common.intToAddress(i))

        origObj.setState(b"key", b"value" + str(i * 2).encode())
        copyObj.setState(b"key", b"value" + str(i * 3).encode())
        ccopyObj.setState(b"key", b"value" + str(i * 4).encode())

        orig._updateStateObject(origObj)
        copy._updateStateObject(copyObj)
        ccopy._updateStateObject(ccopyObj)

    orig.finalise()
    copy.finalise()
    ccopy.finalise()

    # Verify that the three states have been updated independently
    for i in range(255):
        origObj = orig._getOrNewStateObject(common.intToAddress(i))
        copyObj = copy._getOrNewStateObject(common.intToAddress(i))
        ccopyObj = ccopy._getOrNewStateObject(common.intToAddress(i))

        assert origObj.getState(b"key") == b"value" + str(i * 2).encode(), (
            f"orig obj {i}: state mismatch: have {origObj.getState(b'key')}, want {b'value' + str(i * 2).encode()}"
        )
        assert copyObj.getState(b"key") == b"value" + str(i * 3).encode(), (
            f"copy obj {i}: state mismatch: have {copyObj.getState(b'key')}, want {b'value' + str(i * 3).encode()}"
        )
        assert ccopyObj.getState(b"key") == b"value" + str(i * 4).encode(), (
            f"double copy obj {i}: state mismatch: have {ccopyObj.getState(b'key')}, want {b'value' + str(i * 4).encode()}"
        )


def testCopyWithDirtyJournal():
    """
    TestCopyWithDirtyJournal tests if Copy can correct create a equal copied
    stateDB with dirty journal present.
    """
    db = newDatabase(rawdb.newMemorydb())
    orig = StateDB(common.EmptyRootHash, db, None)

    # Fill up the initial states
    for i in range(255):
        origObj = orig._getOrNewStateObject(common.intToAddress(i))
        origObj.setState(b"key", b"value")
        orig._updateStateObject(origObj)

    root = orig.commit(0)
    orig = StateDB(root, db, None)

    # modify all in memory without finalizing
    for i in range(255):
        origObj = orig._getOrNewStateObject(common.intToAddress(i))
        origObj.setState(b"key", b"value" + str(i * 2).encode())
        orig._updateStateObject(origObj)

    cpy = orig.copy()

    orig.finalise()
    for i in range(255):
        root = orig.getStorageRoot(common.intToAddress(i))
        assert root is not None, f"Root is empty for {i}"

    cpy.finalise()
    for i in range(255):
        root = cpy.getStorageRoot(common.intToAddress(i))
        assert root is not None, f"Root is empty for {i}"

    assert cpy.intermediateRoot() == orig.intermediateRoot(), "State is not equal after copy"


def testCopyObjectState():
    """
    TestCopyObjectState creates an original state, S1, and makes a copy S2.
    It then proceeds to make changes to S1. Those changes are _not_ supposed
    to affect S2. This test checks that the copy properly deep-copies the object state
    """
    db = newDatabase(rawdb.newMemorydb())
    orig = StateDB(common.EmptyRootHash, db, None)

    # Fill up the initial states
    for i in range(5):
        obj = orig._getOrNewStateObject(common.intToAddress(i))
        obj.setState(b"key", b"value")
        orig._updateStateObject(obj)

    orig.finalise()
    cpy = orig.copy()
    for op in cpy.mutations.values():
        have, want = op.applied, False
        assert have == want, (
            f"Error in test itself, the 'done' flag should not be set before Commit, have {have}, want {want}"
        )

    orig.commit(0)
    for op in cpy.mutations.values():
        have, want = op.applied, False
        assert have == want, f"Error: original state affected copy, have {have}, want {want}"


def testCopyOfCopy():
    """
    TestCopyOfCopy tests that modified objects are carried over to the copy, and the copy of the copy.
    """
    state = StateDB(common.EmptyRootHash, newDatabase(rawdb.newMemorydb()), None)
    addr = common.intToAddress(42)
    state.createContract(addr)
    state.setState(addr, b"hello", b"world")

    assert (got := state.copy().getState(addr, b"hello")) == b"world", f"1st copy fail, got {got}, want b'world'"

    assert (got := state.copy().copy().getState(addr, b"hello")) == b"world", f"2nd copy fail, got {got}, want b'world'"


def testCopyCommitCopy():
    """
    Tests a regression where committing a copy lost some internal meta information,
    leading to corrupted subsequent copies.
    """
    tdb = newDatabase(rawdb.newMemorydb())
    state = StateDB(common.EmptyRootHash, tdb, None)

    # Create an account and check if the retrieved data is correct
    addr = common.hexToAddress("0x1234567890abcdef1234567890abcdef12345678")
    skey = b"aaa"
    sval = b"bbb"

    state.setCode(addr, b"hello")
    state.setState(addr, skey, sval)

    assert (code := state.getCode(addr)) == b"hello", f"initial code mismatch: have {code}, want b'hello'"
    assert (val := state.getState(addr, skey)) == sval, (
        f"initial non-committed storage slot mismatch: have {val}, want {sval}"
    )
    assert (val := state.getCommittedState(addr, skey)) is None, (
        f"initial committed storage slot mismatch: have {val}, want None"
    )

    # Copy the state database and check pre- / post-commit code and state
    copyOne = state.copy()
    assert (code := copyOne.getCode(addr)) == b"hello", f"first copy code mismatch: have {code}, want b'hello'"
    assert (val := copyOne.getState(addr, skey)) == sval, (
        f"first copy non-committed storage slot mismatch: have {val}, want {sval}"
    )
    assert (val := copyOne.getCommittedState(addr, skey)) is None, (
        f"first copy committed storage slot mismatch: have {val}, want None"
    )

    # Copy the copy and check the same once more
    copyTwo = copyOne.copy()
    assert (code := copyTwo.getCode(addr)) == b"hello", f"second copy code mismatch: have {code}, want b'hello'"
    assert (val := copyTwo.getState(addr, skey)) == sval, (
        f"second copy non-committed storage slot mismatch: have {val}, want {sval}"
    )
    assert (val := copyTwo.getCommittedState(addr, skey)) is None, (
        f"second copy committed storage slot mismatch: have {val}, want None"
    )

    # Commit state, ensure states can be loaded from disk
    root = state.commit(0)
    state = StateDB(root, tdb, None)
    assert (code := state.getCode(addr)) == b"hello", f"state post-commit code mismatch: have {code}, want b'hello'"
    assert (val := state.getState(addr, skey)) == sval, (
        f"state post-commit non-committed storage slot mismatch: have {val}, want {sval}"
    )
    assert (val := state.getCommittedState(addr, skey)) == sval, (
        f"state post-commit committed storage slot mismatch: have {val}, want {sval}"
    )


def testCopyCopyCopy():
    """
    Tests a regression where committing a copy lost some internal meta information,
    leading to corrupted subsequent copies.
    """
    state = StateDB(common.EmptyRootHash, newDatabase(rawdb.newMemorydb()), None)
    addr = common.bytesToAddress(b"\xaf\xfe" * 10)
    skey = b"aaa"
    sval = b"bbb"

    state.setCode(addr, b"hello")
    state.setState(addr, skey, sval)

    assert (code := state.getCode(addr)) == b"hello", f"initial code mismatch: have {code}, want b'hello'"

    assert (val := state.getState(addr, skey)) == sval, (
        f"initial non-committed storage slot mismatch: have {val}, want {sval}"
    )

    assert (val := state.getCommittedState(addr, skey)) is None, (
        f"initial committed storage slot mismatch: have {val}, want None"
    )

    # Copy the non-committed state database and check pre- / post-commit code and state
    copyOne = state.copy()
    assert (code := copyOne.getCode(addr)) == b"hello", f"first copy code mismatch: have {code}, want b'hello'"
    assert (val := copyOne.getState(addr, skey)) == sval, (
        f"first copy non-committed storage slot mismatch: have {val}, want {sval}"
    )
    assert (val := copyOne.getCommittedState(addr, skey)) is None, (
        f"first copy committed storage slot mismatch: have {val}, want None"
    )

    # Copy the copy and check the same once more
    copyTwo = copyOne.copy()
    assert (code := copyTwo.getCode(addr)) == b"hello", f"second copy code mismatch: have {code}, want b'hello'"
    assert (val := copyTwo.getState(addr, skey)) == sval, (
        f"second copy non-committed storage slot mismatch: have {val}, want {sval}"
    )
    assert (val := copyTwo.getCommittedState(addr, skey)) is None, (
        f"second copy committed storage slot mismatch: have {val}, want None"
    )

    # Copy the copy-copy and check the same once more
    copyThree = copyTwo.copy()
    assert (code := copyThree.getCode(addr)) == b"hello", f"third copy code mismatch: have {code}, want b'hello'"
    assert (val := copyThree.getState(addr, skey)) == sval, (
        f"third copy non-committed storage slot mismatch: have {val}, want {sval}"
    )
    assert (val := copyThree.getCommittedState(addr, skey)) is None, (
        f"third copy committed storage slot mismatch: have {val}, want None"
    )


def testCommitCopy():
    """
    TestCommitCopy tests the copy from a committed state is not fully functional
    """
    db = newDatabase(rawdb.newMemorydb())
    state = StateDB(common.EmptyRootHash, db, None)

    # Create an account and check if the retrieved balance is correct
    address = common.hexToAddress("0x1234567890abcdef1234567890abcdef12345678")

    skey1, skey2 = b"a1", b"a2"
    sval1, sval2 = b"b1", b"b2"

    state.setCode(address, b"hello")
    state.setState(address, skey1, sval1)

    assert (code := state.getCode(address) == b"hello"), f"initial code mismatch: have {code}, want b'hello'"
    assert (val := state.getState(address, skey1)) == sval1, (
        f"initial non-committed storage slot mismatch: have {val}, want {sval1}"
    )
    assert (val := state.getCommittedState(address, skey1)) is None, (
        f"initial committed storage slot mismatch: have {val}, want None"
    )

    root = state.commit(0)

    state = StateDB(root, db, None)
    state.setState(address, skey2, sval2)
    state.commit(1)

    copy = state.copy()
    assert (code := copy.getCode(address)) == b"hello", f"copy code mismatch: have {code}, want b'hello'"

    # Miss slots because of non-functional trie after commit
    assert (val := copy.getState(address, skey1)) is None, f"copy storage slot mismatch: have {val}, want None"
    assert (val := copy.getCommittedState(address, skey1)) is None, (
        f"copy committed storage slot mismatch: have {val}, want None"
    )

    # Slots cached in the stateDB, available after commit
    assert (val := copy.getState(address, skey2)) == sval2, f"copy storage slot mismatch: have {val}, want {sval2}"
    assert (val := copy.getCommittedState(address, skey2)) == sval2, (
        f"copy committed storage slot mismatch: have {val}, want {sval2}"
    )

    assert isinstance(copy.error(), tree.TrieAlreadyCommitted), (
        f"unexpected state error, have: {copy.error()}, want: {tree.TrieAlreadyCommitted}"
    )


# TODO: replace trie to have the miss node error control
@pytest.mark.skip("missing trie node error control")
def testMissingTrieNodes():
    """
    TestMissingTrieNodes tests that if the StateDB fails to load parts of the trie,
    the Commit operation fails with an error
    If we are missing trie nodes, we should not continue writing to the trie
    """
    memdb = rawdb.newMemorydb()
    db = newDatabase(memdb)

    state = StateDB(common.EmptyRootHash, db, None)
    addr = common.bytesToAddress(b"so")
    state.setCode(addr, bytes([0x01, 0x02, 0x03]))
    state.setState(addr, b"key", b"value")
    a2 = common.bytesToAddress(b"another")
    state.setCode(a2, bytes([0x01, 0x02, 0x04]))
    state.setState(a2, b"key", b"value1")
    root = state.commit(0)

    # Create a new state on the old root
    state = StateDB(root, db, None)
    # Now we clear out the memdb
    for kv in memdb.newIterator():
        k = kv.key()
        if k != root:
            memdb.delete(k)

    # Now we should have missing trie nodes
    with pytest.raises(trie.exceptions.MissingTrieNode):
        state.getCode(addr)

    state.commit(1)


def testFlushOrderDataLoss():
    # Create a state trie with many accounts and slots
    memdb = rawdb.newMemorydb()
    statedb = newDatabase(memdb)
    state = StateDB(common.EmptyRootHash, statedb, None)

    for a in range(10):
        state.createContract(common.intToAddress(a))
        for s in range(10):
            state.setState(common.intToAddress(a), bytes([a, s]), bytes([a, s]))

    root = state.commit(0)

    # Reopen the state trie from flushed disk and verify it
    state = StateDB(root, newDatabase(memdb), None)
    for a in range(10):
        for s in range(10):
            assert state.getState(common.intToAddress(a), bytes([a, s])) == bytes([a, s]), (
                f"account {a}: slot {s}: state mismatch: have {state.getState(common.intToAddress(a), bytes([a, s]))}, want {bytes([a, s])}"
            )


# TODO: testDeleteStorage needs block snapshot


def testStorageDirtiness():
    disk = rawdb.newMemorydb()
    tdb = tree.TrieDB(disk)
    db = newDatabaseWithTriedb(disk, tdb)
    state = StateDB(common.EmptyRootHash, db, None)
    addr = common.intToAddress(42)

    def checkDirty(key: bytes, value: bytes, dirty: bool):
        obj = state._getStateObject(addr)
        v, exist = obj.dirtyStorage.get(key, b""), key in obj.dirtyStorage
        assert exist == dirty, f"Unexpected dirty marker: have {exist}, want {dirty}"
        assert v == value, f"Unexpected storage slot: have {v}, want {value}"

    state.createContract(addr)

    # the storage change is noop, no dirty marker
    state.setState(addr, b"\x01", b"")
    checkDirty(b"\x01", b"", False)

    # the storage change is valid, dirty marker is expected
    snap = state.snapshot()
    state.setState(addr, b"\x01", b"\x01")
    checkDirty(b"\x01", b"\x01", True)

    # the storage change is reverted, dirtiness should be revoked
    state.revertToSnapshot(snap)
    checkDirty(b"\x01", b"", False)

    # the storage is reset back to its original value, dirtiness should be revoked
    state.setState(addr, b"\x01", b"\x01")
    snap = state.snapshot()
    state.setState(addr, b"\x01", b"")
    checkDirty(b"\x01", b"", False)

    # the storage change is reverted, dirty value should be set back
    state.revertToSnapshot(snap)
    checkDirty(b"\x01", b"\x01", True)
