from __future__ import annotations

import copy
from abc import ABC, abstractmethod
from typing import Dict, List, Optional

from common.state_account import AccountAttributes
from rawdb.accessors_data_indexes import INDEX_KEY_LENGTH_LIMIT

from . import statedb


class JournalEntry(ABC):
    """
    journalEntry is a modification entry in the state change journal that can be
    reverted on demand.
    """

    @abstractmethod
    def revert(self, statedb: statedb.StateDB):
        """
        revert undoes the changes introduced by this journal entry.
        """
        raise NotImplementedError

    @abstractmethod
    def dirtied(self) -> Optional[bytes]:
        """
        dirtied returns the Vgraph address of the state object that was dirtied.
        """
        raise NotImplementedError

    @abstractmethod
    def copy(self) -> JournalEntry:
        """
        copy returns a deep copy of the journal entry.
        """
        raise NotImplementedError


class Journal:
    """
    journal contains the list of state modifications applied since the last state
    commit. These are tracked to be able to be reverted in the case of an execution
    exception or request for reversal.
    """

    def __init__(self):
        # Current changes tracked by the journal
        self.entries: List[JournalEntry] = []
        # Dirty accounts and the number of changes
        self.dirties: Dict[bytes, int] = {}

    def append(self, entry: JournalEntry):
        """
        append inserts a new modification entry to the end of the change journal.
        """
        self.entries.append(entry)
        if (address := entry.dirtied()) is not None:
            self.dirties[address] = self.dirties.get(address, 0) + 1

    def revert(self, statedb: statedb.StateDB, snapshot: int):
        """
        revert undoes a batch of journalled modifications along with any reverted
        dirty handling too.
        """
        for i in range(len(self.entries) - 1, snapshot - 1, -1):
            # Undo the changes made by the operation
            self.entries[i].revert(statedb)

            if (address := self.entries[i].dirtied()) is not None:
                self.dirties[address] -= 1
                if self.dirties[address] == 0:
                    del self.dirties[address]
        self.entries = self.entries[:snapshot]

    def dirty(self, address: bytes):
        """
        dirty explicitly sets an address to dirty, even if the change entries would
        otherwise suggest it as clean.
        """
        self.dirties[address] = self.dirties.get(address, 0) + 1

    def __len__(self) -> int:
        """
        returns the current number of entries in the journal.
        """
        return len(self.entries)

    def copy(self) -> Journal:
        """
        copy returns a deep copy of the journal.
        """
        return copy.deepcopy(self)


class CreateContractChange(JournalEntry):
    """
    createContractChange represents an account becoming a contract-account.
    """

    def __init__(self, account: bytes):
        self.account: bytes = account

    def revert(self, statedb: statedb.StateDB):
        # TODO: graceful way to lazy destruct the contract
        # statedb._getStateObject(self.account)._newContract = False
        statedb.stateObjects.pop(self.account, None)
        statedb.getTrie().deleteAccount(self.account)

    def dirtied(self) -> Optional[bytes]:
        return None

    def copy(self) -> JournalEntry:
        return CreateContractChange(self.account)


class StorageChange(JournalEntry):
    """
    storageChange represents a storage slot modification.
    """

    def __init__(self, account: bytes, key: bytes, preValue: bytes, originalValue: bytes):
        self.account: bytes = account
        self.key: bytes = key
        self.preValue: bytes = preValue
        self.originalValue: bytes = originalValue

    def revert(self, statedb: statedb.StateDB):
        statedb._getStateObject(self.account)._setState(self.key, self.preValue, self.originalValue)

    def dirtied(self) -> Optional[bytes]:
        return self.account

    def copy(self) -> JournalEntry:
        return StorageChange(self.account, self.key, self.preValue, self.originalValue)


class CodeChange(JournalEntry):
    def __init__(self, account: bytes, prevCode: bytes, prevHash: bytes):
        self.account: bytes = account
        self.prevCode: bytes = prevCode
        self.prevHash: bytes = prevHash

    def revert(self, statedb: statedb.StateDB):
        statedb._getStateObject(self.account)._setCode(self.prevCode, self.prevHash)

    def dirtied(self) -> Optional[bytes]:
        return self.account

    def copy(self) -> JournalEntry:
        return copy.deepcopy(self)


# TODO: Implement TransientStorage
# class TransientStorageChange(JournalEntry):
#     def __int__(self, account: bytes, key: bytes, preValue: bytes):
#         self.account: bytes = account
#         self.key: bytes = key
#         self.preValue: bytes = preValue
#
#     def revert(self, statedb: statedb.StateDB):
#         statedb._setTransientState(self.account, self.key, self.preValue)
#
#     def dirtied(self) -> Optional[bytes]:
#         return None
#
#     def copy(self) -> JournalEntry:
#         return copy.deepcopy(self)


class CreateObjectChange(JournalEntry):
    """
    createObjectChange represents an account being created.
    """

    def __init__(self, account: bytes):
        self.account: bytes = account

    def revert(self, statedb: statedb.StateDB):
        statedb.stateObjects.pop(self.account, None)

    def dirtied(self) -> Optional[bytes]:
        return None

    def copy(self) -> JournalEntry:
        return CreateObjectChange(self.account)


class AttributesChange(JournalEntry):
    """
    attributesChange represents an account's attributes being changed.
    """

    def __init__(self, account: bytes, prevAttributes: AccountAttributes):
        self.account: bytes = account
        self.prevAttributes: AccountAttributes = prevAttributes

    def revert(self, statedb: statedb.StateDB):
        statedb._getStateObject(self.account)._setAttributes(self.prevAttributes)

    def dirtied(self) -> Optional[bytes]:
        return self.account

    def copy(self) -> JournalEntry:
        return copy.deepcopy(self)


class ContractDataIndexChange(JournalEntry):
    """
    ContractDataIndexChange represents a contract data index modification.

    This journal entry tracks changes to contract data indexes, allowing them
    to be reverted if needed.
    """

    def __init__(self, contractAddress: bytes, index: bytes, prevValue: Optional[bytes]):
        """
        Initialize a new ContractDataIndexChange.

        Args:
            contractAddress: The contract address
            index: The index key (must not exceed 255 bytes)
            prevValue: The previous value of the index, or None if it was newly created
        """
        if len(index) > INDEX_KEY_LENGTH_LIMIT:
            raise ValueError(f"index key length must not exceed {INDEX_KEY_LENGTH_LIMIT} bytes")

        self.contractAddress: bytes = contractAddress
        self.index: bytes = index
        self.prevValue: Optional[bytes] = prevValue

    def revert(self, statedb: statedb.StateDB):
        """
        Revert the contract data index change.

        If prevValue is None, the index is deleted.
        Otherwise, the index is restored to its previous value.
        This directly updates the index cache to ensure consistency during reverts.
        """
        cacheKey = (self.contractAddress, self.index)

        if self.prevValue is None:
            # This was a new index, so we should remove it from the cache
            if cacheKey in statedb.indexCache:
                del statedb.indexCache[cacheKey]
        else:
            # Restore the previous value in the cache
            statedb.indexCache[cacheKey] = self.prevValue

    def dirtied(self) -> Optional[bytes]:
        """
        Return the contract address that was modified.
        """
        return self.contractAddress

    def copy(self) -> JournalEntry:
        """
        Create a deep copy of this journal entry.
        """
        return copy.deepcopy(self)


class AddLogChange(JournalEntry):
    """
    AddLogChange represents a log entry being added.
    """

    def __init__(self, txHash: bytes) -> None:
        self.txHash: bytes = txHash

    def revert(self, statedb: statedb.StateDB):
        logs = statedb.logs[self.txHash]
        if len(logs) == 1:
            del statedb.logs[self.txHash]
        else:
            statedb.logs[self.txHash] = logs[:-1]
        statedb.logSize -= 1

    def dirtied(self) -> Optional[bytes]:
        return None

    def copy(self) -> JournalEntry:
        return AddLogChange(self.txHash)
