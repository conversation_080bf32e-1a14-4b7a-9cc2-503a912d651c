"""
Performance monitoring utilities for VGraph watcher
"""

import asyncio
import functools
import time
from contextlib import contextmanager
from typing import Any, Callable, Dict, Optional

from watcher.logger import Logger


class PerformanceMonitor:
    """Performance monitoring for VGraph watcher operations"""

    # Store operation statistics
    operation_stats: Dict[str, Dict[str, Any]] = {}

    @classmethod
    @contextmanager
    def measure_time(
        cls, operation_name: str, event_type: str = "METRICS", context: dict = None, log_level: str = "DEBUG"
    ):
        """Context manager to measure operation execution time"""
        start_time = time.time()
        try:
            yield
        finally:
            elapsed = time.time() - start_time

            # Prepare context information for the log
            context_info = ""
            if context:
                context_items = ["{0}={1}".format(k, v) for k, v in context.items()]
                context_info = " (" + ", ".join(context_items) + ")"

            # Log the performance information at the specified level
            if log_level == "DEBUG":
                Logger.debug(f"{operation_name}{context_info} completed in {elapsed:.3f}s", event_type)
            elif log_level == "INFO":
                Logger.info(f"{operation_name}{context_info} completed in {elapsed:.3f}s", event_type)
            elif log_level == "TRACE":
                Logger.trace(f"{operation_name}{context_info} completed in {elapsed:.3f}s", event_type)

            # Store statistics
            if operation_name not in cls.operation_stats:
                cls.operation_stats[operation_name] = {
                    "count": 0,
                    "total_time": 0,
                    "min_time": float("inf"),
                    "max_time": 0,
                }

            stats = cls.operation_stats[operation_name]
            stats["count"] += 1
            stats["total_time"] += elapsed
            stats["min_time"] = min(stats["min_time"], elapsed)
            stats["max_time"] = max(stats["max_time"], elapsed)

    @classmethod
    def get_stats(cls) -> Dict[str, Dict[str, Any]]:
        """Get performance statistics for all operations"""
        result = {}

        for op_name, stats in cls.operation_stats.items():
            result[op_name] = {
                "count": stats["count"],
                "total_time": stats["total_time"],
                "min_time": stats["min_time"],
                "max_time": stats["max_time"],
                "avg_time": stats["total_time"] / stats["count"] if stats["count"] > 0 else 0,
            }

        return result

    @classmethod
    def log_stats(cls, event_type: str = "METRICS"):
        """Log performance statistics for all operations"""
        stats = cls.get_stats()

        if not stats:
            Logger.debug("No performance statistics available", event_type)
            return

        Logger.debug("Performance statistics:", event_type)
        for op_name, op_stats in stats.items():
            Logger.debug(
                f"  {op_name}: {op_stats['count']} calls, "
                f"avg: {op_stats['avg_time']:.3f}s, "
                f"min: {op_stats['min_time']:.3f}s, "
                f"max: {op_stats['max_time']:.3f}s, "
                f"total: {op_stats['total_time']:.3f}s",
                event_type,
            )

    @classmethod
    def reset_stats(cls):
        """Reset all performance statistics"""
        cls.operation_stats.clear()


def measure_performance(
    operation_name: Optional[str] = None,
    event_type: str = "METRICS",
    log_level: str = "DEBUG",
    context_extractor: Callable = None,
):
    """Decorator to measure function execution time

    Args:
        operation_name: Name of the operation (defaults to function name)
        event_type: Event type for logging
        log_level: Log level (DEBUG, INFO, TRACE)
        context_extractor: Function that extracts context from function arguments
                          Should take the same arguments as the decorated function
                          and return a dict of context information
    """

    def decorator(func: Callable):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            op_name = operation_name or func.__name__

            # Extract context if provided
            context = None
            if context_extractor:
                context = context_extractor(*args, **kwargs)

            with PerformanceMonitor.measure_time(op_name, event_type, context, log_level):
                return await func(*args, **kwargs)

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            op_name = operation_name or func.__name__

            # Extract context if provided
            context = None
            if context_extractor:
                context = context_extractor(*args, **kwargs)

            with PerformanceMonitor.measure_time(op_name, event_type, context, log_level):
                return func(*args, **kwargs)

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator
