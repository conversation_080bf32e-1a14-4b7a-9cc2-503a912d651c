import asyncio
from typing import Any, List, Optional

import aiorpcx
import serde.json

from common import bytesToHex
from common.models import Block, Receipt
from watcher.config import WatcherConfig
from watcher.logger import Logger


# Connection pool manager to reuse connections
class ConnectionManager:
    def __init__(self, config: WatcherConfig):
        self.config = config
        # Store the connection context managers, not just the sessions
        self._mainnet_connection = None
        self._sidenet_connection = None
        self.mainnet_session = None
        self.sidenet_session = None

    async def get_mainnet_session(self) -> Any:
        if not self.mainnet_session or self.mainnet_session.is_closing():
            # Store the connection context manager
            self._mainnet_connection = aiorpcx.connect_rs(self.config.mainnet["host"], self.config.mainnet["port"])
            # Enter the context to get the session
            self.mainnet_session = await self._mainnet_connection.__aenter__()
        return self.mainnet_session

    async def get_sidenet_session(self) -> Any:
        if not self.sidenet_session or self.sidenet_session.is_closing():
            # Store the connection context manager
            self._sidenet_connection = aiorpcx.connect_rs(self.config.sidenet["host"], self.config.sidenet["port"])
            # Enter the context to get the session
            self.sidenet_session = await self._sidenet_connection.__aenter__()
        return self.sidenet_session

    async def get_transaction_receipt(self, transaction_hash: str, timeout: int = 30) -> Optional[Receipt]:
        """Get a receipt for a transaction from mainnet

        Args:
            transaction_hash (str): The hash of the transaction
            timeout (int, optional): Request timeout in seconds. Defaults to 30.

        Returns:
            Optional[Receipt]: The receipt if found, None otherwise
        """
        try:
            session = await self.get_mainnet_session()
            result = await asyncio.wait_for(
                session.send_request("transaction.get_receipt", [transaction_hash]), timeout=timeout
            )

            if not result or result.get("message") != "Transaction found" or not result.get("receipt"):
                return None

            receipt_data = result.get("receipt")
            return serde.json.from_dict(Receipt, receipt_data)
        except asyncio.TimeoutError:
            Logger.error(f"Timeout getting receipt for transaction {transaction_hash}", "RECEIPT")
            return None
        except Exception as e:
            Logger.error(f"Failed to get receipt for transaction {transaction_hash}: {e}", "RECEIPT")
            return None

    async def get_block_receipts(self, block: Block) -> List[Receipt]:
        """Get receipts for all transactions in a block

        Args:
            block (Block): The block containing transactions
        Returns:
            List[Receipt]: List of receipts for the transactions in the block
        """
        receipts = []

        # Process transactions in batches to avoid overwhelming the network
        batch_size = 10
        for i in range(0, len(block.transactions), batch_size):
            batch = block.transactions[i : i + batch_size]
            tasks = []

            for tx in batch:
                tasks.append(self.get_transaction_receipt(bytesToHex(tx.hash())))

            # Wait for all tasks in the batch to complete
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    Logger.error(
                        f"Error getting receipt for transaction {bytesToHex(batch[j].hash())}: {result}", "RECEIPT"
                    )
                    continue

                if result is not None:
                    receipts.append(result)

            # Add a small delay between batches to avoid rate limiting
            if i + batch_size < len(block.transactions):
                await asyncio.sleep(0.5)

        Logger.info(f"Retrieved {len(receipts)} receipts for block #{block.height()}", "RECEIPT")
        return receipts

    # get_receipt_by_transaction_hash method has been removed as receipt data is now part of the transaction
    # Use get_transaction_by_hash from explorer_db contract instead

    async def close(self):
        # Close the mainnet connection using the stored context manager
        if self._mainnet_connection:
            try:
                # Call __aexit__ on the connection object
                await self._mainnet_connection.__aexit__(None, None, None)
                Logger.debug("Mainnet connection closed.", "NET")
            except Exception as e:
                Logger.error(f"Error closing mainnet connection: {e}", "NET")
            finally:
                self._mainnet_connection = None
                self.mainnet_session = None

        # Close the sidenet connection using the stored context manager
        if self._sidenet_connection:
            try:
                # Call __aexit__ on the connection object
                await self._sidenet_connection.__aexit__(None, None, None)
                Logger.debug("Sidenet connection closed.", "NET")
            except Exception as e:
                Logger.error(f"Error closing sidenet connection: {e}", "NET")
            finally:
                self._sidenet_connection = None
                self.sidenet_session = None
