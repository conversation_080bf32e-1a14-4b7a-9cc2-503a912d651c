import os
from typing import Any, Dict, Optional

from ruamel.yaml import <PERSON>AM<PERSON>

from watcher.logger import Logger


class WatcherConfig:
    """Configuration manager for VGraph watcher"""

    CONFIG_PATH = "watcher_config.yaml"

    def __init__(self):
        self.config = self._load_config()

        # Initialize logger with config
        log_config = {
            "log_level": self.config.get("log_level", "INFO"),
            "use_colored_logs": self.config.get("use_colored_logs", True),
            "log_to_file": self.config.get("log_to_file", False),
            "log_dir": self.config.get("log_dir", "logs"),
            "max_log_size": self.config.get("max_log_size", 10 * 1024 * 1024),  # 10MB
            "log_rotation_count": self.config.get("log_rotation_count", 5),
        }
        Logger.initialize(log_config)

        if log_config["use_colored_logs"]:
            Logger.debug("Colored logs enabled", "CONFIG")
        else:
            Logger.debug("Colored logs disabled", "CONFIG")

        if log_config["log_to_file"]:
            Logger.debug(f"File logging enabled in directory: {log_config['log_dir']}", "CONFIG")

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from yaml file, preserving comments"""
        yaml = YAML()  # Create a YAML object
        if os.path.exists(self.CONFIG_PATH):
            Logger.debug(f"Loading config from {self.CONFIG_PATH}", "CONFIG")
            try:
                with open(self.CONFIG_PATH, "r") as f:
                    return yaml.load(f)  # Use yaml.load()
            except Exception as e:
                Logger.error(f"Failed to load config file {self.CONFIG_PATH}: {e}", "CONFIG")
                Logger.warn("Using default values due to loading error.", "CONFIG")
                return {}  # Return empty dict on error
        else:
            Logger.warn(f"Config file {self.CONFIG_PATH} not found, using default values", "CONFIG")
            return {}

    def save_config(self) -> None:
        """Save current configuration to yaml file, preserving comments"""
        yaml = YAML()  # Create a YAML object
        yaml.indent(mapping=2, sequence=4, offset=2)  # Optional: control indentation
        Logger.debug(f"Saving config to {self.CONFIG_PATH}", "CONFIG")
        try:
            with open(self.CONFIG_PATH, "w") as f:
                yaml.dump(self.config, f)  # Use yaml.dump()
        except Exception as e:
            Logger.error(f"Failed to save config file {self.CONFIG_PATH}: {e}", "CONFIG")

    def update_contract_address(self, address: str) -> None:
        """Update contract address in config and save to file"""
        # Ensure 'contract' key exists
        if "contract" not in self.config:
            self.config["contract"] = {}
        self.config["contract"]["address"] = address
        self.save_config()
        Logger.info(f"Contract address updated to {address}", "CONTRACT")

    @property
    def mainnet(self) -> Dict[str, Any]:
        return self.config.get("mainnet", {"host": "localhost", "port": 9877})

    @property
    def sidenet(self) -> Dict[str, Any]:
        return self.config.get("sidenet", {"host": "localhost", "port": 10077})

    @property
    def contract_address(self) -> Optional[str]:
        return self.config.get("contract", {}).get("address", "")

    @property
    def wasm_path(self) -> str:
        return self.config.get("contract", {}).get("wasm_path", "../contract/base/explorer_db.wasm")

    @property
    def private_key(self) -> str:
        return self.config.get(
            "private_key",
            "0x0cf9455dbbb7b26ff02fe76d25f228b0eee8d077f8d6ea786c0cefbd2a91480731729d4ec734c5198f72c9ccda9e0bd62ed512ee3e8b526b35d6db147d9d12a5",
        )

    @property
    def sync_interval(self) -> int:
        return self.config.get("watcher", {}).get("sync_interval", 1)

    @property
    def batch_size(self) -> int:
        return self.config.get("watcher", {}).get("batch_size", 100)

    @property
    def log_interval(self) -> int:
        return self.config.get("watcher", {}).get("log_interval", 10)

    @property
    def fuel(self) -> int:
        return self.config.get("fuel", 10_000_000)

    @property
    def batch_sleep_interval(self) -> int:
        """Sleep time in seconds between batches of 100 blocks"""
        return self.config.get("watcher", {}).get("batch_sleep_interval", 15)

    @property
    def request_timeout(self) -> int:
        """Timeout in seconds for blockchain requests"""
        return self.config.get("watcher", {}).get("request_timeout", 60)

    @property
    def max_retries(self) -> int:
        """Maximum number of retries for failed operations"""
        return self.config.get("watcher", {}).get("max_retries", 3)
