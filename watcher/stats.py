import time
from typing import Any, Dict

from watcher.models import StoreBlock


# Stats tracker for runtime performance
class SyncStats:
    def __init__(self):
        self.start_time = time.time()
        self.blocks_processed = 0
        self.transactions_processed = 0
        self.failed_attempts = 0
        self.last_stats_time = time.time()

    def record_block(self, block: StoreBlock):
        self.blocks_processed += 1
        self.transactions_processed += len(block.transactions)

    def record_failure(self):
        self.failed_attempts += 1

    def get_stats(self) -> Dict[str, Any]:
        elapsed = time.time() - self.start_time
        blocks_per_second = self.blocks_processed / elapsed if elapsed > 0 else 0
        return {
            "elapsed_time": elapsed,
            "blocks_processed": self.blocks_processed,
            "transactions_processed": self.transactions_processed,
            "failed_attempts": self.failed_attempts,
            "blocks_per_second": blocks_per_second,
        }

    def should_log_stats(self, interval: int) -> bool:
        now = time.time()
        if now - self.last_stats_time >= interval:
            self.last_stats_time = now
            return True
        return False
