import os
from datetime import datetime


# Enhanced structured logger with multiple levels and file output
class Logger:
    # ANSI color codes
    COLORS = {
        "RESET": "\033[0m",
        "BLACK": "\033[30m",
        "RED": "\033[31m",
        "GREEN": "\033[32m",
        "YELLOW": "\033[33m",
        "BLUE": "\033[34m",
        "MAGENTA": "\033[35m",
        "CYAN": "\033[36m",
        "WHITE": "\033[37m",
        "BOLD": "\033[1m",
        "UNDERLINE": "\033[4m",
        "BG_BLACK": "\033[40m",
        "BG_RED": "\033[41m",
        "BG_GREEN": "\033[42m",
        "BG_YELLOW": "\033[43m",
        "BG_BLUE": "\033[44m",
        "BG_MAGENTA": "\033[45m",
        "BG_CYAN": "\033[46m",
        "BG_WHITE": "\033[47m",
    }

    # Define colors for different log levels
    LEVEL_COLORS = {
        "TRACE": COLORS["MAGENTA"],
        "DEBUG": COLORS["BLUE"],
        "INFO": COLORS["GREEN"],
        "WARNING": COLORS["YELLOW"],
        "ERROR": COLORS["RED"],
        "CRITICAL": COLORS["BG_RED"] + COLORS["WHITE"],
    }

    # Define event types with their colors
    _EVENT_TYPES_BASE = {
        "BLOCK": COLORS["CYAN"],
        "TX": COLORS["MAGENTA"],
        "SYNC": COLORS["GREEN"],
        "NET": COLORS["BLUE"],
        "CONTRACT": COLORS["YELLOW"],
        "CONFIG": COLORS["WHITE"],
        "SYSTEM": COLORS["BOLD"],
        "STATS": COLORS["CYAN"],
        "METRICS": COLORS["YELLOW"],
        "PROGRESS": COLORS["GREEN"],
        "AGGREGATE": COLORS["MAGENTA"],
        "TEST": COLORS["BLUE"],
    }

    # Generate EVENT_TYPES with properly formatted prefixes
    EVENT_TYPES = {}
    for event_type, color in _EVENT_TYPES_BASE.items():
        EVENT_TYPES[event_type] = {"prefix": event_type, "color": color}

    # Define log levels with numeric values
    LEVELS = {"TRACE": -1, "DEBUG": 0, "INFO": 1, "WARNING": 2, "ERROR": 3, "CRITICAL": 4}
    current_level = "INFO"  # Default level
    use_colors = True  # Color can be disabled via configuration
    log_to_file = False  # Whether to log to file
    log_file = None  # Log file handler
    log_dir = "logs"  # Directory for log files
    log_filename = None  # Current log filename
    max_log_size = 10 * 1024 * 1024  # 10MB default max log size
    log_rotation_count = 5  # Keep 5 rotated logs by default

    # Context information for logs
    context = {}

    @staticmethod
    def initialize(config: dict = None):
        """Initialize logger with configuration"""
        if config is None:
            config = {}

        # Set log level from config
        log_level = config.get("log_level", "INFO")

        Logger.set_level(log_level)

        # Set whether to use colored logs
        use_colors = config.get("use_colored_logs", True)
        Logger.set_use_colors(use_colors)

        # Set whether to log to file
        log_to_file = config.get("log_to_file", False)
        if log_to_file:
            log_dir = config.get("log_dir", "logs")
            max_log_size = config.get("max_log_size", 10 * 1024 * 1024)  # 10MB default
            log_rotation_count = config.get("log_rotation_count", 5)
            Logger.setup_file_logging(log_dir, max_log_size, log_rotation_count)

    @staticmethod
    def set_level(level: str):
        """Set the current log level"""
        if level in Logger.LEVELS:
            Logger.current_level = level
            Logger.info(f"Log level set to {level}", "CONFIG")

    @staticmethod
    def set_use_colors(use_colors: bool):
        """Enable or disable colored output"""
        Logger.use_colors = use_colors

    @staticmethod
    def setup_file_logging(log_dir: str = "logs", max_size: int = 10 * 1024 * 1024, backup_count: int = 5):
        """Setup logging to file with rotation"""
        Logger.log_to_file = True
        Logger.log_dir = log_dir
        Logger.max_log_size = max_size
        Logger.log_rotation_count = backup_count

        # Create log directory if it doesn't exist
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # Create a new log file with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        Logger.log_filename = f"{log_dir}/vgraph_watcher_{timestamp}.log"

        # Create the log file
        Logger.log_file = open(Logger.log_filename, "a")
        Logger.info(f"Logging to file: {Logger.log_filename}", "CONFIG")

        # Check for log rotation
        Logger._check_log_rotation()

    @staticmethod
    def _check_log_rotation():
        """Check if log file needs rotation"""
        if not Logger.log_to_file or not Logger.log_filename:
            return

        try:
            # Check current log file size
            if os.path.getsize(Logger.log_filename) > Logger.max_log_size:
                # Close current log file
                if Logger.log_file:
                    Logger.log_file.close()

                # Create a new log file with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                Logger.log_filename = f"{Logger.log_dir}/vgraph_watcher_{timestamp}.log"
                Logger.log_file = open(Logger.log_filename, "a")

                # Remove old log files if we have too many
                log_files = sorted([f for f in os.listdir(Logger.log_dir) if f.startswith("vgraph_watcher_")])
                if len(log_files) > Logger.log_rotation_count:
                    for old_file in log_files[: -Logger.log_rotation_count]:
                        try:
                            os.remove(os.path.join(Logger.log_dir, old_file))
                        except Exception:
                            pass  # Ignore errors when removing old logs
        except Exception as e:
            print(f"Error during log rotation: {e}")

    @staticmethod
    def set_context(key: str, value: any):
        """Set context information for logs"""
        Logger.context[key] = value

    @staticmethod
    def clear_context(key: str = None):
        """Clear specific or all context information"""
        if key is None:
            Logger.context.clear()
        elif key in Logger.context:
            del Logger.context[key]

    @staticmethod
    def _should_log(level: str) -> bool:
        """Check if the given level should be logged based on current level"""
        return Logger.LEVELS[level] >= Logger.LEVELS[Logger.current_level]

    @staticmethod
    def _format_message(level: str, message: str, event_type: str = None) -> str:
        """Format log message with timestamp, level, event type and context"""
        # Get current time with full seconds and milliseconds
        now = datetime.now()
        timestamp = now.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # Format with milliseconds

        # Context information is no longer displayed in logs
        context_str = ""

        if not Logger.use_colors:
            # Output format without colors - Apache style
            if event_type:
                return f"[{timestamp}] [{level}] [{event_type}] - {message}{context_str}"
            else:
                return f"[{timestamp}] [{level}] - {message}{context_str}"

        # Output format with colors - Apache style
        timestamp_color = Logger.COLORS["BOLD"]
        level_color = Logger.LEVEL_COLORS.get(level, Logger.COLORS["RESET"])

        if event_type and event_type in Logger.EVENT_TYPES:
            event_color = Logger.EVENT_TYPES[event_type]["color"]
            return (
                f"{timestamp_color}[{timestamp}]{Logger.COLORS['RESET']} "
                f"{level_color}[{level}]{Logger.COLORS['RESET']} "
                f"{event_color}[{event_type}]{Logger.COLORS['RESET']} - "
                f"{message}{context_str}"
            )
        elif event_type:
            # For custom event types
            return (
                f"{timestamp_color}[{timestamp}]{Logger.COLORS['RESET']} "
                f"{level_color}[{level}]{Logger.COLORS['RESET']} "
                f"{Logger.COLORS['CYAN']}[{event_type}]{Logger.COLORS['RESET']} - "
                f"{message}{context_str}"
            )
        else:
            return (
                f"{timestamp_color}[{timestamp}]{Logger.COLORS['RESET']} "
                f"{level_color}[{level}]{Logger.COLORS['RESET']} - "
                f"{message}{context_str}"
            )

    @staticmethod
    def _log(level: str, message: str, event_type: str = None):
        """Internal method to handle logging to console and file"""
        if not Logger._should_log(level):
            return

        formatted_msg = Logger._format_message(level, message, event_type)

        # Print to console
        print(formatted_msg)

        # Write to log file if enabled
        if Logger.log_to_file and Logger.log_file:
            try:
                # For file logging, strip ANSI color codes
                clean_msg = formatted_msg
                for color_code in Logger.COLORS.values():
                    clean_msg = clean_msg.replace(color_code, "")

                Logger.log_file.write(clean_msg + "\n")
                Logger.log_file.flush()

                # Check if we need to rotate logs
                Logger._check_log_rotation()
            except Exception as e:
                print(f"Error writing to log file: {e}")

    @staticmethod
    def trace(message: str, event_type: str = None):
        """Log a trace message (most detailed level)"""
        Logger._log("TRACE", message, event_type)

    @staticmethod
    def debug(message: str, event_type: str = None):
        """Log a debug message"""
        Logger._log("DEBUG", message, event_type)

    @staticmethod
    def info(message: str, event_type: str = None):
        """Log an info message"""
        Logger._log("INFO", message, event_type)

    @staticmethod
    def warn(message: str, event_type: str = None):
        """Log a warning message"""
        Logger._log("WARNING", message, event_type)

    @staticmethod
    def warning(message: str, event_type: str = None):
        """Alias for warn method"""
        Logger.warn(message, event_type)

    @staticmethod
    def error(message: str, event_type: str = None):
        """Log an error message"""
        Logger._log("ERROR", message, event_type)

    @staticmethod
    def critical(message: str, event_type: str = None):
        """Log a critical error message"""
        Logger._log("CRITICAL", message, event_type)

    @staticmethod
    def close():
        """Close log file if open"""
        if Logger.log_file:
            try:
                Logger.log_file.close()
                Logger.log_file = None
            except Exception as e:
                print(f"Error closing log file: {e}")
