import asyncio
from typing import List, Optional, Tuple

from common import from<PERSON>son, to<PERSON><PERSON>
from common.models import Block
from swaggerapi.utils import getContractHexBytecode
from watcher.config import WatcherConfig
from watcher.connection import ConnectionManager
from watcher.log_aggregator import flush_logs, log_aggregated
from watcher.logger import Logger
from watcher.models import StoreBlock
from watcher.performance import PerformanceMonitor, measure_performance
from watcher.progress import ProgressTracker
from watcher.stats import SyncStats


@measure_performance("Register Contract", "CONTRACT")
async def register_contract(conn_manager: ConnectionManager, config: WatcherConfig) -> str:
    """Register explorer_db contract and return its address"""
    Logger.info("Registering explorer_db Contract", "CONTRACT")

    contractHexBytecodeStr = getContractHexBytecode(config.wasm_path)
    session = await conn_manager.get_sidenet_session()

    params = {
        "contract_hex_bytecode": contractHexBytecodeStr,
        "constructor_parameters": [],
        "contract_source_url": config.config.get("contract", {}).get("source_url", "xxx"),
        "upgradable": False,
        "git_commit_hash": config.config.get("contract", {}).get("git_commit_hash", "xxx"),
        "reproducible_build": False,
        "fuel": config.fuel,  # Using the property that now checks both 'fuel' and 'fuel_limit'
        "signatures": [],
        "public_keys": [],
        "privatekey": config.private_key,
    }

    # Start progress tracking
    ProgressTracker.update("Contract Registration", 0, 2, event_type="CONTRACT")

    result = await session.send_request("contract.create", [params])
    transaction_hash = result["transaction_hash"]
    Logger.debug(f"Contract creation tx: {transaction_hash}", "TX")
    Logger.info("Processing transaction (this may take some time)...", "TX")

    # Update progress
    ProgressTracker.update(
        "Contract Registration", 1, 2, event_type="CONTRACT", extra_info=f"Transaction hash: {transaction_hash}"
    )

    # Wait for transaction confirmation
    retry_count = 0
    while retry_count < 10:
        try:
            result = await session.send_request("transaction.get_receipt", [transaction_hash])
            if result["message"] == "Transaction found":
                contractAddress = result["receipt"]["op_result"]["contract_address"]
                Logger.info(f"Contract registered successfully: {contractAddress}", "CONTRACT")

                # Update config with the new contract address
                config.update_contract_address(contractAddress)

                # Complete progress tracking
                ProgressTracker.update("Contract Registration", 2, 2, event_type="CONTRACT")
                ProgressTracker.complete("Contract Registration", event_type="CONTRACT")

                return contractAddress

            retry_count += 1
            await asyncio.sleep(3)
        except Exception as e:
            Logger.error(f"Failed to get receipt: {e}", "TX")
            retry_count += 1
            await asyncio.sleep(3)

    # Mark progress as failed
    ProgressTracker.complete("Contract Registration", event_type="CONTRACT", success=False)
    raise Exception("Failed to register contract after multiple attempts")


@measure_performance("Get Blockchain Info", "NET")
async def get_blockchain_info(conn_manager: ConnectionManager) -> Tuple[int, Optional[Block]]:
    """Get the latest block height and genesis block from mainnet"""
    session = await conn_manager.get_mainnet_session()

    # Get latest block
    result = await session.send_request("blocktree.best_block", [])
    latest_block = fromJson(Block, result)
    latest_height = latest_block.header.height

    return latest_height, latest_block


@measure_performance("Get Block", "BLOCK")
async def get_block_by_height(height: int, conn_manager: ConnectionManager) -> Optional[StoreBlock]:
    """Get a block by its height from mainnet and include receipt information"""
    try:
        # Get block data
        session = await conn_manager.get_mainnet_session()
        result = await session.send_request("blocktree.get_block_by_height", [height])
        if not result:
            return None

        block = fromJson(Block, result)

        # Get receipt information for the block
        receipts = await conn_manager.get_block_receipts(block)

        # If receipts are available, recreate StoreBlock with receipt information
        if receipts:
            return StoreBlock.fromBlock(block, receipts)
        else:
            # Log the issue but continue with the block without receipts
            Logger.error(f"No receipts found for block #{height}", "RECEIPT")

        return None
    except Exception as e:
        Logger.error(f"Failed to get block at height {height}: {e}", "BLOCK")
        return None


@measure_performance("Batch Get Blocks", "BLOCK")
async def batch_get_blocks(start_height: int, end_height: int, conn_manager: ConnectionManager) -> List[StoreBlock]:
    """Get multiple blocks in parallel for efficiency"""
    # Start progress tracking
    operation_name = f"Fetching Blocks {start_height}-{end_height - 1}"
    total_blocks = end_height - start_height
    ProgressTracker.update(operation_name, 0, total_blocks, event_type="PROGRESS")

    # Create tasks for fetching blocks
    tasks = []
    for height in range(start_height, end_height):
        tasks.append(get_block_by_height(height, conn_manager))

    # Wait for all tasks to complete
    blocks = []
    completed = 0

    for block in await asyncio.gather(*tasks):
        completed += 1
        if block is not None:
            blocks.append(block)

        # Update progress every 10 blocks or at the end
        if completed % 10 == 0 or completed == total_blocks:
            ProgressTracker.update(
                operation_name,
                completed,
                total_blocks,
                event_type="PROGRESS",
                extra_info=f"Found {len(blocks)} valid blocks",
            )

    # Complete progress tracking
    ProgressTracker.complete(operation_name, event_type="PROGRESS")

    # Log aggregated message about fetched blocks
    log_aggregated("INFO", f"Fetched {len(blocks)} valid blocks out of {total_blocks} requested", "BLOCK")
    flush_logs()  # Ensure the message is displayed

    return blocks


@measure_performance(
    "Insert Block",
    "BLOCK",
    log_level="DEBUG",
    context_extractor=lambda block, *args, **kwargs: {"height": block.height},
)
async def insert_block_to_explorer_db(
    block: StoreBlock, contract_address: str, conn_manager: ConnectionManager, config: WatcherConfig
) -> str:
    """Insert block into explorer_db contract and return transaction hash"""
    session = await conn_manager.get_sidenet_session()
    params = {
        "contract_address": contract_address,
        "function_name": "insert_block",
        "args": [toJson(block)],
        "fuel": config.fuel,  # Using the property that now checks both 'fuel' and 'fuel_limit'
        "signatures": [],
        "publickeys": [],
        "privatekey": config.private_key,
    }

    # Add retry logic with timeout
    retry_count = 0
    max_retries = config.max_retries
    timeout = config.request_timeout

    while True:
        try:
            # Set timeout for the request
            result = await asyncio.wait_for(session.send_request("contract.execute", [params]), timeout=timeout)

            if result.__contains__("error"):
                raise Exception(f"Failed to insert block: {result['error']}")

            return result["transaction_hash"]

        except asyncio.TimeoutError as e:
            retry_count += 1
            if retry_count > max_retries:
                raise Exception(f"Block insertion timed out after {timeout}s and {max_retries} retries") from e

            Logger.warning(f"Timeout inserting block #{block.height}, retrying ({retry_count}/{max_retries})", "BLOCK")
            # Exponential backoff
            await asyncio.sleep(2**retry_count)

        except Exception as e:
            retry_count += 1
            if retry_count > max_retries:
                raise

            Logger.warning(
                f"Error inserting block #{block.height}: {e}, retrying ({retry_count}/{max_retries})", "BLOCK"
            )
            # Exponential backoff
            await asyncio.sleep(2**retry_count)


@measure_performance("Get Synced Block Height", "SYNC")
async def get_synced_block_height(
    contract_address: str, conn_manager: ConnectionManager, config: WatcherConfig
) -> Optional[int]:
    """Get the current synced block height from the explorer_db contract"""
    retry_count = 0
    max_retries = config.max_retries
    timeout = config.request_timeout

    while True:
        try:
            session = await conn_manager.get_sidenet_session()
            params = {
                "contract_address": contract_address,
                "function_name": "get_synced_block_height",
                "args": [],
            }

            # Set timeout for the request
            result = await asyncio.wait_for(session.send_request("contract.query", [params]), timeout=timeout)

            if result.__contains__("error"):
                Logger.warning(f"Failed to get synced block height: {result['error']}", "SYNC")
                return None

            return int(result["result"])

        except asyncio.TimeoutError:
            retry_count += 1
            if retry_count > max_retries:
                Logger.error(f"Get synced block height timed out after {timeout}s and {max_retries} retries", "SYNC")
                return None

            Logger.warning(f"Timeout getting synced block height, retrying ({retry_count}/{max_retries})", "SYNC")
            # Exponential backoff
            await asyncio.sleep(2**retry_count)

        except Exception as e:
            retry_count += 1
            if retry_count > max_retries:
                Logger.error(f"Failed to get synced block height: {e}", "SYNC")
                return None

            Logger.warning(f"Error getting synced block height: {e}, retrying ({retry_count}/{max_retries})", "SYNC")
            # Exponential backoff
            await asyncio.sleep(2**retry_count)


@measure_performance("Batch Insert Blocks", "BLOCK")
async def batch_insert_blocks(
    blocks: List[StoreBlock],
    contract_address: str,
    conn_manager: ConnectionManager,
    config: WatcherConfig,
    stats: SyncStats,
) -> List[int]:
    """Insert multiple blocks and record successful heights"""
    successful_heights = []

    # Start progress tracking
    if blocks:
        min_height = min(block.height for block in blocks)
        max_height = max(block.height for block in blocks)
        operation_name = f"Inserting Blocks {min_height}-{max_height}"
    else:
        operation_name = "Inserting Blocks (empty batch)"

    total_blocks = len(blocks)
    # Only show progress for multiple blocks
    ProgressTracker.update(operation_name, 0, total_blocks, event_type="PROGRESS", min_items_for_progress=2)

    for block in blocks:
        Logger.trace('"""' + toJson(block) + '"""', "BLOCK")
    # Use performance monitoring for the whole batch
    # Note: Blocks now include receipt information from get_block_by_height
    with PerformanceMonitor.measure_time(f"Insert {total_blocks} blocks", "METRICS"):
        for i, block in enumerate(blocks):
            try:
                # Insert block directly (now with receipt information included)
                tx_hash = await insert_block_to_explorer_db(block, contract_address, conn_manager, config)

                # Update stats
                stats.record_block(block)

                # Log receipt information
                receipt_count = sum(1 for tx in block.transactions if tx.block_hash != "")
                if receipt_count > 0:
                    Logger.debug(f"Block #{block.height} contains {receipt_count} receipts", "RECEIPT")

                # Update progress (only for multiple blocks)
                ProgressTracker.update(
                    operation_name,
                    i + 1,
                    total_blocks,
                    event_type="PROGRESS",
                    extra_info=f"Current: #{block.height}",
                    min_items_for_progress=2,
                )

                # Periodically log detailed progress
                if stats.blocks_processed % config.log_interval == 0 or block.height == 0:
                    Logger.debug(f"Block #{block.height} inserted ({stats.blocks_processed} blocks processed)", "BLOCK")
                    Logger.debug(f"Transaction hash: {tx_hash}", "TX")
                else:
                    # Use aggregated logging for routine block insertions
                    log_aggregated("DEBUG", f"Block #{block.height} inserted", "BLOCK")

                # Add sleep after every 100 blocks if batch is large
                if (i + 1) % 100 == 0 and i + 1 < total_blocks and total_blocks > 100:
                    sleep_time = config.batch_sleep_interval
                    Logger.info(f"Processed 100 blocks, sleeping for {sleep_time}s to avoid rate limiting", "SYNC")
                    await asyncio.sleep(sleep_time)

                successful_heights.append(block.height)
            except Exception as e:
                Logger.error(f"Failed to insert block #{block.height}: {e}", "BLOCK")
                stats.record_failure()

    # Complete progress tracking (only for multiple blocks)
    ProgressTracker.complete(
        operation_name,
        event_type="PROGRESS",
        success=(len(successful_heights) == total_blocks),
        min_items_for_progress=2,
        total_items=total_blocks,
    )

    # Flush any aggregated logs
    flush_logs()

    # Log performance statistics periodically
    if stats.blocks_processed % 100 == 0:
        PerformanceMonitor.log_stats("METRICS")

    return successful_heights
