#!/usr/bin/env python3

from __future__ import annotations

import asyncio
import os
import sys
import time
import traceback

# Add the project root to Python path to ensure imports work
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from watcher.config import WatcherConfig
from watcher.connection import ConnectionManager
from watcher.logger import Logger
from watcher.operations import (
    batch_get_blocks,
    batch_insert_blocks,
    get_blockchain_info,
    get_synced_block_height,
    register_contract,
)
from watcher.stats import SyncStats


async def main():
    # Load configuration and initialize manager
    config = WatcherConfig()
    conn_manager = ConnectionManager(config)
    stats = SyncStats()

    try:
        # Check if contract address exists, register if not
        contract_address = config.contract_address
        if not contract_address:
            contract_address = await register_contract(conn_manager, config)
        else:
            Logger.info(f"Using existing contract address: {contract_address}", "CONTRACT")

        # Get the latest block height
        latest_height, _ = await get_blockchain_info(conn_manager)
        Logger.info(f"Main network current height: {latest_height}", "NET")

        # Check if we have already synced blocks in the contract
        synced_height = await get_synced_block_height(contract_address, conn_manager, config)

        # Start from genesis block or from the last synced block
        if synced_height is not None and synced_height >= 0:
            start_height = synced_height + 1
            Logger.info(f"Resuming sync from block height {start_height} (last synced: {synced_height})", "SYNC")
        else:
            start_height = 0
            Logger.info("Starting sync from genesis block (height 0)", "SYNC")

        Logger.info(f"Total blocks to sync: {latest_height + 1 - start_height}", "SYNC")

        # Initialize tracking variables
        batch_size = config.batch_size
        total_blocks = latest_height + 1

        # Main sync loop with improved batch processing
        while True:
            try:
                # Get current latest height
                current_height, _ = await get_blockchain_info(conn_manager)

                # Calculate batch range
                end_height = min(start_height + batch_size, current_height + 1)

                if start_height >= end_height:
                    # Already caught up
                    await asyncio.sleep(config.sync_interval)
                    continue

                # Log batch progress
                Logger.info(
                    f"Syncing blocks {start_height} to {end_height - 1} (batch of {end_height - start_height})", "SYNC"
                )

                # Get blocks in batch more efficiently
                blocks = await batch_get_blocks(start_height, end_height, conn_manager)

                if not blocks:
                    Logger.warning(f"No blocks found in range {start_height}-{end_height - 1}, moving forward", "BLOCK")
                    start_height = end_height
                    continue

                # Process batch of blocks
                successful_heights = await batch_insert_blocks(blocks, contract_address, conn_manager, config, stats)

                # Move start_height forward
                if successful_heights:
                    start_height = max(successful_heights) + 1
                else:
                    start_height = end_height

                # Add sleep between batches if we're processing a large number of blocks
                # This helps prevent rate limiting and resource exhaustion
                if current_height - start_height > config.batch_size:
                    sleep_time = config.batch_sleep_interval
                    Logger.info(f"Completed batch, sleeping for {sleep_time}s before next batch", "SYNC")
                    await asyncio.sleep(sleep_time)

                # Log stats periodically
                if stats.should_log_stats(30):  # Every 30 seconds
                    current_stats = stats.get_stats()
                    Logger.info(
                        f"Sync stats: {current_stats['blocks_processed']} blocks, "
                        f"{current_stats['blocks_per_second']:.2f} blocks/sec, "
                        f"{current_stats['elapsed_time']:.1f} seconds elapsed",
                        "STATS",
                    )

                # Check if we've caught up
                if start_height > current_height:
                    # Check again for new blocks
                    new_height, _ = await get_blockchain_info(conn_manager)

                    if new_height > current_height:
                        # New blocks were produced
                        Logger.debug("New blocks detected, continuing sync", "SYNC")
                        total_blocks = new_height + 1
                    else:
                        # No new blocks, waiting
                        if not hasattr(main, "last_waiting_log_time"):
                            main.last_waiting_log_time = 0

                        current_time = time.time()
                        if current_time - main.last_waiting_log_time >= 60:
                            # Get the current synced height from the contract
                            synced_height = await get_synced_block_height(contract_address, conn_manager, config)
                            if synced_height is not None:
                                Logger.info(
                                    f"Caught up with main network at height {current_height} (synced height: {synced_height})",
                                    "SYNC",
                                )
                            else:
                                Logger.info(f"Caught up with main network at height {current_height}", "SYNC")
                            main.last_waiting_log_time = current_time

                        await asyncio.sleep(config.sync_interval)

                # Update total blocks if network grew
                new_latest, _ = await get_blockchain_info(conn_manager)
                if new_latest > latest_height:
                    additional_blocks = new_latest - latest_height
                    total_blocks += additional_blocks
                    latest_height = new_latest
                    Logger.info(
                        f"Network grew by {additional_blocks} blocks, now syncing to height {latest_height}", "NET"
                    )

            except Exception as e:
                Logger.error(f"Sync error: {e}", "SYNC")
                stats.record_failure()
                await asyncio.sleep(config.sync_interval)

    finally:
        # Ensure connections are closed
        await conn_manager.close()


if __name__ == "__main__":
    try:
        # Set additional context for the main process
        Logger.set_context("pid", os.getpid())

        # Run the main async loop
        asyncio.get_event_loop().run_until_complete(main())
    except KeyboardInterrupt:
        Logger.info("Watcher stopped by user", "SYSTEM")
    except Exception as e:
        Logger.critical(f"Fatal error: {e}", "SYSTEM")
        Logger.error(traceback.format_exc(), "SYSTEM")
    finally:
        # Close logger resources
        Logger.close()
