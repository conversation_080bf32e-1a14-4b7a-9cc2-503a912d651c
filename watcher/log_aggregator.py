"""
Log aggregation utilities for VGraph watcher
"""

import time
from typing import Dict, Tuple

# Import Logger at function level to avoid circular imports


class LogAggregator:
    """Aggregate repeated log messages to reduce log volume"""

    def __init__(self, timeout: int = 5):
        """
        Initialize log aggregator

        Args:
            timeout: Time in seconds before flushing aggregated logs (default: 5)
        """
        self.messages: Dict[Tuple[str, str, str], int] = {}  # (level, message, event_type) -> count
        self.timeout = timeout
        self.last_flush = time.time()

    def add(self, level: str, message: str, event_type: str = None):
        """
        Add a log message to the aggregator

        Args:
            level: Log level (INFO, DEBUG, etc.)
            message: Log message
            event_type: Event type for the log
        """
        key = (level, message, event_type)

        if key in self.messages:
            self.messages[key] += 1
        else:
            self.messages[key] = 1

        # Check if we need to flush
        if time.time() - self.last_flush > self.timeout:
            self.flush()

    def flush(self):
        """Flush all aggregated log messages"""
        # Ensure context is set for all aggregated logs
        for (level, message, event_type), count in self.messages.items():
            if count > 1:
                # Add count information to the message
                aggregated_message = f"{message} (repeated {count} times)"
                self._log_message(level, aggregated_message, event_type)
            else:
                # Just log the original message
                self._log_message(level, message, event_type)

        # Clear messages and update last flush time
        self.messages.clear()
        self.last_flush = time.time()

    def _log_message(self, level: str, message: str, event_type: str = None):
        """Log a message with the appropriate level"""
        # Make sure we're using the correct logger
        from watcher.logger import Logger

        if level == "DEBUG":
            Logger.debug(message, event_type)
        elif level == "INFO":
            Logger.info(message, event_type)
        elif level == "WARNING":
            Logger.warn(message, event_type)
        elif level == "ERROR":
            Logger.error(message, event_type)
        elif level == "CRITICAL":
            Logger.critical(message, event_type)
        elif level == "TRACE":
            Logger.trace(message, event_type)


# Global log aggregator instance
log_aggregator = LogAggregator()


def log_aggregated(level: str, message: str, event_type: str = None):
    """
    Log a message through the aggregator

    Args:
        level: Log level (INFO, DEBUG, etc.)
        message: Log message
        event_type: Event type for the log
    """
    log_aggregator.add(level, message, event_type)


def flush_logs():
    """Flush all aggregated logs"""
    log_aggregator.flush()
