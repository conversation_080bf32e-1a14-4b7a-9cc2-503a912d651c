# Contract configuration
contract:
  address: ''
  wasm_path: ../contract/base/explorer_db.wasm

# Logging configuration
# log_level is the log level of the node, can be one of:
#  - TRACE (most detailed)
#  - DEBUG
#  - INFO
#  - WARNING
#  - ERROR
#  - CRITICAL
log_level: DEBUG
# Whether to use colored logs in console output
use_colored_logs: true
# Whether to log to file
log_to_file: false
# Directory for log files
log_dir: logs
# Maximum log file size in bytes (10MB)
max_log_size: 10485760
# Number of log files to keep when rotating
log_rotation_count: 5

# Watcher configuration
watcher:
  # Sync interval in seconds
  sync_interval: 1
  # Batch size for block processing
  batch_size: 100
  # Log interval for block processing
  log_interval: 10
  # Sleep time in seconds between batches of 100 blocks
  batch_sleep_interval: 15
  # Timeout in seconds for blockchain requests
  request_timeout: 60
  # Maximum number of retries for failed operations
  max_retries: 3

# Network configuration
mainnet:
  host: localhost
  port: 9877

sidenet:
  host: localhost
  port: 10077

# Fuel for contract operations
fuel: 10000000
