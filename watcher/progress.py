"""
Progress tracking utilities for VGraph watcher
"""

import time
from typing import Dict, Optional

from watcher.logger import Logger


class ProgressTracker:
    """Track progress of long-running operations"""

    # Store progress for different operations
    _progress: Dict[str, Dict[str, any]] = {}

    @classmethod
    def update(
        cls,
        operation: str,
        current: int,
        total: int,
        interval: int = 5,
        event_type: str = "PROGRESS",
        extra_info: Optional[str] = None,
        min_items_for_progress: int = 2,
    ):
        """
        Update progress for an operation

        Args:
            operation: Name of the operation
            current: Current progress value
            total: Total expected value
            interval: Log interval in percentage (default: 5%)
            event_type: Event type for logging
            extra_info: Additional information to include in the log
            min_items_for_progress: Minimum number of items required to show progress (default: 2)
        """
        # Skip progress tracking for small operations
        if total < min_items_for_progress:
            return

        # Initialize progress tracking for this operation if not exists
        if operation not in cls._progress:
            cls._progress[operation] = {
                "last_logged_percent": -1,
                "start_time": time.time(),
                "last_update_time": time.time(),
            }

        progress = cls._progress[operation]
        progress["last_update_time"] = time.time()

        # Calculate percentage
        percent = (current / total) * 100 if total > 0 else 0

        # Check if we should log progress
        last_logged = progress["last_logged_percent"]
        should_log = False

        # Log if:
        # 1. First update (last_logged_percent is -1)
        # 2. Crossed an interval threshold
        # 3. Reached 100%
        if last_logged == -1 or int(percent / interval) > int(last_logged / interval) or percent >= 100:
            should_log = True

        if should_log:
            # Calculate ETA
            elapsed = time.time() - progress["start_time"]
            if current > 0 and percent < 100:
                eta = (elapsed / current) * (total - current)
                eta_str = f", ETA: {cls._format_time(eta)}"
            else:
                eta_str = ""

            # Format message
            message = f"{operation}: {current}/{total} ({percent:.1f}%){eta_str}"
            if extra_info:
                message += f" - {extra_info}"

            # Log progress
            # Use DEBUG level for all progress updates
            Logger.debug(message, event_type)

            # Update last logged percentage
            progress["last_logged_percent"] = percent

    @classmethod
    def complete(
        cls,
        operation: str,
        event_type: str = "PROGRESS",
        success: bool = True,
        min_items_for_progress: int = 2,
        total_items: int = None,
    ):
        """
        Mark an operation as complete

        Args:
            operation: Name of the operation
            event_type: Event type for logging
            success: Whether the operation completed successfully
            min_items_for_progress: Minimum number of items required to show progress (default: 2)
            total_items: Total number of items processed (if known)
        """
        # Skip completion message for small operations
        if total_items is not None and total_items < min_items_for_progress:
            # Just remove from tracking if it exists
            if operation in cls._progress:
                del cls._progress[operation]
            return

        if operation in cls._progress:
            progress = cls._progress[operation]
            elapsed = time.time() - progress["start_time"]

            status = "completed successfully" if success else "failed"
            # Use DEBUG level for completion messages
            Logger.debug(f"{operation} {status} in {cls._format_time(elapsed)}", event_type)

            # Remove from tracking
            del cls._progress[operation]

    @classmethod
    def _format_time(cls, seconds: float) -> str:
        """Format time in seconds to a human-readable string"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = int(seconds / 60)
            secs = seconds % 60
            return f"{minutes}m {secs:.1f}s"
        else:
            hours = int(seconds / 3600)
            minutes = int((seconds % 3600) / 60)
            return f"{hours}h {minutes}m"
