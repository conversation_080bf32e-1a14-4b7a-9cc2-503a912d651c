#[glue::contract]
mod delegate_call {
    use glue::call::build_call;

    #[glue::storage]
    struct DelegateCall {
        pub token_address: glue::StorageField<String>,
        pub num: glue::StorageField<i64>,
    }

    #[glue::storage_item]
    struct AccountBalance {
        pub balance: i64,
        pub account: String,
    }

    impl DelegateCall {
        #[glue::constructor]
        pub fn new(address: String) -> Self {
            Self {
                token_address: glue::StorageField::new(&address),
                num: glue::StorageField::new(&0),
            }
        }

        #[glue::readonly]
        pub fn balance(&self, account_address: String) -> anyhow::Result<i64> {
            Ok(build_call()
                .contract_address(self.token_address.get().as_str())
                .function_name("balance")
                .push_arg(account_address)
                .call::<i64>()?)
        }

        #[glue::atomic]
        pub fn issue(&self, account_address: String, amount: i64) -> anyhow::Result<()> {
            Ok(build_call()
                .contract_address(self.token_address.get().as_str())
                .function_name("issue")
                .push_arg(account_address)
                .push_arg(amount)
                .call_no_return()?)
        }

        #[glue::atomic]
        pub fn transfer(&self, account1: String, account2: String, amount: i64) -> anyhow::Result<()> {
            Ok(build_call()
                .contract_address(self.token_address.get().as_str())
                .function_name("transfer")
                .push_arg(account1)
                .push_arg(account2)
                .push_arg(amount)
                .call_no_return()?)
        }

        #[glue::readonly]
        pub fn get_account_balance(&self, account: String) -> anyhow::Result<AccountBalance> {
            Ok(AccountBalance {
                account: account.clone(),
                balance: self.balance(account)?,
            })
        }

        #[glue::readonly]
        pub fn get_env(&self) -> String {
            let env = env();
            format!(
                "block_height: {:?}\n\
                sender: {}\n\
                transaction_hash: {}\n\
                transaction_index: {}\n\
                transaction_timestamp: {}\n\
                callers: {}\n\
                contract_address: {}\n\
                readonly: {}\n\
                register: {}\n",
                env.block_height,
                env.sender,
                env.transaction_hash,
                env.transaction_index,
                env.transaction_timestamp,
                env.callers.join(","),
                env.contract_address,
                env.readonly,
                env.register,
            )
        }

        #[glue::readonly]
        pub fn get_token_env(&self) -> String {
            build_call()
                .contract_address(self.token_address.get().as_str())
                .function_name("get_env")
                .call::<String>()
                .unwrap()
        }

        #[glue::readonly]
        pub fn get_num(&self) -> anyhow::Result<i64> {
            return Ok(self.num.get());
        }

        #[glue::atomic]
        pub fn set_num(&self, num: i64) -> anyhow::Result<()> {
            self.num.set(&num);
            Ok(())
        }
    }
}