#[glue::contract]
mod delegate_call2 {
    use glue::call::build_call;

    #[glue::storage]
    struct DelegateCall2 {
        pub delegate_address: glue::StorageField<String>,
        pub num: glue::StorageField<i64>,
    }

    #[glue::storage_item]
    struct AccountBalance {
        pub balance: i64,
        pub account: String,
    }

    impl DelegateCall2 {
        #[glue::constructor]
        pub fn new(address: String) -> Self {
            Self {
                delegate_address: glue::StorageField::new(&address),
                num: glue::StorageField::new(&0),
            }
        }

        #[glue::readonly]
        pub fn balance(&self, account_address: String) -> anyhow::Result<i64> {
            Ok(build_call()
                .contract_address(self.delegate_address.get().as_str())
                .function_name("balance")
                .push_arg(account_address)
                .call::<i64>()?)
        }

        #[glue::atomic]
        pub fn issue(&self, account_address: String, amount: i64) -> anyhow::Result<()> {
            Ok(build_call()
                .contract_address(self.delegate_address.get().as_str())
                .function_name("issue")
                .push_arg(account_address)
                .push_arg(amount)
                .call_no_return()?)
        }

        #[glue::atomic]
        pub fn transfer(&self, account1: String, account2: String, amount: i64) -> anyhow::Result<()> {
            Ok(build_call()
                .contract_address(self.delegate_address.get().as_str())
                .function_name("transfer")
                .push_arg(account1)
                .push_arg(account2)
                .push_arg(amount)
                .call_no_return()?)
        }

        #[glue::readonly]
        pub fn get_account_balance(&self, account_address: String) -> anyhow::Result<AccountBalance> {
            let account_balance = build_call()
                .contract_address(self.delegate_address.get().as_str())
                .function_name("get_account_balance")
                .push_arg(account_address)
                .call::<AccountBalance>()?;
            println!("account_balance.account: {:?}", account_balance.account);
            println!("account_balance.balance: {:?}", account_balance.balance);
            Ok(account_balance)
        }

        #[glue::readonly]
        pub fn get_delegate_env(&self) -> String {
            build_call()
                .contract_address(self.delegate_address.get().as_str())
                .function_name("get_env")
                .call::<String>()
                .unwrap()
        }

        #[glue::readonly]
        pub fn get_token_env(&self) -> String {
            build_call()
                .contract_address(self.delegate_address.get().as_str())
                .function_name("get_token_env")
                .call::<String>()
                .unwrap()
        }

        #[glue::readonly]
        pub fn get_delegate_num(&self) -> anyhow::Result<i64> {
            build_call()
                .contract_address(self.delegate_address.get().as_str())
                .function_name("get_num")
                .call::<i64>()
        }

        #[glue::readonly]
        pub fn get_num(&self) -> anyhow::Result<i64> {
            Ok(self.num.get())
        }

        #[glue::atomic]
        pub fn set_num_error(&self, num: i64) -> anyhow::Result<()> {
            // first to set delegate num
            build_call()
                .contract_address(self.delegate_address.get().as_str())
                .function_name("set_num")
                .push_arg(num)
                .call_no_return()?;

            self.num.set(&num);

            // Deliberately return error
            Err(anyhow::anyhow!("set_num_error"))
        }
    }
}