#[glue::contract]
mod token {

    #[glue::storage]
    pub struct Token {
        pub token_name: glue::StorageField<String>,
        pub total_supply: glue::StorageField<i64>,
        pub balances: glue::collections::Map<String, i64>, // address -> balance
    }

    #[glue::event]
    pub struct TransferEvent {
        #[topic]
        pub from: String,
        #[topic]
        pub to: String,
        pub amount: i64,
    }

    impl Token {
        #[glue::constructor]
        pub fn new(token_name: String) -> Self {
            println!("new token: {:?}", token_name);
            Self {
                token_name: glue::StorageField::new(&token_name),
                total_supply: glue::StorageField::new(&0),
                balances: glue::collections::Map::new(),
            }
        }

        #[glue::atomic]
        pub fn issue(&mut self, address: String, amount: i64) -> anyhow::Result<()> {
            if amount <= 0 {
                return Err(anyhow::anyhow!("amount must be positive"));
            }
            let balance = self.balances.get(&address).unwrap_or(0);
            self.balances.insert(&address, &(balance + amount));
            self.total_supply.set(&(self.total_supply.get() + amount));
            Ok(())
        }

        #[glue::readonly]
        pub fn balance(&self, address: String) -> i64 {
            self.balances.get(&address).unwrap_or(0)
        }

        #[glue::atomic]
        pub fn transfer(&mut self, from: String, to: String, amount: i64) -> anyhow::Result<()> {
            if amount <= 0 {
                return Err(anyhow::anyhow!("amount must be positive"));
            }

            let from_balance = self.balance(from.clone());
            if from_balance < amount {
                return Err(anyhow::anyhow!("insufficient from account balance"));
            }
            self.balances.insert(&from, &(from_balance - amount));

            let to_balance = self.balance(to.clone());
            self.balances.insert(&to, &(to_balance + amount));

            emit(TransferEvent { from, to, amount });
            Ok(())
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use glue::events::Event;

    #[glue::test]
    fn test_issue_transfer_success() {
        token::set_instance(token::Token::new("test".to_string()));
        let token = token::get_instance();
        token.issue("alice".to_string(), 100).unwrap();
        let balance = token.balance("alice".to_string());
        assert_eq!(balance, 100);
        let balance = token.balance("bob".to_string());
        assert_eq!(balance, 0);
        token
            .transfer("alice".to_string(), "bob".to_string(), 10)
            .unwrap();
        let balance = token.balance("alice".to_string());
        assert_eq!(balance, 90);
        let balance = token.balance("bob".to_string());
        assert_eq!(balance, 10);
    }

    #[glue::test]
    fn test_event_code_gen_macro() {
        token::set_instance(token::Token::new("test".to_string()));
        let transer_event = token::TransferEvent {
            from: "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06".to_string(),
            to: "0xcb50efe8f3aba5fdddbac0618884fc5c010c7d9500b7d4f1c1".to_string(),
            amount: 10,
        };

        // event signature's length should be 32 bytes
        assert_eq!(transer_event.signature().len(), 32);

        // there are 2 topics in TransferEvent
        assert_eq!(transer_event.topics().len(), 2);

        // each topic's length should be 32 bytes
        for topic in transer_event.topics() {
            assert_eq!(topic.len(), 32);
        }

        let topics = transer_event.topics();
        assert_eq!(
            hex::encode(topics[0]).trim_start_matches('0'),
            transer_event
                .from
                .trim_start_matches("0x")
                .trim_start_matches('0')
        );
        assert_eq!(
            hex::encode(topics[1]).trim_start_matches('0'),
            transer_event
                .to
                .trim_start_matches("0x")
                .trim_start_matches('0')
        );

        // there is 1 data in TransferEvent
        assert_eq!(transer_event.data().len(), 1);
    }
}
