from typing import Any

import pytest

from vm import ContractTester

tokenClient = ContractTester(
    wasmName="token_lmdb_demo",
)

delegateClient = ContractTester(
    wasmName="delegate_call",
)

delegate2Client = ContractTester(
    wasmName="delegate_call2",
)


@pytest.fixture(autouse=True)
def register_contract():
    print("Register contract")
    tokenClient.constructor("vgraph_token")
    delegateClient.constructor(tokenClient.addressHex)
    delegate2Client.constructor(delegateClient.addressHex)


def test_delegate_call():
    result, err = delegateClient.execute("issue", None, "Alice", 1234)
    assert err is None
    assert result is None

    result, err = delegateClient.executeReadOnly("balance", int, "Alice")
    assert err is None
    assert result == 1234

    result, err = delegateClient.execute("transfer", None, "Alice", "Bob", 234)
    assert err is None
    assert result is None

    result, err = delegateClient.executeReadOnly("balance", int, "Alice")
    assert err is None
    assert result == 1000

    result, err = delegateClient.executeReadOnly("balance", int, "Bob")
    assert err is None
    assert result == 234

    result, err = delegateClient.execute("transfer", None, "Bob", "Alice", 10000)
    assert err == "insufficient from account balance"


def test_double_delegate_call():
    result, err = delegate2Client.execute("issue", None, "Alice", 1234)
    assert err is None
    assert result is None

    result, err = delegate2Client.executeReadOnly("balance", int, "Alice")
    assert err is None
    assert result == 1234

    result, err = delegate2Client.execute("transfer", None, "Alice", "Bob", 234)
    assert err is None
    assert result is None

    result, err = delegate2Client.executeReadOnly("balance", int, "Alice")
    assert err is None
    assert result == 1000

    result, err = delegate2Client.executeReadOnly("balance", int, "Bob")
    assert err is None
    assert result == 234

    result, err = delegate2Client.execute("transfer", None, "Bob", "Alice", 10000)
    assert err == "insufficient from account balance"

    result, err = delegate2Client.executeReadOnly("transfer", None, "Bob", "Alice", 234)
    assert err == "Cannot execute non-readonly contract function."


def test_struct_delegate_call():
    result, err = delegateClient.execute("issue", None, "Alice", 1234)
    assert err is None
    assert result is None

    result, err = delegateClient.executeReadOnly("get_account_balance", Any, "Alice")
    assert err is None
    assert result == {"account": "Alice", "balance": 1234}

    result, err = delegate2Client.executeReadOnly("get_account_balance", Any, "Alice")
    assert err is None
    assert result == {"account": "Alice", "balance": 1234}


def test_env():
    expected = {
        "block_height": "2",
        "sender": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        "transaction_hash": "0x11c818fc2831c0b13286ce322dd8ca0d2bdf643b00d3733a00",
        "transaction_index": "3",
        "transaction_timestamp": "123",
        # first caller(transaction sender) + second caller(delegateClient address)
        "callers": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06" + "," + delegateClient.addressHex,
        "contract_address": tokenClient.addressHex,
        "readonly": "true",
        "register": "false",
    }
    envs = {
        "block_height": "2",
        "sender": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        "callers": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        "transaction_hash": "0x11c818fc2831c0b13286ce322dd8ca0d2bdf643b00d3733a00",
        "transaction_index": "3",
        "transaction_timestamp": "123",
    }
    result, err = delegateClient.executeReadOnlyWithEnv(envs, "get_token_env", str)
    assert err is None
    assert result == (
        f"block_height: {expected['block_height']}\n"
        f"sender: {expected['sender']}\n"
        f"transaction_hash: {expected['transaction_hash']}\n"
        f"transaction_index: {expected['transaction_index']}\n"
        f"transaction_timestamp: {expected['transaction_timestamp']}\n"
        f"callers: {expected['callers']}\n"
        f"contract_address: {expected['contract_address']}\n"
        f"readonly: {expected['readonly']}\n"
        f"register: {expected['register']}\n"
    )

    expected = {
        "block_height": "2",
        "sender": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        "transaction_hash": "0x11c818fc2831c0b13286ce322dd8ca0d2bdf643b00d3733a00",
        "transaction_index": "3",
        "transaction_timestamp": "123",
        # first caller(transaction sender) + second caller(delegateClient2 address) + third caller(delegateClient address)
        "callers": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"
        + ","
        + delegate2Client.addressHex
        + ","
        + delegateClient.addressHex,
        "contract_address": tokenClient.addressHex,
        "readonly": "true",
        "register": "false",
    }
    envs = {
        "block_height": "2",
        "sender": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        "callers": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        "transaction_hash": "0x11c818fc2831c0b13286ce322dd8ca0d2bdf643b00d3733a00",
        "transaction_index": "3",
        "transaction_timestamp": "123",
    }
    result, err = delegate2Client.executeReadOnlyWithEnv(envs, "get_token_env", str)
    assert err is None
    assert result == (
        f"block_height: {expected['block_height']}\n"
        f"sender: {expected['sender']}\n"
        f"transaction_hash: {expected['transaction_hash']}\n"
        f"transaction_index: {expected['transaction_index']}\n"
        f"transaction_timestamp: {expected['transaction_timestamp']}\n"
        f"callers: {expected['callers']}\n"
        f"contract_address: {expected['contract_address']}\n"
        f"readonly: {expected['readonly']}\n"
        f"register: {expected['register']}\n"
    )

    expected = {
        "block_height": "2",
        "sender": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        "transaction_hash": "0x11c818fc2831c0b13286ce322dd8ca0d2bdf643b00d3733a00",
        "transaction_index": "3",
        "transaction_timestamp": "123",
        # first caller(transaction sender) + second caller(delegateClient2 address)
        "callers": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06" + "," + delegate2Client.addressHex,
        "contract_address": delegateClient.addressHex,
        "readonly": "true",
        "register": "false",
    }
    envs = {
        "block_height": "2",
        "sender": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        "callers": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        "transaction_hash": "0x11c818fc2831c0b13286ce322dd8ca0d2bdf643b00d3733a00",
        "transaction_index": "3",
        "transaction_timestamp": "123",
    }
    result, err = delegate2Client.executeReadOnlyWithEnv(envs, "get_delegate_env", str)
    assert err is None
    assert result == (
        f"block_height: {expected['block_height']}\n"
        f"sender: {expected['sender']}\n"
        f"transaction_hash: {expected['transaction_hash']}\n"
        f"transaction_index: {expected['transaction_index']}\n"
        f"transaction_timestamp: {expected['transaction_timestamp']}\n"
        f"callers: {expected['callers']}\n"
        f"contract_address: {expected['contract_address']}\n"
        f"readonly: {expected['readonly']}\n"
        f"register: {expected['register']}\n"
    )


def test_delegate_success_but_client_fail():
    # test delegate get_num set_num
    result, err = delegateClient.execute("get_num", int)
    assert err is None and result == 0
    result, err = delegateClient.execute("set_num", None, 1)
    assert err is None and result is None

    # test delegate2 set_num_error
    result, err = delegate2Client.execute("get_num", int)
    assert err is None and result == 0
    result, err = delegate2Client.execute("set_num_error", None, 2)
    assert err == "set_num_error" and result is None

    # set_num_error failed, delegateClient should not be affected,
    # the delegateClient num should be 1, and delegate2Client num should be 0
    result, err = delegateClient.execute("get_num", int)
    assert err is None and result == 1
    result, err = delegate2Client.execute("get_num", int)
    assert err is None and result == 0
