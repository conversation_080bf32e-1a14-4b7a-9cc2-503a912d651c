[package]
name = "token_lmdb_demo"
version = "0.1.0"
edition = "2021"
authors = ["VGraph"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
glue = { path = "../../../contract/base/source/glue" }
serde = { version = "1.0.193", features = ["derive"] }
serde_json = "1.0.108"
anyhow = "1.0.83"

[dev-dependencies]
serial_test = "3.1.1"

[lib]
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[profile.release]
codegen-units = 1
opt-level = "z"
lto = true
