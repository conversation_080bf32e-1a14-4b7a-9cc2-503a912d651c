#[glue::contract]
mod token {
    #[glue::storage]
    pub struct Token {
        pub token_name: glue::StorageField<String>,
        pub total_supply: glue::StorageField<i64>,
        pub balances: glue::collections::Map<String, i64>, // address -> balance
    }

    impl Token {
        #[glue::constructor]
        pub fn new(token_name: String) -> Self {
            println!("new token: {:?}", token_name);
            Self {
                token_name: glue::StorageField::new(&token_name),
                total_supply: glue::StorageField::new(&0),
                balances: glue::collections::Map::new(),
            }
        }


        #[glue::atomic]
        pub fn issue(&mut self, address: String, amount: i64) -> anyhow::Result<()> {
            if amount <= 0 {
                return Err(anyhow::anyhow!("amount must be positive"));
            }
            let balance = self.balances.get(&address).unwrap_or(0);
            self.balances.insert(&address, &(balance + amount));
            self.total_supply.set(&(self.total_supply.get() + amount));
            Ok(())
        }


        #[glue::readonly]
        pub fn balance(&self, address: String) -> i64 {
            self.balances.get(&address).unwrap_or(0)
        }


        #[glue::atomic]
        pub fn transfer(&mut self, from: String, to: String, amount: i64) -> anyhow::Result<()> {
            if amount <= 0 {
                return Err(anyhow::anyhow!("amount must be positive"));
            }

            let from_balance = self.balance(from.clone());
            if from_balance < amount {
                return Err(anyhow::anyhow!("insufficient from account balance"));
            }
            self.balances.insert(&from, &(from_balance - amount));

            let to_balance = self.balance(to.clone());
            self.balances.insert(&to, &(to_balance + amount));

            Ok(())
        }

        #[glue::atomic]
        #[allow(unreachable_code, unused_variables)]
        pub fn panic_transfer(&mut self, from: String, to: String, amount: i64) -> anyhow::Result<()> {
            if amount <= 0 {
                return Err(anyhow::anyhow!("amount must be positive"));
            }

            let from_balance = self.balance(from.clone());
            if from_balance < amount {
                return Err(anyhow::anyhow!("insufficient from account balance"));
            }
            self.balances.insert(&from, &(from_balance - amount));
            panic!("panic");
            let to_balance = self.balance(to.clone());
            self.balances.insert(&to, &(to_balance + amount));

            Ok(())
        }

        #[glue::readonly]
        pub fn print_env(&self) {
            println!("env: {:?}", env());
            println!("global env: {:?}", glue::env::get_env());
        }

        #[glue::readonly]
        pub fn get_env(&self) -> String {
            let env = env();
            format!(
                "block_height: {:?}\n\
                 sender: {}\n\
                 transaction_hash: {}\n\
                 transaction_index: {}\n\
                 transaction_timestamp: {}\n\
                 callers: {}\n\
                 contract_address: {}\n\
                 readonly: {}\n\
                 register: {}\n",
                env.block_height,
                env.sender,
                env.transaction_hash,
                env.transaction_index,
                env.transaction_timestamp,
                env.callers.join(","),
                env.contract_address,
                env.readonly,
                env.register,
            )
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[glue::test]
    fn test_issue_transfer_success() {
        token::set_instance(token::Token::new("test".to_string()));
        let token = token::get_instance();
        token.issue("alice".to_string(), 100).unwrap();
        let balance = token.balance("alice".to_string());
        assert_eq!(balance, 100);
        let balance = token.balance("bob".to_string());
        assert_eq!(balance, 0);
        token.transfer("alice".to_string(), "bob".to_string(), 10).unwrap();
        let balance = token.balance("alice".to_string());
        assert_eq!(balance, 90);
        let balance = token.balance("bob".to_string());
        assert_eq!(balance, 10);
    }

    #[glue::test]
    fn test_print_env() {
        token::set_instance(token::Token::new("test".to_string()));
        let token = token::get_instance();
        token.print_env();
    }
}
