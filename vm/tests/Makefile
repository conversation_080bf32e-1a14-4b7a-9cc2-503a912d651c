# This variable holds all the subdirectories, skip not contains Makefile directory
SUBDIRS := $(shell find . -mindepth 1 -maxdepth 1 -type d -exec test -e '{}/Makefile' ';' -print)

# Default target
all: $(SUBDIRS)

# Recursive rule for each subdirectory
$(SUBDIRS):
	$(MAKE) -C $@
	@echo ""

# Optional: clean target to clean all subdirectories
clean:
	@for dir in $(SUBDIRS); do \
		$(MAKE) -C $$dir clean; \
	done

cargo-clean:
	@for dir in $(SUBDIRS); do \
		$(MAKE) -C $$dir cargo-clean; \
	done

test:
	@for dir in $(SUBDIRS); do \
		$(MAKE) -C $$dir test; \
	done

.PHONY: all clean $(SUBDIRS)
