import pytest

from vm import ContractTester

wasmName = "token_lmdb_demo"

contractClient = ContractTester(wasmName=wasmName)


@pytest.fixture(autouse=True)
def register_contract():
    print("Register contract")
    contractClient.constructor("vgraph_token")


def test_register_contract_and_run_success():
    print("Issue 100 to acc")
    result, err = contractClient.execute("issue", None, "acc", 100)
    assert err is None

    accBalance, err = contractClient.executeReadOnly("balance", int, "acc")
    assert err is None
    assert accBalance == 100
    print(f"acc balance before transfer: {accBalance}")
    acc2Balance, err = contractClient.executeReadOnly("balance", int, "acc2")
    assert err is None
    assert acc2Balance == 0
    print(f"acc2 balance before transfer: {acc2Balance}")

    print("Transfer 10 from acc to acc2")
    result, err = contractClient.execute("transfer", None, "acc", "acc2", 10)
    assert err is None

    accBalance, err = contractClient.executeReadOnly("balance", int, "acc")
    assert err is None
    assert accBalance == 90
    print(f"acc balance after transfer: {accBalance}")
    acc2Balance, err = contractClient.executeReadOnly("balance", int, "acc2")
    assert err is None
    assert acc2Balance == 10
    print(f"acc2 balance after transfer: {acc2Balance}")


def test_register_contract_and_run_fail():
    print("issue -1 to acc")
    _, err = contractClient.execute("issue", None, "acc", -1)
    print(f"issue -1 error: {err}")
    assert err is not None

    print("Transfer -1 from acc to acc2")
    _, err = contractClient.execute("transfer", None, "acc", "acc2", -1)
    print(f"transfer -1 error: {err}")
    assert err is not None

    print("Transfer 100 from acc to acc2")
    _, err = contractClient.execute("transfer", None, "acc", "acc2", 100)
    print(f"transfer 100 error: {err}")
    assert err is not None


def test_panic():
    print("Issue 100 to acc")
    result, err = contractClient.execute("issue", None, "acc", 100)
    assert err is None

    accBalance, err = contractClient.executeReadOnly("balance", int, "acc")
    assert err is None
    assert accBalance == 100
    print(f"acc balance before transfer: {accBalance}")
    acc2Balance, err = contractClient.executeReadOnly("balance", int, "acc2")
    assert err is None
    assert acc2Balance == 0
    print(f"acc2 balance before transfer: {acc2Balance}")

    print("Transfer 10 from acc to acc2")
    result, err = contractClient.execute("panic_transfer", None, "acc", "acc2", 10)
    assert err is not None

    accBalance, err = contractClient.executeReadOnly("balance", int, "acc")
    assert err is None
    assert accBalance == 100
    print(f"acc balance after transfer: {accBalance}")
    acc2Balance, err = contractClient.executeReadOnly("balance", int, "acc2")
    assert err is None
    assert acc2Balance == 0
    print(f"acc2 balance after transfer: {acc2Balance}")


def test_readonly_fail():
    result, err = contractClient.executeReadOnly("issue", None, "acc", 100)
    assert err == "Cannot execute non-readonly contract function."

    result, err = contractClient.execute("balance", int, "acc")
    assert err is None
    assert result == 0
