import pytest

from common.encode import hexToBytes
from vm import ContractTester, resetTesterState

client = ContractTester(wasmName="test_event")


@pytest.fixture(autouse=True)
def clean_db():
    resetTesterState()


@pytest.fixture(autouse=True)
def register_contract():
    client.constructor("test_token")


def test_transfer_event():
    ALICE = "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"
    BOB = "0xcb50efe8f3aba5fdddbac0618884fc5c010c7d9500b7d4f1c1"

    result, err = client.execute("issue", None, ALICE, 100)
    assert err is None
    assert result is None
    result, err = client.execute(
        "transfer",
        None,
        ALICE,
        BOB,
        100,
    )
    assert err is None
    assert result is None
    logs = ContractTester.state.getAllLogs()
    assert len(logs) == 1
    log = logs[0]
    assert log.contract_address == hexToBytes(client.addressHex)
    assert len(log.topics) == 3
    assert len(log.data) == 1

    assert log.topics[1].lstrip("0x").lstrip("0") == ALICE.lstrip("0x").lstrip("0")
    assert log.topics[2].lstrip("0x").lstrip("0") == BOB.lstrip("0x").lstrip("0")
