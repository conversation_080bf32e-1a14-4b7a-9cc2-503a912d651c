from typing import Dict, Optional


def writeStr(mem, store, inputStr, ptr):
    """
    write string into wasmtime memory
    """
    if isinstance(inputStr, str):
        inputStr = inputStr.encode()
    # write into memory
    mem.write(store, bytearray(inputStr), ptr)
    # write 0 as NULL to terminate C-string
    mem.data_ptr(store)[ptr + len(inputStr)] = 0


def getStr(mem, store, ptr):
    """
    get string from wasmtime memory
    """
    data = mem.data_ptr(store)
    length = 0

    # find the length of the string
    while data[ptr + length] != 0:  # NULL terminator
        length += 1

    # read the bytes from memory
    raw_bytes = bytes(data[ptr : ptr + length])
    return raw_bytes.decode("utf-8")


def mergeEnv(*envs: Optional[Dict[str, str]]) -> Dict:
    """
    Merge the environment variables from multiple envs.
    """
    finalEnv = {}
    for env in envs:
        if env:
            finalEnv.update(env)
    return finalEnv
