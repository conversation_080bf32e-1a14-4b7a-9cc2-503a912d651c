from __future__ import annotations

import os.path
from typing import Dict

import rawdb
import state


def initSystemContractsCode(state: state.StateDB, systemContracts: Dict[str, bytes]):
    """
    Initialize system contracts code, with the given system contracts list.
    :param state: state db
    :param systemContracts: system contracts dict, with contract name as key and contract address as value.
    """
    # load wasm file from system_contracts folder
    for contractName, address in systemContracts.items():
        contractPath = f"contract/base/{contractName}.wasm"
        # TODO: concurrency
        with open(contractPath, "rb") as f:
            contractCode = f.read()
        # state create contract
        state.createContract(address)
        # state set code
        state.setCode(address, contractCode)
        # update code usage count
        rawdb.addCodeUsageCount(state.db.diskDB(), state.getCodeHash(address))


def setTestContractCode(state: state.StateDB, address: bytes, wasmFileName: str):
    """
    Set test contract code to the given state db.
    :param state: state db
    :param address: contract address
    :param wasmFileName: wasm file name, without extension, which will be loaded from contract/base or wasm folder.
    """
    contractPath = f"contract/base/{wasmFileName}.wasm"
    wasmPath = f"wasm/{wasmFileName}.wasm"
    vmWasmPath = f"vm/tests/{wasmFileName}.wasm"
    if os.path.exists(contractPath):
        with open(contractPath, "rb") as f:
            contractCode = f.read()
    elif os.path.exists(wasmPath):
        with open(wasmPath, "rb") as f:
            contractCode = f.read()
    elif os.path.exists(vmWasmPath):
        with open(vmWasmPath, "rb") as f:
            contractCode = f.read()
    else:
        raise FileNotFoundError(f"contract file not found: {contractPath}")
    # state create contract
    state.createContract(address)
    # state set code
    state.setCode(address, contractCode)
