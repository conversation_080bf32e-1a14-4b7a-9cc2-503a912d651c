# default build target
all: build-contracts

# install dependencies
# TODO: use poetry instead of pipenv
install:
	pipenv install --dev

# build all system contracts
build-contracts:
	$(MAKE) -C contract/base/source all
	$(MAKE) -C vm/tests all
	# write a .build-finished file to root directory to indicate build finished
	touch .build-finished

# clean build cache
cargo-clean:
	$(MAKE) -C contract/base/source cargo-clean
	$(MAKE) -C vm/tests cargo-clean

# pytest, without benchmark
pytest:
	pipenv run pytest

# pytest, benchmark only
pytest-benchmark:
	pipenv run pytest --benchmark-only

# rust test and clean
rust-test:
	$(MAKE) -C contract/base/source test
	$(MAKE) -C vm/tests test

lint:
	pipenv run ruff check --fix && ruff format

# run all tests
test: install pytest pytest-benchmark rust-test

# run all tests except benchmark tests
unit-tests: install pytest rust-test

# run all benchmarks
benchmark-tests: install pytest-benchmark

.PHONY: all install build-contracts cargo-clean pytest pytest-benchmark rust-test test unit-tests benchmark-tests lint
