# Copyright © 2021 Primecoin Project
import asyncio
import logging
import logging.handlers
import signal
import subprocess
import traceback
from contextlib import suppress
from dataclasses import dataclass
from functools import partial

import uvloop
from aiorpcx import spawn
from kivy.app import App
from kivy.clock import Clock, mainthread
from kivy.logger import Logger
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label

import swaggerapi.generate_queries as gq
from chains.chains import CHAINS
from config.config import CONFIG
from jsonrpc.server import SessionManager
from swaggerapi.utils import getHostAndPort

# TODO: add log level to config
Logger.setLevel(logging.INFO)


@dataclass
class VGraphApp(App):
    __version__ = "0.0.1"
    VERSION_COPYRIGHT = f"VGraph {__version__} Developer © 2021 Primecoin Project"
    _LOGGER_TITLE = "VGraphApp:"

    # ----- init app -----

    def __init__(self):
        self.chain = None
        super().__init__()

    def build(self):
        self.initializeSwagger()  # init swagger server
        self.initializeChain()  # init chain and server
        self.gui = VGraphAppGUI()
        return self.gui

    def initializeChain(self):
        """
        Initialize chain and server.
        """

        # init chain
        chainId = CONFIG.chainId
        chain, kwargs = CHAINS.get(chainId, None)
        if chain is None:
            raise Exception(f"Chain {chainId} not found")
        self.chain = chain() if kwargs is None else chain(**kwargs)
        self.chain.initChain()

        self.sessionManager = SessionManager(self.chain)

    def initializeSwagger(self):
        """
        Initialize swagger server
        """
        gq.main()
        host, port = getHostAndPort(CONFIG.swaggerServer, defaultHost="localhost", defaultPort=9878)

        self.process = subprocess.Popen(["uvicorn", "swaggerapi.swagger_page:app", "--host", host, "--port", str(port)])
        Logger.info(f"VGraphApp: Starting swagger server at {host}:{port}")

    # ----- app controller -----

    async def startApp(self):
        """
        Start the app:

        - install signal handlers for SIGINT and SIGTERM
        - set loop's exception handler
        - run kivy app
        - handle shutdown event, graceful shutdown
        """
        # get event loop, and create shutdown event
        loop = asyncio.get_event_loop()
        shutdownEvent = asyncio.Event()

        # install signal handlers and exception handler
        self.installSignalHandlers(loop, shutdownEvent)
        self.setExceptionHandler(loop, shutdownEvent)

        # start main app
        appTask = await self.startMainApp(shutdownEvent)

        # waiting for shutdown event
        try:
            await shutdownEvent.wait()
        except KeyboardInterrupt:
            Logger.info(f"{self._LOGGER_TITLE} Received KeyboardInterrupt, initiating shutdown")

        # graceful shutdown
        await self.gracefulShutdown(appTask)

    def installSignalHandlers(self, loop: asyncio.AbstractEventLoop, shutdownEvent: asyncio.Event):
        """
        install signal handlers for SIGINT and SIGTERM to initiate shutdown
        """

        def onSignal(signalName: str):
            shutdownEvent.set()
            Logger.info(f"{self._LOGGER_TITLE} Received signal: {signalName}, initiating shutdown")

        for signalName in ("SIGINT", "SIGTERM"):
            loop.add_signal_handler(getattr(signal, signalName), partial(onSignal, signalName))

    def setExceptionHandler(self, loop: asyncio.AbstractEventLoop, shutdownEvent: asyncio.Event):
        """
        Set default exception handler for the event loop
        """

        def onException(loop, context):
            exception = context.get("exception")
            if exception:
                tb = "".join(traceback.format_exception(type(exception), exception, exception.__traceback__))
                message = f"Exception: {str(exception)}\nTraceback:\n{tb}"
            else:
                message = context["message"]

            Logger.error(f"{self._LOGGER_TITLE} Caught exception: {message}, shutting down...")
            shutdownEvent.set()

        loop.set_exception_handler(onException)

    async def startMainApp(self, shutdownEvent: asyncio.Event):
        """
        start main app
        """

        async def serve():
            try:
                # async_run() is used in Kivy applications to enable asynchronous operation,
                # allowing the integration and concurrent execution of asyncio tasks without blocking the UI or the event loop.
                await self.async_run(async_lib="asyncio")
            finally:
                shutdownEvent.set()

        return await spawn(serve, daemon=True)

    async def gracefulShutdown(self, appTask: asyncio.Task):
        """
        graceful shutdown of the app

        1. cancel server task
        2. cancel all pending tasks
        3. close lmdb env
        """
        Logger.info(f"{self._LOGGER_TITLE} Shutting down gracefully...")
        appTask.cancel()
        try:
            # terminate swagger api service first
            self.process.terminate()

            with suppress(asyncio.CancelledError):
                await appTask

            pending = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
            for task in pending:
                task.cancel()
                with suppress(asyncio.CancelledError):
                    await task

            # close diskdb
            self.chain.closeDb()
        finally:
            Logger.info(f"{self._LOGGER_TITLE} Shutdown complete")


class NodeLabel(Label):
    pass


class VGraphAppGUI(BoxLayout):
    def __init__(self, **kwargs):
        super(VGraphAppGUI, self).__init__(**kwargs)
        # setup GUI
        self.refreshList()
        Clock.schedule_interval(lambda dt: self.refreshList(), 5)

    @mainthread
    def refreshList(self):
        """
        update peer list
        """
        app = App.get_running_app()
        sessionManager: SessionManager = app.sessionManager

        # clear previous list
        app.root.scrollStack.clear_widgets()

        # add header
        app.root.nodeNum = sessionManager.peerManager.getTotalOtherPeersCount()
        app.root.currentKeepAlive = len(sessionManager.peerManager.getOtherRecentGoodPeers())

        # add self's metadata
        metadata = sessionManager._serverMetadata()
        label = NodeLabel()
        label.text = f"""
Myself:
-----------
Local servers: {", ".join(metadata["services"])}
Server version: {metadata["serverVersion"]}
Protocol version: {metadata["protocolVersion"]}
"""
        app.root.scrollStack.add_widget(label)

        # add node label for other peers
        label = NodeLabel()
        label.text = f"""
Total known peer count is {app.root.nodeNum}
Current connected node count is {app.root.currentKeepAlive}
Below is the list of good peers:
"""
        app.root.scrollStack.add_widget(label)

        for peer in sessionManager.peerManager.getOtherRecentGoodPeers():
            label = NodeLabel()
            label.text = f"""
Hosts: {peer.host}
Servers: {", ".join(peer.metadata["services"])}
Server version: {peer.metadata["serverVersion"]}
Protocol version: {peer.metadata["protocolVersion"]}
"""
            app.root.scrollStack.add_widget(label)


if __name__ == "__main__":
    app = VGraphApp()
    uvloop.install()
    loop = asyncio.get_event_loop()
    loop.run_until_complete(app.startApp())
