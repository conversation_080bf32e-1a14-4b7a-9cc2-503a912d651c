# Handles utility functions used by the swaggerapi module.
import asyncio
import json
import logging
import re
from typing import Any, List, Optional

from aiorpcx import Service, ServicePart
from fastapi import HTTPException
from pydantic import BaseModel

# ================================
# Models
# ================================


class StakeAccountModel(BaseModel):
    address: str
    total_stake_in: int  # stake in amount
    total_stake_out: int  # stake out amount

    # class Config:
    #     orm_mode = True  # If interacting with ORM objects


# Define the Rust to Python type mapping
RUST_TO_PYTHON_TYPE_MAP = {
    "String": str,
    "i64": int,
    "u32": int,
    "u64": int,
    "bool": bool,
    "f64": float,
    "Self": str,
    "StakeAccount": StakeAccountModel,
}


# Compile regex patterns at the module level for efficiency
VEC_PATTERN = re.compile(r"Vec\s*<\s*(.+?)\s*>")
OPTION_PATTERN = re.compile(r"Option\s*<\s*(.+?)\s*>")
RESULT_PATTERN = re.compile(r"Result\s*<\s*(.+?)\s*,\s*[^<>]+\s*>")


def mapRustType(rust_type: str) -> Any:
    """
    Recursively maps Rust types to Python types, handling generics like Vec<T> and Option<T>.

    :param rust_type: The Rust type as a string.
    :return: The corresponding Python type.
    """
    rust_type = rust_type.strip()

    # Handle Vec<T>
    match = VEC_PATTERN.fullmatch(rust_type)
    if match:
        inner_type = match.group(1).strip()
        return List[mapRustType(inner_type)]

    # Handle Option<T>
    match = OPTION_PATTERN.fullmatch(rust_type)
    if match:
        inner_type = match.group(1).strip()
        return Optional[mapRustType(inner_type)]

    # Handle Result<T, E> by focusing on T (success type)
    match = RESULT_PATTERN.fullmatch(rust_type)
    if match:
        success_type = match.group(1).strip()
        return mapRustType(success_type)

    # Handle custom types or basic types
    return RUST_TO_PYTHON_TYPE_MAP.get(rust_type, Any)


def cleanList(lst: Optional[List[str]]) -> Optional[List[str]]:
    """
    Cleans the input list by removing empty strings and handling None.

    :param lst: The list to clean.
    :return: A cleaned list or an empty list.
    """
    if lst is None:
        return []
    # Remove all empty strings
    cleaned = [item for item in lst if item.strip()]
    return cleaned if cleaned else []


def getContractHexBytecode(filePath: str) -> str:
    """
    Reads a file and returns its content as a hex string.

    :param filePath: Path to the file.
    :return: Hexadecimal string of the file's content.
    """
    with open(filePath, "rb") as f:
        contractCode = f.read()
    return contractCode.hex()


async def readUntilNewline(
    reader: asyncio.StreamReader, timeout: float = 5.0, maxBytes: int = 10 * 1024 * 1024
) -> bytes:
    """
    Read from the reader until a newline is encountered or the maximum byte limit is reached.

    :param reader: The asyncio StreamReader to read from.
    :param timeout: Timeout for reading.
    :param maxBytes: Maximum number of bytes to read.
    :return: Bytes read until newline.
    """
    buffer = bytearray()
    try:
        while True:
            chunk = await asyncio.wait_for(reader.read(1024), timeout=timeout)
            if not chunk:
                break  # EOF reached
            buffer.extend(chunk)
            newlinePos = buffer.find(b"\n")
            if newlinePos != -1:
                return bytes(buffer[: newlinePos + 1])
            if len(buffer) > maxBytes:
                raise HTTPException(status_code=502, detail="JSON-RPC response too large.")
    except asyncio.TimeoutError as e:
        raise HTTPException(status_code=504, detail="JSON-RPC request timed out.") from e
    return bytes(buffer)


def loadJsonFile(file_path: str, logger: logging.Logger, logger_title: str) -> dict:
    """
    Load a JSON file and return its content as a dictionary.
    Logs an error if the file is not found.

    :param file_path: Path to the JSON file.
    :param logger: Logger instance for logging errors.
    :param logger_title: Title to use in the log messages.
    :return: Dictionary containing the JSON file content, or an empty dictionary if the file is not found.
    """
    try:
        with open(file_path, "r") as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"{logger_title} {file_path} not found.")
        return {}


# Helper function that extracts the ip and port function from the CONFIG.swaggerServer variable
def getHostAndPort(swaggerConfigStr, defaultHost, defaultPort):
    """
    Get the host and port from the swaggerConfigStr.

    :param swaggerConfigStr: The swaggerConfigStr to parse.
    :param defaultHost: The default host to use if the host is not found.
    :param defaultPort: The default port to use if the port is not found.
    :return: A tuple containing the host and port.
    """
    defaultServices = {
        "tcp": {ServicePart.HOST: defaultHost, ServicePart.PORT: defaultPort},
    }

    def default_part(protocol, part):
        if protocol == "udp":
            raise ValueError("UDP is not supported")
        return defaultServices.get(protocol, {}).get(part)

    serviceObject = Service.from_string(swaggerConfigStr, default_func=default_part)

    return str(serviceObject.host), int(serviceObject.port)
