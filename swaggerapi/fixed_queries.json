[{"jsonrpc": "2.0", "method": "blocktree.get_block_by_hash", "params": ["blockHash", "includeFullTransactions"], "id": 1}, {"jsonrpc": "2.0", "method": "blocktree.get_block_by_height", "params": ["height", "includeFullTransactions"], "id": 1}, {"jsonrpc": "2.0", "method": "transaction.get_transaction_by_hash", "params": ["transactionHash"], "id": 1}, {"jsonrpc": "2.0", "method": "transaction.get_receipt", "params": ["transactionHash"], "id": 1}, {"jsonrpc": "2.0", "method": "mempool.new_transaction", "params": ["transactionJsonData"], "id": 1}, {"jsonrpc": "2.0", "method": "blocktree.get_blocks", "params": [["locator"]], "id": 1}, {"jsonrpc": "2.0", "method": "contract.execute", "params": [], "id": 1}, {"jsonrpc": "2.0", "method": "contract.create", "params": [], "id": 1}, {"jsonrpc": "2.0", "method": "contract.query", "params": [], "id": 1}, {"jsonrpc": "2.0", "method": "contract.fork", "params": [], "id": 1}, {"jsonrpc": "2.0", "method": "contract.get_code_hash", "params": ["contractCodeAddress"], "id": 1}, {"jsonrpc": "2.0", "method": "contract.upgrade", "params": []}, {"jsonrpc": "2.0", "method": "contract.get_logs", "params": [], "id": 1}]