from typing import Any, List, Optional

import pytest

from .utils import StakeAccountModel, getHostAndPort, mapRustType


# Test cases for the helper function
def test_getHostAndPort():
    assert getHostAndPort("tcp://:9878", "localhost", 9879) == ("localhost", 9878)

    # Test udp and raise ValueError
    with pytest.raises(ValueError, match="UDP is not supported"):
        getHostAndPort("udp://:9878", "localhost", 9879)

    assert getHostAndPort(["tcp://:9878"][0], "localhost", 9879) == ("localhost", 9878)


@pytest.mark.parametrize(
    "rust_type, expected",
    [
        # Basic types
        ("String", str),
        ("i64", int),
        ("u32", int),
        ("u64", int),
        ("bool", bool),
        ("f64", float),
        ("Self", str),
        ("StakeAccount", StakeAccountModel),
        # Vec<T>
        ("Vec<String>", List[str]),
        ("Vec<i64>", List[int]),
        ("Vec<StakeAccount>", List[StakeAccountModel]),
        # Option<T>
        ("Option<String>", Optional[str]),
        ("Option<i64>", Optional[int]),
        ("Option<StakeAccount>", Optional[StakeAccountModel]),
        # Result<T, E>
        ("Result<String, Error>", str),
        ("Result<i64, Error>", int),
        ("Result<StakeAccount, Error>", StakeAccountModel),
        # Nested generics
        ("Option<Vec<String>>", Optional[List[str]]),
        ("Vec<Option<i64>>", List[Optional[int]]),
        ("Option<Result<StakeAccount, Error>>", Optional[StakeAccountModel]),
        ("Result<Option<u32>, Error>", Optional[int]),
        # Unmapped types
        ("UnknownType", Any),
        ("Vec<UnknownType>", List[Any]),
        ("Option<UnknownType>", Optional[Any]),
        ("Result<UnknownType, Error>", Any),
        # Whitespace handling
        (" Vec < String > ", List[str]),
        (" Option < Vec < i64 > > ", Optional[List[int]]),
        ("Result < Option < StakeAccount > , Error>", Optional[StakeAccountModel]),
        # Complex nested generics
        ("Option<Result<Vec<Option<StakeAccount>>, Error>>", Optional[List[Optional[StakeAccountModel]]]),
    ],
)
def test_mapRustType(rust_type, expected):
    assert mapRustType(rust_type) == expected
