# VGraph

VGraph Client + server

### Installation


```sh
# Instructions for Ubuntu 20.04 LTS

git clone ssh://**************/virtualeconomy/vgraph
cd vgraph

# Some dependent libraries
# Refer to these issues if forgetting to install some packages here
# https://github.com/kivy/buildozer/issues/963
# https://github.com/kivy/kivy/issues/4036
sudo apt install build-essential cmake autoconf automake libtool libffi-dev libssl-dev
sudo apt install default-jdk
sudo apt install xsel xclip

# Set up pipenv
sudo apt install python3-pip
pip3 install --user --upgrade pipenv
# Maybe need logout and login here
pipenv shell
pipenv install
```

### Test and Run on Linux

```sh
# Run unit test
python -m unittest

# Run app
python main.py -m screen:onex,portrait
```

### Run Server Node

```sh
# Run server
python udpHolePunching/server.py
```

### Build and Run Server Node with Docker

```sh
# Build node pipenv dependency docker image
docker build . -f vgs/Dockerfile-pipenv -t primecoin/vgsenv

# Build server node docker image
cd udpHolePunching
python -m compileall -b .
docker build . -f Dockerfile-server -t vgraph-server
```

To run server node locally for testing (on Linux):

```sh
# Run server node
docker run -p 33478:33478/udp --network=host --memory=1g vgraph-server
```

### Build Android App for Debug

To enable USB debugging on Android, tap 7 times on settings/system/about-phone/build-number to enable developer settings. Then enable USB debugging in settings/system/developer-options/usb-debugging.

```sh
# Make sure necessary tools are installed
sudo apt install default-jdk
sudo apt install build-essential autotools-dev autoconf libtool libffi-dev libssl-dev

# This builds the debug apk, pushes to smart phone and run
# Deployment requires Android to be in debugging mode with USB file transfer
buildozer android debug deploy run
```

```sh
# Find where adb is located
buildozer android logcat

# Filtering logcat while running app on smart phone
~/.buildozer/android/platform/android-sdk/platform-tools/adb shell
$ logcat -s python,service,AndroidRuntime,Messenger
```

If adb complains about permission on deploy, try set up udev rule according to [this instruction](https://askubuntu.com/a/349191).

### Connect multiple clients with a server node

VGraph helps different clients establish peer-to-peer connections through an intermediate server node, you can test it with below steps.

1、First run the server node.

2、Set the ip of server node in client.py and then run multiple client nodes on different devices.

3、Once a connection between two client noides is established, the client nodes will send keepalive message to each other to keep the connection.

### Build Android App for Release

Release key required.

[Instructions by kivy](https://github.com/kivy/kivy/wiki/Creating-a-Release-APK)

### Contract Build Instruction

[Instructions](CONTRACT_README.md)
