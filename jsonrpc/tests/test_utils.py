import unittest

from jsonrpc.utils import isValidIp


class TestIsValidIp(unittest.TestCase):
    def test_valid_ipv4(self):
        self.assertTrue(isValidIp("***********"))

    def test_valid_ipv6(self):
        self.assertTrue(isValidIp("2001:0db8:85a3:0000:0000:8a2e:0370:7334"))

    def test_invalid_ip(self):
        self.assertFalse(isValidIp("256.256.256.256"))
        self.assertFalse(isValidIp("123"))

    def test_empty_string(self):
        self.assertFalse(isValidIp(""))

    def test_non_string_input(self):
        self.assertFalse(isValidIp(12345))


if __name__ == "__main__":
    unittest.main()
