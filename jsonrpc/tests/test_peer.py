import unittest
from socket import AF_INET, AF_INET6

from jsonrpc.peer import Peer


class TestRealName(unittest.TestCase):
    # test Peer.realName

    def test_valid_real_name(self):
        metadata = {
            "hosts": {
                "testpeer.net": {
                    "tcpPort": 8000,
                }
            },
            "tcpPort": 8000,
            "protocolVersion": "0.0.1",
            "serverVersion": "0.0.1",
        }
        peer = Peer.peersFromMetadata(metadata, "test")[0]
        self.assertEqual(peer.realName(), "testpeer.net t8000 p0.0.1 s0.0.1")

    def test_use_default_port(self):
        metadata = {
            "hosts": {
                "testpeer.net": {
                    "tcpPort": Peer.DEFAULT_PORTS["t"],
                }
            },
            "tcpPort": Peer.DEFAULT_PORTS["t"],
            "protocolVersion": "0.0.1",
            "serverVersion": "0.0.1",
        }
        peer = Peer.peersFromMetadata(metadata, "test")[0]
        self.assertEqual(peer.realName(), "testpeer.net t p0.0.1 s0.0.1")


class TestTcpPort(unittest.TestCase):
    # test Peer.tcpPort

    def test_valid_tcp_port(self):
        metadata = {
            "hosts": {
                "testpeer.net": {
                    "tcpPort": 8000,
                }
            },
            "tcpPort": 8000,
            "protocolVersion": "0.0.1",
            "serverVersion": "0.0.1",
        }
        peer = Peer.peersFromMetadata(metadata, "test")[0]
        self.assertEqual(peer.tcpPort, 8000)

    def test_port_out_of_range(self):
        metadata = {
            "hosts": {
                "testpeer.net": {
                    "tcpPort": 65536,
                }
            },
            "tcpPort": 65536,
            "protocolVersion": "0.0.1",
            "serverVersion": "0.0.1",
        }
        peer = Peer.peersFromMetadata(metadata, "test")[0]
        self.assertEqual(peer.tcpPort, None)

    def test_port_not_an_integer(self):
        metadata = {
            "hosts": {
                "testpeer.net": {
                    "tcpPort": "8000",
                }
            },
            "tcpPort": "8000",
            "protocolVersion": "0.0.1",
            "serverVersion": "0.0.1",
        }
        peer = Peer.peersFromMetadata(metadata, "test")[0]
        self.assertEqual(peer.tcpPort, 8000)


class TestMatches(unittest.TestCase):
    # test Peer.matches

    def test_match_real_name(self):
        metadata = {
            "hosts": {
                "testpeer.net": {
                    "tcpPort": 8000,
                }
            },
            "tcpPort": 8000,
            "protocolVersion": "0.0.1",
            "serverVersion": "0.0.1",
        }
        peer = Peer.peersFromMetadata(metadata, "test")[0]
        metadata_list = [
            {
                "hosts": {
                    "testpeer.net": {
                        "tcpPort": 8000,
                    }
                },
                "tcpPort": 8000,
                "protocolVersion": "0.0.1",
                "serverVersion": "0.0.1",
            },
            {
                "hosts": {
                    "testpeer1.net": {
                        "tcpPort": 8001,
                    }
                },
                "tcpPort": 8001,
                "protocolVersion": "0.0.1",
                "serverVersion": "0.0.1",
            },
        ]

        peers = [Peer.peersFromMetadata(metadata, "test")[0] for metadata in metadata_list]
        self.assertEqual(len(peer.matches(peers)), 1)

    def test_empty_peers(self):
        metadata = {
            "hosts": {
                "testpeer.net": {
                    "tcpPort": 8000,
                }
            },
            "tcpPort": 8000,
            "protocolVersion": "0.0.1",
            "serverVersion": "0.0.1",
        }
        peer = Peer.peersFromMetadata(metadata, "test")[0]
        self.assertEqual(len(peer.matches([])), 0)

    def test_not_match(self):
        metadata = {
            "hosts": {
                "testpeer.net": {
                    "tcpPort": 8000,
                }
            },
            "tcpPort": 8000,
            "protocolVersion": "0.0.1",
            "serverVersion": "0.0.1",
        }
        peer = Peer.peersFromMetadata(metadata, "test")[0]
        metadata_list = [
            {
                "hosts": {
                    "testpeer1.net": {
                        "tcpPort": 8001,
                    }
                },
                "tcpPort": 8001,
                "protocolVersion": "0.0.1",
                "serverVersion": "0.0.1",
            }
        ]

        peers = [Peer.peersFromMetadata(metadata, "test")[0] for metadata in metadata_list]
        self.assertEqual(len(peer.matches(peers)), 0)


class TestConnectionTuples(unittest.TestCase):
    # test Peer.connectionTuples

    def test_ipv4(self):
        metadata = {
            "hosts": {
                "127.0.0.1": {
                    "tcpPort": 8000,
                }
            },
            "tcpPort": 8000,
            "protocolVersion": "0.0.1",
            "serverVersion": "0.0.1",
        }
        peer = Peer.peersFromMetadata(metadata, "test")[0]
        self.assertEqual(peer.connectionTuples(), [("TCP", 8000, AF_INET)])

    def test_ipv6(self):
        metadata = {
            "hosts": {
                "::1": {
                    "tcpPort": 8000,
                }
            },
            "tcpPort": 8000,
            "protocolVersion": "0.0.1",
            "serverVersion": "0.0.1",
        }
        peer = Peer.peersFromMetadata(metadata, "test")[0]
        self.assertEqual(peer.connectionTuples(), [("TCP", 8000, AF_INET6)])

    def test_host_name_is_not_an_ip_address(self):
        metadata = {
            "hosts": {
                "testpeer.net": {
                    "tcpPort": 8000,
                }
            },
            "tcpPort": 8000,
            "protocolVersion": "0.0.1",
            "serverVersion": "0.0.1",
        }
        peer = Peer.peersFromMetadata(metadata, "test")[0]
        self.assertEqual(peer.connectionTuples(), [("TCP", 8000, AF_INET), ("TCP", 8000, AF_INET6)])


if __name__ == "__main__":
    unittest.main()
