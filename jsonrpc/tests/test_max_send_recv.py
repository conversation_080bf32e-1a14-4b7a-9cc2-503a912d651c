import asyncio
from contextlib import suppress

import aiorpcx
import pytest
import pytest_asyncio

HOST = "localhost"
PORT = 19999
TIMEOUT = 1  # 1 second for timeout is enough because the server is running locally
SERVER_MAX_SEND_RECV = 2_000_000  # 2MB


class Session(aiorpcx.RPCSession):
    """
    Test session class for testing.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.request_handlers = {
            "echo": self.echo,
        }

    async def echo(self, message):
        return message

    async def connection_lost(self):
        await super().connection_lost()

    async def handle_request(self, request):
        handler = self.request_handlers.get(request.method)
        coro = aiorpcx.handler_invocation(handler, request)()
        return await coro

    def default_framer(self):
        # Server max receive
        return aiorpcx.NewlineFramer(max_size=SERVER_MAX_SEND_RECV)


@pytest_asyncio.fixture
async def prepare_server():
    """
    Prepare a server for testing.
    """
    await aiorpcx.serve_rs(Session, HOST, PORT)

    yield

    pending = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
    for task in pending:
        task.cancel()
        with suppress(asyncio.CancelledError, asyncio.TimeoutError):
            await asyncio.wait_for(task, timeout=1)


@pytest.mark.asyncio
async def test_server_can_handle(prepare_server):
    """
    Test case: sending a large request that the server can handle.
    client send = server max receive
    """

    client_max_send_receive = SERVER_MAX_SEND_RECV
    async with aiorpcx.connect_rs(HOST, PORT) as session:
        session.transport._framer.max_size = client_max_send_receive
        session.sent_request_timeout = TIMEOUT

        data = str(b"a" * client_max_send_receive)
        result = await session.send_request("echo", {"message": data})
        assert result == data


@pytest.mark.asyncio
async def test_server_cannot_handle(prepare_server):
    """
    Test case: sending a large request that the server cannot handle.
    client send = server max receive + 1MB
    """

    client_max_send_receive = SERVER_MAX_SEND_RECV + 1_000_000
    async with aiorpcx.connect_rs(HOST, PORT) as session:
        session.transport._framer.max_size = client_max_send_receive
        session.sent_request_timeout = TIMEOUT

        # should raise an exception
        with pytest.raises(aiorpcx.curio.TaskTimeout):
            data = str(b"a" * client_max_send_receive)
            await session.send_request("echo", {"message": data})


@pytest.mark.asyncio
async def test_client_cannot_send(prepare_server):
    """
    Test case: sending a large request from the client.
    client send = client max send / 2, so the client cannot send the request.
    """

    client_max_send_receive = SERVER_MAX_SEND_RECV
    async with aiorpcx.connect_rs(HOST, PORT) as session:
        # Set the client max send/receive to half of the server max send/receive
        # so that the client cannot send the request.
        session.transport._framer.max_size = client_max_send_receive // 2
        session.sent_request_timeout = TIMEOUT

        # should raise an exception
        with pytest.raises(aiorpcx.curio.TaskTimeout):
            data = str(b"a" * client_max_send_receive)
            await session.send_request("echo", {"message": data})
