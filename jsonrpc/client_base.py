from typing import Any, List, Optional

from aiorpcx import TaskGroup, connect_rs
from kivy.logger import Logger

from config.config import CONFIG
from jsonrpc.peer import Peer
from jsonrpc.peers import PeerManager


class ClientBase:
    """
    Base client for sending request to JSON-RPC services.
    """

    def __init__(self):
        pass

    async def sendRequest(
        self,
        host: str,
        port: int,
        method: str,
        params: Optional[dict] = None,
        timeout: int = 30,
        connectionKwargs: Optional[dict] = None,
    ) -> Any:
        if params is None:
            params = {}
        if connectionKwargs is None:
            connectionKwargs = {}

        try:
            async with connect_rs(host=host, port=port, **connectionKwargs) as session:
                session.transport._framer.max_size = CONFIG.maxSendRecv
                session.sent_request_timeout = timeout
                return await session.send_request(method, params)
        except Exception as e:
            raise e


class VGraphClient(ClientBase):
    """
    VGraphClient is the client for the VGraph service.

    """

    _LOGGER_TITLE = "VGraphClient:"

    def __init__(self):
        super().__init__()
        self.peerManager = PeerManager()

    async def sendRequestToOnePeer(
        self,
        peer: Peer,
        method: str,
        params: Optional[dict] = None,
        timeout: int = 30,
    ) -> Any:
        """
        Send request to one peer, return the result
        """
        if params is None:
            params = {}

        for kind, port, family in peer.connectionTuples():
            peerText = f"[{peer}:{port} {kind}]"

            connectionKwargs = {"family": family}
            try:
                result = await self.sendRequest(peer.host, port, method, params, timeout, connectionKwargs)
                return result
            except Exception as e:
                Logger.warning(f"{self._LOGGER_TITLE} Sending message {method} to {peerText} error: {e}")

    async def sendRequestToPeers(
        self,
        method: str,
        params: Optional[dict] = None,
        targetPeers: Optional[List[Peer]] = None,
        timeout: int = 30,
    ) -> Any:
        """
        Send request to target list of peers concurrently.
        Gather results and return as a list

        If target peers is None, send to all peers
        """

        if params is None:
            params = {}
        if targetPeers is None:
            targetPeers = [peer for peer in self.peerManager.peers if peer not in self.peerManager.myselves]

        results = []
        async with TaskGroup() as group:
            for peer in targetPeers:
                await group.spawn(self.sendRequestToOnePeer(peer, method, params, timeout))

            async for task in group:
                if not task.cancelled():
                    result = task.result()
                    results.append(result)

        return results
