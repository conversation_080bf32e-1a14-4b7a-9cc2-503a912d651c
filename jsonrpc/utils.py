import concurrent.futures
import ipaddress
import logging
import random
from collections import Counter
from typing import List, Optional

import requests
from kivy.logger import Logger

logger = logging.getLogger("urllib3")
logger.setLevel(logging.CRITICAL)

# ----- network utils -----

ipServices = [
    "https://api.ipify.org",
    "https://ipinfo.io/ip",
    "https://icanhazip.com/",
    "https://ident.me/",
    "https://ip.42.pl/raw",
    "https://myexternalip.com/raw",
    "https://ipecho.net/plain",
    "https://ifconfig.co/ip",
    "https://ifconfig.me/ip",
    "https://ifconfig.io/ip",
    "https://ip.tyk.nu/",
    "https://ipapi.co/ip/",
]


def isValidIp(ip: str) -> bool:
    """
    check if a given string is a valid ip address
    """
    try:
        assert isinstance(ip, str)
        ipaddress.ip_address(ip)
        return True
    except Exception:
        return False


def fetchIp(service: str) -> Optional[str]:
    """
    fetch the ip address from a given service
    """
    try:
        Logger.info(f"Network: fetching ip from {service}")
        response = requests.get(service, timeout=5)
        if response.status_code == 200:
            ip = response.text.strip()
            if isValidIp(ip):
                return ip
    except requests.RequestException:
        Logger.warning(f"Network Util: failed to fetch ip from {service}")
        return None

    return None


def getPublicIp():
    """
    get the ip address of the current machine
    """
    ipResponses = []

    # just use 5 random services
    selectedServices = random.sample(ipServices, 5)

    with concurrent.futures.ThreadPoolExecutor(max_workers=len(selectedServices)) as executor:
        futureToIp = {executor.submit(fetchIp, service): service for service in selectedServices}
        for future in concurrent.futures.as_completed(futureToIp):
            ip = future.result()
            if ip:
                ipResponses.append(ip)

    # get the most common ip address
    if ipResponses:
        mostCommonIp, _ = Counter(ipResponses).most_common(1)[0]
        Logger.info(f"Network Util: Internet/external IP is {mostCommonIp}")
        return mostCommonIp

    Logger.warning("Network Util: failed to get public ip")
    return None


# Method decorator.  To be used for calculations that will always
# deliver the same result.  The method cannot take any arguments
# and should be accessed as an attribute.
class cachedproperty(object):
    def __init__(self, f):
        self.f = f

    def __get__(self, obj, type_):
        obj = obj or type_
        value = self.f(obj)
        setattr(obj, self.f.__name__, value)
        return value


def sessionsLines(data: List[tuple]):
    """
    A generator returning lines for a list of sessions.
    data is the return value of `VGraph._sessionData()`.
    """
    formatString = "{:^10} {:^8} {:^13} {:^18}{:^10} {:^12} {:^10} {:^12} {:^10} {:^21}"
    yield formatString.format(
        "Session ID",
        "Cost",
        "Extra Cost",
        "Pending Requests",
        "Received",
        "Received KB",
        "Sent",
        "Sent KB",
        "Time",
        "From",
    )
    for sessionID, fromAddress, cost, extraCost, requestsNum, received, receivedSize, sent, sentSize, time in data:
        yield formatString.format(
            sessionID,
            f"{int(cost):,d}",
            f"{int(extraCost):,d}",
            f"{requestsNum:,d}",
            f"{received:,d}",
            f"{receivedSize // 1024:,d}",
            f"{sent:,d}",
            f"{sentSize // 1024:,d}",
            formatTime(time, sep=""),
            fromAddress,
        )


def formatTime(time, sep=" ") -> str:
    """
    return a number of seconds as a string in days, hours, mins and
    maybe secs.
    """

    time = int(time)
    formats = (("{:d}d", 86400), ("{:02d}h", 3600), ("{:02d}m", 60))
    parts = []
    for format_, n in formats:
        value = time // n
        if parts or value:
            parts.append(format_.format(value))
        time %= n
    if len(parts) < 3:
        parts.append(f"{time:02d}s")
    return sep.join(parts)
