import asyncio
import random
import socket
import time
from collections import Counter, defaultdict
from ipaddress import IPv4<PERSON><PERSON><PERSON>, IPv6Address
from typing import Dict, List, Optional, Set

from aiorpcx import (
    Event,
    ProtocolError,
    RPCError,
    Service,
    SOCKSError,
    TaskGroup,
    TaskTimeout,
    connect_rs,
    ignore_after,
)
from aiorpcx.util import NetAddress
from kivy.logger import Logger

from config.config import CONFIG
from jsonrpc.peer import Peer

PEER_SERVERS = [peer for peer in CONFIG.peerServers if peer]
PEER_GOOD, PEER_NEVER, PEER_BAD = range(3)
TRY_LIMIT = 2
MONITOR_INTERVAL = 30  # s
PEER_ADD_PAUSE = 600  # s


def assertGood(message, result, instance):
    if not isinstance(result, instance):
        raise BadPeerError(f"{message} returned bad result type {type(result).__name__}")


class PeerManager:
    """Looks after the DB of peer network servers.

    Attempts to maintain a connection with up to 8 peers.
    Issues a 'peers.subscribe' RPC to them and tells them our data.
    """

    _LOGGER_TITLE = "PeerManager:"

    def __new__(cls):
        # Use Singleton pattern, return the same instance
        if not hasattr(cls, "instance"):
            cls.instance = super(PeerManager, cls).__new__(cls)
        return cls.instance

    def __init__(self):
        self.group = TaskGroup()
        self.myselves: List[Peer] = list()
        self.peers: Set[Peer] = set()
        self.recentPeerAdds: Dict[str, float] = dict()

        self.services: List[Service] = list()

        self.addPeerObservers = set()

    def _metadataToRegister(self, peer: Peer, remotePeers: List[Peer]) -> Optional[Dict]:
        """
        Return metadata to register with remote peer,
        or None if no registration is needed.
        """
        if peer in self.myselves:
            return None
        my = self.myselves[0] if self.myselves else None
        if not my:
            return None

        return my.metadata if peer not in my.matches(remotePeers) else None

    def info(self):
        """
        the number of peers.
        """
        self._setPeerStatuses()
        counter = Counter(peer.status for peer in self.peers)
        return {
            "good": counter[PEER_GOOD],
            "never": counter[PEER_NEVER],
            "bad": counter[PEER_BAD],
        }

    def lookUpPeerFromAddress(self, address: NetAddress):
        """Look up peer from ip address."""
        if isinstance(address.host, (IPv4Address, IPv6Address)):
            host = str(address.host)
            for peer in self.peers:
                if peer.ipAddr == host:
                    return peer
        return None

    async def managePeerStatuses(self) -> None:
        """Manage peer statuses."""
        while True:
            self._setPeerStatuses()
            await asyncio.sleep(10)

    def _setPeerStatuses(self) -> None:
        """Set the status of each peer."""
        for peer in self.peers:
            if peer.bad:
                peer.status = PEER_BAD
            elif peer.lastGood > 0:
                peer.status = PEER_GOOD
            else:
                peer.status = PEER_NEVER

    async def fillPeers(self) -> None:
        """Fill the peer list when len(self.peers) < 8."""
        while len(self.peers) < 8:
            await asyncio.sleep(30)
            await self.discoverPeers()

    async def discoverPeers(self) -> None:
        """Perform peer maintenance.  This includes

        1) Forgetting unreachable peers.
        2) Verifying connectivity of new peers.
        3) Retrying old peers at regular intervals.
        """
        Logger.info(f"{self._LOGGER_TITLE} Starting peer discovery")
        async with self.group as group:
            await group.spawn(self._importPeers())

            async for task in group:
                if not task.cancelled():
                    task.result()

    async def _importPeers(self) -> None:
        """
        import hard-coded peers from config file, and add them to self. peers

        self servers will be also included.
        """
        importedPeers = self.myselves.copy()
        importedPeers.extend(Peer.fromService(service, "config.yaml") for service in PEER_SERVERS)
        await self._addPeers(importedPeers, limit=None)

    async def _addPeers(self, peers: List[Peer], limit: int = 2, source: str = None) -> bool:
        """
        add a limited number of peers that are not already present.
        """

        newPeers: List[Peer] = list()
        alreadyPresentPeers = self.peers.copy()
        for peer in peers:
            matches = peer.matches(alreadyPresentPeers)
            if not matches:
                # if peer not in self.peers, add it to newPeers
                alreadyPresentPeers.add(peer)
                newPeers.append(peer)

        # if there are at least a new peer
        if newPeers:
            source = source or newPeers[0].source
            if limit:
                random.shuffle(newPeers)
                # just use the first limit peers
                usePeers = newPeers[:limit]
            else:
                usePeers = newPeers

            # accept new peers, and monitor them
            for peer in usePeers:
                Logger.info(f"{self._LOGGER_TITLE} Accepted new peer {peer} from {source}")
                # initialize peer retry event, which is mentioned before
                peer.retryEvent = Event()
                # add peer
                self.peers.add(peer)
                # notify all observers that a new peer has been added
                await self.notifyAddPeerObserver(peer)
                # start monitor peer task
                await self.group.spawn(self._monitorPeer(peer))

        return True

    async def _monitorPeer(self, peer: Peer) -> None:
        """
        monitor peer connection.
        """
        # when peer not in self.peers, stop monitoring
        while peer in self.peers:
            # check if peer should be dropped
            if await self._shouldDropPeer(peer):
                self.peers.discard(peer)
                break

            # figure out how long to sleep before retrying
            pause = MONITOR_INTERVAL

            Logger.debug(f"{self._LOGGER_TITLE} Retrying peer {peer} in {pause} seconds")

            async with ignore_after(pause):
                await peer.retryEvent.wait()
                peer.retryEvent.clear()

    async def _shouldDropPeer(self, peer: Peer) -> bool:
        """
        Check if the peer should be dropped.
        """

        Logger.debug(f"{self._LOGGER_TITLE} Checking peer {peer} for drop")

        peer.tryCount += 1
        isGood = False
        peerText = ""
        for kind, port, family in peer.connectionTuples():
            peer.lastTry = time.time()

            kwargs = {
                "family": family,
            }
            # use out listening host/ip for outgoing non-proxy connections
            # so our peers see the correct source
            localHosts = {
                service.host for service in self.services if isinstance(service.host, (IPv4Address, IPv6Address))
            }
            if localHosts:
                kwargs["local_addr"] = (str(localHosts.pop()), None)

            peerText = f"[{peer.host}:{port} {kind}]"
            try:
                # connect to the peer
                async with connect_rs(peer.host, port, **kwargs) as session:
                    session.sent_request_timeout = 30
                    await self._verifyPeer(session, peer)
                isGood = True
                break
            except BadPeerError:
                Logger.warn(f"{self._LOGGER_TITLE} {peerText} marking bad")
                peer.markBad()
                break
            except (RPCError, ProtocolError) as e:
                Logger.warn(f"{self._LOGGER_TITLE} rpc error {peerText}, {e.message}")
            except (OSError, SOCKSError, ConnectionError, TaskTimeout) as e:
                Logger.warn(f"{self._LOGGER_TITLE} Peer connection error {peerText}, {e}")

        if isGood:
            now = time.time()
            elapsed = now - peer.lastTry
            Logger.debug(f"{self._LOGGER_TITLE} Peer {peerText} verified in {elapsed:.1f}s")
            peer.tryCount = 0
            peer.lastGood = now
            peer.source = "peer"

            matches = peer.matches(self.peers)
            for match in matches:
                if match.ipAddress:
                    if len(matches) > 1:
                        self.peers.remove(match)
                        # force the peer's monitoring task to exit
                        match.retryEvent.set()
                elif peer.host in match.metadata["hosts"]:
                    match.updateMetadataFromPeer(peer)
            # trim this data structure
            self.recentPeerAdds = {k: v for k, v in self.recentPeerAdds.items() if v + PEER_ADD_PAUSE < now}
        else:
            # forget the peer if long-term unreachable
            tryLimit = TRY_LIMIT
            if peer.tryCount >= tryLimit:
                desc = "bad" if peer.bad else "unreachable"
                Logger.info(f"{self._LOGGER_TITLE} Forget peer {desc} {peerText}")
                return True

        return False

    async def _verifyPeer(self, session, peer: Peer) -> None:
        """
        verify a peer.

        steps:
            1. set Peer.ipAddr to remote_address()
            2. check if peer's ip address is already in recent good peers(excluded peer itself)
            3. send server.metadata request to peer's server, get peer's server metadata, and update peer using this metadata
            4. send server.get_peers request to peer's server, get peers stored in peer's server, and add these peers to self.peers
            5. send server.add_peer request to peer's server, register ourself with peer's server
        """
        Logger.debug(f"{self._LOGGER_TITLE} Verifying peer {peer}")

        # store peer ip address
        address: NetAddress = session.remote_address()
        if isinstance(address.host, (IPv4Address, IPv6Address)):
            peer.ipAddr = str(address.host)

        # if peer in recent good peers, remove it first
        recentPeers = self.getRecentGoodPeers()
        if peer in recentPeers:
            recentPeers.remove(peer)

        # check if peer's real name is already in recent good peers(excluded peer itself)
        buckets = defaultdict(list)
        for recentPeer in recentPeers:
            buckets[recentPeer.realName()].append(recentPeer)
        bucket = peer.realName()
        if buckets[bucket]:
            raise BadPeerError(f"too many peers already in bucket {bucket}")
        # -------

        async with TaskGroup() as g:
            await g.spawn(self._requestPeerServerMetadata(session, peer))
            peersTask = await g.spawn(self._requestPeers(session, peer))

            async for task in g:
                if not task.cancelled():
                    task.result()

        # process reported peers if remote peer is good
        remotePeers = peersTask.result()
        await self._addPeers(remotePeers)

        metadata = self._metadataToRegister(peer, remotePeers)
        if metadata:
            Logger.debug(f"{self._LOGGER_TITLE} registering ourself with {peer}")
            # We only care to wait for the response
            await session.send_request(
                "server.add_peer",
                {
                    "metadata": metadata,
                },
            )

    @staticmethod
    async def _requestPeerServerMetadata(session, peer: Peer):
        """
        1. send server.metadata request to peer's server, get peer's server metadata
        2. update peer from metadata if peer is in metadata hosts list
        """
        message = "server.metadata"
        metadata = await session.send_request(message)
        assertGood(message, metadata, dict)
        hosts = [host.lower() for host in metadata.get("hosts", {})]
        if peer.host.lower() in hosts:
            peer.updateMetadata(metadata)
        else:
            raise BadPeerError(f"not listed in own hosts list {hosts}")

    async def _requestPeers(self, session, peer: Peer) -> List[Peer]:
        Logger.debug(f"{self._LOGGER_TITLE} Requesting peers from {peer}")
        message = "server.get_peers"
        rawPeers = await session.send_request(message)
        assertGood(message, rawPeers, list)

        try:
            result = []
            for metadata in rawPeers:
                result.extend(Peer.peersFromMetadata(metadata, peer.host))
            return result
        except Exception:
            raise BadPeerError(f"Bad Response: bad {message} response") from None

    def getRecentGoodPeers(self) -> List[Peer]:
        """Return a list of recently good peers."""
        return [peer for peer in self.peers if peer.status == PEER_GOOD]

    def getOtherRecentGoodPeers(self) -> List[Peer]:
        """Return a list of recent good peers, excluding selves."""
        return [peer for peer in self.getRecentGoodPeers() if peer not in self.myselves]

    def getTotalOtherPeersCount(self) -> int:
        """Return the total number of other peers."""
        return max(len(self.peers) - len(self.myselves), 0)

    # ----- rpc method handlers -----

    async def addPeer(self, metadata: dict, sourceAddr: NetAddress) -> bool:
        if not sourceAddr:
            Logger.debug(f"{self._LOGGER_TITLE} Ignored add_peer from unknown peer")
            return False

        # parse metadata into peers
        sourceHost = str(sourceAddr.host)
        peers = Peer.peersFromMetadata(metadata, sourceHost)
        if not peers:
            Logger.debug(f"{self._LOGGER_TITLE} ignored add_peer with no hosts")
            return False

        # just look at the first peer, require it
        peer = peers[0]
        host = peer.host
        now = time.time()

        bucket = peer.realName()
        last = self.recentPeerAdds.get(bucket, 0)
        self.recentPeerAdds[bucket] = now
        if last + PEER_ADD_PAUSE >= now:
            Logger.debug(f"{self._LOGGER_TITLE} Ignored add_peer for {host} due to recent add")
            return False

        # check if the source is the same as the peer; resolve the host
        getaddrinfo = asyncio.get_event_loop().getaddrinfo
        try:
            infos = await getaddrinfo(host, 80, type=socket.SOCK_STREAM)
        except socket.gaierror:
            permit = False
            reason = "address resolution failure"
        else:
            permit = any(sourceHost == info[-1][0] for info in infos)
            reason = "source-destination mismatch"

        # accept private source addresses
        if isinstance(sourceAddr.host, (IPv4Address, IPv6Address)) and sourceAddr.host.is_private:
            permit = True
            reason = "private source"

        if permit:
            Logger.debug(f"{self._LOGGER_TITLE} Accepted add_peer request from {sourceHost} for {host}")
            await self._addPeers([peer])
        else:
            Logger.warning(f"{self._LOGGER_TITLE} Rejected add_peer request from {sourceHost} for {host}: {reason}")

        return permit

    async def getPeers(self):
        """Returns the server peers as a list of (ip, host, metadata) tuples."""
        recentGoodPeers = self.getRecentGoodPeers()

        # first get myselves
        peers: Set[Peer] = set(myself for myself in self.myselves if not myself.bad)

        buckets = defaultdict(list)
        for peer in recentGoodPeers:
            buckets[peer.realName()].append(peer)
        for bucketPeers in buckets.values():
            random.shuffle(bucketPeers)
            # then add peers from each bucket
            peers.update(bucketPeers)
        return [peer.metadata for peer in peers]

    # ----- misc -----
    async def logPeersInfo(self, interval: int = 60):
        """
        Log peers info periodically.
        """

        while True:
            log = f"""{self._LOGGER_TITLE} Current peer status:
Peers: {self.getTotalOtherPeersCount()}
Good peers: {len(self.getOtherRecentGoodPeers())}"""

            for peer in self.getOtherRecentGoodPeers():
                log += f"""
----------------
Peer {peer.nodeId}:
    Hosts: {peer.host}
    Servers: {", ".join(peer.metadata.get("services", "unknown"))}
"""

            Logger.info(log)
            await asyncio.sleep(interval)

    def registerAddPeerObserver(self, observer):
        """
        Register an observer that will be notified when a new peer is added.
        """
        self.addPeerObservers.add(observer)

    async def notifyAddPeerObserver(self, peer: Peer):
        """
        Notify all observers that a new peer has been added.
        """
        for observer in self.addPeerObservers:
            await observer.onAddPeer(peer)


class BadPeerError(Exception):
    pass
