import asyncio
import itertools
from typing import Dict

from aiorpcx import <PERSON><PERSON><PERSON><PERSON><PERSON>, Request, RPCSession, handler_invocation
from kivy import Logger

from config.config import CONFIG
from jsonrpc.server import SessionManager


class SessionBase(RPCSession):
    """
    Base class of a JSON-RPC session.

    When a session connects to the server, a SessionBase or its subclass will be created to handle the session.
    Each session runs its tasks in asynchronous parallelism with other sessions.
    """

    sessionCounter = itertools.count()
    _LOGGER_TITLE = "Session:"

    def __init__(self, sessionManager: SessionManager, transport: asyncio.Transport):
        super().__init__(transport)

        self.sessionManager = sessionManager

        self.session_id = None
        self.sessionId = next(self.sessionCounter)
        self.request_handlers = dict()

        self.sessionManager.addSession(self)

        self.recalc_concurrency()

    async def connection_lost(self):
        """
        Handle client disconnection.
        """
        await super().connection_lost()
        self.sessionManager.removeSession(self)

    async def handle_request(self, request):
        """
        Handle a JSON-RPC request.
        """
        handler = None
        if isinstance(request, Request):
            handler = self.request_handlers.get(request.method, None)
        method = "invalid method" if handler is None else request.method
        self.sessionManager.methodCounts[method] += 1
        Logger.debug(f"{self._LOGGER_TITLE} {request.method} request from {self.remote_address()}")

        if handler is None:
            return None
        coro = handler_invocation(handler, request)()
        return await coro

    def default_framer(self):
        return NewlineFramer(max_size=CONFIG.maxSendRecv)


class VGraphSession(SessionBase):
    """
    VGraphSession is the base JSON-RPC session handler for the VGraph server.

    Include p2p request handlers.
    """

    def __init__(self, sessionManager: SessionManager, transport: asyncio.Transport):
        super().__init__(sessionManager, transport)

        self.cost = 5.0  # connection cost
        self.request_handlers = {
            "server.add_peer": self.addPeer,
            "server.get_peers": self.getPeers,
            "server.metadata": self.getServerMetadata,
        }

    async def addPeer(self, metadata: Dict):
        """
        try to add a peer to the server peer list.
        """
        return await self.sessionManager.peerManager.addPeer(metadata, self.remote_address())

    async def getPeers(self):
        """
        return the server peers as a list of (ip, host, metadata) tuples.
        """
        return await self.sessionManager.peerManager.getPeers()

    async def getServerMetadata(self):
        """
        return the server metadata dictionary.
        """
        return self.sessionManager._serverMetadata()


class ErrorMessage:
    """
    ErrorMessage is a class for general error message.
    """

    @staticmethod
    def invalidRequestParams() -> Dict:
        return {"error": "invalid request params"}

    @staticmethod
    def internalError(e: Exception) -> Dict:
        return {"error": f"internal error: {str(e)}"}
