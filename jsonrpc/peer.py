from ipaddress import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPv4<PERSON>work, IPv6<PERSON><PERSON>ress, IPv6Network, ip_address
from socket import AF_INET, AF_INET6
from typing import List, Optional, Tuple, Union

from aiorpcx import Event

from jsonrpc.utils import cachedproperty


class Peer(object):
    ATTRS = ("host", "metadata", "source", "ipAddr", "lastGood", "lastTry", "tryCount")
    METADATA = ("tcpPort", "protocolVersion", "serverVersion", "nodeId")
    DEFAULT_PORTS = {
        "t": "9841",  # default tcp port
    }

    def __init__(
        self,
        host: str,
        metadata: dict,
        source: str = "unknown",
        ipAddr=None,
        lastGood: int = 0,
        lastTry: int = 0,
        tryCount: int = 0,
    ):
        assert isinstance(host, str)
        assert isinstance(metadata, dict)
        assert isinstance(metadata.get("hosts"), dict)
        assert host in metadata.get("hosts").keys()
        self.host = host
        # set metadata
        self.metadata = metadata.copy()
        # canonicalize / clean-up
        for data in self.METADATA:
            self.metadata[data] = getattr(self, data)
        # source indicates where this peer comes from
        self.source = source
        # ipAddr indicate the ip address of this peer
        # will be set to `remote_address()` if this peer is from `addPeer`
        self.ipAddr = ipAddr
        # lastGood represents the last connection that was
        # successful *and* successfully verified, at which point
        # tryCount is set to 0.  Failure to connect or failure to
        # verify increment the tryCount.
        self.lastGood = lastGood
        self.lastTry = lastTry
        self.tryCount = tryCount
        self.bad = False
        self.otherPortPairs = set()

        # other attributes used in PeerManager
        self.retryEvent: Optional[Event] = None
        self.status: Optional[int] = None

    def __str__(self) -> str:
        return self.connectionEndpoints()

    def serialize(self) -> dict:
        return {attr: getattr(self, attr) for attr in self.ATTRS}

    @classmethod
    def deserialize(cls, data) -> "Peer":
        return cls(**data)

    @classmethod
    def peersFromMetadata(cls, metadata: dict, source: str) -> List["Peer"]:
        """
        parse metadata into a list of Peer objects
        """
        peers = []
        if isinstance(metadata, dict):
            hosts = metadata.get("hosts")
            if isinstance(hosts, dict):
                peers = [Peer(host, metadata, source=source) for host in hosts.keys() if isinstance(host, str)]
        return peers

    @classmethod
    def fromRealName(cls, realName: str, source: str) -> "Peer":
        """
        parse real name into a Peer object

        example real name: "erbium1.sytes.net t8081 p1 c1"
        """
        host = "nohost"
        metadata = dict()
        ports = dict()

        for n, part in enumerate(realName.split()):
            if n == 0:
                # first part is host
                host = part
                continue
            # parse tcp port
            if part[0] == "t":
                if len(part) == 1:
                    port = cls.DEFAULT_PORTS[part[0]]
                else:
                    port = part[1:]
                    ports["tcpPort"] = port
            # parse protocol version
            if part[0] == "p":
                protocolVersion = part[1:]
                metadata["protocolVersion"] = protocolVersion
            # parse server version
            if part[0] == "s":
                serverVersion = part[1:]
                metadata["serverVersion"] = serverVersion

        metadata.update(ports)
        metadata["hosts"] = {host: ports}

        return cls(host, metadata, source)

    @classmethod
    def fromService(cls, service: str, source: str) -> "Peer":
        """
        parse service into a Peer object

        example service: "tcp://***********:8081"
        """
        protocol, address = service.split("://")
        host, port = address.split(":")
        metadata = dict()
        ports = dict()
        if protocol == "tcp":
            ports["tcpPort"] = port

        metadata.update(ports)
        metadata["hosts"] = {host: ports}

        return cls(host, metadata, source)

    def bucketForInternalPurposes(self) -> str:
        """Used for keeping the internal peer list manageable in size.
        Restrictions are loose.
        """
        if not self.ipAddr:
            return ""
        ipAddr = ip_address(self.ipAddr)
        if ipAddr.version == 4:
            return str(ipAddr)
        elif ipAddr.version == 6:
            slash64 = IPv6Network(self.ipAddr).supernet(prefixlen_diff=128 - 64)
            return str(slash64)
        return ""

    def bucketForExternalInterface(self):
        if not self.ipAddr:
            return ""
        ipAddr = ip_address(self.ipAddr)
        if ipAddr.version == 4:
            slash16 = IPv4Network(self.ipAddr).supernet(prefixlen_diff=32 - 16)
            return str(slash16)
        elif ipAddr.version == 6:
            slash56 = IPv6Network(self.ipAddr).supernet(prefixlen_diff=128 - 56)
            return str(slash56)
        return ""

    def realName(self) -> str:
        """
        returns the real name of the peer.

        e.g. "erbium1.sytes.net t8081 p1 c1"
        """

        def portText(letter, port):
            if str(port) == self.DEFAULT_PORTS.get(letter):
                return letter
            else:
                return letter + str(port)

        # host
        parts = [
            self.host,
        ]

        # ports
        for letter, port in (
            ("t", self.tcpPort),  # current we just support tcp port
        ):
            if port:
                parts.append(portText(letter, port))

        # protocol version
        parts.append("p" + self.protocolVersion)

        # server version
        parts.append("s" + self.serverVersion)

        return " ".join(parts)

    def connectionEndpoints(self) -> str:
        """
        return connection endpoints of the peer.

        e.g. "erbium1.sytes.net t8081", "*********** t8081"
        """

        def portText(letter, port):
            if str(port) == self.DEFAULT_PORTS.get(letter):
                return letter
            else:
                return letter + str(port)

        parts = [
            self.host,
        ]
        for letter, port in (
            ("t", self.tcpPort),  # current we just support tcp port
        ):
            if port:
                parts.append(portText(letter, port))
        return " ".join(parts)

    @cachedproperty
    def tcpPort(self) -> Optional[int]:
        """
        return the tcp port of the peer.
        """
        # get port from metadata
        hosts = self.metadata.get("hosts")
        if isinstance(hosts, dict):
            host = hosts.get(self.host)
            port = host.get("tcpPort") if isinstance(host, dict) else None
            if isinstance(port, str):
                try:
                    port = int(port)
                except ValueError:
                    pass
            port = port if isinstance(port, int) else None
            if port and 0 < port < 65535:
                return port
        return None

    @cachedproperty
    def ipAddress(self) -> Optional[Union[IPv4Address, IPv6Address]]:
        """
        Return the IP address of the peer.
        """
        try:
            return ip_address(self.host)
        except ValueError:
            return None

    @cachedproperty
    def protocolVersion(self) -> str:
        """
        Return the protocol version of the peer.
        """
        return self.metadata.get("protocolVersion", "0")

    @cachedproperty
    def serverVersion(self) -> str:
        """
        Return the server version of the peer.
        """
        return self.metadata.get("serverVersion", "0")

    @cachedproperty
    def nodeId(self) -> str:
        """
        Return the node id of the peer.

        Node id is the public key of the peer.
        """
        return self.metadata.get("nodeId", "unknown")

    def matches(self, peers: List["Peer"]) -> List["Peer"]:
        """
        return peers whose real name matches this peer's real name.
        """
        return [peer for peer in peers if peer.connectionEndpoints() == self.connectionEndpoints()]

    def connectionTuples(self) -> List[Tuple]:
        """
        Return a list of (kind, port, family) tuples to try when making a
        connection.
        """

        pairs = [
            ("TCP", self.tcpPort),
        ]
        while self.otherPortPairs:
            pairs.append(self.otherPortPairs.pop())
        if isinstance(self.ipAddress, IPv4Address):
            families = [AF_INET]
        elif isinstance(self.ipAddress, IPv6Address):
            families = [AF_INET6]
        else:
            families = [AF_INET, AF_INET6]
        return [(kind, port, family) for kind, port in pairs if port for family in families]

    def markBad(self) -> None:
        """
        mark as bad to avoid reconnects
        but also to remember for a while.
        """
        self.bad = True

    def updateMetadataFromPeer(self, peer: "Peer") -> None:
        """
        update metadata from another peer.
        the other peer is assumed to be the same peer at a different address.
        the metadata from the other peer is used to update this peer's metadata
        if the other peer's metadata is newer.
        """
        if peer != self:
            self.metadata = peer.metadata
            for data in self.METADATA:
                setattr(self, data, getattr(peer, data))

    def updateMetadata(self, metadata: dict) -> None:
        """
        update metadata in-place
        """
        try:
            tempPeer = Peer(self.host, metadata)
        except AssertionError:
            # ignore bad peer
            pass
        else:
            self.updateMetadataFromPeer(tempPeer)
