from __future__ import annotations

from collections import defaultdict
from typing import Dict, Set

from kivy import Logger

from common.models import Block


class OrphanBlockPool:
    """
    Orphan block pool, used to cache orphan blocks in memory
    """

    _LOGGER_TITLE = "Orphan Block Pool:"

    def __init__(self):
        # key: parent block id
        # value: a set of orphan blocks that have the same parent block id
        self.orphanBlocks: Dict[bytes, Set[Block]] = defaultdict(set)
        self.count = 0

    def addOrphanBlock(self, block: Block):
        """
        add an orphan block to the pool
        """
        self.orphanBlocks[block.parentHash()].add(block)
        self.count += 1
        Logger.debug(
            f"{self._LOGGER_TITLE} Block {block.hashHex()} added to the orphan pool, current orphans: {self.count}"
        )

    def removeOrphanBlocks(self, parentHash: bytes):
        """
        remove orphan blocks that have the same parent block id
        """
        self.count -= len(self.orphanBlocks[parentHash])
        self.orphanBlocks.pop(parentHash, None)
        Logger.debug(
            f"{self._LOGGER_TITLE} Orphan blocks with parent block id {parentHash} removed, current orphans: {self.count}"
        )

    def getOrphanBlocks(self, parentHash: bytes) -> Set[Block]:
        """
        return orphan blocks that have the same parent block id
        """
        return self.orphanBlocks[parentHash]

    def getStatusString(self) -> str:
        """
        return the status of the orphan block pool
        """
        return f"{self._LOGGER_TITLE} {self.count} orphan block(s)"
