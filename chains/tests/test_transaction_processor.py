from copy import deepcopy

import pytest

from chains.tests import (
    CONTRACT_CODE_HASH,
    CONTRACT_CODE_HEX,
    CREATE_TOKEN_CONTRACT_TX,
    FORK_TOKEN_CONTRACT_TX,
    TOKEN_ISSUE_TX,
    TOKEN_TRANSFER_TX,
    TRANSACTIONS_200,
    UPGRADE_TOKEN_CONTRACT_TX,
    UPGRADED_CODE_HASH,
)
from common import bytesToHex, generateContractAddress, hexToBytes
from common.models import TransactionExecutionStatus
from rawdb import readCodeUsageCount


@pytest.mark.asyncio
@pytest.mark.benchmark(group="transaction_processor")
async def test_apply_transactions(sposChain, benchmark):
    """
    Benchmark for chainStateManager._applyTransactions

    Test for executing transactions in the block.
    Case: 200 transactions, 100 issue and 100 transfer.
    """
    block = sposChain.minter._getCandidateBlock(transactions=[])
    state = sposChain.statedbManager.getState()
    blockEnv = block.toEnvironment()

    # Benchmark the applyTransactions method
    benchmark(sposChain.chainStateManager.transactionProcessor.applyTransactions, state, blockEnv, TRANSACTIONS_200, [])


# ----- create contract transaction -----
@pytest.mark.asyncio
async def test_apply_create_contract_transaction(sposChain):
    """
    Test applying a create contract transaction.
    """
    state = sposChain.statedbManager.getState()
    blockEnv = {}
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, CREATE_TOKEN_CONTRACT_TX, blockEnv, 1, False
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS

    # assert account is created
    contractAddressBytes = generateContractAddress(
        CREATE_TOKEN_CONTRACT_TX.op_data.contract_hex_bytecode,
        CREATE_TOKEN_CONTRACT_TX.sender,
        CREATE_TOKEN_CONTRACT_TX.timestamp,
    )
    assert state.hasAccount(contractAddressBytes)


@pytest.mark.asyncio
async def test_apply_create_contract_transaction_invalid_code(sposChain):
    """
    Test applying a create contract transaction with invalid code.
    """
    state = sposChain.statedbManager.getState()
    blockEnv = {}
    invalidTx = deepcopy(CREATE_TOKEN_CONTRACT_TX)
    invalidTx.op_data.contract_hex_bytecode = "invalid_code"
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(state, invalidTx, blockEnv, 1, False)
    assert receipt.status == TransactionExecutionStatus.FAILURE

    # assert account is not created
    contractAddressBytes = generateContractAddress(
        invalidTx.op_data.contract_hex_bytecode, invalidTx.sender, invalidTx.timestamp
    )
    assert not state.hasAccount(contractAddressBytes)


# ----- execute contract transaction -----
@pytest.mark.asyncio
async def test_apply_execute_contract_transaction(sposChain):
    """
    Test applying an execute contract transaction.
    """
    state = sposChain.statedbManager.getState()
    blockEnv = {}
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, TOKEN_ISSUE_TX, blockEnv, 2, False
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, TOKEN_TRANSFER_TX, blockEnv, 3, False
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS


@pytest.mark.asyncio
async def test_apply_execute_contract_transaction_invalid_function(sposChain):
    """
    Test applying an execute contract transaction with an invalid function name.
    """
    state = sposChain.statedbManager.getState()
    blockEnv = {}
    invalidTx = TOKEN_ISSUE_TX
    invalidTx.op_data.function_name = "non_existent_function"
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(state, invalidTx, blockEnv, 2, False)
    assert receipt.status == TransactionExecutionStatus.FAILURE


@pytest.mark.asyncio
async def test_apply_execute_contract_transaction_with_invalid_parameters(sposChain):
    """
    Test applying an execute contract transaction with invalid parameters.
    """
    state = sposChain.statedbManager.getState()
    blockEnv = {}
    invalidTx = TOKEN_ISSUE_TX
    invalidTx.op_data.parameters = []
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(state, invalidTx, blockEnv, 2, False)
    assert receipt.status == TransactionExecutionStatus.FAILURE


# ----- fork contract transaction -----
@pytest.mark.asyncio
async def test_apply_fork_contract_transaction(sposChain):
    """
    Test applying a fork contract transaction.
    """
    state = sposChain.statedbManager.getState()
    blockEnv = {}
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, FORK_TOKEN_CONTRACT_TX, blockEnv, 4, False
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS

    # assert account is created
    contractAddressBytes = generateContractAddress(
        CONTRACT_CODE_HEX,
        FORK_TOKEN_CONTRACT_TX.sender,
        FORK_TOKEN_CONTRACT_TX.timestamp,
    )
    assert state.hasAccount(contractAddressBytes)


@pytest.mark.asyncio
async def test_apply_fork_contract_transaction_invalid_hash(sposChain):
    """
    Test applying a fork contract transaction with an invalid contract code hash.
    """
    state = sposChain.statedbManager.getState()
    blockEnv = {}
    invalidTx = FORK_TOKEN_CONTRACT_TX
    invalidTx.op_data.contract_code_hash = "invalid_hash"
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(state, invalidTx, blockEnv, 4, False)
    assert receipt.status == TransactionExecutionStatus.FAILURE

    # assert account is not created
    contractAddressBytes = generateContractAddress(CONTRACT_CODE_HEX, invalidTx.sender, invalidTx.timestamp)
    assert not state.hasAccount(contractAddressBytes)


# ----- upgrade contract transaction -----
@pytest.mark.asyncio
async def test_apply_upgrade_contract_transaction(sposChain):
    """
    Test applying upgrade contract transaction.

    1. create contract
    2. upgrade contract
    3. execute a new method in new contract
    """
    state = sposChain.statedbManager.getState()
    blockEnv = {}

    # create contract first
    upgradableTx = deepcopy(CREATE_TOKEN_CONTRACT_TX)
    upgradableTx.op_data.upgradable = True
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(state, upgradableTx, blockEnv, 5, False)
    assert receipt.status == TransactionExecutionStatus.SUCCESS
    # assert account is created
    contractAddressBytes = generateContractAddress(
        upgradableTx.op_data.contract_hex_bytecode, upgradableTx.sender, upgradableTx.timestamp
    )
    assert state.hasAccount(contractAddressBytes)

    # upgrade contract
    UPGRADE_TOKEN_CONTRACT_TX.op_data.contract_address = bytesToHex(contractAddressBytes)
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, UPGRADE_TOKEN_CONTRACT_TX, blockEnv, 6, False
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS

    # execute a new method(`get_env`) in new contract
    executeGetEnvTx = deepcopy(TOKEN_ISSUE_TX)
    executeGetEnvTx.op_data.contract_address = bytesToHex(contractAddressBytes)
    executeGetEnvTx.op_data.function_name = "get_env"
    executeGetEnvTx.op_data.parameters = []
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, executeGetEnvTx, blockEnv, 7, False
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS


@pytest.mark.asyncio
async def test_apply_upgrade_contract_transaction_invalid_deployer(sposChain):
    """
    Test applying upgrade contract transaction sent by another address.
    """

    state = sposChain.statedbManager.getState()
    blockEnv = {}

    # create contract first
    upgradableTx = deepcopy(CREATE_TOKEN_CONTRACT_TX)
    upgradableTx.op_data.upgradable = True
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(state, upgradableTx, blockEnv, 5, False)
    assert receipt.status == TransactionExecutionStatus.SUCCESS
    # assert account is created
    contractAddressBytes = generateContractAddress(
        upgradableTx.op_data.contract_hex_bytecode, upgradableTx.sender, upgradableTx.timestamp
    )
    assert state.hasAccount(contractAddressBytes)

    # upgrade contract, should fail because the sender is different
    invalidUpgradeTx = deepcopy(UPGRADE_TOKEN_CONTRACT_TX)
    invalidUpgradeTx.op_data.contract_address = bytesToHex(contractAddressBytes)
    invalidUpgradeTx.sender = "0xb4ab27b1ca381fada54815fcaeadf74b4440e5ed00f89a010f"
    invalidUpgradeTx.op_data.contract_address = bytesToHex(contractAddressBytes)

    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, invalidUpgradeTx, blockEnv, 6, False
    )
    assert receipt.status == TransactionExecutionStatus.FAILURE


@pytest.mark.asyncio
async def test_apply_upgrade_contract_transaction_not_upgradable(sposChain):
    """
    Test applying upgrade contract transaction on a non-upgradable contract.
    """

    state = sposChain.statedbManager.getState()
    blockEnv = {}

    # create contract first
    unupgradableTx = deepcopy(CREATE_TOKEN_CONTRACT_TX)
    unupgradableTx.op_data.upgradable = False
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, unupgradableTx, blockEnv, 5, False
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS
    # assert account is created
    contractAddressBytes = generateContractAddress(
        unupgradableTx.op_data.contract_hex_bytecode, unupgradableTx.sender, unupgradableTx.timestamp
    )
    assert state.hasAccount(contractAddressBytes)

    # upgrade contract, should fail because the contract is not upgradable
    invalidUpgradeTx = deepcopy(UPGRADE_TOKEN_CONTRACT_TX)
    invalidUpgradeTx.op_data.contract_address = bytesToHex(contractAddressBytes)
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, invalidUpgradeTx, blockEnv, 6, False
    )
    assert receipt.status == TransactionExecutionStatus.FAILURE


# ----- mix -----


@pytest.mark.asyncio
async def test_code_usage_update_logic(sposChain):
    """
    Test code usage update logic.

    1. create a token contract (token code hash usage: 2)
    2. upgrage the token contract to lmdb token demo (token code hash usage: 1, lmdb token demo code hash usage: 1)
    3. upgrade the lmdb token demo contract to token contract (token code hash usage: 2, lmdb token demo code hash usage: 0)

    check code usage count at each step
    """

    state = sposChain.statedbManager.getState()
    blockEnv = {}

    # 1. create a token contract (token code hash usage: 2)
    upgradableTx = deepcopy(CREATE_TOKEN_CONTRACT_TX)
    upgradableTx.op_data.upgradable = True
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(state, upgradableTx, blockEnv, 5, False)
    assert receipt.status == TransactionExecutionStatus.SUCCESS
    # assert account is created
    contractAddressBytes = generateContractAddress(
        upgradableTx.op_data.contract_hex_bytecode, upgradableTx.sender, upgradableTx.timestamp
    )
    assert state.hasAccount(contractAddressBytes)
    assert readCodeUsageCount(state.db.diskDB(), hexToBytes(CONTRACT_CODE_HASH)) == 2
    assert readCodeUsageCount(state.db.diskDB(), hexToBytes(UPGRADED_CODE_HASH)) == 0

    # 2. upgrage the token contract to lmdb token demo (token code hash usage: 1, lmdb token demo code hash usage: 1)
    UPGRADE_TOKEN_CONTRACT_TX.op_data.contract_address = bytesToHex(contractAddressBytes)
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, UPGRADE_TOKEN_CONTRACT_TX, blockEnv, 6, False
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS
    assert readCodeUsageCount(state.db.diskDB(), hexToBytes(CONTRACT_CODE_HASH)) == 1
    assert readCodeUsageCount(state.db.diskDB(), hexToBytes(UPGRADED_CODE_HASH)) == 1

    # 3. upgrade the lmdb token demo contract to token contract (token code hash usage: 2, lmdb token demo code hash usage: 0)
    upgradeToTokenTx = deepcopy(UPGRADE_TOKEN_CONTRACT_TX)
    upgradeToTokenTx.op_data.contract_hex_bytecode = CONTRACT_CODE_HEX
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, upgradeToTokenTx, blockEnv, 7, False
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS
    assert readCodeUsageCount(state.db.diskDB(), hexToBytes(CONTRACT_CODE_HASH)) == 2
    assert readCodeUsageCount(state.db.diskDB(), hexToBytes(UPGRADED_CODE_HASH)) == 0
