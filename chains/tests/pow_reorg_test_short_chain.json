[{"header": {"parent_hash": "0x00000000000000000000000000000000", "height": 0, "state_root": "0x3d7fca94751fda3d85132d25f5f9bc395e891c4b0bbfa5dce62c796057b01031", "transactions_root": "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421", "receipts_root": "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421", "bloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "difficulty_score": 1, "difficulty_score_overall": 1, "timestamp": 0, "nonce": 0, "multiplier": 1}, "transactions": []}, {"header": {"parent_hash": "0x427d894c1eb2940ac849f67808b7accba559971501bce0cb341bfdd40e82f3db", "height": 1, "state_root": "0xe9ada866a9a0ba7214a056eb7603f1f83eb634429bc5a3ebc684abab7f2f43fb", "transactions_root": "0x5fbcd7ca306ae1a017d45dd05c21051a96d387ea9f22f2ca81bb066a1cac8ed7", "receipts_root": "0x502d206f3acf50825f828f96df1e011cf926127fa2bf5e15b49e62e80cd01d41", "bloom": "0x00000000000000000000000000000000000000000000000000000000000080000000000000000000000000000008000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004000000000000000000002000000000000000000000000000000000000000000000000000000002000400080000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000008000000000800000000000000000000000000000000000000000", "difficulty_score": 33554432, "difficulty_score_overall": 33554433, "timestamp": 0, "nonce": 0, "multiplier": 0}, "transactions": [{"sender": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06", "timestamp": 1750043446231951000, "dependent_transaction_hash": "", "fuel": 1000000, "op_data": {"op_type": 1, "contract_address": "0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3", "function_name": "issue", "parameters": ["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06", 100000000000]}, "public_keys": ["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"], "signatures": ["0x1a8062dd353a592d9082afe73e8472232baa48aec633762a432bac9cf3f5de19c14cce563eb457826f774722b8c3834211e664df7ceee413d777394d59d7978c"]}, {"sender": "0xb34421d7ae0feb3dcf10ac9b6ae4e12a3253d61a004791517f", "timestamp": 1, "dependent_transaction_hash": "", "fuel": 1000000, "op_data": {"op_type": 1, "contract_address": "0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3", "function_name": "issue", "parameters": ["0xb34421d7ae0feb3dcf10ac9b6ae4e12a3253d61a004791517f", 1001000]}, "public_keys": ["0x82cc75fe87bfc8b67cb3544460a7cce8cf86b5cddc7205550e5d011299923f2c"], "signatures": ["0x8c66c265583ee65038eb867d3b99414d67b917f7f5bf130f2ae0c04fe1d72a20b017c748eb41c7f16e75b0b01949868ea6c940421ac5db07f80eb70bc1bfc78c"]}]}, {"header": {"parent_hash": "0xcd1ba8988060846ae9f8c2ba228c56de6aa862fd94f02b99d2ea10619c8af378", "height": 2, "state_root": "0xd21d86f95d21b655b5fb305b2690093f128415eb471cc9cf151f0a388e6c4e2a", "transactions_root": "0x09e5f75e8966c672a40f7f19da506d80aa51d004e0ad53847a35de84b0c35416", "receipts_root": "0x2bce4119ecbd26d3dc26266ed517796f78da0deb86fa9bdaecf9d326d2c58449", "bloom": "0x00000000000000000000000000000000000000000000000000000000000080000000000000000000000000000008000000000004000000000000000400000000000000000000000000000000000000000000010000000000000000200000000000000000000000000010000000000000000000000000000000000000000000000000010000000000000004000000000000000000002000000000000400000000000000010000000000000000000000000002000400080000000000000000000000000000000000000000000000800000010000000000000000000000800000000000000000008000000000800000000000000000000000000000000010000000", "difficulty_score": 33554432, "difficulty_score_overall": 67108865, "timestamp": 0, "nonce": 0, "multiplier": 0}, "transactions": [{"sender": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06", "timestamp": 1750043446239498000, "dependent_transaction_hash": "", "fuel": 1000000, "op_data": {"op_type": 1, "contract_address": "0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3", "function_name": "issue", "parameters": ["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06", 100000000000]}, "public_keys": ["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"], "signatures": ["0x160a9572674cd33f343144bdd702c67dbab320923192b3a1edfd7284faed936dac37371ecc78b216379844c690941b6af399026426bc9a2aafaff38f870c2d81"]}, {"sender": "0xb34421d7ae0feb3dcf10ac9b6ae4e12a3253d61a004791517f", "timestamp": 2, "dependent_transaction_hash": "", "fuel": 1000000, "op_data": {"op_type": 1, "contract_address": "0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3", "function_name": "transfer", "parameters": ["0xb34421d7ae0feb3dcf10ac9b6ae4e12a3253d61a004791517f", "0xbd059c3365b6a51553144a57cf01686f9c9cf50200a7a3be1f", 100]}, "public_keys": ["0x82cc75fe87bfc8b67cb3544460a7cce8cf86b5cddc7205550e5d011299923f2c"], "signatures": ["0xca129182229097345a1cacd3ade308367a00090f2a218e3c5c0a8a8eac0c4976e82c3a15526a3d633f7f153e53f1d56b10277d4ff443c99e8dc653743023e68e"]}]}]