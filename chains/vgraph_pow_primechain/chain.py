from __future__ import annotations

from typing import Dict, Optional

from kivy import Logger
from serde.json import json_loads

import rawdb
from chains.header_chain import Header<PERSON>hain
from chains.log_retriever import LogRetriever
from chains.mempool import MemoryPool
from chains.signature_manager import SignatureManager
from chains.statedb_manager import StatedbManager
from chains.vgraph_pow_primechain.chain_state_manager import VGraphPOWPrimeChainStateManager
from chains.vgraph_pow_primechain.client import VGraphPOWClient
from chains.vgraph_pow_primechain.miner import VGraphPOWPrimeChainMiner
from chains.vgraph_pow_primechain.session import VGraphPOWChainSession
from common.encode import hexToBytes
from common.models import Block, POWHeader
from jsonrpc.peer import Peer
from vm import ContractExecutor, initSystemContractsCode


class VGraphPOWPrimeChain:
    """
    VGraphPOWPrimeChain is a chain implementation for VGraph with POW consensus algorithm
    """

    _LOGGER_TITLE = "VGraphPOWPrimeChain:"

    chainId = "vgraphpowprime"
    tokenId = "vgraph_token"
    consensusSettings = {
        "algorithm": "pow",
    }
    SESSION_CLASS = VGraphPOWChainSession
    HEADER_CLASS = POWHeader

    def __init__(self):
        self.statedbManager: Optional[StatedbManager] = None
        self.client: Optional[VGraphPOWClient] = None
        self.headerChain: Optional[HeaderChain] = None
        self.miner: Optional[VGraphPOWPrimeChainMiner] = None
        self.chainStateManager: Optional[VGraphPOWPrimeChainStateManager] = None
        self.memoryPool: Optional[MemoryPool] = None
        self.signatureManager: Optional[SignatureManager] = None
        self.logRetriver: Optional[LogRetriever] = None

    def initChain(self):
        """
        Initialize the chain, including:
        - init db, triedb, statedb
        - register system contracts
        - initialize memory pool, clock, chain state manager, etc.
        - init chain state
        """

        self.memoryPool = MemoryPool()

        db = rawdb.newLmdb()
        self.headerChain = HeaderChain(headerType=self.HEADER_CLASS, chainDb=db)
        self.statedbManager = StatedbManager(db, self.headerChain)
        self.logRetriver = LogRetriever(db, self.headerChain)

        # register system contract if it's genesis block
        if self.headerChain.getCanonicalHash(0) is None:
            self.registerSystemContract()

        self.signatureManager = SignatureManager(statedbManager=self.statedbManager)
        self.client = VGraphPOWClient()

        self.chainStateManager = VGraphPOWPrimeChainStateManager(
            db=db,
            headerChain=self.headerChain,
            statedbManager=self.statedbManager,
            memoryPool=self.memoryPool,
            client=self.client,
        )

        def onInitChain():
            # note: initKeys should be called after initChainState, because get state should be called after restore local
            self.signatureManager.initKeys()

        self.chainStateManager.initChainState(onInitChain)

        self.miner = VGraphPOWPrimeChainMiner(
            memoryPool=self.memoryPool,
            chainStateManager=self.chainStateManager,
            signatureManager=self.signatureManager,
            statedbManager=self.statedbManager,
        )

        # register connect block event observers
        self.chainStateManager.registerConnectBlockObserver(self.miner)  # clear candidate block pool
        self.chainStateManager.registerConnectBlockObserver(self.memoryPool)  # remove transactions in block from pool
        self.chainStateManager.registerConnectBlockObserver(self)  # log statuses of each component
        self.chainStateManager.registerConnectBlockObserver(self.client)  # broadcast block to peers

        # register add new peer event observers
        self.client.peerManager.registerAddPeerObserver(self)

    def registerSystemContract(self):
        """
        Register system contracts for the chain
        """
        systemContractNameToAddressMap: Dict[str, bytes] = {}
        with open("chains/vgraph_pow_primechain/system_contracts.json") as f:
            systemContracts = json_loads(f.read())

            for contract in systemContracts:
                systemContractNameToAddressMap[contract["contract_name"]] = hexToBytes(contract["contract_address"])

        state = self.statedbManager.getState()
        initSystemContractsCode(state, systemContractNameToAddressMap)

        # load wasm file from system_contracts folder and register them
        token = ContractExecutor(systemContractNameToAddressMap["token"])
        blocktreeUtils = ContractExecutor(systemContractNameToAddressMap["primechain_utils"])

        registerResults = [
            ("Token", token.constructor(state, self.tokenId)),
            ("PrimechainUtils", blocktreeUtils.constructor(state)),
        ]
        for name, (_, err) in registerResults:
            if err is not None:
                Logger.error(f"{self._LOGGER_TITLE} Failed to register contract {name}")
                exit(1)
        Logger.info(f"{self._LOGGER_TITLE} System Contracts Registered")

    # ----- utils -----

    def onConnectBlock(self, block: Block):
        """
        log the block status
        """
        Logger.info(f"""{self._LOGGER_TITLE} Block connect successfully! Current chain status:
{self.chainStateManager.getStatusString()}
{self.chainStateManager.orphanBlocks.getStatusString()}
{self.memoryPool.getStatusString()}
{self.miner.candidateBlockPool.getStatusString()}
{self.signatureManager.getStatusString()}""")

    async def onAddPeer(self, peer: Peer):
        """
        When peer manager add a new peer, do the following:
        1. ask peer for its best block
        2. compare the best block with the local best block
        3. if the peer has a better block(higher difficulty score overall), request the missing blocks
        """

        # 1. ask peer for its best block
        peerBestBlock = await self.client.sendBlocktreeBestBlock(peer)
        if peerBestBlock is None:
            Logger.debug(f"{self._LOGGER_TITLE} Peer {peer.nodeId} has no best block")
            return

        # 2. compare the best block with the local best block
        localBestBlock = self.chainStateManager.currentBlock()
        if localBestBlock is None:
            Logger.debug(f"{self._LOGGER_TITLE} Local best block is None")
            return

        if peerBestBlock.header.difficulty_score_overall <= localBestBlock.header.difficulty_score_overall:
            # no need to request blocks from the peer
            return

        # 3. if the peer has a better block(higher difficulty score overall), request the missing blocks
        locator = self.chainStateManager.generateLocator()
        syncBlocks = await self.client.sendBlocktreeGetBlocks(
            peer=peer,
            locator=locator,
        )
        if syncBlocks is not None:
            self.chainStateManager.processSyncBlocks(syncBlocks)

    def closeDb(self):
        """
        close the db
        """
        self.statedbManager.db.close()
