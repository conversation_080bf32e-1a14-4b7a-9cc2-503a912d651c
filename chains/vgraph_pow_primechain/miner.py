from __future__ import annotations

import copy
import json
import threading
import time
from typing import Any, Dict, List, Optional

from kivy.logger import Logger

from chains.mempool import MemoryPool
from chains.signature_manager import SignatureManager
from chains.statedb_manager import StatedbManager
from chains.vgraph_pow_primechain.chain_state_manager import VGraphPOWPrimeChainStateManager
from common import bytesToHex, deriveSha, toJson
from common.models import Block, BloomFilter, OperationCallContract, OperationType, POWHeader, Transaction
from common.models.transaction import Transactions
from common.utils import doubleSha256, doubleSha256Hex
from config import CONFIG
from tree import MemoryTrie
from vm import ContractExecutor


class VGraphPOWPrimeCandidateBlockPool:
    """
    VGraphPOWPrimeCandidateBlockPool is a pool to store candidate blocks for mining

    Logic:
    - when get work, add the candidate block to the pool
    - when submit work, get the candidate block from the pool
    - clear the pool when a block is connected
    """

    _LOGGER_TITLE = "VGraphPOWPrimeChain Candidate Block Pool:"

    def __init__(self):
        # key: header hash, value: block
        self.candidateBlocks: Dict[str, Block] = dict()

    def add(self, block: Block) -> None:
        """
        add a block to the pool
        """
        headerHash = doubleSha256Hex(toJson(blockHeaderForMining(block.header)))
        self.candidateBlocks[headerHash] = block
        Logger.debug(f"{self._LOGGER_TITLE} Candidate block added: {block.hashHex()}")

    def clear(self) -> None:
        """
        clear the pool
        """
        self.candidateBlocks.clear()
        Logger.debug(f"{self._LOGGER_TITLE} Candidate block pool cleared")

    def get(self, headerHash: str) -> Optional[Block]:
        """
        get a block from the pool
        """
        return self.candidateBlocks.get(headerHash, None)

    def getStatusString(self) -> str:
        """
        return the status of the pool
        """
        return f"{self._LOGGER_TITLE} {len(self.candidateBlocks)} candidate block(s)"


class VGraphPOWPrimeChainMiner:
    """
    Miner for VGraphPOWPrimeChain

    Main functions:
    - getWork: get work for mining
    - submitWork: submit work result

    """

    _LOGGER_TITLE = "VGraphPOWPrimeChain Miner:"

    def __init__(
        self,
        memoryPool: MemoryPool,
        chainStateManager: VGraphPOWPrimeChainStateManager,
        signatureManager: SignatureManager,
        statedbManager: StatedbManager,
    ):
        self.statedbManager = statedbManager

        self.memoryPool = memoryPool
        self.candidateBlockPool = VGraphPOWPrimeCandidateBlockPool()
        self.chainStateManager = chainStateManager
        self.signatureManager = signatureManager

        self.submitWorkLock = threading.Lock()

        self.address = self.signatureManager.address
        self._initContractClients()

    # ----- init -----

    def _initContractClients(self) -> None:
        """
        Initialize contract clients for block operations
        """
        self.blocktreeUtilsContractClient = ContractExecutor(
            address="0x4fdbfd69087cea21618844fdf7a4d69a476e8de3869ba9a181a42e983aabdcbf",
        )

    # ----- mine -----

    def getWork(self) -> Optional[Dict[str, Any]]:
        """
        getWork returns work for mining

        Logic:
        1. get candidate block and pack it
        2. add the candidate block to the candidate block pool
        3. return the block header for mining
        """
        transactions: List[Transaction] = self.memoryPool.getTransactions()
        candidateBlock = self._getCandidateBlock(transactions=transactions)
        if candidateBlock is None:
            return None
        self.candidateBlockPool.add(candidateBlock)
        return blockHeaderForMining(candidateBlock.header)

    def submitWork(self, work: dict):
        """
        submitWork submits proof of work result
        """
        with self.submitWorkLock:
            return self._submitWork(work)

    def _submitWork(self, work: dict):
        """
        submit proof of work result (contains nonce and multiplier)
        origin = hash(header + nonce) * multiplier
        work: {
            'parent_hash': str,
            'height': int,
            'difficulty': int,
            'merkle': str,
            'nonce': int,
        }
        :return: if success, return True, else return reject message string
        """
        # get multiplier from work, default is 1
        multiplier = work.pop("multiplier", 1)
        difficulty = work["difficulty"]

        # set nonce to 0 to get header hash
        header = copy.deepcopy(work)
        header["nonce"] = 0
        headerHash = doubleSha256Hex(toJson(header))

        candidateBlock = self.candidateBlockPool.get(headerHash)
        if candidateBlock is None:
            Logger.warning(f"{self._LOGGER_TITLE} Received submit work, but it's stale")
            return "stale work"

        # get work hash
        # use json.dumps to make its format same as cryptomark
        workData = json.dumps(work).encode()
        workHash = int.from_bytes(doubleSha256(workData), byteorder="little")
        # get origin and test prime
        origin = workHash * multiplier
        result, err = self.blocktreeUtilsContractClient.executeReadOnly(
            self.statedbManager.getState(), "verify_work", bool, str(origin), difficulty
        )
        if err:
            Logger.warning(f"{self._LOGGER_TITLE} Received submit work, but runtime error")
            return "internal error"
        if not result:
            Logger.warning(f"{self._LOGGER_TITLE} Received submit work, but difficulty is not satisfied")
            return "insufficient work"

        # pack block
        candidateBlock.header.timestamp = int(time.time_ns())
        # Add proof of work result to block
        candidateBlock.header.nonce = work["nonce"]
        candidateBlock.header.multiplier = multiplier

        # clean hash cache
        candidateBlock.resetHashCache()
        Logger.info(f"{self._LOGGER_TITLE} Received valid submit work, creating block...")
        result = self.chainStateManager.processBlock(candidateBlock, isGenesis=False)
        if result.isBlockConnected():
            return True
        else:
            Logger.error(f"{self._LOGGER_TITLE} New block creation failed: {result.error}")
            return "connect block failed"

    def _getCandidateBlock(self, transactions: List[Transaction]) -> Optional[Block]:
        """
        return a candidate block

        the parameters are the transactions in the mempool

        Logic:
        1. get block template
        2. pack block
        """

        # get block template
        candidateHeader = POWHeader(
            parent_hash=b"",
            height=0,
            state_root="",
            transactions_root="",
            receipts_root="",
            bloom=BloomFilter(),
            difficulty_score=0,
            difficulty_score_overall=0,
            timestamp=0,
            nonce=0,
            multiplier=0,
        )
        currentHeader: POWHeader = self.chainStateManager.currentHeader()
        if currentHeader is not None:
            candidateHeader.parent_hash = currentHeader.hash()
            candidateHeader.height = currentHeader.height + 1
            candidateHeader.difficulty_score_overall = currentHeader.difficulty_score_overall

        # candidate block includes the following information:
        # - id: block id
        # - parent_id: parent block id
        # - difficulty_score_overall: overall difficulty score of the chain, up to the *parent block*
        #   (so it need to be updated)
        # - height: height of the block

        # missing information(need to be packed):
        # - difficulty_score: difficulty score of the block
        # - transactions: transactions in the block
        # - merkle: merkle root of transactions
        # - timestamp: timestamp of the block (will be packed after mining)
        # - nonce: nonce of the block (will be packed after mining)
        # - multiplier: multiplier of the block (will be packed after mining)

        # pack block
        nextDifficulty = self.getNextDifficulty()
        if nextDifficulty is None:
            return None

        candidateHeader.difficulty_score = nextDifficulty
        candidateHeader.difficulty_score_overall += candidateHeader.difficulty_score
        finalTransactions: List[Transaction] = list()
        coinbaseTransaction = Transaction(
            dependent_transaction_hash="",
            sender=self.address,
            op_data=OperationCallContract(
                op_type=OperationType.CALL_CONTRACT,
                contract_address=CONFIG.tokenAddress,
                function_name="issue",
                parameters=[self.address, 100_000_000_000],
            ),
            public_keys=[],
            signatures=[],
            timestamp=int(time.time_ns()),
            fuel=1000000,
        )
        # sign
        coinbaseTransaction = self.signatureManager.signTransaction(coinbaseTransaction)
        finalTransactions.append(coinbaseTransaction)
        finalTransactions.extend(transactions)

        # calculate transactions_root using memory trie
        candidateHeader.transactions_root = bytesToHex(deriveSha(Transactions(finalTransactions), MemoryTrie()))

        candidateHeader.nonce = 0  # set init nonce as 0

        Logger.debug(
            f"{self._LOGGER_TITLE} Get candidate block: {candidateHeader.hashHex()}, parent: {bytesToHex(candidateHeader.parent_hash)}"
        )

        return Block(candidateHeader, finalTransactions)

    # ----- utils -----

    def getNextDifficulty(self) -> Optional[int]:
        # get initial difficulty
        initialDifficulty, err = self.blocktreeUtilsContractClient.executeReadOnly(
            self.statedbManager.getState(), "get_initial_difficulty", int
        )
        if err:
            Logger.error(f"{self._LOGGER_TITLE} Get initial difficulty error: {err}")
            return None

        currentHeader = self.chainStateManager.currentHeader()
        if currentHeader is None:
            return initialDifficulty
        if currentHeader.height == 0 or currentHeader.height == 1:
            return initialDifficulty

        currentParentHeader = self.chainStateManager.getBlock(currentHeader.parent_hash, currentHeader.height - 1)
        if currentParentHeader is None:
            Logger.error(f"{self._LOGGER_TITLE} Best block's parent not found")
            return initialDifficulty

        nextDifficulty, err = self.blocktreeUtilsContractClient.executeReadOnly(
            self.statedbManager.getState(),
            "get_next_difficulty",
            int,
            currentHeader.difficulty_score,
            currentHeader.timestamp,
            currentParentHeader.header.timestamp,
        )
        if err:
            # TODO: how to handle get next difficulty error
            Logger.error(f"{self._LOGGER_TITLE} Get next difficulty error: {err}")
            return None
        Logger.debug(f"{self._LOGGER_TITLE} Next difficulty: {nextDifficulty}")
        return nextDifficulty

    def onConnectBlock(self, block: Block):
        self.candidateBlockPool.clear()


# ----- utils -----


def blockHeaderForMining(header: POWHeader) -> Dict[str, Any]:
    return {
        "parent_hash": bytesToHex(header.parent_hash),
        "height": header.height,
        "difficulty": header.difficulty_score,
        "merkle": header.transactions_root,
        "nonce": header.nonce,
    }
