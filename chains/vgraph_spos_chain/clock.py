from __future__ import annotations

import threading
import time

from kivy import Logger
from ntplib import NTPClient


class Clock:
    """
    Python clock for NTP synchronization
    """

    _LOGGER_TITLE = "Clock:"

    def __init__(self, updateInterval=30, retries=3, delay=10):
        self.offset = 0  # Offset between NTP time and local time
        self.currentTime = 0  # Current time in nanoseconds, synchronized with NTP

        self.offsetPanicThreshold = 1e9  # Offset threshold to trigger panic, 1s

        self.ntpServer = "pool.ntp.org"  # NTP server address
        self.updateInterval = updateInterval  # Interval in seconds to update the offset
        self.retryTimes = retries  # Number of retries to get NTP time
        self.retryDelay = delay  # Delay between retries in seconds

        self.thread = None
        self.start()

    def getCorrectedTime(self) -> int:
        """
        return the current time in nanoseconds
        """
        correctedTime = time.time_ns() + self.offset
        if correctedTime <= self.currentTime and self.currentTime - correctedTime < 1e6:
            self.currentTime = correctedTime + 1e6
        else:
            self.currentTime = correctedTime

        return self.currentTime

    def stop(self):
        """
        Stop the NTP clock
        """
        self.thread.join()
        Logger.info(f"{self._LOGGER_TITLE} NTP Clock stopped")

    def start(self):
        """
        Start the NTP clock
        """
        self.thread = threading.Thread(target=self._updateOffsetPeriodically, daemon=True)
        self.thread.start()
        Logger.info(f"{self._LOGGER_TITLE} NTP Clock started")

    def _updateOffset(self):
        """
        update the offset between NTP time and local time
        """

        for attempt in range(self.retryTimes):
            try:
                ntpClient = NTPClient()
                response = ntpClient.request(self.ntpServer, timeout=8)
                offset = int(response.offset * 1e9)  # in nanosecond
                Logger.info(f"{self._LOGGER_TITLE} Adjusting time with offset {offset} ns, source: {self.ntpServer}")
                if abs(offset) > self.offsetPanicThreshold and not self.offset == 0:
                    Logger.warning(
                        f"{self._LOGGER_TITLE} Offset {offset} exceeds threshold {self.offsetPanicThreshold}"
                    )
                    raise Exception("Offset exceeds threshold")
                self.offset = offset

                return  # Exit the function if offset is successfully updated
            except Exception as e:
                Logger.warning(
                    f"{self._LOGGER_TITLE} Failed to get NTP time: {e}, attempt {attempt + 1}/{self.retryTimes}"
                )
                time.sleep(self.retryDelay)

        Logger.warning(f"{self._LOGGER_TITLE} Failed to get NTP time after {self.retryTimes} attempts")

    def _updateOffsetPeriodically(self):
        """
        update the offset between NTP time and local time periodically
        """
        while True:
            try:
                self._updateOffset()
            except Exception as e:
                Logger.warning(f"{self._LOGGER_TITLE} Failed to update offset: {e}")
            time.sleep(self.updateInterval)

    def getStatusString(self):
        """
        return the status of the clock
        """
        return f"""{self._LOGGER_TITLE}
    Corrected Time: {self.getCorrectedTime()} ns
    Offset: {self.offset} ns,
    Current Time: {self.currentTime} ns"""


class ClockUtils:
    """
    Clock utilities
    """

    @staticmethod
    def nanosecondsToSeconds(nanoseconds: int) -> int:
        """
        convert nanoseconds to seconds
        """
        return int(nanoseconds // 1e9)
