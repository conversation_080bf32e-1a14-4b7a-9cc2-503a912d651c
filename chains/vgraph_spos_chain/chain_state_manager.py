from __future__ import annotations

import asyncio
import threading
from dataclasses import dataclass
from enum import Enum
from typing import Callable, Dict, List, Optional

from cachetools import LRUCache
from kivy.logger import Logger
from readerwriterlock.rwlock import RWLockFair

import rawdb
import vgraphdb
from chains.header_chain import <PERSON><PERSON><PERSON><PERSON><PERSON>
from chains.mempool import Memory<PERSON><PERSON>
from chains.orphan_block_pool import OrphanBlockPool
from chains.statedb_manager import StatedbManager
from chains.transaction_processor import TransactionProcessor
from chains.vgraph_spos_chain.client import VGraphSPOSClient
from common import (
    Atomic,
    bytesToHex,
    deriveSha,
    getRequiredKey,
    hexToBytes,
    toJson,
    verifyBlockHeaderSignature,
    verifyTransactionSignatures,
)
from common.models import (
    AbstractHeader,
    Block,
    BloomFilter,
    Body,
    OperationCallContract,
    OperationType,
    Receipt,
    Receipts,
    SPOSHeader,
    Transaction,
    Transactions,
    createBloom,
    hashDifference,
)
from state import index_undo_log
from tree import MemoryTrie
from vm import ContractExecutor

BodyCacheLimit = 256
BlockCacheLimit = 256
ReceiptsCacheLimit = 32
TransactionLookupCacheLimit = 1024


@dataclass
class TransactionLookup:
    blockHash: bytes
    blockIndex: int
    index: int
    transaction: Transaction


class VGraphSPOSChainStateManager:
    """
    VGraphSPOSChainStateManager is the chain state manager of VGraphSPOSChain,
    it manages the chain state, including blocks, transactions, transaction receipts, etc.

    Main functions:
    - processBlock: process a block, including validate, save, connect, etc.
    - handleNewBlockReceived: handle new block received
    - processSyncBlocks: process sync blocks
    - reorganize: reorganize the chain
    """

    _LOGGER_TITLE = "VGraphSPOSChain State Manager:"

    class ProcessBlockResult:
        """
        ProcessBlockResult is used to return the result of processing a block.
        It contains the process state of the block and the error if there's any.
        """

        class ProcessBlockState(Enum):
            UNVALIDATED = 1  # initial state
            VALIDATED = 2  # block is validated
            BLOCK_SAVED = 3  # block is saved to the disk, but not connected to the blocktree
            BLOCK_CONNECTED = 4  # block connected to the blocktree

        class ProcessBlockError(Enum):
            NONE = 0
            RUNTIME_ERROR = 1  # wasm runtime error
            INVALID_BLOCK = 2  # this block is invalid
            MINTING_TX_FAILED = 3  # minting transaction failed
            NEED_REORG = 4  # need reorg
            BLOCK_EXISTS = 5  # block already exists
            ORPHAN_BLOCK = 6  # this block is an orphan block
            LOWER_OR_EQUAL_HEIGHT = 7  # this block is lower or equal to the current best block

        def __init__(self, state: ProcessBlockState, error: ProcessBlockError):
            self.state = state
            self.error = error

        @classmethod
        def unvalidated(cls, error: ProcessBlockError = ProcessBlockError.NONE):
            return cls(cls.ProcessBlockState.UNVALIDATED, error)

        @classmethod
        def validated(cls, error: ProcessBlockError = ProcessBlockError.NONE):
            return cls(cls.ProcessBlockState.VALIDATED, error)

        @classmethod
        def blockSaved(cls, error: ProcessBlockError = ProcessBlockError.NONE):
            return cls(cls.ProcessBlockState.BLOCK_SAVED, error)

        @classmethod
        def blockConnected(cls, error: ProcessBlockError = ProcessBlockError.NONE):
            return cls(cls.ProcessBlockState.BLOCK_CONNECTED, error)

        def isBlockSaved(self):
            return self.state in [self.ProcessBlockState.BLOCK_SAVED, self.ProcessBlockState.BLOCK_CONNECTED]

        def isBlockConnected(self):
            return self.state == self.ProcessBlockState.BLOCK_CONNECTED

        def isError(self):
            return self.error != self.ProcessBlockError.NONE

        def isNeedReorg(self):
            return self.error == self.ProcessBlockError.NEED_REORG

        def isExists(self):
            return self.error == self.ProcessBlockError.BLOCK_EXISTS

        def isOrphan(self):
            return self.error == self.ProcessBlockError.ORPHAN_BLOCK

    def __init__(
        self,
        consensusSettings: Dict,
        db: vgraphdb.KeyValueStore,
        headerChain: HeaderChain,
        statedbManager: StatedbManager,
        memoryPool: MemoryPool,
        client: VGraphSPOSClient,
    ) -> None:
        self.memoryPool = memoryPool
        self.client = client

        # consensus settings
        self.consensusSettings = consensusSettings
        self.slotNums = getRequiredKey(self.consensusSettings, "slotNums")

        self.db = db
        self.headerChain = headerChain

        self._currentBlock = Atomic(None)

        self.blockCache: LRUCache[bytes, Block] = LRUCache(maxsize=BlockCacheLimit)
        self.bodyCache: LRUCache[bytes, Body] = LRUCache(maxsize=BodyCacheLimit)
        self.bodyRLPCache: LRUCache[bytes, bytes] = LRUCache(maxsize=BodyCacheLimit)
        self.receiptsCache: LRUCache[bytes, List[Receipt]] = LRUCache(maxsize=ReceiptsCacheLimit)

        self.transactionLookupLock = RWLockFair()
        self.transactionLookupCache: LRUCache[bytes, TransactionLookup] = LRUCache(maxsize=TransactionLookupCacheLimit)

        self.genesisBlock: Optional[SPOSHeader] = None

        # TODO: clean stale orphan blocks in the memory occasionally
        self.orphanBlocks = OrphanBlockPool()

        self.statedbManager = statedbManager
        self.transactionProcessor = TransactionProcessor()

        # locks
        # reentrant lock, use lock to protect block state
        self.blockStateLock = threading.RLock()

        # observer for observing connection block events
        self.connectBlockObservers = []

        # init contract clients
        self._initContractClients()

    # ----- init -----

    def _initContractClients(self) -> None:
        """
        Initialize contract clients for block operations
        """
        self.sposContractClient = ContractExecutor(
            address="0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9",
        )

    def initChainState(self, onInit: Callable[[], None]) -> None:
        """
        Initialize the chain state, including:
        1. connect genesis block if the chain is empty
        """
        # connect genesis block if the chain is empty
        if (genesisHash := rawdb.readCanonicalHash(self.db, 0)) is None:
            # genesis block not exists
            genesisBlock = self._getGenesisBlock()
            result = self.processBlock(genesisBlock, isGenesis=True)
            if result.isError():
                Logger.error(f"{self._LOGGER_TITLE} initialize chain state fails: {result.error}")
                raise Exception("process genesis block fails.")
            Logger.info(f"{self._LOGGER_TITLE} Chain process genesis block successfully: \n{self.getStatusString()}")
            Logger.info(f"{self._LOGGER_TITLE} Chain state initialized")
        else:
            # restore chain state from the database
            head = rawdb.readHeadBlockHash(self.db)
            if head is None:
                # TODO: auto reset when database file broken
                Logger.error(f"{self._LOGGER_TITLE} Empty database, resetting chain")
                raise Exception("Empty database, resetting chain, delete db file and restart")
            # make sure the entire head block is available
            headBlock = self.getBlockByHash(head)
            if headBlock is None:
                # TODO: auto reset when database file broken
                Logger.error(f"{self._LOGGER_TITLE} Head block not found in the database, resetting chain")
                raise Exception("Head block not found in the database, delete db file and restart")
            # everything seems to be fine, set as the head block
            self._currentBlock.set(headBlock)

            # restore the last known head header
            headHeader = headBlock.header
            if (head := rawdb.readHeadHeaderHash(self.db)) is not None:
                if (header := self.getHeaderByHash(head)) is not None:
                    headHeader = header
            self.headerChain.setCurrentHeader(headHeader)

            # restore the genesis block
            if (genesisBlock := self.getBlockByHash(genesisHash)) is not None:
                self.genesisBlock = genesisBlock
                self.headerChain.setGenesis(genesisBlock.header)
                Logger.info(f"{self._LOGGER_TITLE} Chain state restored from the database")
            else:
                Logger.error(f"{self._LOGGER_TITLE} Genesis block not found in the database")
                raise Exception("Genesis block not found in the database, delete db file and restart")
        # call onInit after chain state initialized
        onInit()

    # ----- blocks -----

    def _getGenesisBlock(self) -> Block:
        """
        return the genesis block of the chain
        """

        header = SPOSHeader(
            parent_hash=hexToBytes("0x00000000000000000000000000000000"),
            height=0,
            state_root="",
            transactions_root="",
            receipts_root="",
            bloom=BloomFilter(),
            local_timestamp=0,
            protocol_timestamp=0,
            slot_id=1,
            proposer_address=hexToBytes("0x00000000000000000000000000000000"),
            public_keys=[],
            signatures=[],
        )

        # pack transactions
        transactions: List[Transaction] = []

        # pack transactions
        # set supernode
        transaction = Transaction(
            dependent_transaction_hash="",
            sender="0x00000000000000000000000000000000",
            op_data=OperationCallContract(
                op_type=OperationType.CALL_CONTRACT,
                contract_address="0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9",
                function_name="content_slot",
                parameters=[
                    1,
                    "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
                ],
            ),
            public_keys=[],
            signatures=[],
            timestamp=0,
            fuel=1000000,
        )

        transactions.append(transaction)

        # calculate transactions_root using memory trie
        header.transactions_root = bytesToHex(deriveSha(Transactions(transactions), MemoryTrie()))
        return Block(header, transactions)

    def validateBlock(self, block: Block) -> bool:
        """
        validate block. return True if the block is valid, otherwise return False
        """
        # ----- block -----
        # validation:
        #   - block id
        #   - block height
        #   - slot id
        #   - block's signature
        header: SPOSHeader = block.header
        if header.height <= 0:
            Logger.debug(f"{self._LOGGER_TITLE} Block height {header.height} is invalid")
            return False
        if header.slot_id <= 0 or header.slot_id > self.slotNums:
            Logger.debug(f"{self._LOGGER_TITLE} Slot id {header.slot_id} is invalid")
            return False
        if not verifyBlockHeaderSignature(header):
            Logger.debug(f"{self._LOGGER_TITLE} Block {block.hashHex()} signature verification failed")
            return False

        # ----- parent block -----
        # validation:
        #   - block height = parent block height + 1
        parentBlock = self.getBlock(block.parentHash(), block.height() - 1)
        if parentBlock is not None:
            if block.height() != parentBlock.height() + 1:
                Logger.debug(
                    f"{self._LOGGER_TITLE} Block height {block.height} is not equal to parent block height {parentBlock.height} + 1"
                )
                return False

        # ----- consensus -----
        # validation:
        #   - slot owner must be the proposer address
        slotOwner, err = self.sposContractClient.executeReadOnly(
            self.statedbManager.getState(), "get_slot_owner", str, header.slot_id
        )
        if err:
            Logger.debug(f"{self._LOGGER_TITLE} Get slot owner error: {err}")
            return False
        if slotOwner != bytesToHex(header.proposer_address):
            Logger.debug(
                f"{self._LOGGER_TITLE} Slot owner {slotOwner} is not equal to proposer address {header.proposer_address}"
            )
            return False

        # ----- transactions -----
        # validation:
        #   - transactions must not be empty
        #   - first transaction must be minting transaction, the rest must not be
        #   - moreover, check if the minting transaction is valid(validator_address is from_address)
        #   - each transaction signature must be valid (batch check)
        if len(block.transactions) == 0:
            Logger.debug(f"{self._LOGGER_TITLE} Block {block.hashHex()} has no transactions")
            return False
        firstTransaction = block.transactions[0]

        # isMintingTransaction
        if not firstTransaction.isMintingTransaction():
            Logger.debug(f"{self._LOGGER_TITLE} First transaction is not minting transaction")
            return False
        if hexToBytes(firstTransaction.sender) != header.proposer_address:
            Logger.debug(
                f"{self._LOGGER_TITLE} Minting transaction from address {firstTransaction.sender} is not equal to proposer address {block.proposer_address}"
            )
            return False

        for transaction in block.transactions[1:]:
            if transaction.isMintingTransaction():
                Logger.debug(
                    f"{self._LOGGER_TITLE} Transaction {bytesToHex(transaction.hash())} is minting transaction"
                )
                return False

        if not verifyTransactionSignatures(block.transactions):
            Logger.debug(f"{self._LOGGER_TITLE} Transaction signatures verification failed")
            return False

        # ----- merkle root -----
        # validation:
        #   - check if the transactions_root is correct
        if block.transactionsRoot() != bytesToHex(deriveSha(Transactions(block.transactions), MemoryTrie())):
            Logger.debug(f"{self._LOGGER_TITLE} Block {block.hashHex()} transactions root is incorrect")
            return False

        # TODO: validate bloom

        return True

    def processBlock(self, block: Block, isGenesis: bool) -> ProcessBlockResult:
        """
        Process a block, whether it's a new block or a sync block

        Logic:
        1. check if block exists
        2. validate block if it's not genesis block, or it doesn't exist
        3. check if block is orphan, if it is, cache it to memory
            3.1 if orphan block is higher than the current best block, return need reorg (someone sends a higher orphan block, so we need to reorg)
            3.2 if orphan block is lower or equal to the current best block, return state(validated, but orphan)
        4. save block to disk if it doesn't exist
        5. check if the block can be the best block. if it can, connect block; if not and it's higher than the current best block, return need reorg

        Note: if minting transaction fails, the block will not be connected to the blocktree
        """
        Logger.debug(f"{self._LOGGER_TITLE} Processing block {block.hashHex()}...")

        with self.blockStateLock:
            # get current best block header
            currentHeader = self.headerChain.currentHeader.get()
            if currentHeader is None and not isGenesis:
                return self.ProcessBlockResult.unvalidated(self.ProcessBlockResult.ProcessBlockError.RUNTIME_ERROR)

            # check if block already saved
            if self.hasBlock(block.hash(), block.height()):
                Logger.debug(f"{self._LOGGER_TITLE} Block {block.hashHex()} already exists, skip validation and saving")
            else:
                # validate ans save block

                # validate block
                if not isGenesis:
                    if not self.validateBlock(block):
                        # validated, but it's invalid
                        return self.ProcessBlockResult.validated(
                            self.ProcessBlockResult.ProcessBlockError.INVALID_BLOCK
                        )

                    # check if block is orphan
                    if self.headerChain.getHeaderByHash(block.parentHash()) is None:
                        self.orphanBlocks.addOrphanBlock(block)
                        Logger.debug(f"{self._LOGGER_TITLE} Block {block.hashHex()} is orphan")

                        # if the orphan block is higher than the current best block, return need reorg
                        if block.height() > currentHeader.height:
                            return self.ProcessBlockResult.validated(
                                self.ProcessBlockResult.ProcessBlockError.NEED_REORG
                            )
                        else:
                            return self.ProcessBlockResult.validated(
                                self.ProcessBlockResult.ProcessBlockError.ORPHAN_BLOCK
                            )

                rawdb.writeBlock(self.db, block)
                Logger.debug(f"{self._LOGGER_TITLE} Block {block.hashHex()} saved to db")

            # check if it can be the best block
            # this block can be the best block if:
            # there's no best block, or
            # the block is the child of the best block
            if currentHeader is None or currentHeader.hash() == block.parentHash():
                err = self.connectBlock(block, isGenesis)
                if err:
                    # TODO: need to rollback the transactions data
                    # block saved, but connect block failed
                    return self.ProcessBlockResult.blockSaved(self.ProcessBlockResult.ProcessBlockError.RUNTIME_ERROR)

                # block is connected
                # if block is connected, process orphan blocks
                self.processOrphanBlocks(block.hash())

                return self.ProcessBlockResult.blockConnected()
            else:
                # if the block is higher than current best block, we need to reorg
                # otherwise, we just save the block to disk
                if block.height() > currentHeader.height:
                    return self.ProcessBlockResult.blockSaved(self.ProcessBlockResult.ProcessBlockError.NEED_REORG)
                Logger.debug(
                    f"{self._LOGGER_TITLE} Block {block.hashHex()} is lower or equal to the current best block"
                )
                return self.ProcessBlockResult.blockSaved(
                    self.ProcessBlockResult.ProcessBlockError.LOWER_OR_EQUAL_HEIGHT
                )

    def connectBlock(self, block: Block, isGenesis: bool) -> Optional[str]:
        """
        Connect a block to the blocktree.

        logic:
        1. apply transactions in the block
            - apply minting transaction, if minting transaction fails, return error
            - apply other transactions
        2. update best block table
        3. notify observers
        """

        # connect block
        if not isGenesis and not self.validateBlock(block):
            Logger.error(f"{self._LOGGER_TITLE} Block {block.hashHex()} is invalid")
            return "Block is invalid"

        currentState = self.statedbManager.getState()

        # apply transactions
        blockEnv = block.toEnvironment()
        transactions = block.transactions

        # if the block is genesis block, apply all transactions directly. No need to execute minting transaction particularly
        if isGenesis:
            receipts = self.transactionProcessor.applyTransactions(currentState, blockEnv, transactions)
        else:
            # apply the minting transaction
            mintingTransaction: Transaction = transactions[0]
            if mintingTransaction is None:
                Logger.error(f"{self._LOGGER_TITLE} Minting transaction not found in block {block.hashHex()}")
                return "Minting transaction not found"
            if not mintingTransaction.isMintingTransaction():
                Logger.error(f"{self._LOGGER_TITLE} First transaction is not minting transaction")
                return "First transaction is not minting transaction"
            try:
                # super node don't have token at block_height 1
                blockHeight = int(blockEnv.get("block_height", 0))
                useFuel = blockHeight > 1
                mintingReceipt = self.transactionProcessor.applyTransaction(
                    currentState, mintingTransaction, blockEnv, transactionIndex=0, useFuel=useFuel, allowError=False
                )
            except Exception as e:
                Logger.error(f"{self._LOGGER_TITLE} Minting transaction failed")
                return f"Minting transaction failed, error: {e}"

            # apply other transactions
            receipts = self.transactionProcessor.applyTransactions(
                currentState, blockEnv, transactions[1:], [mintingReceipt]
            )

        # commit to the state
        rootHash = currentState.commit(block.height())
        stateRootHex = bytesToHex(rootHash)

        # TODO: commit error handle

        # delete block
        rawdb.deleteBlock(self.db, block.hash(), block.height())

        # remove block hash cache
        self.blockCache.pop(block.hash(), None)
        block.resetHashCache()

        receiptsRootHex = bytesToHex(deriveSha(Receipts(receipts), MemoryTrie()))

        # if it is received block, validate state root
        if block.header.state_root != "":
            if block.header.state_root != stateRootHex:
                Logger.error(f"{self._LOGGER_TITLE} State root is incorrect")
                # TODO: rollback the block
                return "State root is incorrect"
            if block.header.receipts_root != receiptsRootHex:
                Logger.error(f"{self._LOGGER_TITLE} Receipts root is incorrect")
                # TODO: rollback the block
                return "Receipts root is incorrect"

        # update state root, may need refactor
        block.header.state_root = stateRootHex
        # update receipts root
        block.header.receipts_root = receiptsRootHex

        # set bloom filter
        block.header.bloom = createBloom(receipts)

        rawdb.writeBlock(self.db, block)
        self.blockCache[block.hash()] = block

        # set head
        self.writeHeadBlock(block)

        # write receipts
        blockHash = block.hash()
        for mintingReceipt in receipts:
            mintingReceipt.block_hash = blockHash
        rawdb.writeReceipts(self.db, block.hash(), block.height(), receipts)

        # if it's genesis block, set genesis block hash
        if isGenesis:
            self.genesisBlock = block.header
            self.headerChain.setGenesis(block.header)

        self.headerChain.setCurrentHeader(block.header)
        # connect block successfully
        Logger.debug(f"{self._LOGGER_TITLE} Block {block.hashHex()} connected successfully.")
        self.notifyConnectBlockObservers(block)

        return None

    def processOrphanBlocks(self, parentBlockHash: bytes):
        """
        Process orphan blocks that have the same parent block hash, find one that can be connected to the blocktree
        """
        orphanBlocks = self.orphanBlocks.getOrphanBlocks(parentBlockHash)
        for block in orphanBlocks:
            result = self.processBlock(block, isGenesis=False)
            if result.isBlockConnected():
                # once a block is connected, remove all orphan blocks with the same parent block hash
                self.orphanBlocks.removeOrphanBlocks(parentBlockHash)
                break
            else:
                Logger.error(f"{self._LOGGER_TITLE} Orphan block {block.hashHex()} failed to connect")

    def handleNewBlockReceived(self, block: Block) -> Optional[List[str]]:
        """
        Handle new block received.

        Logic:
        1. process block
        2. if it needs reorg, generate locator and return it; otherwise, return None
        """
        Logger.debug(f"{self._LOGGER_TITLE} New block received: {block.hashHex()}")

        # process block
        result = self.processBlock(block=block, isGenesis=False)
        if result.isNeedReorg():
            Logger.debug(f"{self._LOGGER_TITLE} Need reorganize, request blocks")
            # get locator
            return self.generateLocator()

        return None

    def processSyncBlocks(self, blocks: List[Block]):
        """
        Process sync blocks.

        Logic:
        1. simple check
        2. process each block, ignore blocks that already exist
        3. if it needs reorg, get reorganization path and reorganize
        """
        Logger.debug(f"{self._LOGGER_TITLE} Sync blocks received: {blocks}")

        # check if blocks contain None
        if any([block is None for block in blocks]):
            Logger.debug(f"{self._LOGGER_TITLE} Sync blocks contain None")
            return

        needReorg = False

        for block in blocks:
            result = self.processBlock(block, isGenesis=False)
            if result.isExists():
                continue
            elif result.isNeedReorg():
                needReorg = True
            elif not result.isBlockSaved():  # we need all sync blocks to be saved
                Logger.warning(f"{self._LOGGER_TITLE} Sync block failed: {result.error}")
                return

        if needReorg:
            currentBlock = self.currentBlock()
            if currentBlock is not None:
                self.reorganize(currentBlock.header, blocks[-1])

    def reorganize(self, oldHead: AbstractHeader, newHead: Block):
        """
        Reorganize the chain

        reorg takes two blocks, an old chain and a new chain and will reconstruct the
        blocks and inserts them to be part of the new canonical chain and accumulates
        potential missing transactions
        """
        newChain: List[Block] = []
        oldChain: List[Block] = []
        commonBlock: Optional[Block] = None

        deleteTxs: List[bytes] = []
        addTxs: List[bytes] = []

        oldBlock = self.getBlock(oldHead.hash(), oldHead.height)
        if oldBlock is None:
            Logger.error(f"{self._LOGGER_TITLE} current head block missing")
            return
        newBlock = newHead

        # Reduce the longer chain to the same height as the shorter one
        if oldBlock.height() > newBlock.height():
            # Old chain is longer, gather all transactions and logs as deleted ones
            while oldBlock.height() > newBlock.height():
                oldChain.append(oldBlock)
                deleteTxs.extend([tx.hash() for tx in oldBlock.transactions])
                oldBlock = self.getBlock(oldBlock.parentHash(), oldBlock.height() - 1)
                if oldBlock is None:
                    Logger.error(f"{self._LOGGER_TITLE} invalid old chain")
                    return
        else:
            # New chain is longer, stash all blocks away for subsequent insertion
            while newBlock.height() > oldBlock.height():
                newChain.append(newBlock)
                newBlock = self.getBlock(newBlock.parentHash(), newBlock.height() - 1)
                if newBlock is None:
                    Logger.error(f"{self._LOGGER_TITLE} invalid new chain")
                    return

        # Both sides of the reorg are at the same number, reduce both until the common ancestor is found
        while True:
            # If the common ancestor was found, break
            if oldBlock.hash() == newBlock.hash():
                commonBlock = oldBlock
                break

            # remove the old block as well as stash away a new block
            oldChain.append(oldBlock)
            deleteTxs.extend([tx.hash() for tx in oldBlock.transactions])
            newChain.append(newBlock)

            # Step back with both chains
            oldBlock = self.getBlock(oldBlock.parentHash(), oldBlock.height() - 1)
            if oldBlock is None:
                Logger.error(f"{self._LOGGER_TITLE} invalid old chain")
                return
            newBlock = self.getBlock(newBlock.parentHash(), newBlock.height() - 1)
            if newBlock is None:
                Logger.error(f"{self._LOGGER_TITLE} invalid new chain")
                return

        # print reorg log
        if len(oldChain) > 0 and len(newChain) > 0:
            Logger.info(
                f"{self._LOGGER_TITLE} Chain reorg detected: height {commonBlock.height()}, hash {commonBlock.hashHex()}, drop {len(oldChain)}, dropfrom {oldChain[0].hashHex()}, add {len(newChain)}, addfrom {newChain[0].hashHex()}"
            )
        elif len(newChain) > 0:
            # Special case happens in the post merge stage that current head is
            # the ancestor of new head while these two blocks are not consecutive
            Logger.info(
                f"{self._LOGGER_TITLE} Extend chain: add {len(newChain)}, height {newChain[0].height()}, hash {newChain[0].hashHex()}"
            )
        else:
            # len(newChain) == 0 && len(oldChain) > 0
            # rewind the canonical chain to a lower point.
            Logger.error(
                f"{self._LOGGER_TITLE} Impossible reorg, please file an issue, oldheight {oldBlock.height()}, oldhash {oldBlock.hashHex()}, oldblocks {len(oldChain)}, newheight {newBlock.height()}, newhash {newBlock.hashHex()}, newblocks {len(newChain)}"
            )

        with self.blockStateLock:
            # Acquire the tx-lookup lock before mutation. This step is essential
            # as the txlookups should be changed atomically, and all subsequent
            # reads should be blocked until the mutation is complete.
            with self.transactionLookupLock.gen_wlock():
                # Insert the new chain segment in incremental order, from the old to the new.
                for block in reversed(newChain):
                    # Collect the new added transactions.
                    addTxs.extend([tx.hash() for tx in block.transactions])

                # Delete useless indexes right now which includes the non-canonical
                # transaction indexes, canonical chain indexes which above the head.
                indexesBatch = self.db.newBatch()
                diffs = hashDifference(deleteTxs, addTxs)
                for tx in diffs:
                    rawdb.deleteTransactionLookupEntry(indexesBatch, tx)

                # Delete all hash markers that are not part of the new canonical chain.
                # Because the reorg function does not handle new chain head, all hash
                # markers greater than or equal to new chain head should be deleted.
                height = commonBlock.height()

                i = height + 1
                while True:
                    hash = rawdb.readCanonicalHash(self.db, i)
                    if hash is None:
                        break
                    rawdb.deleteCanonicalHash(indexesBatch, i)
                    i += 1

                try:
                    indexesBatch.write()
                except Exception as e:
                    Logger.error(f"{self._LOGGER_TITLE} Failed to delete useless indexes: {e}")

                self.transactionLookupCache.clear()
            # TODO: handle log events

            # Rollback on-chain indexes
            index_undo_log.rollbackToBlock(self.db, commonBlock.height())

            # Recover state to the common ancestor block
            # header chain rollback to common block
            self.headerChain.setHead(commonBlock.height())
            self.statedbManager.clearStateCache()
            # Clean up the chain state manager cache
            for block in oldChain:
                self.blockCache.pop(block.hash(), None)
                self.bodyCache.pop(block.hash(), None)
                self.bodyRLPCache.pop(block.hash(), None)
                self.receiptsCache.pop(block.hash(), None)

            # Roll forward the new chain
            for block in reversed(newChain):
                err = self.connectBlock(block, isGenesis=False)
                if err is not None:
                    Logger.error(f"{self._LOGGER_TITLE} Connect block {block.hash()} failed: {err}")
                    return

            # Handle old transactions which are not included in the new chain
            # Broadcast orphan transactions
            orphanTxs = []
            for block in oldChain:
                for tx in block.transactions[1:]:
                    if tx.hash() in diffs:
                        orphanTxs.append(tx)

            for tx in orphanTxs:
                self.memoryPool.addTransaction(tx)
                # broadcast the transaction to all peers
                asyncio.create_task(self.client.broadcastTransaction(toJson(tx)))

    def getBlocks(self, locator: List[bytes]) -> Optional[List[Block]]:
        """
        return the blocks by locator
        """
        bestBlock = self.currentBlock()
        # if current best block is None, return empty list
        if bestBlock is None:
            return []

        # find common ancestor
        commonAncestorHash = b""
        commonAncestorHeight = -1
        for blockHash in locator:
            if commonAncestorHeight == -1:
                locatorBlock = self.headerChain.getHeaderByHash(blockHash)
            else:
                locatorBlock = self.getBlock(blockHash, commonAncestorHeight)
            if locatorBlock is not None:
                commonAncestorHeight = locatorBlock.height
                if locatorBlock.height < bestBlock.height():
                    commonAncestorHash = blockHash
                    break

        # if common ancestor is not found, set it to genesis block
        if len(commonAncestorHash) == 0:
            commonAncestorHash = self.genesis().hash()
            commonAncestorHeight = 0

        commonAncestor = self.getBlock(commonAncestorHash, commonAncestorHeight)
        if commonAncestor is None:
            # fail to get common ancestor, error
            return None

        # find and return path from common ancestor to this node's best block
        path = []
        currentBlockHash = bestBlock.hash()
        currentBlockHeight = bestBlock.height()
        while currentBlockHash != commonAncestorHash:
            currentBlock = self.getBlock(currentBlockHash, currentBlockHeight)
            if currentBlock is None:
                # fail to get block, error
                return None
            path.append(currentBlock)
            currentBlockHash = currentBlock.parentHash()
            currentBlockHeight -= 1

        path.reverse()
        return path

    # ----- block chain reader -----

    def currentHeader(self) -> Optional[SPOSHeader]:
        """
        return the current best block header
        """
        return self.headerChain.currentHeader.get()

    def currentBlock(self) -> Optional[Block]:
        """
        CurrentBlock retrieves the current head block of the canonical chain. The
        block is retrieved from the blockchain's internal cache.
        """
        return self._currentBlock.get()

    def hasHeader(self, hash: bytes, height: int) -> bool:
        """
        HasHeader returns true if the header with the given hash is in the database
        """
        return self.headerChain.hasHeader(hash, height)

    def getHeader(self, hash: bytes, height: int) -> Optional[SPOSHeader]:
        """
        GetHeader retrieves a block header from the database by hash, caching it if
        """
        return self.headerChain.getHeader(hash, height)

    def getHeaderByHash(self, hash: bytes) -> Optional[SPOSHeader]:
        """
        GetHeader retrieves a block header from the database by hash, caching it if
        """
        return self.headerChain.getHeaderByHash(hash)

    def getHeaderByHeight(self, height: int) -> Optional[SPOSHeader]:
        """
        GetHeaderByHeight retrieves the header belonging to the given height
        from the cache or database
        """
        return self.headerChain.getHeaderByHeight(height)

    def getBody(self, hash: bytes) -> Optional[Body]:
        """
        GetBody retrieves a block body (transactions) from the database by
        hash, caching it if found
        """
        if (body := self.bodyCache.get(hash)) is not None:
            return body
        height = self.headerChain.getBlockHeight(hash)
        if height is None:
            return None
        body = rawdb.readBody(self.db, hash, height)
        if body is None:
            return None
        self.bodyCache[hash] = body
        return body

    def getBodyRLP(self, hash: bytes) -> Optional[bytes]:
        """
        GetBodyRLP retrieves a block body (transactions) from the database by
        hash, caching it if found
        """
        if (bodyRLP := self.bodyRLPCache.get(hash)) is not None:
            return bodyRLP
        height = self.headerChain.getBlockHeight(hash)
        if height is None:
            return None
        bodyRLP = rawdb.readBodyRLP(self.db, hash, height)
        if bodyRLP is None:
            return None
        self.bodyRLPCache[hash] = bodyRLP
        return bodyRLP

    def hasBlock(self, hash: bytes, height: int) -> bool:
        """
        HasBlock checks if a block is fully present in the database or not.
        """
        if hash in self.blockCache:
            return True
        if not self.hasHeader(hash, height):
            return False
        return rawdb.hasBody(self.db, hash, height)

    def getBlock(self, hash: bytes, height: int) -> Optional[Block]:
        """
        GetBlock retrieves a block by hash, caching it if found
        """
        if (block := self.blockCache.get(hash)) is not None:
            return block
        block = rawdb.readBlock(self.db, SPOSHeader, hash, height)
        if block is None:
            return None
        self.blockCache[block.hash()] = block
        return block

    def getBlockByHash(self, hash: bytes) -> Optional[Block]:
        """
        GetBlockByHash retrieves a block by hash, caching it if found
        """
        height = self.headerChain.getBlockHeight(hash)
        if height is None:
            return None
        return self.getBlock(hash, height)

    def getBlockByHeight(self, height: int) -> Optional[Block]:
        """
        GetBlockByHeight retrieves a block by height, caching it if found
        """
        hash = rawdb.readCanonicalHash(self.db, height)
        if hash is None:
            return None
        return self.getBlock(hash, height)

    def getReceiptsByHash(self, hash: bytes) -> Optional[List[Receipt]]:
        """
        GetReceiptsByHash retrieves a block's receipts by hash, caching it if found
        """
        if (receipts := self.receiptsCache.get(hash)) is not None:
            return receipts
        height = rawdb.readHeaderHeight(self.db, hash)
        if height is None:
            return None
        receipts = rawdb.readReceipts(self.db, hash, height)
        if receipts is None:
            return None
        self.receiptsCache[hash] = receipts
        return receipts

    def getCanonicalHash(self, height: int) -> Optional[bytes]:
        """
        GetCanonicalHash retrieves the hash of the canonical block at a given height
        """
        return self.headerChain.getCanonicalHash(height)

    def getTransactionLookup(self, hash: bytes) -> Optional[TransactionLookup]:
        """
        GetTransactionLookup retrieves the lookup along with the transaction
        itself associate with the given transaction hash.
        """
        with self.transactionLookupLock.gen_rlock():
            if (item := self.transactionLookupCache.get(hash)) is not None:
                return item
            transaction, blockHash, blockHeight, txIndex = rawdb.readTransaction(self.db, hash)
            if transaction is None:
                return None
            lookup = TransactionLookup(blockHash, blockHeight, txIndex, transaction)
            self.transactionLookupCache[hash] = lookup
            return lookup

    def hasState(self, hashHex: str) -> bool:
        """
        HasState returns true if the state with the given hash is in the database
        """
        return self.statedbManager.hasState(hexToBytes(hashHex))

    def hasBlockAndState(self, hash: bytes, height: int) -> bool:
        """
        HasBlockAndState returns true if the block and state with the given hash and height are in the database
        """
        block: Optional[Block] = self.getBlock(hash, height)
        if block is None:
            return False
        return self.hasState(block.header.state_root)

    def genesis(self) -> Optional[SPOSHeader]:
        """
        Genesis retrieves the genesis block of the chain
        """
        return self.genesisBlock

    def writeHeadBlock(self, block: Block) -> None:
        """
        writeHeadBlock injects a new head block into the current blockchain. This method assumes that the block
        is indeed a true head. It will also reset the head header and the head snap sync block to this very same
        block if they are older or if they are on a different side chain.

        Note, this function assumes that the `mu` mutex is held!
        """
        batch = self.db.newBatch()
        rawdb.writeHeadHeaderHash(batch, block.hash())
        rawdb.writeCanonicalHash(batch, block.hash(), block.height())
        rawdb.writeTxLookupEntriesByBlock(batch, block)
        rawdb.writeHeadBlockHash(batch, block.hash())

        rawdb.writeHeader(batch, block.header)

        # Flush the whole batch into the disk, exit the node if failed
        try:
            batch.write()
        except Exception as e:
            Logger.error(f"Failed to update chain indexes and markers: {e}")

        # Update all in-memory chain markers in the last step
        self.headerChain.setCurrentHeader(block.header)
        self._currentBlock.set(block)

    # ----- utils -----

    def generateLocator(self) -> List[str]:
        currentBlock = self.currentBlock()
        if currentBlock is None:
            return []

        currentBlockHash = currentBlock.hash()

        # start from the best block
        currentBlockHash = currentBlockHash
        currentBlockHeight = currentBlock.height()
        locator = []
        step = 1
        counter = 1
        MAX_LOCATOR_LENGTH = 32

        while currentBlockHash != hexToBytes("00000000000000000000000000000000") and counter < MAX_LOCATOR_LENGTH:
            locator.append(bytesToHex(currentBlockHash))

            for _ in range(step):
                currentBlock = self.getBlock(currentBlockHash, currentBlockHeight)
                if currentBlock is None:
                    break
                currentBlockHash = currentBlock.parentHash()
                currentBlockHeight -= 1
                if currentBlockHeight < 0:
                    return locator

            if counter >= 10:
                # double steps, exponential growth
                step *= 2
            counter += 1

        return locator

    # ----- misc -----

    def registerConnectBlockObserver(self, observer):
        """
        Register observer on connect block event
        """
        self.connectBlockObservers.append(observer)

    def notifyConnectBlockObservers(self, block: Block):
        """
        Notify observers on connect block event
        """
        for observer in self.connectBlockObservers:
            observer.onConnectBlock(block)

    def getStatusString(self) -> str:
        """
        return the status of the chain state manager, including:
        - the current state of the chain(best block, parent block, height, etc.)
        """
        currentHeader: SPOSHeader = self.headerChain.currentHeader.get()
        if currentHeader is None:
            return "No blocks in the chain"
        transactions = self.getBody(currentHeader.hash()).transactions
        return f"""{self._LOGGER_TITLE}
    Current best block: {currentHeader.hashHex()}
    Parent block: {bytesToHex(currentHeader.parent_hash)}
    Height: {currentHeader.height}
    Transaction count: {len(transactions)}
    Signature count: {len(currentHeader.signatures)}
    State Root: {currentHeader.state_root}
    Transactions Root: {currentHeader.transactions_root}
    Receipts Root: {currentHeader.receipts_root}
    Protocol timestamp: {currentHeader.protocol_timestamp}
    Proposer address: {bytesToHex(currentHeader.proposer_address)}"""
