from __future__ import annotations

import asyncio
from typing import Dict, List, Optional

from kivy.logger import Logger

from common import fromJson, publicKeyToAddress, to<PERSON>son
from common.models import Block
from jsonrpc.client_base import VGraphClient
from jsonrpc.peer import Peer
from jsonrpc.peers import PeerManager


class VGraphSPOSClient(VGraphClient):
    """
    VGraphSPOSClient is the client for the SPOS chain.

    Target server should be SPOSChainSession type
    """

    def __init__(self):
        super().__init__()
        self.peerManager = PeerManager()

    # ----- send requests -----
    async def sendBlocktreeNewBlock(self, peer: Peer, blockJsonData: str) -> None:
        """
        Send blocktree.new_block to target peer
        """
        await self.sendRequestToOnePeer(
            peer=peer,
            method="blocktree.new_block",
            params={
                "blockJsonData": blockJsonData,
            },
        )

    async def sendBlocktreeGetBlock(self, peer: Peer, blockHash: str) -> Optional[Block]:
        """
        Send blocktree.get_block_by_hash to target peer
        """
        data = await self.sendRequestToOnePeer(
            peer=peer,
            method="blocktree.get_block_by_hash",
            params={
                "blockHash": blockHash,
            },
        )
        try:
            return fromJson(Block, data)
        except Exception:
            return None

    async def sendBlocktreeGetBlocks(self, peer: Peer, locator: List[str]) -> Optional[List[Block]]:
        """
        Send blocktree.get_blocks to target peer
        """
        data = await self.sendRequestToOnePeer(
            peer=peer,
            method="blocktree.get_blocks",
            params={
                "locator": locator,
            },
        )
        try:
            return fromJson(List[Block], data)
        except Exception:
            return None

    async def sendBlocktreeBestBlock(self, peer: Peer) -> Optional[Block]:
        """
        Send blocktree.best_block to target peer
        """
        data = await self.sendRequestToOnePeer(peer=peer, method="blocktree.best_block", params={})
        try:
            return fromJson(Block, data)
        except Exception:
            return None

    async def sendMempoolNewTransaction(self, peer: Peer, transactionJsonData: str) -> None:
        """
        Send mempool.new_transaction message to target peer
        """
        await self.sendRequestToOnePeer(
            peer=peer, method="mempool.new_transaction", params={"transactionJsonData": transactionJsonData}
        )

    async def broadcastBlock(self, blockJsonData: str, targetPeers: Optional[List[Peer]] = None) -> None:
        """
        Broadcast block to target peers

        If target peers is None, broadcast to all peers
        """
        await self.sendRequestToPeers(
            method="blocktree.new_block",
            params={"blockJsonData": blockJsonData},
            targetPeers=targetPeers,
        )

    async def broadcastTransaction(self, transactionJsonData: str, targetPeers: Optional[List[Peer]] = None) -> None:
        """
        Broadcast transaction to target peers

        If target peers is None, broadcast to all peers
        """
        await self.sendRequestToPeers(
            targetPeers=targetPeers,
            method="mempool.new_transaction",
            params={"transactionJsonData": transactionJsonData},
        )

    async def requestSignatures(self, block: Block, supernodeList: List[str]):
        """
        request signatures from supernodes concurrently

        `supernodeList` is a list of supernode addresses
        """
        # find target peers
        targetPeers = [
            peer
            for peer in self.peerManager.getOtherRecentGoodPeers()
            if publicKeyToAddress(peer.nodeId) in supernodeList
        ]

        # set timeout
        timeout = 5
        requestResults: List[Dict] = await self.sendRequestToPeers(
            method="blocktree.sign_block",
            params={
                "blockJsonData": toJson(block),
            },
            targetPeers=targetPeers,
            timeout=timeout,
        )

        # filter out invalid results
        results = []
        for result in requestResults:
            if result is None or type(result) is not dict or "signature" not in result or "publickey" not in result:
                continue
            results.append(result)

        return results

    # ----- event listener -----
    def onConnectBlock(self, block: Block):
        """
        handle connect block event from ChainStateManager
        """
        # broadcast the block to all peers
        Logger.debug(f"{self._LOGGER_TITLE} Broadcast new block to all peers")
        asyncio.create_task(self.broadcastBlock(toJson(block)))
