from __future__ import annotations

import asyncio
import time
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

from kivy.logger import Logger
from serde import from_dict, serde
from serde.json import to_dict

import vm
from chains.log_retriever import LogRetriever
from chains.signature_manager import SignatureManager
from chains.statedb_manager import StatedbManager
from chains.vgraph_spos_chain.chain_state_manager import VGraphSPOSChainStateManager
from chains.vgraph_spos_chain.client import VGraphSPOSClient
from chains.vgraph_spos_chain.minter import VGraphSPOSChainMinter
from common import (
    bytesToHex,
    fromJson,
    hexToBytes,
    privateKeyToPublicKey,
    publicKeyToAddress,
    signTransactionWithPrivateKey,
    toJson,
    verifyTransactionSignatures,
)
from common.errors import DeserializeError, LogRetrieverError
from common.models import (
    Block,
    OperationCallContract,
    OperationCreateContract,
    OperationForkContract,
    OperationType,
    OperationUpgradeContract,
    Transaction,
)
from config import CONFIG
from jsonrpc.server import SessionManager
from jsonrpc.session_base import ErrorMessage, VGraphSession

# Special height value to request the current head block
HEAD_BLOCK_HEIGHT = -1


class SPOSChainSession(VGraphSession):
    """
    SPOSChainSession is the JSON-RPC session for the SPOS chain.

    Define the handler for blocktree and transactions.
    """

    def __init__(self, sessionManager: SessionManager, transport: asyncio.Transport):
        super().__init__(sessionManager, transport)
        self.chain = self.sessionManager.chain
        self.statedbManager: StatedbManager = self.chain.statedbManager
        self.chainStateManager: VGraphSPOSChainStateManager = self.chain.chainStateManager
        self.client: VGraphSPOSClient = self.chain.client
        self.signatureManager: SignatureManager = self.chain.signatureManager
        self.minter: VGraphSPOSChainMinter = self.chain.minter
        self.logRetriever: LogRetriever = self.chain.logRetriever

        self.request_handlers.update(
            {
                "blocktree.new_block": self.handleNewBlockMessage,
                "blocktree.get_block_by_hash": self.getBlockByHash,
                "blocktree.get_block_by_height": self.getBlockByHeight,
                "blocktree.get_blocks": self.getBlocks,
                "blocktree.best_block": self.getCurrentBestBlock,
                "blocktree.sign_block": self.signBlock,
                "contract.execute": self.executeContract,
                "contract.query": self.queryContract,
                "contract.create": self.createContract,
                "contract.get_code_hash": self.getCodeHash,
                "contract.fork": self.forkContract,
                "contract.upgrade": self.upgradeContract,
                "transaction.get_transaction_by_hash": self.getTransactionByHash,
                "contract.get_logs": self.queryLogs,
                "transaction.get_receipt": self.getTransactionReceipt,
                "mempool.new_transaction": self.handleNewTransactionMessage,
            }
        )

    async def handleNewBlockMessage(self, blockJsonData: str):
        """
        handle blocktree.new_block message from other peers
        """
        # validate block data
        try:
            block = fromJson(Block, blockJsonData)
        except Exception:
            Logger.debug(f"{self._LOGGER_TITLE} receive new block, block data json error")
            Logger.debug(f"{self._LOGGER_TITLE} Block data: {blockJsonData}")
            return {"error": "invalid block"}

        # process block
        result = self.chainStateManager.processBlock(block=block, isGenesis=False)

        if not result.isNeedReorg():
            return

        # if chain need reorg: get locator and request blocks
        Logger.debug(f"{self._LOGGER_TITLE} Need reorganize, request blocks")
        locator = self.chainStateManager.generateLocator()
        if locator is None:
            # generate locator error
            Logger.debug(f"{self._LOGGER_TITLE} generate locator error")
            return

        # request blocks using locator
        # first, find a peer that has the block
        targetPeers = [peer for peer in self.client.peerManager.peers if peer not in self.client.peerManager.myselves]
        for peer in targetPeers:
            result = await self.client.sendBlocktreeGetBlock(peer, block.hashHex())
            if result is None:
                continue

            # if found, request blocks
            blocks = await self.client.sendBlocktreeGetBlocks(peer, locator)
            if blocks is not None:
                self.chainStateManager.processSyncBlocks(blocks)

    async def getBlockByHash(self, blockHash: str, includeFullTransactions: bool = True):
        """
        Return the block with the given block hash.

        Args:
            blockHash (str): The hash of the block to retrieve
            includeFullTransactions (bool, optional): If True, returns full transaction data.
                - If False, returns only transaction hashes.
                - Defaults to True.

        Returns:
            dict: JSON response containing either:
                - The serialized block with full transaction data
                - The serialized block with transaction hashes
                - An error message if block not found or parameters invalid
        """
        # Validate input parameter type
        if not isinstance(blockHash, str):
            return ErrorMessage.invalidRequestParams()

        # Get block using chain state manager
        block = self.chainStateManager.getBlockByHash(hexToBytes(blockHash))
        if not block:
            return {"error": f"Block {blockHash} not found"}

        # Create a copy of the block to modify transaction data
        block_dict = to_dict(block)

        # Modify transaction data based on includeFullTransactions parameter
        if not includeFullTransactions:
            # Replace full transaction data with transaction hashes
            block_dict["transactions"] = [bytesToHex(tx.hash()) for tx in block.transactions]
        else:
            # Attach transaction hash to the transaction dict
            block_dict["transactions"] = [
                {**to_dict(tx), "transaction_hash": bytesToHex(tx.hash())} for tx in block.transactions
            ]

        # Attach block hash to the block
        block_dict["block_hash"] = bytesToHex(block.hash())

        return toJson(block_dict)

    async def getBlockByHeight(self, height: int, includeFullTransactions: bool = True):
        """
        Handle blocktree.get_block_by_height JSON-RPC request.
        Returns the block at the specified height in the canonical chain.

        Args:
            height (int): The height of the block to retrieve.
                - Special case: if height is HEAD_BLOCK_HEIGHT (-1), returns the current head block.
            includeFullTransactions (bool, optional): If True, returns full transaction data.
                - If False, returns only transaction hashes.
                - Defaults to True.

        Returns:
            dict: JSON response containing either:
                - The serialized block with full transaction data
                - The serialized block with transaction hashes
                - An error message if block not found or parameters invalid
        """
        # Validate input parameter type
        if not isinstance(height, int):
            return ErrorMessage.invalidRequestParams()

        # Special case: HEAD_BLOCK_HEIGHT returns current head block
        if height == HEAD_BLOCK_HEIGHT:
            block = self.chainStateManager.currentBlock()
            if not block:
                return {"error": "No head block found"}
        else:
            # Validate height is non-negative for normal cases
            if height < 0:
                return ErrorMessage.invalidRequestParams()

            # Get block using chain state manager
            block = self.chainStateManager.getBlockByHeight(height)
            if not block:
                return {"error": f"Block at height {height} not found"}

        # Create a copy of the block to modify transaction data
        block_dict = to_dict(block)

        # Modify transaction data based on includeFullTransactions parameter
        if not includeFullTransactions:
            # Replace full transaction data with transaction hashes
            block_dict["transactions"] = [bytesToHex(tx.hash()) for tx in block.transactions]
        else:
            # Attach transaction hash to the transaction dict
            block_dict["transactions"] = [
                {"transaction_hash": bytesToHex(tx.hash()), **to_dict(tx)} for tx in block.transactions
            ]

        # Attach block hash to the block
        block_dict["block_hash"] = bytesToHex(block.hash())

        return toJson(block_dict)

    async def getBlocks(self, locator: List[str]):
        """
        return the blocks with the given locator
        """
        if not isinstance(locator, list):
            return ErrorMessage.invalidRequestParams()

        blocks = self.chainStateManager.getBlocks([hexToBytes(item) for item in locator])
        return toJson(blocks) if blocks else {"error": "Get blocks error"}

    async def getCurrentBestBlock(self):
        """
        return the best block in the blocktree
        """
        block = self.chainStateManager.currentBlock()
        return toJson(block) if block else {"error": "Get best block error"}

    async def signBlock(self, blockJsonData: str):
        """
        Sign the block if the block is valid

        Return public key and signature
        """
        if not self.minter.slotId > 0:  # not supernode
            return {"error": "This node is not supernode, stop signing block"}

        try:
            block = fromJson(Block, blockJsonData)
        except Exception:
            return ErrorMessage.invalidRequestParams()

        if not self.chainStateManager.validateBlock(block):
            return {"error": "Invalid block"}
        signedBlock = self.signatureManager.signBlockHeader(block.header)
        if signedBlock is None:
            return {"error": "Failed to sign block"}
        return {"publickey": signedBlock.public_keys[-1], "signature": signedBlock.signatures[-1]}

    def checkSenderBalance(self, transaction: Transaction) -> Optional[str]:
        """
        Check if the sender has enough balance to execute the contract
        """
        # check sender balance
        tokenContractAddress = CONFIG.tokenAddress
        contractExecutor = vm.ContractExecutor(address=tokenContractAddress)
        balance, err = contractExecutor.executeReadOnly(
            self.statedbManager.getState(), "balance", int, transaction.sender
        )
        if err is not None:
            return "Failed to get sender balance"
        if balance < transaction.fuel:
            return "Insufficient balance to execute contract function"
        return None

    async def executeContract(self, callContractParams: Dict):
        """
        execute a contract function
        """
        try:
            callContractParams: CallContractParams = from_dict(CallContractParams, callContractParams)

            # validate: private key or public key/signature must be provided
            if not callContractParams.privatekey and (
                not callContractParams.public_keys or not callContractParams.signatures
            ):
                return ErrorMessage.invalidRequestParams()

            # validate: dependent transaction hash must be valid
            if (
                callContractParams.dependent_transaction_hash is not None
                and callContractParams.dependent_transaction_hash != ""
            ):
                try:
                    _ = hexToBytes(callContractParams.dependent_transaction_hash)
                except ValueError:
                    return {"error": "Invalid dependent transaction hash format"}

            senderAddress = (
                publicKeyToAddress(privateKeyToPublicKey(callContractParams.privatekey))
                if callContractParams.privatekey
                else publicKeyToAddress(callContractParams.public_keys[0])
            )

        except Exception:
            return ErrorMessage.invalidRequestParams()

        transaction = callContractParams.toTransaction(sender=senderAddress)

        # check fuel value is int and positive
        if not isinstance(transaction.fuel, int) or transaction.fuel <= 0:
            return {"error": "Invalid fuel value"}

        # sign transaction if private key is given
        # TODO: for developing and testing, should be removed in production
        if callContractParams.privatekey:
            try:
                transaction = signTransactionWithPrivateKey(transaction, callContractParams.privatekey)
            except Exception:
                return {"error": "Failed to sign transaction"}

        # verify signature
        if not verifyTransactionSignatures([transaction]):
            return {"error": "Invalid transaction signature"}

        # check sender balance before add transaction to mempool
        balanceErr = self.checkSenderBalance(transaction)
        if balanceErr is not None:
            return {"error": balanceErr}

        # add transaction to mempool
        # TODO: refactor with observer pattern, after add transaction, broadcast to peers
        self.chain.memoryPool.addTransaction(transaction)
        await self.client.broadcastTransaction(toJson(transaction))

        return {"transaction_hash": bytesToHex(transaction.hash())}

    def getMemoryPoolTransaction(self, transactionHashHex: str) -> Optional[Transaction]:
        """
        Get the transaction on chain or memory pool. If not exists, returns None.
        """
        try:
            transactionHash = hexToBytes(transactionHashHex)
        except Exception:
            return None
        return self.chain.memoryPool.getTransaction(transactionHash)

    async def queryContract(self, queryContractParams: Dict):
        """
        query a contract function
        """
        try:
            queryContractParams: QueryContractParams = from_dict(QueryContractParams, queryContractParams)

            # validate: snapshot transaction hash must be valid
            if (
                queryContractParams.snapshot_transaction_hash is not None
                and queryContractParams.snapshot_transaction_hash != ""
            ):
                try:
                    _ = hexToBytes(queryContractParams.snapshot_transaction_hash)
                except ValueError:
                    return {"error": "Invalid snapshot transaction hash format"}

        except Exception:
            return ErrorMessage.invalidRequestParams()

        # check query api key
        # if CONFIG.queryApiKey is None, query api key authentication is disabled
        if CONFIG.queryApiKey is not None and queryContractParams.query_api_key != CONFIG.queryApiKey:
            return {
                "error": "Invalid query API key. This node enables query api key authentication, please provide the correct query API key."
            }

        # handle signatures and public keys
        contractExecutor = vm.ContractExecutor(
            address=queryContractParams.contract_address,
        )

        # use a copy of the state to prevent changes to the state cache
        state = self.statedbManager.getState().copy()

        # whether dependent_transaction_hash is exist
        if (
            queryContractParams.snapshot_transaction_hash is not None
            and queryContractParams.snapshot_transaction_hash != ""
        ):
            # construct the dependent transaction chain
            transactionChain = {}
            currentTransactionHash = queryContractParams.snapshot_transaction_hash

            # check circle transaction dependent attack
            while (
                currentTransactionHash not in transactionChain
                and (currentTransaction := self.getMemoryPoolTransaction(currentTransactionHash)) is not None
            ):
                transactionChain[currentTransactionHash] = currentTransaction
                currentTransactionHash = currentTransaction.dependent_transaction_hash

            # apply reversed transaction chain
            if len(transactionChain) > 0:
                transactionChainList = [tx for tx in reversed(transactionChain.values())]
                self.chainStateManager.transactionProcessor.applyTransactions(state, {}, transactionChainList)

        result, err = contractExecutor.executeReadOnlyWithEnv(
            state,
            queryContractParams.toQueryEnv(),
            queryContractParams.function_name,
            Any,
            *queryContractParams.args,
        )
        if err is not None:
            return {"error": f"Failed to query contract function {queryContractParams.function_name}: {err}"}
        return {"result": result}

    async def createContract(self, createContractParams: Dict):
        """
        create a contract
        """
        try:
            createContractParams: CreateContractParams = from_dict(CreateContractParams, createContractParams)

            # validate: private key or public key/signature must be provided
            if not createContractParams.privatekey and (
                not createContractParams.public_keys or not createContractParams.signatures
            ):
                return ErrorMessage.invalidRequestParams()

            senderAddress = (
                publicKeyToAddress(privateKeyToPublicKey(createContractParams.privatekey))
                if createContractParams.privatekey
                else publicKeyToAddress(createContractParams.public_keys[0])
            )
        except Exception:
            return ErrorMessage.invalidRequestParams()

        transaction = createContractParams.toTransaction(sender=senderAddress)

        # check fuel value is int and positive
        if not isinstance(transaction.fuel, int) or transaction.fuel <= 0:
            return {"error": "Invalid fuel value"}

        # sign transaction if private key is given
        # TODO: for developing and testing, should be removed in production
        if createContractParams.privatekey:
            try:
                transaction = signTransactionWithPrivateKey(transaction, createContractParams.privatekey)
            except Exception:
                return {"error": "Failed to sign transaction"}

        # verify signature
        if not verifyTransactionSignatures([transaction]):
            return {"error": "Invalid transaction signature"}

        # check sender balance before add transaction to mempool
        balanceErr = self.checkSenderBalance(transaction)
        if balanceErr is not None:
            return {"error": balanceErr}

        # add transaction to mempool
        # TODO: refactor with observer pattern, after add transaction, broadcast to peers
        self.chain.memoryPool.addTransaction(transaction)
        await self.client.broadcastTransaction(toJson(transaction))

        return {"transaction_hash": bytesToHex(transaction.hash())}

    async def getCodeHash(self, contractAddress: str):
        """
        Retrieve the code hash of the contract by its address.
        """
        try:
            if not isinstance(contractAddress, str):
                return {"error": "Invalid contract address format"}

            state = self.statedbManager.getState()
            addressBytes = hexToBytes(contractAddress)
            codeHash = state.getCodeHash(addressBytes)

            # Check if code hash exists
            if codeHash is None or codeHash == b"":
                return {"error": "Contract not found"}

            # Return code hash in hex format
            return {"code_hash": bytesToHex(codeHash)}
        except Exception as e:
            # Log unexpected errors
            Logger.error(f"Failed to retrieve code hash for address {contractAddress}: {e}")
            return {"error": "Internal server error"}

    async def forkContract(self, forkContractParams: Dict):
        """
        Fork a contract
        """
        try:
            forkContractParams: ForkContractParams = from_dict(ForkContractParams, forkContractParams)

            # validate: private key or public key/signature must be provided
            if not forkContractParams.privatekey and (
                not forkContractParams.public_keys or not forkContractParams.signatures
            ):
                return ErrorMessage.invalidRequestParams()

            senderAddress = (
                publicKeyToAddress(privateKeyToPublicKey(forkContractParams.privatekey))
                if forkContractParams.privatekey
                else publicKeyToAddress(forkContractParams.public_keys[0])
            )
        except Exception:
            return ErrorMessage.invalidRequestParams()

        transaction = forkContractParams.toTransaction(sender=senderAddress)

        # check fuel value is int and positive
        if not isinstance(transaction.fuel, int) or transaction.fuel <= 0:
            return {"error": "Invalid fuel value"}

        # sign transaction if private key is given
        # TODO: for developing and testing, should be removed in production
        if forkContractParams.privatekey:
            try:
                transaction = signTransactionWithPrivateKey(transaction, forkContractParams.privatekey)
            except Exception:
                return {"error": "Failed to sign transaction"}

        # Verify the transaction signature
        if not verifyTransactionSignatures([transaction]):
            return {"error": "Invalid transaction signature"}

        # check sender balance before add transaction to mempool
        balanceErr = self.checkSenderBalance(transaction)
        if balanceErr is not None:
            return {"error": balanceErr}

        # Add transaction to the mempool
        # TODO: refactor with observer pattern, after add transaction, broadcast to peers
        self.chain.memoryPool.addTransaction(transaction)

        # Broadcast the transaction to other peers
        await self.client.broadcastTransaction(toJson(transaction))

        return {"transaction_hash": bytesToHex(transaction.hash())}

    async def upgradeContract(self, upgradeContractParams: Dict):
        """
        Handle the upgrade contract request.
        """
        try:
            upgradeContractParams: UpgradeContractParams = from_dict(UpgradeContractParams, upgradeContractParams)

            # validate: private key or public key/signature must be provided
            if not upgradeContractParams.privatekey and (
                not upgradeContractParams.public_keys or not upgradeContractParams.signatures
            ):
                return ErrorMessage.invalidRequestParams()

            senderAddress = (
                publicKeyToAddress(privateKeyToPublicKey(upgradeContractParams.privatekey))
                if upgradeContractParams.privatekey
                else publicKeyToAddress(upgradeContractParams.public_keys[0])
            )
        except Exception:
            return ErrorMessage.invalidRequestParams()

        transaction = upgradeContractParams.toTransaction(sender=senderAddress)

        # Sign the transaction if a private key is provided
        if upgradeContractParams.privatekey:
            try:
                transaction = signTransactionWithPrivateKey(transaction, upgradeContractParams.privatekey)
            except Exception:
                return {"error": "Failed to sign transaction"}

        # Verify the transaction signature
        if not verifyTransactionSignatures([transaction]):
            return {"error": "Invalid transaction signature"}

        # Add the transaction to the mempool
        self.chain.memoryPool.addTransaction(transaction)

        # Broadcast the transaction to other peers
        await self.client.broadcastTransaction(toJson(transaction))

        return {"transaction_hash": bytesToHex(transaction.hash())}

    async def getTransactionByHash(self, transactionHashHex: str):
        """
        get the transaction by hash, only return confirmed transaction
        """
        try:
            transactionHash: bytes = hexToBytes(transactionHashHex)
        except Exception:
            return ErrorMessage.invalidRequestParams()

        transactionLookup = self.chainStateManager.getTransactionLookup(transactionHash)
        if transactionLookup is None:
            return {
                "message": "Transaction not found",
            }
        return toJson(transactionLookup.transaction)

    async def queryLogs(self, queryLogParams: Dict):
        """
        Query events
        """
        try:
            queryLogParams: QueryLogParams = from_dict(QueryLogParams, queryLogParams)
            blockHash = hexToBytes(queryLogParams.block_hash) if queryLogParams.block_hash else None
            addresses = (
                [hexToBytes(address) for address in queryLogParams.contract_addresses]
                if queryLogParams.contract_addresses
                else None
            )
            topics = (
                [list(map(hexToBytes, topicList)) for topicList in queryLogParams.topics]
                if queryLogParams.topics
                else None
            )
            logs = self.logRetriever.getLogs(
                blockHash=blockHash,
                begin=queryLogParams.from_block,
                end=queryLogParams.to_block,
                addresses=addresses,
                topics=topics,
            )
            return {"logs": [to_dict(log) for log in logs]}
        except LogRetrieverError as e:
            return ErrorMessage.internalError(e)
        except (ValueError, DeserializeError):
            return ErrorMessage.invalidRequestParams()
        except Exception as e:
            return ErrorMessage.internalError(e)

    async def getTransactionReceipt(self, transactionHashHex: str):
        """
        return the transaction receipt for the given transaction hash
        """

        transactionHash: bytes = hexToBytes(transactionHashHex)
        if self.chain.memoryPool.hasTransaction(transactionHash):
            return {
                "message": "Transaction pending in mempool",
            }

        transactionLookup = self.chainStateManager.getTransactionLookup(transactionHash)
        if transactionLookup is not None:
            receipts = self.chainStateManager.getReceiptsByHash(transactionLookup.blockHash)
            for receipt in receipts:
                if receipt.transaction_hash == transactionHash:
                    return {
                        "message": "Transaction found",
                        "receipt": to_dict(receipt),
                    }
        return {
            "message": "Transaction not found",
        }

    async def handleNewTransactionMessage(self, transactionJsonData: str):
        """
        handle mempool.new_transaction message from other peers
        """
        # validate transaction data
        try:
            transaction = fromJson(Transaction, transactionJsonData)
        except Exception:
            Logger.error(f"{self._LOGGER_TITLE} receive new transaction, transaction data json error")
            Logger.debug(f"Transaction data: {transactionJsonData}")
            return {"error": "Invalid transaction"}

        # check fuel value is int and positive
        if not isinstance(transaction.fuel, int) or transaction.fuel <= 0:
            return {"error": "Invalid fuel value"}

        # check if transaction already in mempool
        if self.chain.memoryPool.hasTransaction(transaction.hash()):
            return {"error": "Transaction already in mempool"}

        # check if transaction already in chain block to avoid replay attack
        if self.chainStateManager.getTransactionLookup(transaction.hash()):
            return {"error": "Transaction already in blockchain"}

        # verify signature
        if not verifyTransactionSignatures([transaction]):
            return {"error": "Invalid transaction signature"}

        # check sender balance before add transaction to mempool
        balanceErr = self.checkSenderBalance(transaction)
        if balanceErr is not None:
            return {"error": balanceErr}

        # add to mempool
        self.chain.memoryPool.addTransaction(transaction)
        # broadcast the transaction to all peers
        await self.client.broadcastTransaction(transactionJsonData)


@serde
@dataclass
class CallContractParams:
    dependent_transaction_hash: Optional[str]
    contract_address: str
    function_name: str
    fuel: int
    args: List[Any]
    # signature
    public_keys: Optional[List[str]] = field(default_factory=list)
    signatures: Optional[List[str]] = field(default_factory=list)
    privatekey: Optional[str] = None  # private key of the sender

    def toTransaction(self, sender: str) -> Transaction:
        return Transaction(
            dependent_transaction_hash=""
            if self.dependent_transaction_hash is None
            else self.dependent_transaction_hash,
            sender=sender,
            op_data=OperationCallContract(
                op_type=OperationType.CALL_CONTRACT,
                contract_address=self.contract_address,
                function_name=self.function_name,
                parameters=self.args,
            ),
            public_keys=self.public_keys,
            signatures=self.signatures,
            timestamp=time.time_ns(),
            fuel=self.fuel,
        )


@serde
@dataclass
class QueryContractParams:
    query_api_key: Optional[str]
    snapshot_transaction_hash: Optional[str]
    contract_address: str
    function_name: str
    args: List[Any]

    def toQueryEnv(self) -> Dict:
        result = {
            "contract_address": self.contract_address,
        }
        return result


@serde
@dataclass
class CreateContractParams:
    contract_hex_bytecode: str
    constructor_parameters: List[Any]
    contract_source_url: str
    upgradable: bool
    git_commit_hash: str
    reproducible_build: bool
    fuel: int
    # signature
    public_keys: Optional[List[str]] = field(default_factory=list)
    signatures: Optional[List[str]] = field(default_factory=list)
    privatekey: Optional[str] = None  # private key of the sender

    def toTransaction(self, sender: str) -> Transaction:
        return Transaction(
            dependent_transaction_hash="",
            sender=sender,
            op_data=OperationCreateContract(
                op_type=OperationType.CREATE_CONTRACT,
                contract_hex_bytecode=self.contract_hex_bytecode,
                constructor_parameters=self.constructor_parameters,
                contract_source_url=self.contract_source_url,
                upgradable=self.upgradable,
                git_commit_hash=self.git_commit_hash,
                reproducible_build=self.reproducible_build,
            ),
            public_keys=self.public_keys,
            signatures=self.signatures,
            timestamp=time.time_ns(),
            fuel=self.fuel,
        )


@serde
@dataclass
class ForkContractParams:
    contract_code_hash: str
    constructor_parameters: List[Any]
    contract_source_url: str
    upgradable: bool
    git_commit_hash: str
    reproducible_build: bool
    fuel: int
    # signature
    public_keys: Optional[List[str]] = field(default_factory=list)
    signatures: Optional[List[str]] = field(default_factory=list)
    privatekey: Optional[str] = None

    def toTransaction(self, sender: str) -> Transaction:
        return Transaction(
            dependent_transaction_hash="",
            sender=sender,
            op_data=OperationForkContract(
                op_type=OperationType.FORK_CONTRACT,
                contract_code_hash=self.contract_code_hash,
                constructor_parameters=self.constructor_parameters,
                contract_source_url=self.contract_source_url,
                upgradable=self.upgradable,
                git_commit_hash=self.git_commit_hash,
                reproducible_build=self.reproducible_build,
            ),
            public_keys=self.public_keys,
            signatures=self.signatures,
            timestamp=time.time_ns(),
            fuel=self.fuel,
        )


@serde
@dataclass
class UpgradeContractParams:
    contract_hex_bytecode: str
    contract_address: str
    contract_source_url: str
    git_commit_hash: str
    reproducible_build: bool
    fuel: int

    # signature
    public_keys: Optional[List[str]] = field(default_factory=list)
    signatures: Optional[List[str]] = field(default_factory=list)
    privatekey: Optional[str] = None  # private key of the sender

    def toTransaction(self, sender: str) -> Transaction:
        return Transaction(
            dependent_transaction_hash="",
            sender=sender,
            op_data=OperationUpgradeContract(
                op_type=OperationType.UPGRADE_CONTRACT,
                contract_address=self.contract_address,
                contract_hex_bytecode=self.contract_hex_bytecode,
                contract_source_url=self.contract_source_url,
                git_commit_hash=self.git_commit_hash,
                reproducible_build=self.reproducible_build,
            ),
            public_keys=self.public_keys,
            signatures=self.signatures,
            timestamp=time.time_ns(),
            fuel=self.fuel,
        )


@serde
@dataclass
class QueryLogParams:
    block_hash: Optional[str]
    from_block: Optional[int]
    to_block: Optional[int]
    contract_addresses: Optional[List[str]] = field(default_factory=list)
    topics: Optional[List[List[str]]] = field(default_factory=list)
