import pytest

from chains.tests import TRANSACTIONS_200


@pytest.mark.asyncio
@pytest.mark.benchmark(group="spos")
async def test_get_block(sposChain, benchmark):
    """
    Benchmark for chainStateManager.getBlock

    test for getting block info
    """
    block = sposChain.chainStateManager.genesis()
    benchmark(sposChain.chainStateManager.getBlock, block.hash(), block.height)


@pytest.mark.asyncio
@pytest.mark.benchmark(group="spos")
async def test_get_blocks(sposChain, benchmark):
    """
    Benchmark for chainStateManager.getBlocks

    test for locator generator algorithm
    """
    locator = []
    benchmark(sposChain.chainStateManager.getBlocks, locator)


@pytest.mark.asyncio
@pytest.mark.benchmark(group="spos")
async def test_process_block(sposChain, benchmark):
    """
    Benchmark for chainStateManager.processBlock

    test for processing block
    case: block with 201 transactions, 1 minting transaction, 100 issue and 100 transfer
    """

    def generate_and_connect_block():
        block = sposChain.minter._getCandidateBlock(transactions=TRANSACTIONS_200)
        sposChain.chainStateManager.processBlock(block, False)

    benchmark(generate_and_connect_block)


@pytest.mark.asyncio
@pytest.mark.benchmark(group="spos")
async def test_connect_block(sposChain, benchmark):
    """
    Benchmark for chainStateManager.connectBlock

    test for connecting block
    case: block with 201 transactions, 1 minting transaction, 100 issue and 100 transfer
    """

    @benchmark
    def generate_and_connect_block():
        block = sposChain.minter._getCandidateBlock(transactions=TRANSACTIONS_200)
        sposChain.chainStateManager.connectBlock(block, False)
