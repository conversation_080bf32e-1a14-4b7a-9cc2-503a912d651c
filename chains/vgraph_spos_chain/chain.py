from __future__ import annotations

from typing import Dict, Optional

from kivy import Logger
from serde.json import json_loads

import rawdb
from chains.header_chain import Header<PERSON>hain
from chains.log_retriever import LogRetriever
from chains.mempool import MemoryPool
from chains.signature_manager import SignatureManager
from chains.statedb_manager import StatedbManager
from chains.vgraph_spos_chain.chain_state_manager import VGraphSPOSChainStateManager
from chains.vgraph_spos_chain.client import VGraphSPOSClient
from chains.vgraph_spos_chain.clock import Clock
from chains.vgraph_spos_chain.minter import VGraphSPOSChainMinter
from chains.vgraph_spos_chain.session import SPOSChainSession
from common import hexToBytes
from common.models import Block, SPOSHeader
from jsonrpc.peer import Peer
from vm import ContractExecutor, initSystemContractsCode


class VGraphSPOSChain:
    """
    VGraphSPOSChain is a chain implementation for VGraph with SPOS consensus algorithm
    """

    _LOGGER_TITLE = "VGraphSPOSChain:"

    chainId = "vgraphspos"
    tokenId = "vgraph_token"
    consensusSettings = {
        "algorithm": "spos",
        "slotNums": 3,
        "blockInterval": 5,  # in seconds, at least 1
    }
    SESSION_CLASS = SPOSChainSession
    HEADER_CLASS = SPOSHeader

    def __init__(self):
        self.headerChain: Optional[HeaderChain] = None
        self.statedbManager: Optional[StatedbManager] = None
        self.minter: Optional[VGraphSPOSChainMinter] = None
        self.chainStateManager: Optional[VGraphSPOSChainStateManager] = None
        self.clock: Optional[Clock] = None
        self.memoryPool: Optional[MemoryPool] = None
        self.signatureManager: Optional[SignatureManager] = None
        self.client: Optional[VGraphSPOSClient] = None
        self.logRetriever: Optional[LogRetriever] = None

    def initChain(self):
        """
        Initialize the chain, including:
        - init db, triedb, statedb
        - register system contracts
        - initialize memory pool, clock, chain state manager, client, etc.
        - init chain state
        """

        self.memoryPool = MemoryPool()
        # TODO: start clock only if the node is a supernode
        # when the node is not a supernode, stop the clock
        # maybe move clock to minter
        self.clock = Clock()

        db = rawdb.newLmdb()
        self.headerChain = HeaderChain(headerType=self.HEADER_CLASS, chainDb=db)
        self.statedbManager = StatedbManager(db, self.headerChain)
        self.logRetriever = LogRetriever(db, self.headerChain)

        # register system contract if it's genesis block
        if self.headerChain.getCanonicalHash(0) is None:
            self.registerSystemContract()

        self.signatureManager = SignatureManager(statedbManager=self.statedbManager)
        self.client = VGraphSPOSClient()

        self.chainStateManager = VGraphSPOSChainStateManager(
            consensusSettings=self.consensusSettings,
            db=db,
            headerChain=self.headerChain,
            statedbManager=self.statedbManager,
            memoryPool=self.memoryPool,
            client=self.client,
        )

        def onInitChain():
            # note: initKeys should be called after initChainState, because get state should be called after restore local
            self.signatureManager.initKeys()

        self.chainStateManager.initChainState(onInitChain)

        self.minter = VGraphSPOSChainMinter(
            statedbManager=self.statedbManager,
            chainStateManager=self.chainStateManager,
            consensusSettings=self.consensusSettings,
            memoryPool=self.memoryPool,
            clock=self.clock,
            signatureManager=self.signatureManager,
            client=self.client,
        )

        # register connect block event observers
        self.chainStateManager.registerConnectBlockObserver(self.minter)  # update slot id
        self.chainStateManager.registerConnectBlockObserver(self.memoryPool)  # remove transactions in block from pool
        self.chainStateManager.registerConnectBlockObserver(self)  # log statuses of each component
        self.chainStateManager.registerConnectBlockObserver(self.client)  # broadcast block to all peers

        # register add new peer event observers
        self.client.peerManager.registerAddPeerObserver(self)

    def registerSystemContract(self):
        """
        Register system contracts for the chain, only called in genesis block
        """
        systemContractNameToAddressMap: Dict[str, bytes] = {}
        with open("chains/vgraph_spos_chain/system_contracts.json") as f:
            systemContracts = json_loads(f.read())

            for contract in systemContracts:
                systemContractNameToAddressMap[contract["contract_name"]] = hexToBytes(contract["contract_address"])

        slotNums = self.consensusSettings.get("slotNums", None)
        if slotNums is None:
            Logger.error(f"{self._LOGGER_TITLE} Initialize chain fails: slotNums is not set in the consensus settings.")
            raise Exception("slotNums is not set in the consensus settings.")

        # load wasm file from system_contracts folder and register them
        state = self.statedbManager.getState()
        initSystemContractsCode(state, systemContractNameToAddressMap)

        token = ContractExecutor(systemContractNameToAddressMap["token"])
        spos = ContractExecutor(systemContractNameToAddressMap["spos"])

        registerResults = [
            ("Token", token.constructor(state, self.tokenId)),
            ("Spos", spos.constructor(state, slotNums)),
        ]
        for name, (_, err) in registerResults:
            if err is not None:
                Logger.error(f"{self._LOGGER_TITLE} Failed to register contract {name}")
                exit(1)
        Logger.info(f"{self._LOGGER_TITLE} System Contracts Registered")

    # ----- utils ----

    def onConnectBlock(self, block: Block):
        """
        log the block status
        """
        Logger.info(f"""{self._LOGGER_TITLE} Block connect successfully! Current chain status:
{self.chainStateManager.getStatusString()}
{self.chainStateManager.orphanBlocks.getStatusString()}
{self.memoryPool.getStatusString()}
{self.minter.getStatusString()}
{self.clock.getStatusString()}
{self.signatureManager.getStatusString()}""")

    async def onAddPeer(self, peer: Peer):
        """
        When peer manager add a new peer, do the following:
        1. ask peer for its best block
        2. compare the best block with the local best block
        3. if the peer has a higher block, request the missing blocks
        """

        # 1. ask peer for its best block
        peerBestBlock = await self.client.sendBlocktreeBestBlock(peer)
        if peerBestBlock is None:
            Logger.debug(f"{self._LOGGER_TITLE} Peer {peer.nodeId} has no best block")
            return

        # 2. compare the best block with the local best block
        localBestBlock = self.chainStateManager.currentBlock()
        if localBestBlock is None:
            Logger.debug(f"{self._LOGGER_TITLE} Local best block is None")
            return

        if peerBestBlock.height() <= localBestBlock.height():
            # no need to request blocks from the peer
            return

        # 3. if the peer has a higher block, request the missing blocks
        locator = self.chainStateManager.generateLocator()
        syncBlocks = await self.client.sendBlocktreeGetBlocks(
            peer=peer,
            locator=locator,
        )
        if syncBlocks is not None:
            self.chainStateManager.processSyncBlocks(syncBlocks)

    def closeDb(self):
        """
        close the db
        """
        self.statedbManager.db.close()
