from __future__ import annotations

import common
import state
import tree
import vgraphdb
from chains.header_chain import <PERSON><PERSON><PERSON><PERSON><PERSON>
from common import hexToBytes


class StatedbManager:
    def __init__(self, db: vgraphdb.KeyValueStore, headerChain: HeaderChain):
        self.db = db
        self.triedb = tree.TrieDB(self.db)
        self.statedb = state.newDatabaseWithTriedb(self.db, self.triedb)
        self.headerChain = headerChain
        self.__stateCache = None

    def getState(self) -> state.StateDB:
        if self.__stateCache is not None and not self.__stateCache.trie.isCommitted():
            return self.__stateCache
        self.__stateCache = self._getState()
        return self.__stateCache

    def _getState(self) -> state.StateDB:
        if self.headerChain.genesisHeader is None:  # genesis block
            return state.StateDB(common.EmptyRootHash, self.statedb)
        else:
            bestBlockHeader = self.headerChain.currentHeader.get()
            return state.StateDB(hexToBytes(bestBlockHeader.state_root), self.statedb)

    def hasState(self, hash: bytes) -> bool:
        try:
            self.__stateCache.openTrie(hash)
            return True
        except Exception:
            return False

    def clearStateCache(self):
        self.__stateCache = None
