from __future__ import annotations

from typing import List, Optional

from cachetools import LRUCache
from kivy import Logger

from chains.header_chain import <PERSON><PERSON><PERSON><PERSON><PERSON>
from common.encode import bytesToHex, hexToBytes
from common.errors import LogRetrievalError, UnknownBlockError
from common.models import <PERSON>Filter, Log
from rawdb import readLogs
from vgraphdb import KeyValueStore

# TODO: maybe write it to the config file
DEFAULT_LOG_CACHE_SIZE = 64


class LogRetriever:
    """
    A retriever for logs from the blockchain.
    """

    _LOGGER_TITLE = "LogRetriever:"

    def __init__(self, db: KeyValueStore, headerChain: HeaderChain):
        self.db = db
        # TODO: maybe use chain state manager, not header chain
        self.headerChain = headerChain
        self.logsCache: LRUCache[bytes, List[Log]] = LRUCache(maxsize=DEFAULT_LOG_CACHE_SIZE)

    def getLogs(
        self,
        blockHash: Optional[bytes],
        begin: Optional[int],
        end: Optional[int],
        addresses: Optional[List[bytes]],
        topics: Optional[List[List[bytes]]],
    ) -> List[Log]:
        """
        Retrieve logs based on the filter criteria.

        Args:
            blockHash (Optional[bytes]): Hash of the specific block to filter logs. If provided, filters only that block.
            begin (Optional[int]): Start of the block range (inclusive). If None, defaults to the latest block.
            end (Optional[int]): End of the block range (inclusive). If None, defaults to the latest block.
            addresses (Optional[List[bytes]]): List of contract addresses to filter logs by.
            topics (Optional[List[List[bytes]]]): List of topic lists to filter logs by.

        Returns:
            List[Log]: A list of logs matching the filter criteria.

        Raises:
            UnknownBlockError: If the block header for the specified range cannot be found.
            ValueError: If the block range is invalid (e.g., begin > end).
        """
        Logger.debug(
            f"{self._LOGGER_TITLE} getLogs called with blockHash={bytesToHex(blockHash) if blockHash else ''}, begin={begin}, end={end}, addresses={[bytesToHex(address) for address in addresses] if addresses else '[]'}, topics={[[bytesToHex(topic) for topic in sub] for sub in topics] if topics else '[]'}"
        )

        # check if length of addresses and topics are the same
        if addresses is not None and topics is not None and len(addresses) != len(topics):
            raise ValueError("Length of addresses and topics must be the same.")

        # if block hash is set, filter logs for that block
        if blockHash:
            return self.getLogsByBlockHash(blockHash, addresses, topics)

        # otherwise, filter logs for a range of blocks
        # resolve begin and end
        currentHeight = self.headerChain.currentHeader.get().height if self.headerChain.currentHeader.get() else None
        if end is None:
            end = currentHeight
        else:
            if end > currentHeight:
                raise ValueError("end must be less than or equal to current height")
        if end is None:
            Logger.warning(f"{self._LOGGER_TITLE} Current block header unavailable, cannot resolve block range.")
            raise UnknownBlockError("Current block header unavailable, cannot resolve block range.")

        if begin is None:
            begin = currentHeight
        else:
            if begin > currentHeight:
                raise ValueError("begin must be less than or equal to current height")

        if begin > end:
            Logger.warning(f"{self._LOGGER_TITLE} Invalid block range: begin={begin} is greater than end={end}.")
            raise ValueError("begin must be less than or equal to end")
        return self.getRangeLogs(begin, end, addresses, topics)

    def getLogsByBlockHash(
        self, blockHash: bytes, addresses: Optional[List[bytes]], topics: Optional[List[List[bytes]]]
    ) -> List[Log]:
        """
        Retrieve logs for a block by block hash.

        Assume that length of addresses and topics are the same.

        Args:
            blockHash (bytes): Hash of the block to retrieve logs from.
            addresses (Optional[List[bytes]]): List of contract addresses to filter logs by.
            topics (Optional[List[List[bytes]]]): List of topic lists to filter logs by.

        Returns:
            List[Log]: A list of logs matching the filter criteria.

        Raises:
            UnknownBlockError: If the block header is unavailable.
            LogRetrievalError: If logs cannot be retrieved from the database.
        """
        Logger.debug(
            f"{self._LOGGER_TITLE} getLogsByBlockHash called with blockHash={bytesToHex(blockHash)}, \
            addresses={[bytesToHex(address) for address in addresses] if addresses else '[]'}, \
            topics={[[bytesToHex(topic) for topic in sub] for sub in topics] if topics else '[]'}"
        )

        header = self.headerChain.getHeaderByHash(blockHash)
        if not header:
            raise UnknownBlockError(f"The block header {bytesToHex(blockHash)} is unavailable.")

        # check if the block contains the desired logs
        if not bloomFilter(header.bloom, addresses, topics):
            return []
        logs = self.getLogsFromDB(blockHash, header.height)
        return filterLogs(logs, addresses, topics)

    def getRangeLogs(
        self, begin: int, end: int, addresses: Optional[List[bytes]], topics: Optional[List[List[bytes]]]
    ) -> List[Log]:
        """
        Retrieve logs for a range of blocks.

        Assume that length of addresses and topics are the same.

        Args:
            begin (int): Start of the block range (inclusive).
            end (int): End of the block range (inclusive).
            addresses (Optional[List[bytes]]): List of contract addresses to filter logs by.
            topics (Optional[List[List[bytes]]]): List of topic lists to filter logs by.

        Returns:
            List[Log]: A list of logs matching the filter criteria.
        """
        Logger.debug(
            f"{self._LOGGER_TITLE} getRangeLogs called with begin={begin}, end={end}, \
            addresses={[bytesToHex(address) for address in addresses] if addresses else '[]'}, \
            topics={[[bytesToHex(topic) for topic in sub] for sub in topics] if topics else '[]'}"
        )
        logs = []
        for height in range(begin, end + 1):
            Logger.debug(f"{self._LOGGER_TITLE} Processing block at height={height}.")
            if (header := self.headerChain.getHeaderByHeight(height)) is not None:
                logs.extend(self.getLogsByBlockHash(header.hash(), addresses, topics))
        return logs

    def getLogsFromDB(self, blockHash: bytes, blockNumber: int) -> List[Log]:
        """
        Retrieve logs for a block from the cache or database.

        Args:
            blockHash (bytes): Hash of the block to retrieve logs from.
            blockNumber (int): Number of the block.

        Returns:
            List[Log]: A list of logs from the specified block.

        Raises:
            LogRetrievalError: If logs cannot be retrieved from the database.
        """
        Logger.debug(
            f"{self._LOGGER_TITLE} getLogsFromDB called with blockHash={bytesToHex(blockHash)}, blockNumber={blockNumber}"
        )
        cached = self.logsCache.get(blockHash)
        if cached:
            Logger.debug(f"{self._LOGGER_TITLE} Cache hit for blockHash={bytesToHex(blockHash)}.")
            return cached

        logs = readLogs(self.db, blockHash, blockNumber)
        if logs is None:
            Logger.warning(
                f"{self._LOGGER_TITLE} No logs found in database for blockHash={bytesToHex(blockHash)}, height={blockNumber}."
            )
            raise LogRetrievalError("Failed to retrieve logs for block.")
        self.logsCache[blockHash] = logs
        return logs


# ----- helper functions -----


def bloomFilter(bloom: BloomFilter, addresses: Optional[List[bytes]], topics: Optional[List[List[bytes]]]) -> bool:
    """
    Check if a bloom filter matches any of the addresses and any of the topic lists.

    Assume that length of addresses and topics are the same.

    Args:
        bloom (BloomFilter): The bloom filter to test against.
        addresses (Optional[List[bytes]]): List of addresses to test.
        topics (Optional[List[List[bytes]]]): List of topic lists to test.

    Returns:
        bool: True if any address and any topic list matches, False otherwise.
    """
    if addresses and len(addresses) > 0:
        # Check if any address matches
        if not any(bloom.test(address) for address in addresses):
            return False

    # If topics are provided, check if any topic list matches
    if topics:
        return any(any(bloom.test(topic) for topic in topic_list) for topic_list in topics)
    return True


def filterLogs(logs: List[Log], addresses: Optional[List[bytes]], topics: Optional[List[List[bytes]]]) -> List[Log]:
    """
    Filter logs by multiple addresses and multiple topic lists.

    Assume that length of addresses and topics are the same.

    Args:
        logs (List[Log]): The logs to filter.
        addresses (Optional[List[bytes]]): The list of target addresses to match.
        topics (Optional[List[List[bytes]]]): The list of topic lists to match in the logs.

    Returns:
        List[Log]: Logs that match any of the specified addresses and any of the topic lists.
    """

    def isTargetLog(log: Log) -> bool:
        # Check if the log's contract address matches any of the target addresses
        if addresses and log.contract_address not in addresses:
            return False

        # If no topic filters are provided, match the address only
        if not topics:
            return True

        # Check if the log's topics match any of the topic lists
        for i, sub in enumerate(topics):
            if len(sub) == 0:
                continue  # Skip if the topic list is empty
            if hexToBytes(log.topics[i]) not in sub:
                return False  # If any topic does not match, return False
        return True

    return [log for log in logs if isTargetLog(log)]
