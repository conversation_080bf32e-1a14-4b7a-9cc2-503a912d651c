from __future__ import annotations

import threading
from typing import Dict, List, Optional

from kivy.logger import Logger

from common import bytesToHex
from common.models import Block, Transaction


class MemoryPool:
    """
    Transaction pool, used to cache transactions in memory
    """

    _LOGGER_TITLE = "Memory Pool:"

    def __init__(self):
        self.transactions: Dict[bytes, Transaction] = dict()
        self.lock = threading.RLock()

    def addTransaction(self, transaction: Transaction):
        """
        add a transaction to the pool
        """
        with self.lock:
            self.transactions[transaction.hash()] = transaction
        Logger.trace(f"{self._LOGGER_TITLE} Transaction {bytesToHex(transaction.hash())} added to the memory pool")

    def removeTransaction(self, transactionHash: bytes):
        """
        remove a transaction from the pool
        """
        with self.lock:
            result = self.transactions.pop(transactionHash, None)
            if result:
                Logger.debug(f"{self._LOGGER_TITLE} Transaction {transactionHash} removed from the memory pool")

    def getTransaction(self, transactionHash: bytes) -> Optional[Transaction]:
        """
        return a transaction by transaction hash
        """
        return self.transactions.get(transactionHash)

    def getTransactions(self) -> List[Transaction]:
        """
        return all transactions in the pool
        """
        return list(self.transactions.values())

    def hasTransaction(self, transactionHash: bytes) -> bool:
        """
        check if the transaction is in the pool
        """
        return transactionHash in self.transactions

    def onConnectBlock(self, block: Block):
        """
        remove transactions in the block from the pool
        """
        transactions = block.transactions
        with self.lock:
            for transaction in transactions:
                self.removeTransaction(transaction.hash())

    def getStatusString(self):
        """
        return the status of the pool
        """
        return f"{self._LOGGER_TITLE} {len(self.transactions)} transaction(s)"
