from __future__ import annotations

from kivy import Logger

from chains.statedb_manager import StatedbManager
from common import (
    generateKeys,
    privateKeyToPublicKey,
    publicKeyToAddress,
    signBlockHeaderWithPrivateKey,
    signTransactionWithPrivateKey,
)
from common.models import SPOSHeader, Transaction
from config import CONFIG
from vm import ContractExecutor


class SignatureManager:
    """
    SignatureManager is responsible for generating and verifying signatures
    for the transactions and blocks.
    """

    _LOGGER_TITLE = "SignatureManager:"
    # TODO: temporary signing context for dev
    signingContext = "ccbacbdc-e033-4c37-bd37-a1c69e159d9f"

    def __init__(self, statedbManager: StatedbManager):
        self.statedbManager = statedbManager
        self.schnorrUtilsContractClient = ContractExecutor(
            address="0x2975aa6f101bc21dd00b7b09ee8204597b5df48629c35dbe889fde76aba97502",
        )

    def initKeys(self):
        """
        Initialize private key, public key, and address
        """
        self.privateKey = CONFIG.privateKey

        if self.privateKey is None:
            # generate a random private key
            keypair = generateKeys()
            if keypair is None:
                raise Exception("Error while generating keys")
            self.publicKey, self.privateKey = keypair
            self.address = publicKeyToAddress(self.publicKey)
        else:
            self.publicKey = privateKeyToPublicKey(self.privateKey)
            self.address = publicKeyToAddress(self.publicKey)

            if self.publicKey is None or self.address is None:
                raise Exception("Invalid private key")

        Logger.info(f"{self._LOGGER_TITLE} Your public key is: {self.publicKey}")
        Logger.info(f"{self._LOGGER_TITLE} Your address is: {self.address}")

    def getPublicKey(self) -> str:
        """
        return the public key
        """
        return self.publicKey

    def signTransaction(self, transaction: Transaction) -> Transaction:
        """
        sign the transaction with the self's private key
        """
        return signTransactionWithPrivateKey(transaction, self.privateKey)

    def signBlockHeader(self, header: SPOSHeader) -> SPOSHeader:
        """
        sign the block header with the self's private key
        """
        return signBlockHeaderWithPrivateKey(header, self.privateKey)

    def getStatusString(self) -> str:
        """
        return the status of the SignatureManager
        """
        return f"""{self._LOGGER_TITLE}
    Public key: {self.publicKey}
    Address: {self.address}"""
