import asyncio
import os
import shutil
from contextlib import suppress

import pytest
import pytest_asyncio

import vgraphdb
from chains.vgraph_pow_primechain.chain import VGraphPOWPrimeChain
from chains.vgraph_spos_chain.chain import VGraphSPOSChain

vgraphdb.lmdb.dbPath = "vgraph_lmdb_test"


@pytest.fixture(autouse=True)
def cleanDb():
    # clean old db
    if os.path.exists(vgraphdb.lmdb.dbPath):
        shutil.rmtree(vgraphdb.lmdb.dbPath)


@pytest_asyncio.fixture
async def sposChain():
    chain = VGraphSPOSChain()
    chain.initChain()
    # cancel all pending tasks, e.g. the generateBlock task, clock task...
    pending = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
    for task in pending:
        task.cancel()
        with suppress(asyncio.CancelledError, asyncio.TimeoutError):
            await asyncio.wait_for(task, timeout=1)

    yield chain
    chain.closeDb()


@pytest_asyncio.fixture
async def powChain():
    chain = VGraphPOWPrimeChain()
    chain.initChain()
    # cancel all pending tasks, e.g. the generateBlock task, clock task...
    pending = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
    for task in pending:
        task.cancel()
        with suppress(asyncio.CancelledError, asyncio.TimeoutError):
            await asyncio.wait_for(task, timeout=1)

    yield chain
    chain.closeDb()
