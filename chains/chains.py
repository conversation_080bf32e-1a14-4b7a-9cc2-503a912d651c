from chains.vgraph_pow_primechain.chain import VGraphPOWPrimeChain
from chains.vgraph_spos_chain.chain import VGraphSPOSChain

chainClasses = [
    # (chain class, init kwargs), kwargs should be None if no args, otherwise a dict
    (VGraphSPOSChain, None),
    (VGraphPOWPrimeChain, None),
]

# dict, key: chainId, value: tuple (chain class, init kwargs)
CHAINS = {chainClass[0].chainId: chainClass for chainClass in chainClasses}
