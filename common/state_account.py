from __future__ import annotations

from dataclasses import dataclass
from typing import Type

from rlp.sedes import Binary, binary, boolean
from serde import serde

from .consts import HashLength
from .encode import Model, RLPModel


@dataclass
@serde
class AccountAttributes(Model):
    """
    AccountAttributes contains the attributes of a StateAccount.
    """

    deployer: bytes  # address of the contract deployer

    # defined by the contract deployer
    # the following fields can not be upgraded
    upgradable: bool  # whether the contract is upgradable.

    # the following fields can be upgraded
    contractSourceUrl: str  # URL of the contract source code
    gitCommitHash: str  # commit hash of the contract code
    reproducibleBuild: bool  # whether the contract code build is reproducible

    def toRLP(self) -> RLPModel:
        return AccountAttributesRLP(
            self.deployer,
            self.upgradable,
            self.contractSourceUrl.encode(),
            self.gitCommitHash.encode(),
            self.reproducibleBuild,
        )

    @staticmethod
    def getRLPClass() -> Type[RLPModel]:
        return AccountAttributesRLP

    def copy(self) -> AccountAttributes:
        return AccountAttributes(
            self.deployer,
            self.upgradable,
            self.contractSourceUrl,
            self.gitCommitHash,
            self.reproducibleBuild,
        )


class AccountAttributesRLP(RLPModel):
    fields = [
        ("deployer", binary),  # TODO: should be Binary(common.AddressLength)
        ("upgradable", boolean),
        ("contract_source_url", binary),
        ("git_commit_hash", binary),
        ("reproducible_build", boolean),
    ]

    def toModel(self) -> Model:
        return AccountAttributes(
            self.deployer,
            self.upgradable,
            self.contract_source_url.decode(),
            self.git_commit_hash.decode(),
            self.reproducible_build,
        )


class StateAccountRLP(RLPModel):
    fields = [
        ("root", Binary(HashLength)),
        ("code_hash", Binary(HashLength)),
        ("attrs", AccountAttributesRLP),
    ]

    def toModel(self) -> Model:
        return StateAccount(self.root, self.code_hash, self.attrs.toModel())


class StateAccount(Model):
    def __init__(self, root: bytes, codeHash: bytes, attrs: AccountAttributes):
        self.root: bytes = root
        self.codeHash: bytes = codeHash

        self.attrs: AccountAttributes = attrs

    def toRLP(self) -> RLPModel:
        return StateAccountRLP(self.root, self.codeHash, self.attrs.toRLP())

    @staticmethod
    def getRLPClass() -> Type[RLPModel]:
        return StateAccountRLP

    def copy(self) -> StateAccount:
        return StateAccount(self.root, self.codeHash, self.attrs.toRLP())
