from .encode import hexToBytes
from .utils import keccak256

AddressLength = 32

EMPTY_ADDRESS = bytes(AddressLength)


def bytesToAddress(address: bytes) -> bytes:
    """
    Convert bytes to address.
    """
    if len(address) > AddressLength:
        return address[:AddressLength]
    return address.rjust(AddressLength, b"\x00")


def intToAddress(big: int) -> bytes:
    """
    Convert big integer to address.
    """
    return big.to_bytes(AddressLength, "big")


def hexToAddress(hexStr: str) -> bytes:
    """
    Convert hex string to address.
    """
    return bytesToAddress(hexToBytes(hexStr))


def generateContractAddress(hexBytecode: str, senderAddress: str, timestamp: int) -> bytes:
    """
    Generate contract address (in bytes) from the contract code, sender address and timestamp.
    """

    # TODO: temp contract address generation method
    return hexToAddress(keccak256(f"{hexBytecode}{senderAddress}{timestamp}"))
