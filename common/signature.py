from typing import List, Optional, Tuple

from .models import SP<PERSON><PERSON>eader, Transaction
from .utils import publicKeyToAddress

SIGNING_CONTEXT = "ccbacbdc-e033-4c37-bd37-a1c69e159d9f"


# Move the initialization into a function to avoid circular imports
def getSchnorrClient():
    global _schnorrClient
    if not hasattr(getSchnorrClient, "_schnorrClient"):
        # Import ContractTester here to break circular dependency
        from vm import ContractTester

        # Initialize contract client
        client = ContractTester(
            address="0x2975aa6f101bc21dd00b7b09ee8204597b5df48629c35dbe889fde76aba97502",
            wasmName="schnorr_signature",
        )
        # Register contract in test state
        client.constructor()
        getSchnorrClient._schnorrClient = client
    return getSchnorrClient._schnorrClient


def generateKeys() -> Tuple[str, str]:
    """
    generate a new key pair
    return the private key and public key
    """
    (publicKey, private<PERSON>ey), err = getSchnorrClient().execute("generate_keys", tuple)
    if err is not None:
        return None

    return publicKey, privateKey


def privateKeyToPublicKey(privateKey: str) -> str:
    """
    Generate the public key from the given private key.

    Args:
        privateKey: A string representing the private key

    Returns:
        str: The corresponding public key

    Raises:
        Exception: If the public key generation process fails
    """
    publicKey, err = getSchnorrClient().execute("private_key_to_public_key", str, privateKey)
    if err is not None:
        raise Exception(f"Failed to generate public key from private key: {err}")
    return publicKey


def signTransactionWithPrivateKey(transaction: Transaction, privateKey: str) -> Transaction:
    """
    Sign the transaction with the given private key.

    Args:
        transaction: Transaction object to be signed
        privateKey: Private key to sign the transaction with

    Returns:
        Transaction: The signed transaction with signature and public key added

    Raises:
        Exception: If the transaction signing process fails

    Note: This function modifies the input transaction by adding the signature
    and corresponding public key to its signature lists
    """
    message = transaction.toSignableMessage()
    signature, err = getSchnorrClient().execute("sign_message", str, message, privateKey, SIGNING_CONTEXT)
    if err is not None:
        raise Exception(f"Failed to sign transaction: {err}")

    # pack
    publicKey = privateKeyToPublicKey(privateKey)
    transaction.signatures.append(signature)
    transaction.public_keys.append(publicKey)
    return transaction


def verifyTransactionSignatures(transactions: List[Transaction]) -> bool:
    """
    Verify the signatures of multiple transactions.

    Args:
        transactions: List of Transaction objects to verify

    Returns:
        bool: True if all signatures are valid, False otherwise

    Raises:
        Exception: If the signature verification process fails

    Note: Does not support multi-signature with key aggregation or signature aggregation
    """
    if not transactions:
        return False

    messages: List[str] = []
    publicKeys: List[str] = []
    signatures: List[str] = []

    def _validateTransaction(transaction: Optional[Transaction]) -> bool:
        if not transaction:
            return False

        if (
            # public keys is required
            not transaction.public_keys
            # signatures is required
            or not transaction.signatures
            # public keys and signatures must have the same length
            or len(transaction.public_keys) != len(transaction.signatures)
        ):
            return False

        # public keys and signatures must not contain None
        if None in transaction.public_keys or None in transaction.signatures:
            return False

        # check if sender signs the transaction
        if transaction.sender not in map(publicKeyToAddress, transaction.public_keys):
            return False

        return True

    for transaction in transactions:
        if not _validateTransaction(transaction):
            return False

        message = transaction.toSignableMessage()  # Calculate once per transaction
        for publicKey, signature in zip(transaction.public_keys, transaction.signatures):
            messages.append(message)
            publicKeys.append(publicKey)
            signatures.append(signature)

    # batch validation
    result, err = getSchnorrClient().execute(
        "verify_batch_signatures", bool, messages, signatures, publicKeys, SIGNING_CONTEXT
    )
    if err is not None:
        raise Exception(f"Failed to verify transaction signatures: {err}")

    return result


def signBlockHeaderWithPrivateKey(header: SPOSHeader, privateKey: str) -> SPOSHeader:
    """
    Sign the block header with the given private key.

    Args:
        blockHeader: SPOSHeader object to be signed
        privateKey: Private key to sign the block header with

    Returns:
        SPOSHeader: The signed block header with signature and public key added

    Raises:
        Exception: If the block header signing process fails

    Note: This function modifies the input block header by adding the signature
    and corresponding public key to its signature lists
    """
    message = header.toSignableMessage()
    signature, err = getSchnorrClient().execute("sign_message", str, message, privateKey, SIGNING_CONTEXT)
    if err is not None:
        raise Exception(f"Failed to sign block header: {err}")

    # pack
    header.signatures.append(signature)
    header.public_keys.append(privateKeyToPublicKey(privateKey))
    return header


def verifyBlockHeaderSignature(blockHeader: SPOSHeader) -> bool:
    """
    Verify the signature of the block header.

    Args:
        blockHeader: SPOSHeader object to verify

    Returns:
        bool: True if all signatures are valid, False otherwise

    Raises:
        Exception: If the signature verification process fails

    Note: Verifies that the block header has matching numbers of public keys and signatures
    before performing batch signature verification
    """
    publicKeys = blockHeader.public_keys
    signatures = blockHeader.signatures

    if (
        # public keys is required
        not publicKeys
        # signatures is required
        or not signatures
        # public keys and signatures must have the same length
        or len(publicKeys) != len(signatures)
    ):
        return False

    message = blockHeader.toSignableMessage()
    messages = [message] * len(publicKeys)
    result, err = getSchnorrClient().execute(
        "verify_batch_signatures", bool, messages, signatures, publicKeys, SIGNING_CONTEXT
    )
    if err is not None:
        raise Exception(f"Failed to verify block header signature: {err}")
    return result
