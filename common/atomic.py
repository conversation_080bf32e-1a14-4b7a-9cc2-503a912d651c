from __future__ import annotations

import threading
from typing import Generic, TypeVar

from serde import serde

T = TypeVar("T")


@serde
class Atomic(Generic[T]):
    def __init__(self, initial_value: T = None):
        self._value = initial_value
        self._lock = threading.Lock()

    def get(self) -> T:
        with self._lock:
            return self._value

    def set(self, new_value: T) -> None:
        with self._lock:
            self._value = new_value
