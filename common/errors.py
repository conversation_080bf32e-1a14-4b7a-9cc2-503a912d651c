class SerializeError(Exception):
    """
    Serialize error when serializing data
    """

    def __init__(self, message: str):
        super().__init__(message)


class DeserializeError(Exception):
    """
    Deserialize error when deserializing data
    """

    def __init__(self, message: str):
        super().__init__(message)


class UnsupportedOperationTypeError(Exception):
    """
    Operation type does not exist error
    """

    def __init__(self, message: str):
        super().__init__(message)


class ContractExecutionError(Exception):
    """
    Contract execution error. Raised when contract execution fails, even though failure is not permitted
    """

    def __init__(self, message: str):
        super().__init__(message)


# ----- log retrieval -----


class LogRetrieverError(Exception):
    """Base exception for errors in LogRetriever."""

    pass


class UnknownBlockError(LogRetrieverError):
    """Raised when the block is unknown."""

    pass


class LogRetrievalError(LogRetrieverError):
    """Raised when logs cannot be retrieved."""

    pass
