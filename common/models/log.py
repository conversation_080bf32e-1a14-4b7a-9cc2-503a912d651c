from dataclasses import dataclass
from typing import List, Type

import rlp
from serde import field, serde

from common import bytesToHex, hexToBytes
from common.encode import Model, RLPModel


@serde
@dataclass
class Log(Model):
    """
    Representing a log entry in the transaction receipt

    log = event, which is emitted by the contract.
    """

    # TODO: attach the following fields
    # transaction hash of the log
    transaction_hash: bytes = field(
        serializer=bytesToHex,
        deserializer=hexToBytes,
    )
    # transaction index of the log
    transaction_index: int = field()
    # index of the log in the block
    log_index: int = field()

    # address of the contract that emits the event
    contract_address: bytes = field(
        serializer=bytesToHex,
        deserializer=hexToBytes,
    )
    # topics of the log
    topics: List[str] = field(default_factory=list)
    # data of the log
    data: List[str] = field(default_factory=list)

    def toRLP(self) -> RLPModel:
        return LogRLP(
            self.contract_address,
            [hexToBytes(topic) for topic in self.topics],
            [hexToBytes(data) for data in self.data],
            self.transaction_hash,
            self.transaction_index,
            self.log_index,
        )

    @staticmethod
    def getRLPClass() -> Type[RLPModel]:
        return LogRLP


class LogRLP(RLPModel):
    """
    RLP serializable log entry
    """

    fields = [
        ("contract_address", rlp.sedes.binary),
        ("topics", rlp.sedes.CountableList(rlp.sedes.binary)),
        ("data", rlp.sedes.CountableList(rlp.sedes.binary)),
        ("transaction_hash", rlp.sedes.binary),
        ("transaction_index", rlp.sedes.big_endian_int),
        ("log_index", rlp.sedes.big_endian_int),
    ]

    def toModel(self) -> Log:
        return Log(
            contract_address=self.contract_address,
            topics=[bytesToHex(topic) for topic in self.topics],
            data=[bytesToHex(data) for data in self.data],
            transaction_hash=self.transaction_hash,
            transaction_index=self.transaction_index,
            log_index=self.log_index,
        )
