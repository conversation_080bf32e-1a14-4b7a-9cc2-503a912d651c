import pytest

from common.models.bloom import <PERSON><PERSON>ilter


def test_add_and_test_normal_inputs():
    """Test adding and testing normal inputs."""
    bf = BloomFilter()

    # Add items to the bloom filter
    bf.add("hello")
    bf.add("world")

    # Test for presence
    assert bf.test("hello") is True, "Expected 'hello' to be in the Bloom filter"
    assert bf.test("world") is True, "Expected 'world' to be in the Bloom filter"

    # Test for absence
    assert bf.test("hello1") is False, "Expected 'hello1' to not be in the Bloom filter"
    assert bf.test("world1") is False, "Expected 'world1' to not be in the Bloom filter"


def test_add_and_test_bytes_input():
    """Test adding and testing byte inputs."""
    bf = BloomFilter()

    # Add byte data
    bf.add(b"hello")
    bf.add(b"world")

    # Test for presence
    assert bf.test(b"hello") is True, "Expected 'hello' to be in the Bloom filter"
    assert bf.test(b"world") is True, "Expected 'world' to be in the Bloom filter"

    # Test for absence
    assert bf.test(b"hello1") is False, "Expected 'hello1' to not be in the Bloom filter"
    assert bf.test(b"world1") is False, "Expected 'world1' to not be in the Bloom filter"


def test_edge_cases_empty_input():
    """Test edge cases with empty inputs."""

    # Adding empty string
    bf = BloomFilter()
    bf.add("")
    assert bf.test("") is True, "Expected empty string to be in the Bloom filter after adding"

    # Testing for empty bytes
    bf = BloomFilter()
    bf.add(b"")
    assert bf.test(b"") is True, "Expected empty bytes to be in the Bloom filter after adding"


def test_edge_cases_large_data():
    """Test edge cases with large inputs."""
    bf = BloomFilter()
    large_data = "a" * 10_000  # 10,000 characters

    bf.add(large_data)
    assert bf.test(large_data) is True, "Expected large data to be in the Bloom filter"
    assert bf.test("different_large_data") is False, "Expected unrelated data to not be in the Bloom filter"


def test_invalid_inputs():
    """Test invalid inputs."""
    bf = BloomFilter()

    # Add invalid types
    with pytest.raises(TypeError):
        bf.add(None)

    with pytest.raises(TypeError):
        bf.add(1234)  # Integers are not supported


def test_bytes_serialization_and_deserialization():
    """Test bytes serialization and deserialization."""
    bf = BloomFilter()

    # Add items
    bf.add("hello")
    bf.add("world")

    # Serialize and create a new filter
    serialized = bf.toBytes()
    bf2 = BloomFilter.fromBytes(serialized)

    # Test the new filter
    assert bf2.test("hello") is True, "Expected 'hello' to be in the deserialized Bloom filter"
    assert bf2.test("world") is True, "Expected 'world' to be in the deserialized Bloom filter"
    assert bf2.test("hello1") is False, "Expected 'hello1' to not be in the deserialized Bloom filter"
    assert bf2.test("world1") is False, "Expected 'world1' to not be in the deserialized Bloom filter"


def test_hex_serialization_and_deserialization():
    """Test hex serialization and deserialization."""
    bf = BloomFilter()

    # Add items
    bf.add("hello")
    bf.add("world")

    # Serialize and create a new filter
    serialized = bf.toHex()
    bf2 = BloomFilter.fromHex(serialized)

    # Test the new filter
    assert bf2.test("hello") is True, "Expected 'hello' to be in the deserialized Bloom filter"
    assert bf2.test("world") is True, "Expected 'world' to be in the deserialized Bloom filter"
    assert bf2.test("hello1") is False, "Expected 'hello1' to not be in the deserialized Bloom filter"
    assert bf2.test("world1") is False, "Expected 'world1' to not be in the deserialized Bloom filter"


def test_invalid_hex_input():
    """Test invalid hex string input."""
    with pytest.raises(ValueError):
        BloomFilter.fromHex("invalid_hex_string")


def test_bloom_filter_initial_state():
    """Test Bloom filter's initial state."""
    bf = BloomFilter()

    # No items added, everything should test as absent
    assert bf.test("any_item") is False, "Expected no item to be in the empty Bloom filter"
    assert bf.test("another_item") is False, "Expected no item to be in the empty Bloom filter"


# ----- benchmark -----


@pytest.mark.benchmark(group="bloom")
def test_bloom_filter_benchmark(benchmark):
    """Benchmark the Bloom filter."""
    bf = BloomFilter()

    # Add a large number of items
    for i in range(10000):
        bf.add(f"test_{i}")

    # Benchmark the test method with an existing and non-existing item
    def benchmark_test():
        # Test existing item only due to false positives
        assert bf.test("item_500") is True

    # Benchmark testing
    benchmark(benchmark_test)
