import pytest

from common import decodeRLP, encodeRLP, from<PERSON><PERSON>, to<PERSON><PERSON>

from .transaction import (
    OperationCallContract,
    OperationCreateContract,
    OperationForkContract,
    OperationType,
    OperationUpgradeContract,
    Transaction,
)


@pytest.mark.parametrize(
    "name, testData, expected",
    [
        (
            "tx data serialize, create contract data",
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationCreateContract(
                    op_type=OperationType.CREATE_CONTRACT,
                    contract_hex_bytecode="0x1234567890abcdef",
                    constructor_parameters=[1, 2, 3],
                    contract_source_url="https://example.com/contract",
                    git_commit_hash="1234567890abcdef",
                    reproducible_build=True,
                    upgradable=True,
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
            to<PERSON><PERSON>(
                {
                    "sender": "0x1234567890abcdef",
                    "timestamp": 1234567890,
                    "dependent_transaction_hash": "",
                    "fuel": 1000000,
                    "op_data": {
                        "op_type": OperationType.CREATE_CONTRACT,
                        "contract_hex_bytecode": "0x1234567890abcdef",
                        "constructor_parameters": [1, 2, 3],
                        "contract_source_url": "https://example.com/contract",
                        "git_commit_hash": "1234567890abcdef",
                        "reproducible_build": True,
                        "upgradable": True,
                    },
                    "public_keys": ["0x1234567890abcdef"],
                    "signatures": ["0x1234567890abcdef"],
                }
            ),
        ),
        (
            "tx data serialize, call contract data",
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationCallContract(
                    op_type=OperationType.CALL_CONTRACT,
                    contract_address="0x1234567890abcdef",
                    function_name="foo",
                    parameters=[1, 2, 3],
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
            toJson(
                {
                    "sender": "0x1234567890abcdef",
                    "timestamp": 1234567890,
                    "dependent_transaction_hash": "",
                    "fuel": 1000000,
                    "op_data": {
                        "op_type": OperationType.CALL_CONTRACT,
                        "contract_address": "0x1234567890abcdef",
                        "function_name": "foo",
                        "parameters": [1, 2, 3],
                    },
                    "public_keys": ["0x1234567890abcdef"],
                    "signatures": ["0x1234567890abcdef"],
                },
            ),
        ),
        (
            "tx data serialize, fork contract data",
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationForkContract(
                    op_type=OperationType.FORK_CONTRACT,
                    contract_code_hash="0x1234567890abcdef",
                    constructor_parameters=[1, 2, 3],
                    contract_source_url="https://example.com/contract",
                    git_commit_hash="1234567890abcdef",
                    reproducible_build=True,
                    upgradable=True,
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
            toJson(
                {
                    "dependent_transaction_hash": "",
                    "sender": "0x1234567890abcdef",
                    "timestamp": 1234567890,
                    "fuel": 1000000,
                    "op_data": {
                        "op_type": OperationType.FORK_CONTRACT,
                        "contract_code_hash": "0x1234567890abcdef",
                        "constructor_parameters": [1, 2, 3],
                        "contract_source_url": "https://example.com/contract",
                        "git_commit_hash": "1234567890abcdef",
                        "reproducible_build": True,
                        "upgradable": True,
                    },
                    "public_keys": ["0x1234567890abcdef"],
                    "signatures": ["0x1234567890abcdef"],
                }
            ),
        ),
        (
            "tx data serialize, upgrade contract data",
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationUpgradeContract(
                    op_type=OperationType.UPGRADE_CONTRACT,
                    contract_address="0x1234567890abcdef",
                    contract_hex_bytecode="0x1234567890abcdef",
                    contract_source_url="https://example.com/contract",
                    git_commit_hash="1234567890abcdef",
                    reproducible_build=True,
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
            toJson(
                {
                    "dependent_transaction_hash": "",
                    "sender": "0x1234567890abcdef",
                    "timestamp": 1234567890,
                    "fuel": 1000000,
                    "op_data": {
                        "op_type": OperationType.UPGRADE_CONTRACT,
                        "contract_address": "0x1234567890abcdef",
                        "contract_hex_bytecode": "0x1234567890abcdef",
                        "contract_source_url": "https://example.com/contract",
                        "git_commit_hash": "1234567890abcdef",
                        "reproducible_build": True,
                    },
                    "public_keys": ["0x1234567890abcdef"],
                    "signatures": ["0x1234567890abcdef"],
                }
            ),
        ),
    ],
)
def test_transaction_json_serialize(name, testData, expected):
    assert toJson(testData) == expected, f"Test case({name}) failed."


@pytest.mark.parametrize(
    "name, testData, expected",
    [
        (
            "tx data deserialize, create contract data",
            toJson(
                {
                    "sender": "0x1234567890abcdef",
                    "timestamp": 1234567890,
                    "dependent_transaction_hash": "",
                    "fuel": 1000000,
                    "op_data": {
                        "op_type": OperationType.CREATE_CONTRACT,
                        "contract_hex_bytecode": "0x1234567890abcdef",
                        "constructor_parameters": [1, 2, 3],
                        "contract_source_url": "https://example.com/contract",
                        "git_commit_hash": "1234567890abcdef",
                        "reproducible_build": True,
                        "upgradable": True,
                    },
                    "public_keys": ["0x1234567890abcdef"],
                    "signatures": ["0x1234567890abcdef"],
                }
            ),
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationCreateContract(
                    op_type=OperationType.CREATE_CONTRACT,
                    contract_hex_bytecode="0x1234567890abcdef",
                    constructor_parameters=[1, 2, 3],
                    contract_source_url="https://example.com/contract",
                    git_commit_hash="1234567890abcdef",
                    reproducible_build=True,
                    upgradable=True,
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
        ),
        (
            "tx data deserialize, call contract data",
            toJson(
                {
                    "sender": "0x1234567890abcdef",
                    "timestamp": 1234567890,
                    "dependent_transaction_hash": "",
                    "fuel": 1000000,
                    "op_data": {
                        "op_type": OperationType.CALL_CONTRACT,
                        "contract_address": "0x1234567890abcdef",
                        "function_name": "foo",
                        "parameters": [1, 2, 3],
                    },
                    "public_keys": ["0x1234567890abcdef"],
                    "signatures": ["0x1234567890abcdef"],
                },
            ),
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationCallContract(
                    op_type=OperationType.CALL_CONTRACT,
                    contract_address="0x1234567890abcdef",
                    function_name="foo",
                    parameters=[1, 2, 3],
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
        ),
        (
            "tx data deserialize, fork contract data",
            toJson(
                {
                    "dependent_transaction_hash": "",
                    "sender": "0x1234567890abcdef",
                    "timestamp": 1234567890,
                    "fuel": 1000000,
                    "op_data": {
                        "op_type": OperationType.FORK_CONTRACT,
                        "contract_code_hash": "0x1234567890abcdef",
                        "constructor_parameters": [1, 2, 3],
                        "contract_source_url": "https://example.com/contract",
                        "git_commit_hash": "1234567890abcdef",
                        "reproducible_build": True,
                        "upgradable": True,
                    },
                    "public_keys": ["0x1234567890abcdef"],
                    "signatures": ["0x1234567890abcdef"],
                }
            ),
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationForkContract(
                    op_type=OperationType.FORK_CONTRACT,
                    contract_code_hash="0x1234567890abcdef",
                    constructor_parameters=[1, 2, 3],
                    contract_source_url="https://example.com/contract",
                    git_commit_hash="1234567890abcdef",
                    reproducible_build=True,
                    upgradable=True,
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
        ),
        (
            "tx data deserialize, upgrade contract data",
            toJson(
                {
                    "dependent_transaction_hash": "",
                    "sender": "0x1234567890abcdef",
                    "timestamp": 1234567890,
                    "fuel": 1000000,
                    "op_data": {
                        "op_type": OperationType.UPGRADE_CONTRACT,
                        "contract_address": "0x1234567890abcdef",
                        "contract_hex_bytecode": "0x1234567890abcdef",
                        "contract_source_url": "https://example.com/contract",
                        "git_commit_hash": "1234567890abcdef",
                        "reproducible_build": True,
                    },
                    "public_keys": ["0x1234567890abcdef"],
                    "signatures": ["0x1234567890abcdef"],
                }
            ),
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationUpgradeContract(
                    op_type=OperationType.UPGRADE_CONTRACT,
                    contract_address="0x1234567890abcdef",
                    contract_hex_bytecode="0x1234567890abcdef",
                    contract_source_url="https://example.com/contract",
                    git_commit_hash="1234567890abcdef",
                    reproducible_build=True,
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
        ),
    ],
)
def test_transaction_json_deserialize(name, testData, expected):
    assert fromJson(Transaction, testData) == expected, f"Test case({name}) failed."


@pytest.mark.parametrize(
    "name, testData",
    [
        (
            "tx data serialize, create contract data",
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationCreateContract(
                    op_type=OperationType.CREATE_CONTRACT,
                    contract_hex_bytecode="0x1234567890abcdef",
                    constructor_parameters=[1, 2, 3],
                    contract_source_url="https://example.com/contract",
                    git_commit_hash="1234567890abcdef",
                    reproducible_build=True,
                    upgradable=True,
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
        ),
        (
            "tx data serialize, call contract data",
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationCallContract(
                    op_type=OperationType.CALL_CONTRACT,
                    contract_address="0x1234567890abcdef",
                    function_name="foo",
                    parameters=[1, 2, 3],
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
        ),
        (
            "tx data serialize, fork contract data",
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationForkContract(
                    op_type=OperationType.FORK_CONTRACT,
                    contract_code_hash="0x1234567890abcdef",
                    constructor_parameters=[1, 2, 3],
                    contract_source_url="https://example.com/contract",
                    git_commit_hash="1234567890abcdef",
                    reproducible_build=True,
                    upgradable=True,
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
        ),
        (
            "tx data serialize, upgrade contract data",
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationUpgradeContract(
                    op_type=OperationType.UPGRADE_CONTRACT,
                    contract_address="0x1234567890abcdef",
                    contract_hex_bytecode="0x1234567890abcdef",
                    contract_source_url="https://example.com/contract",
                    git_commit_hash="1234567890abcdef",
                    reproducible_build=True,
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
        ),
    ],
)
def test_transaction_rlp_serialize_deserialize(name, testData):
    encoded = encodeRLP(testData)
    decoded = decodeRLP(Transaction, encoded)
    assert testData == decoded, f"Test case({name}) failed."


@pytest.mark.parametrize(
    "name, testData, expected",
    [
        (
            "toSignableMessage, create contract",
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationCreateContract(
                    op_type=OperationType.CREATE_CONTRACT,
                    contract_hex_bytecode="0x1234567890abcdef",
                    constructor_parameters=[1, 2, 3],
                    contract_source_url="https://example.com/contract",
                    git_commit_hash="1234567890abcdef",
                    reproducible_build=True,
                    upgradable=True,
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
            toJson(
                {
                    "transaction_hash": "0x9b391d19193b8f5cc22398d2a6ddaa6309f25387541893ec6108dd9426307443",
                    "sender": "0x1234567890abcdef",
                    "op_data": {
                        "op_type": OperationType.CREATE_CONTRACT,
                        "contract_hex_bytecode": "0x1234567890abcdef",
                        "constructor_parameters": [1, 2, 3],
                        "contract_source_url": "https://example.com/contract",
                        "upgradable": True,
                        "git_commit_hash": "1234567890abcdef",
                        "reproducible_build": True,
                    },
                }
            ),
        ),
        (
            "toSignableMessage, call contract",
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationCallContract(
                    op_type=OperationType.CALL_CONTRACT,
                    contract_address="0x1234567890abcdef",
                    function_name="foo",
                    parameters=[1, 2, 3],
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
            toJson(
                {
                    "transaction_hash": "0xb42226dc84de166e2ce87e0b4f248bfbf2deef82c276266b4fbb990ec86ba82d",
                    "sender": "0x1234567890abcdef",
                    "op_data": {
                        "op_type": OperationType.CALL_CONTRACT,
                        "contract_address": "0x1234567890abcdef",
                        "function_name": "foo",
                        "parameters": [1, 2, 3],
                    },
                }
            ),
        ),
        (
            "toSignableMessage, fork contract",
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationForkContract(
                    op_type=OperationType.FORK_CONTRACT,
                    contract_code_hash="0x1234567890abcdef",
                    constructor_parameters=[1, 2, 3],
                    contract_source_url="https://example.com/contract",
                    git_commit_hash="1234567890abcdef",
                    reproducible_build=True,
                    upgradable=True,
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
            toJson(
                {
                    "transaction_hash": "0x793b98495b473945bbf807494e7419484f6fdf90cf1396446ba1c3f78f49062d",
                    "sender": "0x1234567890abcdef",
                    "op_data": {
                        "op_type": OperationType.FORK_CONTRACT,
                        "contract_code_hash": "0x1234567890abcdef",
                        "constructor_parameters": [1, 2, 3],
                        "contract_source_url": "https://example.com/contract",
                        "git_commit_hash": "1234567890abcdef",
                        "reproducible_build": True,
                        "upgradable": True,
                    },
                }
            ),
        ),
        (
            "toSignableMessage, upgrade contract",
            Transaction(
                dependent_transaction_hash="",
                sender="0x1234567890abcdef",
                timestamp=1234567890,
                fuel=1000000,
                op_data=OperationUpgradeContract(
                    op_type=OperationType.UPGRADE_CONTRACT,
                    contract_address="0x1234567890abcdef",
                    contract_hex_bytecode="0x1234567890abcdef",
                    contract_source_url="https://example.com/contract",
                    git_commit_hash="1234567890abcdef",
                    reproducible_build=True,
                ),
                public_keys=["0x1234567890abcdef"],
                signatures=["0x1234567890abcdef"],
            ),
            toJson(
                {
                    "transaction_hash": "0x0562546dfa866a34506640c08276d7a99e564dfaced8b19cc6ae56e5dbd416cf",
                    "sender": "0x1234567890abcdef",
                    "op_data": {
                        "op_type": OperationType.UPGRADE_CONTRACT,
                        "contract_address": "0x1234567890abcdef",
                        "contract_hex_bytecode": "0x1234567890abcdef",
                        "contract_source_url": "https://example.com/contract",
                        "git_commit_hash": "1234567890abcdef",
                        "reproducible_build": True,
                    },
                }
            ),
        ),
    ],
)
def test_to_signable_message(name, testData, expected):
    assert testData.toSignableMessage() == expected, f"Test case({name}) failed."
