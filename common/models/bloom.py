import hashlib
from typing import List, Union

from common.encode import bytesToHex, hexToBytes

from .receipt import Receipt

BLOOM_BYTE_LENGTH = 256
BLOOM_BIT_LENGTH = 8 * BLOOM_BYTE_LENGTH


class BloomFilter:
    """
    A Bloom filter implementation inspired by Ethereum's design.
    A Bloom filter is a space-efficient probabilistic data structure used to test membership in a set.
    """

    def __init__(self):
        """
        Initializes the Bloom filter.
        """
        self.filter = bytearray(BLOOM_BYTE_LENGTH)

    def _hashValues(self, data: bytes) -> tuple:
        """
        Generate indices and bit positions for the data
        :param data: Data to be added to or tested in the Bloom filter.

        return: A tuple of (index, bit) for each hash position.
            - index: The byte index in the filter.
            - bit: The bit position in the byte.
        """
        if not isinstance(data, bytes):
            raise TypeError("Data must be of type bytes")

        sha = hashlib.sha3_256()
        sha.update(data)
        hashbuf = sha.digest()

        # Calculate the bits to set
        v1 = 1 << (hashbuf[1] & 0x7)
        v2 = 1 << (hashbuf[3] & 0x7)
        v3 = 1 << (hashbuf[5] & 0x7)

        # Calculate the byte indices
        i1 = BLOOM_BYTE_LENGTH - ((int.from_bytes(hashbuf[:2], "big") & 0x7FF) >> 3) - 1
        i2 = BLOOM_BYTE_LENGTH - ((int.from_bytes(hashbuf[2:4], "big") & 0x7FF) >> 3) - 1
        i3 = BLOOM_BYTE_LENGTH - ((int.from_bytes(hashbuf[4:6], "big") & 0x7FF) >> 3) - 1

        return (i1, v1), (i2, v2), (i3, v3)

    def add(self, data: Union[bytes, str]):
        """
        Adds data to the Bloom filter.
        :param data: The data to add. Must be bytes or convertible to bytes.
        """
        if isinstance(data, str):
            data = data.encode()

        for idx, bit in self._hashValues(data):
            self.filter[idx] |= bit

    def test(self, data: Union[bytes, str]) -> bool:
        """
        Tests if data is in the Bloom filter.
        Note: A positive result does not guarantee membership.
        :param data: The data to test. Must be bytes or convertible to bytes.

        return: True if the data might be in the set, False if it's definitely not.
        """
        if isinstance(data, str):
            data = data.encode()

        return all(self.filter[idx] & bit for idx, bit in self._hashValues(data))

    @classmethod
    def fromBytes(cls, data: bytes) -> "BloomFilter":
        """
        Create a Bloom filter from bytes.
        :param data: A bytes object representing a Bloom filter.
        :return: A new BloomFilter object.
        """
        bf = cls()
        bf.filter = bytearray(data)
        return bf

    def toBytes(self) -> bytes:
        """
        Convert the Bloom filter to bytes.

        return: A bytes object representing the filter's state.
        """
        return bytes(self.filter)

    @classmethod
    def fromHex(cls, hexStr: str) -> "BloomFilter":
        """
        Create a Bloom filter from a hex string.
        :param hexStr: A hex string representing a Bloom filter.

        return: A new BloomFilter object.
        """
        try:
            b = hexToBytes(hexStr)
            return cls.fromBytes(b)
        except Exception as e:
            raise ValueError("Invalid hex string") from e

    def toHex(self) -> str:
        """
        Serializes the Bloom filter into a bytes object.

        return: A hex string representing the filter.
        """
        return bytesToHex(self.filter)

    @staticmethod
    def _toHex(bf: "BloomFilter") -> str:
        """
        Convert a BloomFilter object to a hex string.
        :param bf: A BloomFilter object.

        return: A hex string representing the filter.
        """
        return bf.toHex()

    def __eq__(self, value: "BloomFilter") -> bool:
        """
        Compare two Bloom filters for equality.
        :param value: The other BloomFilter object.

        return: True if the filters are equal, False otherwise.
        """
        return self.filter == value.filter


EMPTY_BLOOM_HEX = BloomFilter().toHex()


def createBloom(receipts: List[Receipt]) -> BloomFilter:
    """
    Create a Bloom filter from a list of receipts.
    :param receipts: A list of receipts to create the filter from.

    return: A BloomFilter object.
    """
    bf = BloomFilter()
    for receipt in receipts:
        for log in receipt.logs:
            # Add the contract address and topics to the filter
            bf.add(log.contract_address)
            for topic in log.topics:
                bf.add(hexToBytes(topic))
    return bf
