from __future__ import annotations

import io
from dataclasses import dataclass
from enum import Enum
from typing import Any, List, Type

import rlp
from serde import field, from_dict, serde  # is pyserde not serde

from common.encode import Model, RLPModel, bytesToHex, bytesToJson, hexToBytes, jsonToBytes
from common.errors import DeserializeError

from .. import DerivableList, encodeRL<PERSON>, from<PERSON>son, to<PERSON>son
from .common import OperationType
from .log import Log, LogRLP


class TransactionExecutionStatus(Enum):
    """
    Enum for transaction receipt status
    """

    FAILURE = False
    SUCCESS = True


class ReceiptFactory:
    @staticmethod
    def getReceiptOpResultClass(op_type: OperationType) -> Type[OperationResult]:
        if op_type == OperationType.CREATE_CONTRACT:
            return OperationCreateContractResult
        elif op_type == OperationType.CALL_CONTRACT:
            return OperationCallContractResult
        elif op_type == OperationType.FORK_CONTRACT:
            return OperationForkContractResult
        elif op_type == OperationType.UPGRADE_CONTRACT:
            return OperationUpgradeContractResult
        else:
            raise ValueError(f"Unknown operation type: {op_type}")


@serde
@dataclass
class OperationResult:
    """
    base class for operation result data
    """

    op_type: OperationType

    @staticmethod
    def deserializeFromDict(data: dict) -> OperationResult:
        """
        Deserialize operation result data from dict
        """
        try:
            opType = OperationType(data["op_type"])
            return from_dict(ReceiptFactory.getReceiptOpResultClass(opType), data)
        except Exception as e:
            raise DeserializeError("Invalid operation type") from e


@serde
@dataclass
class OperationCreateContractResult(OperationResult):
    """
    create contract operation result data
    """

    contract_address: str  # Address of the newly created contract
    code_hash: str  # Hash of the contract code


@serde
@dataclass
class OperationCallContractResult(OperationResult):
    """
    call contract operation result data
    """

    return_data: Any  # Data returned from the contract call


@serde
@dataclass
class OperationForkContractResult(OperationResult):
    """
    fork contract operation result data
    """

    contract_address: str  # Address of the newly created contract
    code_hash: str  # Hash of the contract code


@serde
@dataclass
class OperationUpgradeContractResult(OperationResult):
    """
    upgrade contract operation result data
    """

    code_hash: str  # Hash of the contract code


@serde
@dataclass
class Receipt(Model):
    """
    Transaction receipt data
    """

    # Attached when processing transaction
    transaction_hash: bytes = field(
        serializer=bytesToHex,
        deserializer=hexToBytes,
    )
    transaction_index: int = field()
    status: TransactionExecutionStatus = field()
    op_result: OperationResult = field(deserializer=OperationResult.deserializeFromDict)

    block_hash: bytes = field(
        serializer=bytesToHex,
        deserializer=hexToBytes,
    )

    logs: List[Log] = field(default_factory=list)

    def toRLP(self) -> RLPModel:
        return ReceiptRLP(
            self.transaction_hash,
            self.transaction_index,
            self.status.value,
            jsonToBytes(toJson(self.op_result)),
            self.block_hash,
            [log.toRLP() for log in self.logs],
        )

    @staticmethod
    def getRLPClass() -> Type[RLPModel]:
        return ReceiptRLP


class ReceiptRLP(RLPModel):
    fields = [
        ("transaction_hash", rlp.sedes.binary),
        ("transaction_index", rlp.sedes.big_endian_int),
        ("status", rlp.sedes.boolean),
        ("op_result", rlp.sedes.binary),
        ("block_hash", rlp.sedes.binary),
        ("logs", rlp.sedes.CountableList(LogRLP)),
    ]

    def toModel(self) -> Model:
        return Receipt(
            self.transaction_hash,
            self.transaction_index,
            TransactionExecutionStatus(self.status),
            OperationResult.deserializeFromDict(fromJson(dict, bytesToJson(self.op_result))),
            self.block_hash,
            [log.toModel() for log in self.logs],
        )


class Receipts(list, Model, DerivableList):
    def __init__(self, receipts: List[Receipt]):
        super().__init__(receipts)

    def toRLP(self) -> RLPModel:
        rlpTransactionReceipts = [receipt.toRLP() for receipt in self]
        return ReceiptsRLP(rlpTransactionReceipts)

    @staticmethod
    def getRLPClass() -> Type[RLPModel]:
        return ReceiptsRLP

    def encodeIndex(self, i: int, w: io.BytesIO) -> None:
        r = self[i]
        w.write(encodeRLP(r))

    def __len__(self):
        return super().__len__()


class ReceiptsRLP(RLPModel):
    fields = [
        ("receipts", rlp.sedes.CountableList(ReceiptRLP)),
    ]

    def toModel(self) -> Model:
        return Receipts([receipt.toModel() for receipt in self.receipts])
