import pytest

from common import decodeRLP, encodeRLP, from<PERSON>son, hexToBytes, toJson

from .receipt import (
    OperationCallContractResult,
    OperationCreateContractResult,
    OperationType,
    Receipt,
    Receipts,
    TransactionExecutionStatus,
)


@pytest.mark.parametrize(
    "name, testData, expected",
    [
        (
            "Receipt with create contract operation",
            Receipt(
                transaction_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                transaction_index=0,
                status=TransactionExecutionStatus.SUCCESS,
                op_result=OperationCreateContractResult(
                    op_type=OperationType.CREATE_CONTRACT,
                    contract_address="0x1234567890123456789012345678901234567890",
                    code_hash="0x1234567890123456789012345678901234567890",
                ),
                block_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                logs=[],
            ),
            to<PERSON>son(
                {
                    "transaction_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                    "transaction_index": 0,
                    "status": TransactionExecutionStatus.SUCCESS,
                    "op_result": {
                        "op_type": OperationType.CREATE_CONTRACT,
                        "contract_address": "0x1234567890123456789012345678901234567890",
                        "code_hash": "0x1234567890123456789012345678901234567890",
                    },
                    "block_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                    "logs": [],
                }
            ),
        ),
        (
            "Receipt with call contract operation",
            Receipt(
                transaction_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                transaction_index=0,
                status=TransactionExecutionStatus.SUCCESS,
                op_result=OperationCallContractResult(
                    op_type=OperationType.CALL_CONTRACT, return_data="0x1234567890123456789012345678901234567890"
                ),
                block_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                logs=[],
            ),
            toJson(
                {
                    "transaction_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                    "transaction_index": 0,
                    "status": TransactionExecutionStatus.SUCCESS,
                    "op_result": {
                        "op_type": OperationType.CALL_CONTRACT,
                        "return_data": "0x1234567890123456789012345678901234567890",
                    },
                    "block_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                    "logs": [],
                }
            ),
        ),
    ],
)
def test_receipt_json_serialize(name, testData, expected):
    assert toJson(testData) == expected, f"Test case({name}) failed."


@pytest.mark.parametrize(
    "name, testData, expected",
    [
        (
            "Receipt with create contract operation",
            toJson(
                {
                    "transaction_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                    "transaction_index": 0,
                    "status": TransactionExecutionStatus.SUCCESS,
                    "op_result": {
                        "op_type": OperationType.CREATE_CONTRACT,
                        "contract_address": "0x1234567890123456789012345678901234567890",
                        "code_hash": "0x1234567890123456789012345678901234567890",
                    },
                    "block_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                    "logs": [],
                }
            ),
            Receipt(
                transaction_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                transaction_index=0,
                status=TransactionExecutionStatus.SUCCESS,
                op_result=OperationCreateContractResult(
                    op_type=OperationType.CREATE_CONTRACT,
                    contract_address="0x1234567890123456789012345678901234567890",
                    code_hash="0x1234567890123456789012345678901234567890",
                ),
                block_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                logs=[],
            ),
        ),
        (
            "Receipt with call contract operation",
            toJson(
                {
                    "transaction_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                    "transaction_index": 0,
                    "status": TransactionExecutionStatus.SUCCESS,
                    "op_result": {
                        "op_type": OperationType.CALL_CONTRACT,
                        "return_data": "0x1234567890123456789012345678901234567890",
                    },
                    "block_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                    "logs": [],
                }
            ),
            Receipt(
                transaction_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                transaction_index=0,
                status=TransactionExecutionStatus.SUCCESS,
                op_result=OperationCallContractResult(
                    op_type=OperationType.CALL_CONTRACT, return_data="0x1234567890123456789012345678901234567890"
                ),
                block_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                logs=[],
            ),
        ),
    ],
)
def test_receipt_json_deserialize(name, testData, expected):
    assert fromJson(Receipt, testData) == expected, f"Test case({name}) failed."


@pytest.mark.parametrize(
    "name, testData",
    [
        (
            "Receipt with create contract operation",
            Receipt(
                transaction_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                transaction_index=0,
                status=TransactionExecutionStatus.SUCCESS,
                op_result=OperationCreateContractResult(
                    op_type=OperationType.CREATE_CONTRACT,
                    contract_address="0x1234567890123456789012345678901234567890",
                    code_hash="0x1234567890123456789012345678901234567890",
                ),
                block_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                logs=[],
            ),
        ),
        (
            "Receipt with call contract operation",
            Receipt(
                transaction_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                transaction_index=0,
                status=TransactionExecutionStatus.SUCCESS,
                op_result=OperationCallContractResult(
                    op_type=OperationType.CALL_CONTRACT, return_data="0x1234567890123456789012345678901234567890"
                ),
                block_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                logs=[],
            ),
        ),
    ],
)
def test_receipt_rlp_serialize_deserialize(name, testData):
    encoded = encodeRLP(testData)
    decoded = decodeRLP(Receipt, encoded)
    assert decoded == testData, f"Test case({name}) failed."


def test_receipts_rlp_serialize_deserialize():
    receipts = Receipts(
        [
            Receipt(
                transaction_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                transaction_index=0,
                status=TransactionExecutionStatus.SUCCESS,
                op_result=OperationCreateContractResult(
                    op_type=OperationType.CREATE_CONTRACT,
                    contract_address="0x1234567890123456789012345678901234567890",
                    code_hash="0x1234567890123456789012345678901234567890",
                ),
                block_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                logs=[],
            ),
            Receipt(
                transaction_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                transaction_index=0,
                status=TransactionExecutionStatus.SUCCESS,
                op_result=OperationCallContractResult(
                    op_type=OperationType.CALL_CONTRACT, return_data="0x1234567890123456789012345678901234567890"
                ),
                block_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                logs=[],
            ),
        ]
    )
    encoded = encodeRLP(receipts)
    decoded = decodeRLP(Receipts, encoded)
    assert decoded == receipts, "Test case failed."
