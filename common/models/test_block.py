import pytest

from .. import decodeRLP, encodeRLP, from<PERSON><PERSON>, hexToBytes, to<PERSON><PERSON>
from .block import Block, Body, POWHeader, SPOSHeader
from .bloom import EMPTY_BLOOM_HEX, BloomFilter
from .transaction import (
    OperationCallContract,
    OperationCreateContract,
    OperationForkContract,
    OperationType,
    OperationUpgradeContract,
    Transaction,
)


@pytest.mark.parametrize(
    "name, testData",
    [
        (
            "SPOS Header rlp serialize deserialize",
            SPOSHeader(
                parent_hash=hexToBytes("0x00000000000000000000000000000000"),
                height=1,
                state_root="0x9872189371298372287213878273",
                transactions_root="0x9872189371298372287213878273",
                receipts_root="0x9872189371298372287213878273",
                bloom=BloomFilter(),
                local_timestamp=123,
                protocol_timestamp=122,
                slot_id=1,
                proposer_address=hexToBytes("0x192873981273981273981273"),
                public_keys=["0x192873981273981211181273"],
                signatures=["0x1928739812739222281273"],
            ),
        ),
        (
            "POW Header rlp serialize deserialize",
            POWHeader(
                parent_hash=hexToBytes("0x00000000000000000000000000000000"),
                height=1,
                state_root="0x9872189371298372287213878273",
                transactions_root="0x9872189371298372287213878273",
                receipts_root="0x9872189371298372287213878273",
                bloom=BloomFilter(),
                difficulty_score=123,
                difficulty_score_overall=123,
                timestamp=123,
                nonce=122,
                multiplier=1,
            ),
        ),
    ],
)
def test_header_rlp_serialize_deserialize(name, testData):
    encoded = encodeRLP(testData)
    decoded = decodeRLP(testData.__class__, encoded)
    assert decoded == testData, f"Test case({name}) failed."


@pytest.mark.parametrize(
    "name, testData",
    [
        (
            "Body RLP serialize deserialize",
            Body(
                transactions=[
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdef",
                        timestamp=1234567890,
                        fuel=1000000,
                        op_data=OperationCreateContract(
                            op_type=OperationType.CREATE_CONTRACT,
                            contract_hex_bytecode="0x1234567890abcdef",
                            constructor_parameters=[1, 2, 3],
                            contract_source_url="https://example.com/contract",
                            git_commit_hash="1234567890abcdef",
                            reproducible_build=True,
                            upgradable=True,
                        ),
                        public_keys=["0x1234567890abcdef"],
                        signatures=["0x1234567890abcdef"],
                    ),
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdea",
                        timestamp=1234567890,
                        fuel=1000000,
                        op_data=OperationCallContract(
                            op_type=OperationType.CALL_CONTRACT,
                            contract_address="0x1234567890abcdea",
                            function_name="foo",
                            parameters=[1, 2, 3],
                        ),
                        public_keys=["0x1234567890abcdea"],
                        signatures=["0x1234567890abcdea"],
                    ),
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdeb",
                        timestamp=1234567890,
                        fuel=1000000,
                        op_data=OperationForkContract(
                            op_type=OperationType.FORK_CONTRACT,
                            contract_code_hash="0x1234567890abcdeb",
                            constructor_parameters=[1, 2, 3],
                            contract_source_url="https://example.com/contract",
                            upgradable=True,
                            git_commit_hash="1234567890abcdef",
                            reproducible_build=True,
                        ),
                        public_keys=["0x1234567890abcdeb"],
                        signatures=["0x1234567890abcdeb"],
                    ),
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdec",
                        timestamp=1234567890,
                        fuel=1000000,
                        op_data=OperationUpgradeContract(
                            op_type=OperationType.UPGRADE_CONTRACT,
                            contract_address="0x1234567890abcdec",
                            contract_hex_bytecode="0x1234567890abcdec",
                            contract_source_url="https://example.com/contract",
                            git_commit_hash="1234567890abcdef",
                            reproducible_build=True,
                        ),
                        public_keys=["0x1234567890abcdec"],
                        signatures=["0x1234567890abcdec"],
                    ),
                ]
            ),
        )
    ],
)
def test_body_rlp_serialize_deserialize(name, testData):
    encoded = encodeRLP(testData)
    decoded = decodeRLP(Body, encoded)
    assert decoded == testData, f"Test case({name}) failed."


@pytest.mark.parametrize(
    "name, test_data, expected",
    [
        (
            "SPOS Block with transactions",
            Block(
                header=SPOSHeader(
                    parent_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                    height=0,
                    state_root="0x1234567890123456789012345678901234567890123456789012345678901234",
                    transactions_root="0x1234567890123456789012345678901234567890123456789012345678901234",
                    receipts_root="0x1234567890123456789012345678901234567890123456789012345678901234",
                    bloom=BloomFilter(),
                    local_timestamp=0,
                    protocol_timestamp=0,
                    slot_id=0,
                    proposer_address=hexToBytes("0x1234567890abcdef"),
                    public_keys=["0x1234567890abcdef"],
                    signatures=["0x1234567890abcdef"],
                ),
                transactions=[
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdef",
                        timestamp=1234567890,
                        fuel=1000000,
                        op_data=OperationCreateContract(
                            op_type=OperationType.CREATE_CONTRACT,
                            contract_hex_bytecode="0x1234567890abcdef",
                            constructor_parameters=[1, 2, 3],
                            contract_source_url="https://example.com/contract",
                            git_commit_hash="1234567890abcdef",
                            reproducible_build=True,
                            upgradable=True,
                        ),
                        public_keys=["0x1234567890abcdef"],
                        signatures=["0x1234567890abcdef"],
                    ),
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdef",
                        timestamp=0,
                        fuel=1000000,
                        op_data=OperationCallContract(
                            op_type=OperationType.CALL_CONTRACT,
                            contract_address="0x1234567890abcdef",
                            function_name="function",
                            parameters=["param1", "param2"],
                        ),
                        public_keys=["0x1234567890abcdef"],
                        signatures=["0x1234567890abcdef"],
                    ),
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdeb",
                        timestamp=1234567890,
                        fuel=1000000,
                        op_data=OperationForkContract(
                            op_type=OperationType.FORK_CONTRACT,
                            contract_code_hash="0x1234567890abcdeb",
                            constructor_parameters=[1, 2, 3],
                            contract_source_url="https://example.com/contract",
                            upgradable=True,
                            git_commit_hash="1234567890abcdef",
                            reproducible_build=True,
                        ),
                        public_keys=["0x1234567890abcdeb"],
                        signatures=["0x1234567890abcdeb"],
                    ),
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdec",
                        timestamp=1234567890,
                        fuel=1000000,
                        op_data=OperationUpgradeContract(
                            op_type=OperationType.UPGRADE_CONTRACT,
                            contract_address="0x1234567890abcdec",
                            contract_hex_bytecode="0x1234567890abcdec",
                            contract_source_url="https://example.com/contract",
                            git_commit_hash="1234567890abcdef",
                            reproducible_build=True,
                        ),
                        public_keys=["0x1234567890abcdec"],
                        signatures=["0x1234567890abcdec"],
                    ),
                ],
            ),
            toJson(
                {
                    "header": {
                        "parent_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "height": 0,
                        "state_root": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "transactions_root": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "receipts_root": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "bloom": EMPTY_BLOOM_HEX,
                        "local_timestamp": 0,
                        "protocol_timestamp": 0,
                        "slot_id": 0,
                        "proposer_address": "0x1234567890abcdef",
                        "public_keys": ["0x1234567890abcdef"],
                        "signatures": ["0x1234567890abcdef"],
                    },
                    "transactions": [
                        {
                            "sender": "0x1234567890abcdef",
                            "timestamp": 1234567890,
                            "dependent_transaction_hash": "",
                            "fuel": 1000000,
                            "op_data": {
                                "op_type": OperationType.CREATE_CONTRACT,
                                "contract_hex_bytecode": "0x1234567890abcdef",
                                "constructor_parameters": [1, 2, 3],
                                "contract_source_url": "https://example.com/contract",
                                "git_commit_hash": "1234567890abcdef",
                                "reproducible_build": True,
                                "upgradable": True,
                            },
                            "public_keys": ["0x1234567890abcdef"],
                            "signatures": ["0x1234567890abcdef"],
                        },
                        {
                            "sender": "0x1234567890abcdef",
                            "timestamp": 0,
                            "dependent_transaction_hash": "",
                            "fuel": 1000000,
                            "op_data": {
                                "op_type": OperationType.CALL_CONTRACT,
                                "contract_address": "0x1234567890abcdef",
                                "function_name": "function",
                                "parameters": ["param1", "param2"],
                            },
                            "public_keys": ["0x1234567890abcdef"],
                            "signatures": ["0x1234567890abcdef"],
                        },
                        {
                            "sender": "0x1234567890abcdeb",
                            "timestamp": 1234567890,
                            "dependent_transaction_hash": "",
                            "fuel": 1000000,
                            "op_data": {
                                "op_type": OperationType.FORK_CONTRACT,
                                "contract_code_hash": "0x1234567890abcdeb",
                                "constructor_parameters": [1, 2, 3],
                                "contract_source_url": "https://example.com/contract",
                                "upgradable": True,
                                "git_commit_hash": "1234567890abcdef",
                                "reproducible_build": True,
                            },
                            "public_keys": ["0x1234567890abcdeb"],
                            "signatures": ["0x1234567890abcdeb"],
                        },
                        {
                            "sender": "0x1234567890abcdec",
                            "timestamp": 1234567890,
                            "dependent_transaction_hash": "",
                            "fuel": 1000000,
                            "op_data": {
                                "op_type": OperationType.UPGRADE_CONTRACT,
                                "contract_address": "0x1234567890abcdec",
                                "contract_hex_bytecode": "0x1234567890abcdec",
                                "contract_source_url": "https://example.com/contract",
                                "git_commit_hash": "1234567890abcdef",
                                "reproducible_build": True,
                            },
                            "public_keys": ["0x1234567890abcdec"],
                            "signatures": ["0x1234567890abcdec"],
                        },
                    ],
                }
            ),
        ),
        (
            "POW Block with transactions",
            Block(
                header=POWHeader(
                    parent_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                    height=0,
                    state_root="0x1234567890123456789012345678901234567890123456789012345678901234",
                    transactions_root="0x1234567890123456789012345678901234567890123456789012345678901234",
                    receipts_root="0x1234567890123456789012345678901234567890123456789012345678901234",
                    bloom=BloomFilter(),
                    difficulty_score=0,
                    difficulty_score_overall=0,
                    timestamp=0,
                    nonce=0,
                    multiplier=0,
                ),
                transactions=[
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdef",
                        timestamp=1234567890,
                        fuel=1000000,
                        op_data=OperationCreateContract(
                            op_type=OperationType.CREATE_CONTRACT,
                            contract_hex_bytecode="0x1234567890abcdef",
                            constructor_parameters=[1, 2, 3],
                            contract_source_url="https://example.com/contract",
                            git_commit_hash="1234567890abcdef",
                            reproducible_build=True,
                            upgradable=True,
                        ),
                        public_keys=["0x1234567890abcdef"],
                        signatures=["0x1234567890abcdef"],
                    ),
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdef",
                        timestamp=0,
                        fuel=1000000,
                        op_data=OperationCallContract(
                            op_type=OperationType.CALL_CONTRACT,
                            contract_address="0x1234567890abcdef",
                            function_name="function",
                            parameters=["param1", "param2"],
                        ),
                        public_keys=["0x1234567890abcdef"],
                        signatures=["0x1234567890abcdef"],
                    ),
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdeb",
                        timestamp=1234567890,
                        fuel=1000000,
                        op_data=OperationForkContract(
                            op_type=OperationType.FORK_CONTRACT,
                            contract_code_hash="0x1234567890abcdeb",
                            constructor_parameters=[1, 2, 3],
                            contract_source_url="https://example.com/contract",
                            upgradable=True,
                            git_commit_hash="1234567890abcdef",
                            reproducible_build=True,
                        ),
                        public_keys=["0x1234567890abcdeb"],
                        signatures=["0x1234567890abcdeb"],
                    ),
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdec",
                        timestamp=1234567890,
                        fuel=1000000,
                        op_data=OperationUpgradeContract(
                            op_type=OperationType.UPGRADE_CONTRACT,
                            contract_address="0x1234567890abcdec",
                            contract_hex_bytecode="0x1234567890abcdec",
                            contract_source_url="https://example.com/contract",
                            git_commit_hash="1234567890abcdef",
                            reproducible_build=True,
                        ),
                        public_keys=["0x1234567890abcdec"],
                        signatures=["0x1234567890abcdec"],
                    ),
                ],
            ),
            toJson(
                {
                    "header": {
                        "parent_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "height": 0,
                        "state_root": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "transactions_root": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "receipts_root": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "bloom": EMPTY_BLOOM_HEX,
                        "difficulty_score": 0,
                        "difficulty_score_overall": 0,
                        "timestamp": 0,
                        "nonce": 0,
                        "multiplier": 0,
                    },
                    "transactions": [
                        {
                            "sender": "0x1234567890abcdef",
                            "timestamp": 1234567890,
                            "dependent_transaction_hash": "",
                            "fuel": 1000000,
                            "op_data": {
                                "op_type": OperationType.CREATE_CONTRACT,
                                "contract_hex_bytecode": "0x1234567890abcdef",
                                "constructor_parameters": [1, 2, 3],
                                "contract_source_url": "https://example.com/contract",
                                "git_commit_hash": "1234567890abcdef",
                                "reproducible_build": True,
                                "upgradable": True,
                            },
                            "public_keys": ["0x1234567890abcdef"],
                            "signatures": ["0x1234567890abcdef"],
                        },
                        {
                            "sender": "0x1234567890abcdef",
                            "timestamp": 0,
                            "dependent_transaction_hash": "",
                            "fuel": 1000000,
                            "op_data": {
                                "op_type": OperationType.CALL_CONTRACT,
                                "contract_address": "0x1234567890abcdef",
                                "function_name": "function",
                                "parameters": ["param1", "param2"],
                            },
                            "public_keys": ["0x1234567890abcdef"],
                            "signatures": ["0x1234567890abcdef"],
                        },
                        {
                            "sender": "0x1234567890abcdeb",
                            "timestamp": 1234567890,
                            "dependent_transaction_hash": "",
                            "fuel": 1000000,
                            "op_data": {
                                "op_type": OperationType.FORK_CONTRACT,
                                "contract_code_hash": "0x1234567890abcdeb",
                                "constructor_parameters": [1, 2, 3],
                                "contract_source_url": "https://example.com/contract",
                                "upgradable": True,
                                "git_commit_hash": "1234567890abcdef",
                                "reproducible_build": True,
                            },
                            "public_keys": ["0x1234567890abcdeb"],
                            "signatures": ["0x1234567890abcdeb"],
                        },
                        {
                            "sender": "0x1234567890abcdec",
                            "timestamp": 1234567890,
                            "dependent_transaction_hash": "",
                            "fuel": 1000000,
                            "op_data": {
                                "op_type": OperationType.UPGRADE_CONTRACT,
                                "contract_address": "0x1234567890abcdec",
                                "contract_hex_bytecode": "0x1234567890abcdec",
                                "contract_source_url": "https://example.com/contract",
                                "git_commit_hash": "1234567890abcdef",
                                "reproducible_build": True,
                            },
                            "public_keys": ["0x1234567890abcdec"],
                            "signatures": ["0x1234567890abcdec"],
                        },
                    ],
                }
            ),
        ),
    ],
)
def test_block_json_serialize(name, test_data, expected):
    assert toJson(test_data) == expected, f"Test case({name}) failed."


@pytest.mark.parametrize(
    "name, testData, expected",
    [
        (
            "SPOS Block with transactions",
            toJson(
                {
                    "header": {
                        "parent_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "height": 0,
                        "state_root": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "transactions_root": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "receipts_root": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "bloom": EMPTY_BLOOM_HEX,
                        "local_timestamp": 0,
                        "protocol_timestamp": 0,
                        "slot_id": 0,
                        "proposer_address": "0x1234567890abcdef",
                        "public_keys": ["0x1234567890abcdef"],
                        "signatures": ["0x1234567890abcdef"],
                    },
                    "transactions": [
                        {
                            "sender": "0x1234567890abcdef",
                            "timestamp": 1234567890,
                            "dependent_transaction_hash": "",
                            "fuel": 1000000,
                            "op_data": {
                                "op_type": OperationType.CREATE_CONTRACT,
                                "contract_hex_bytecode": "0x1234567890abcdef",
                                "constructor_parameters": [1, 2, 3],
                                "contract_source_url": "https://example.com/contract",
                                "git_commit_hash": "1234567890abcdef",
                                "reproducible_build": True,
                                "upgradable": True,
                            },
                            "public_keys": ["0x1234567890abcdef"],
                            "signatures": ["0x1234567890abcdef"],
                        },
                        {
                            "sender": "0x1234567890abcdef",
                            "timestamp": 0,
                            "dependent_transaction_hash": "",
                            "fuel": 1000000,
                            "op_data": {
                                "op_type": OperationType.CALL_CONTRACT,
                                "contract_address": "0x1234567890abcdef",
                                "function_name": "function",
                                "parameters": ["param1", "param2"],
                            },
                            "public_keys": ["0x1234567890abcdef"],
                            "signatures": ["0x1234567890abcdef"],
                        },
                    ],
                }
            ),
            Block(
                header=SPOSHeader(
                    parent_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                    height=0,
                    state_root="0x1234567890123456789012345678901234567890123456789012345678901234",
                    transactions_root="0x1234567890123456789012345678901234567890123456789012345678901234",
                    receipts_root="0x1234567890123456789012345678901234567890123456789012345678901234",
                    bloom=BloomFilter(),
                    local_timestamp=0,
                    protocol_timestamp=0,
                    slot_id=0,
                    proposer_address=hexToBytes("0x1234567890abcdef"),
                    public_keys=["0x1234567890abcdef"],
                    signatures=["0x1234567890abcdef"],
                ),
                transactions=[
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdef",
                        timestamp=1234567890,
                        fuel=1000000,
                        op_data=OperationCreateContract(
                            op_type=OperationType.CREATE_CONTRACT,
                            contract_hex_bytecode="0x1234567890abcdef",
                            constructor_parameters=[1, 2, 3],
                            contract_source_url="https://example.com/contract",
                            git_commit_hash="1234567890abcdef",
                            reproducible_build=True,
                            upgradable=True,
                        ),
                        public_keys=["0x1234567890abcdef"],
                        signatures=["0x1234567890abcdef"],
                    ),
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdef",
                        timestamp=0,
                        fuel=1000000,
                        op_data=OperationCallContract(
                            op_type=OperationType.CALL_CONTRACT,
                            contract_address="0x1234567890abcdef",
                            function_name="function",
                            parameters=["param1", "param2"],
                        ),
                        public_keys=["0x1234567890abcdef"],
                        signatures=["0x1234567890abcdef"],
                    ),
                ],
            ),
        ),
        (
            "POW Block with transactions",
            toJson(
                {
                    "header": {
                        "parent_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "height": 0,
                        "state_root": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "transactions_root": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "receipts_root": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "bloom": EMPTY_BLOOM_HEX,
                        "difficulty_score": 0,
                        "difficulty_score_overall": 0,
                        "timestamp": 0,
                        "nonce": 0,
                        "multiplier": 0,
                    },
                    "transactions": [
                        {
                            "sender": "0x1234567890abcdef",
                            "timestamp": 1234567890,
                            "dependent_transaction_hash": "",
                            "fuel": 1000000,
                            "op_data": {
                                "op_type": OperationType.CREATE_CONTRACT,
                                "contract_hex_bytecode": "0x1234567890abcdef",
                                "constructor_parameters": [1, 2, 3],
                                "contract_source_url": "https://example.com/contract",
                                "git_commit_hash": "1234567890abcdef",
                                "reproducible_build": True,
                                "upgradable": True,
                            },
                            "public_keys": ["0x1234567890abcdef"],
                            "signatures": ["0x1234567890abcdef"],
                        },
                        {
                            "sender": "0x1234567890abcdef",
                            "timestamp": 0,
                            "dependent_transaction_hash": "",
                            "fuel": 1000000,
                            "op_data": {
                                "op_type": OperationType.CALL_CONTRACT,
                                "contract_address": "0x1234567890abcdef",
                                "function_name": "function",
                                "parameters": ["param1", "param2"],
                            },
                            "public_keys": ["0x1234567890abcdef"],
                            "signatures": ["0x1234567890abcdef"],
                        },
                    ],
                }
            ),
            Block(
                header=POWHeader(
                    parent_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
                    height=0,
                    state_root="0x1234567890123456789012345678901234567890123456789012345678901234",
                    transactions_root="0x1234567890123456789012345678901234567890123456789012345678901234",
                    receipts_root="0x1234567890123456789012345678901234567890123456789012345678901234",
                    bloom=BloomFilter(),
                    difficulty_score=0,
                    difficulty_score_overall=0,
                    timestamp=0,
                    nonce=0,
                    multiplier=0,
                ),
                transactions=[
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdef",
                        timestamp=1234567890,
                        fuel=1000000,
                        op_data=OperationCreateContract(
                            op_type=OperationType.CREATE_CONTRACT,
                            contract_hex_bytecode="0x1234567890abcdef",
                            constructor_parameters=[1, 2, 3],
                            contract_source_url="https://example.com/contract",
                            git_commit_hash="1234567890abcdef",
                            reproducible_build=True,
                            upgradable=True,
                        ),
                        public_keys=["0x1234567890abcdef"],
                        signatures=["0x1234567890abcdef"],
                    ),
                    Transaction(
                        dependent_transaction_hash="",
                        sender="0x1234567890abcdef",
                        timestamp=0,
                        fuel=1000000,
                        op_data=OperationCallContract(
                            op_type=OperationType.CALL_CONTRACT,
                            contract_address="0x1234567890abcdef",
                            function_name="function",
                            parameters=["param1", "param2"],
                        ),
                        public_keys=["0x1234567890abcdef"],
                        signatures=["0x1234567890abcdef"],
                    ),
                ],
            ),
        ),
    ],
)
def test_block_json_deserialize(name, testData, expected):
    assert fromJson(Block, testData) == expected, f"Test case({name}) failed."
