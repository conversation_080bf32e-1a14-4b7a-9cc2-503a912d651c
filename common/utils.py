from hashlib import new as hashlib_new
from hashlib import sha256
from typing import AnyStr, Optional

from Crypto.Hash import keccak

from .encode import bytesToHex, hexToBytes

# ----- hash functions -----


def keccak256(message: AnyStr) -> str:
    """
    return the keccak 256 hash of the message
    """
    if not isinstance(message, bytes):
        message = message.encode()
    keccak_hash = keccak.new(digest_bits=256)
    keccak_hash.update(message)
    return keccak_hash.hexdigest()


def doubleSha256(message: bytes) -> bytes:
    """
    return the double sha256 hash of the message
    """
    return sha256(sha256(message).digest()).digest()


def doubleSha256Hex(message: AnyStr) -> str:
    """
    return the hex hash of the message
    """
    if not isinstance(message, bytes):
        message = message.encode()
    return sha256(sha256(message).digest()).digest().hex()


def publicKeyToAddress(publicKey: str) -> Optional[str]:
    """
    Generate the address from the given public key.

    Args:
        publicKey: A string representing the public key

    Returns:
        Optional[str]: The generated address, or None if the public key is invalid

    Note: This is a temporary implementation. The real address generation needs to be implemented.
    The current implementation follows these steps:
    1. Perform SHA-256 hash on the public key
    2. Perform RIPEMD-160 hashing on the result of SHA-256
    3. Add network byte (0x00 for VGraph mainnet)
    4. Add checksum to the result of step 3
    5. Encode the result of step 4 with base58 encoding
    """
    try:
        publicKeyBytes = hexToBytes(publicKey)
    except Exception:
        return None

    addressSha256 = sha256(publicKeyBytes).digest()
    addressRipemd160 = hashlib_new("ripemd160", addressSha256).digest()
    networkByte = b"\x00"

    checksum = sha256(sha256(networkByte + addressRipemd160).digest()).digest()[:4]

    address = bytesToHex(addressRipemd160 + networkByte + checksum)
    return address


# ----- misc -----


def getRequiredKey(d: dict, key: str):
    """
    return the value of the key in the dictionary
    if the key is not found, raise an exception
    """
    if key not in d:
        raise Exception(f"Key {key} not found in the dictionary")
    return d[key]
