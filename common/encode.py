from __future__ import annotations

import io
from abc import ABC, abstractmethod
from copy import deepcopy
from typing import Any, Type, TypeVar

import orjson
import rlp
from rlp.sedes import big_endian_int
from serde.json import from_json, to_json

from .errors import SerializeError


class Model(ABC):
    @abstractmethod
    def toRLP(self) -> RLPModel:
        raise NotImplementedError

    @staticmethod
    @abstractmethod
    def getRLPClass() -> Type[RLPModel]:
        raise NotImplementedError


class RLPModel(ABC, rlp.Serializable):
    @abstractmethod
    def toModel(self) -> Model:
        raise NotImplementedError


def bytesToHex(b: bytes) -> str:
    if len(b) == 0:
        return ""
    return "0x" + b.hex()


def hexToBytes(s: str) -> bytes:
    if s.startswith("0x"):
        s = s[2:]

    return bytes.fromhex(s)


def jsonToBytes(s: str) -> bytes:
    return s.encode()


def bytesToJson(b: bytes) -> str:
    return b.decode()


# global serialize methods


def encodeRLP(model: Model) -> bytes:
    try:
        return rlp.encode(model.toRLP())
    except Exception as e:
        raise SerializeError("Failed to rlp encode") from e


M = TypeVar("M", bound=Model)


def decodeRLP(modelType: Type[M], rlpBytes: bytes) -> M:
    try:
        return rlp.decode(rlpBytes, modelType.getRLPClass()).toModel()
    except Exception as e:
        raise SerializeError("Failed to rlp decode") from e


def toJson(obj, **opts: Any) -> str:
    try:
        return to_json(obj, option=orjson.OPT_SORT_KEYS, **opts)
    except Exception as e:
        raise SerializeError("Invalid data") from e


T = TypeVar("T")


def fromJson(cls: Type[T], data: str) -> T:
    try:
        return from_json(cls, data)
    except Exception as e:
        raise SerializeError("Invalid data") from e


def encodeUint64(value: int) -> bytes:
    if value < 0 or value >= 2**64:
        raise ValueError("Value out of range for uint64")
    return rlp.encode(value, sedes=big_endian_int)


class TrieHasher(ABC):
    @abstractmethod
    def update(self, index: bytes, value: bytes) -> None:
        raise NotImplementedError

    @abstractmethod
    def hash(self) -> bytes:
        raise NotImplementedError

    @abstractmethod
    def reset(self) -> None:
        raise NotImplementedError


class DerivableList(ABC):
    @abstractmethod
    def encodeIndex(self, i: int, w: io.BytesIO) -> None:
        raise NotImplementedError

    @abstractmethod
    def __len__(self):
        raise NotImplementedError


def encodeForDerive(list: DerivableList, i: int, buf: io.BytesIO) -> bytes:
    buf.truncate(0)  # reset buffer
    list.encodeIndex(i, buf)
    # copy the bytes
    return deepcopy(buf.getvalue())


def deriveSha(list: DerivableList, hasher: TrieHasher) -> bytes:
    """
    DeriveSha creates the tree hashes of transactions, receipts in a block header.
    return a hash of the tree
    """
    hasher.reset()
    # TODO: refactor to stack trie
    # TODO: add encode buffer pool
    valueBuf = io.BytesIO()

    # requires values to be inserted in increasing hash order, which is not the order that `list` provides hashes in.
    # This insertion sequence ensures that the order is correct.
    for i in range(1, min(len(list), 0x7F + 1)):
        indexBuf = encodeUint64(i)
        value = encodeForDerive(list, i, valueBuf)
        hasher.update(indexBuf, value)

    if len(list) > 0:
        indexBuf = encodeUint64(0)
        value = encodeForDerive(list, 0, valueBuf)
        hasher.update(indexBuf, value)

    for i in range(0x80, len(list)):
        indexBuf = encodeUint64(i)
        value = encodeForDerive(list, i, valueBuf)
        hasher.update(indexBuf, value)

    return hasher.hash()
