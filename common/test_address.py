from .address import Ad<PERSON><PERSON><PERSON><PERSON>, bytes<PERSON><PERSON><PERSON><PERSON><PERSON>, hex<PERSON><PERSON><PERSON><PERSON><PERSON>, intTo<PERSON>ddress
from .encode import hexTo<PERSON><PERSON>s


def test_bytes_to_address_short_input():
    address = b"\x01"
    expected = b"\x00" * (AddressLength - 1) + b"\x01"
    assert bytesToAddress(address) == expected


def test_bytes_to_address_exact_input():
    address = b"\x01" * AddressLength
    expected = address
    assert bytesToAddress(address) == expected


def test_bytes_to_address_long_input():
    address = b"\x01" * 37
    expected = b"\x01" * AddressLength
    assert bytesToAddress(address) == expected


def test_bytes_to_address_empty_input():
    address = b""
    expected = b"\x00" * AddressLength
    assert bytesToAddress(address) == expected


def test_bytes_to_address_full_input():
    address = b"\xff" * AddressLength
    expected = address
    assert bytesToAddress(address) == expected


def test_int_to_address_small_int():
    # Test a small integer (e.g., 1) to ensure it is padded correctly to the address length
    big_int = 1
    expected = b"\x00" * (AddressLength - 1) + b"\x01"
    assert intToAddress(big_int) == expected


def test_int_to_address_zero():
    # Test the zero integer to ensure the output is all zeros of address length
    big_int = 0
    expected = b"\x00" * AddressLength
    assert intToAddress(big_int) == expected


def test_int_to_address_max():
    # Test the maximum integer that fits in AddressLength bytes
    big_int = (1 << (8 * AddressLength)) - 1  # Largest 20-byte integer
    expected = b"\xff" * AddressLength
    assert intToAddress(big_int) == expected


def test_int_to_address_large_number():
    # Test a large integer, specifically 256 bits, to ensure it fits the address size
    big_int = 2**256 - 1
    expected = b"\xff" * AddressLength
    assert intToAddress(big_int) == expected


def test_hex_to_address_short_hex():
    # Test a short hex string to ensure it is padded correctly to the address length
    hex_str = "aaaa"
    expected = b"\x00" * (AddressLength - 2) + b"\xaa\xaa"
    assert hexToAddress(hex_str) == expected


def test_hex_to_address_exact_hex():
    # Test a hex string that exactly converts to the address length
    hex_str = "a" * 64  # 64 hex digits = 32 bytes
    expected = hexToBytes(hex_str)
    assert hexToAddress(hex_str) == expected


def test_hex_to_address_long_hex():
    # Test a hex string that would result in a byte sequence longer than the address length
    hex_str = "a" * 66  # 66 hex digits = 33 bytes
    expected = hexToBytes(hex_str[:64])  # Should truncate to first 64 hex digits
    assert hexToAddress(hex_str) == expected


def test_hex_to_address_empty_hex():
    # Test an empty hex string to ensure it returns an address of all zeros
    hex_str = ""
    expected = b"\x00" * AddressLength
    assert hexToAddress(hex_str) == expected


def test_hex_to_address_full_zeros():
    # Test a hex string of all zeros to ensure correct padding and conversion
    hex_str = "0" * 64  # 64 zeros, which is exactly 32 bytes of zeros
    expected = b"\x00" * AddressLength
    assert hexToAddress(hex_str) == expected


def test_hex_to_address_with_0x_prefix():
    # Test a hex string with '0x' prefix to ensure it's handled correctly
    hex_str = "0x" + "b" * 64
    expected = b"\xbb" * AddressLength
    assert hexToAddress(hex_str) == expected
