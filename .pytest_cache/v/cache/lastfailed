{"contract/base/tests/test_unified_interface.py::test_unified_delete_many_order": true, "contract/base/tests/test_unified_interface.py::test_unified_insert_many_user_service_with_duplicates": true, "contract/base/tests/test_unified_interface.py::test_unified_insert_many_order_with_duplicates": true, "contract/base/tests/test_unified_interface.py::test_unified_string_length_limits": true, "contract/base/tests/test_unified_interface.py::test_unified_insert_user_service_with_complex_service_options": true, "contract/base/tests/test_unified_interface.py::test_unified_memory_efficiency_large_objects": true, "contract/base/tests/test_unified_interface.py::test_unified_transaction_like_behavior": true}