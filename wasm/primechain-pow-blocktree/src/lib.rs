#[glue::contract]
mod primechain_pow_blocktree {
    use anyhow::anyhow;
    use serde::{Deserialize, Serialize};
    use serde_json::Value;
    use sha3::Digest;
    use sha2::Sha256;

    pub const ROOT_PARENT_ID: &str = "00000000-0000-0000-0000-000000000000";
    pub const MAX_LOCATOR_LENGTH: usize = 32;

    // ------- blocktree -------
    #[glue::storage_item]
    pub struct Block {
        pub id: String,
        pub parent_id: String,
        pub difficulty_score: i64,
        pub difficulty_score_overall: i64,
        pub height: i64,
        pub transactions: String,
        pub merkle: String,
        pub timestamp: i64,
        pub nonce: i64,
        pub multiplier: i64,
    }

    #[derive(Debug, Serialize, Deserialize)]
    pub struct BlockHeader {
        pub parent_id: String,
        pub difficulty_score: i64,
        pub difficulty_score_overall: i64,
        pub height: i64,
        pub merkle: String,
        pub timestamp: i64,
        pub nonce: i64,
        pub multiplier: i64,
    }

    #[derive(Debug, Serialize, Deserialize)]
    pub struct ReorganizationPath {
        pub roll_back_path: Vec<String>,
        pub common_ancestor: String,
        pub roll_forward_path: Vec<String>,
    }

    #[glue::storage]
    pub struct BlockTree {
        pub best_block_id: glue::StorageField<String>, // best block id
        pub genesis_block_id: glue::StorageField<String>, // genesis block id
        pub blocktree_map: glue::collections::Map<String, Block>, // block id -> block
    }

    impl BlockTree {
        #[glue::constructor]
        pub fn new() -> Self {
            Self {
                best_block_id: glue::StorageField::new(&"".to_string()),
                genesis_block_id: glue::StorageField::new(&"".to_string()),
                blocktree_map: glue::collections::Map::default(),
            }
        }

        // ------ wasm ------
        // ---------- readonly ----------
        #[glue::readonly]
        pub fn get_block_template(&self) -> anyhow::Result<Block> {
            match self.get_current_best_block()? {
                Some(best_block) => {
                    let mut block = Block {
                        id: "".to_string(),
                        parent_id: best_block.id.clone(),
                        difficulty_score: 0,
                        difficulty_score_overall: best_block.difficulty_score_overall,
                        height: best_block.height + 1,
                        transactions: "[]".to_string(),
                        merkle: _keccak256("[]".to_string()),
                        timestamp: 0,
                        nonce: 0,
                        multiplier: 0,
                    };
                    // get hash and set block id
                    block.id = self.get_block_hash(block.clone())?;
                    Ok(block)
                },
                None => {
                    let mut block = Block {
                        id: "".to_string(),
                        parent_id: ROOT_PARENT_ID.to_string(),
                        difficulty_score: 0,
                        difficulty_score_overall: 0,
                        height: 1,
                        transactions: "[]".to_string(),
                        merkle: _keccak256("[]".to_string()),
                        timestamp: 0,
                        nonce: 0,
                        multiplier: 0,
                    };
                    // get hash and set block id
                    block.id = self.get_block_hash(block.clone())?;
                    Ok(block)
                },
            }
        }

        #[glue::readonly]
        pub fn get_current_best_block(&self) -> anyhow::Result<Option<Block>> {
            let block_id = self.best_block_id.get().clone();
            match self.blocktree_map.get(&block_id) {
                Some(block) => Ok(Some(block.clone())),
                None => Ok(None),
            }
        }

        #[glue::readonly]
        pub fn get_block(&self, block_id: String) -> anyhow::Result<Option<Block>> {
            match self.blocktree_map.get(&block_id) {
                Some(block) => Ok(Some(block.clone())),
                None => Ok(None),
            }
        }

        #[glue::readonly]
        pub fn get_blocks(&self, locator: Vec<String>) -> anyhow::Result<Vec<Block>> {
            let best_block = match self.get_current_best_block()? {
                Some(block) => block,
                None => return Ok(vec![]),
            };

            // find common ancestor
            let mut common_ancestor_id = String::new();
            for locator_block_id in locator {
                match self.get_block(locator_block_id.clone()) {
                    Ok(Some(locator_block)) => {
                        if locator_block.height < best_block.height {
                            common_ancestor_id = locator_block_id;
                            break;
                        }
                    }
                    _ => {}
                }
            }

            // if common ancestor is not found, set it to genesis block
            let common_ancestor = match common_ancestor_id.is_empty() {
                true => match self.get_block(self.genesis_block_id.get().clone())? {
                    Some(block) => block,
                    None => return Err(anyhow!("Failed to get genesis block")),
                },
                false => match self.get_block(common_ancestor_id.clone())? {
                    Some(block) => block,
                    None => return Err(anyhow!("Failed to get common ancestor")),
                },
            };

            // find and return path from common ancestor to this node's best block
            let mut path = vec![];
            let mut current_block_id = best_block.id.clone();
            while current_block_id != common_ancestor.id {
                match self.get_block(current_block_id.clone()) {
                    Ok(Some(block)) => {
                        path.push(block.clone());
                        current_block_id = block.parent_id.clone();
                    }
                    _ => return Err(anyhow!("Failed to get block")),
                }
            }

            path.reverse();
            Ok(path)
        }

        #[glue::readonly]
        pub fn validate_block(&self, block: Block) -> anyhow::Result<()> {
            // ----- block -----
            // validation: trivial validation
            if block.id.is_empty() || block.id == ROOT_PARENT_ID {
                return Err(anyhow!("Block id is empty"));
            }
            if block.height <= 0 {
                return Err(anyhow!("Block height is invalid."));
            }
            if block.difficulty_score <= 0 {
                return Err(anyhow!("Block difficulty score is invalid."));
            }
            if block.difficulty_score_overall <= 0 {
                return Err(anyhow!("Block overall difficulty score is invalid."));
            }
            // validation: block hash
            if !self.validate_block_hash(block.clone())? {
                return Err(anyhow!("Block hash is invalid."));
            }

            // validation: parent block
            // TODO: when use block hash, we need to validate the parent block hash
            if block.parent_id == ROOT_PARENT_ID {
                if block.height != 1 {
                    return Err(anyhow!("Genesis block height should be 1."));
                }
            } else {
                match self.get_block(block.parent_id.clone()) {
                    Ok(Some(parent_block)) => {
                        if parent_block.height + 1 != block.height {
                            return Err(anyhow!("Block height is invalid."));
                        }
                        // validate the parent block hash
                        if !self.validate_block_hash(parent_block.clone())? {
                            return Err(anyhow!("Parent block hash is invalid."));
                        }
                    }
                    _ => {}
                }
            }

            // ----- transactions -----
            // validation: merkle root
            if _keccak256(block.transactions.clone()) != block.merkle {
                return Err(anyhow!("Merkle root is invalid."));
            }
            // TODO: skip empty transaction validation, because we don't have Coinbase now
            // let transactions = block.transactions.clone();
            // let transactions: Vec<Transaction> = serde_json::from_str(&transactions)?;
            // if transactions.is_empty() {
            //     return Err(anyhow!("Transactions are empty."));
            // }

            Ok(())
        }

        #[glue::readonly]
        pub fn generate_locator(&self) -> anyhow::Result<Vec<String>> {
            if self.best_block_id.get().is_empty() {
                return Ok(vec![]);
            } else {
                // start from the best block
                let mut current_block_id = self.best_block_id.get().clone();

                let mut locator = vec![];
                let mut step = 1;
                let mut counter = 1;

                while current_block_id != ROOT_PARENT_ID && counter < MAX_LOCATOR_LENGTH {
                    locator.push(current_block_id.clone());

                    for _ in 0..step {
                        current_block_id = match self.get_block(current_block_id.clone()) {
                            Ok(Some(block)) => block.parent_id.clone(),
                            _ => break,
                        };
                    }

                    if counter >= 10 {
                        // double steps, exponential growth
                        step *= 2;
                    }
                    counter += 1;
                }

                Ok(locator)
            }
        }

        #[glue::readonly]
        pub fn get_reorganization_path(
            &self,
            old_best_block_id: String,
            new_best_block_id: String,
        ) -> anyhow::Result<ReorganizationPath> {
            if old_best_block_id == new_best_block_id {
                return Ok(ReorganizationPath {
                    roll_back_path: [].to_vec(),
                    common_ancestor: old_best_block_id,
                    roll_forward_path: [].to_vec(),
                });
            }

            let mut block1 = match self.get_block(old_best_block_id.clone()) {
                Ok(Some(block)) => block,
                _ => return Err(anyhow!("Failed to get block")),
            };

            let mut block2 = match self.get_block(new_best_block_id.clone()) {
                Ok(Some(block)) => block,
                _ => return Err(anyhow!("Failed to get block")),
            };

            let mut roll_back_path: Vec<String> = vec![];
            let mut roll_forward_path: Vec<String> = vec![];

            // Move the higher block down to the same height as another block
            while block1.height > block2.height {
                roll_back_path.push(block1.id.clone());
                block1 = match self.get_block(block1.parent_id.clone()) {
                    Ok(Some(block)) => block,
                    _ => return Err(anyhow!("Failed to get block")),
                };
            }

            while block2.height > block1.height {
                roll_forward_path.push(block2.id.clone());
                block2 = match self.get_block(block2.parent_id.clone()) {
                    Ok(Some(block)) => block,
                    _ => return Err(anyhow!("Failed to get block")),
                };
            }

            // Starting at the same height, traverse upwards until you find a common ancestor
            while block1.id != block2.id {
                roll_back_path.push(block1.id.clone());
                roll_forward_path.push(block2.id.clone());

                block1 = match self.get_block(block1.parent_id.clone()) {
                    Ok(Some(block)) => block,
                    _ => return Err(anyhow!("Failed to get block")),
                };

                block2 = match self.get_block(block2.parent_id.clone()) {
                    Ok(Some(block)) => block,
                    _ => return Err(anyhow!("Failed to get block")),
                };
            }

            // reverse roll_forward_path in place
            roll_forward_path.reverse();

            Ok(ReorganizationPath {
                roll_back_path,
                common_ancestor: block1.id.clone(),
                roll_forward_path,
            })
        }

        // ---------- atomic ----------
        #[glue::atomic]
        pub fn save_block_to_disk(&mut self, block: Block) -> anyhow::Result<()> {
            self.blocktree_map.insert(&block.id.clone(), &block.clone());
            Ok(())
        }

        #[glue::atomic]
        pub fn connect_block(&mut self, block: Block, is_genesis: bool) -> anyhow::Result<()> {
            // validate block if it's not genesis block
            if !is_genesis {
                self.validate_block(block.clone())?;
            }

            // update best block
            match self.get_current_best_block()? {
                Some(best_block) => {
                    if block.height > best_block.height {
                        self.best_block_id.set(&block.id.clone());
                    }
                }
                None => {
                    self.best_block_id.set(&block.id.clone());
                }
            }
            if is_genesis {
                self.genesis_block_id.set(&block.id.clone());
            }
            Ok(())
        }

        #[glue::readonly]
        pub fn get_block_hash(&self, _block: Block) -> anyhow::Result<String> {
            // get block header from block
            let _block_header = BlockHeader {
                parent_id: _block.parent_id,
                difficulty_score: _block.difficulty_score,
                difficulty_score_overall: _block.difficulty_score_overall,
                height: _block.height,
                merkle: _block.merkle,
                timestamp: _block.timestamp,
                nonce: _block.nonce,
                multiplier: _block.multiplier,
            };
            let message = serde_json::to_string(&_block_header)?;
            // get sha256 hash
            let mut hasher = Sha256::new();
            hasher.update(message.as_bytes());
            let result = hasher.finalize();
            // get sha256 hash for second time
            let result2 = Sha256::digest(&result);

            let hash = hex::encode(result2);
            Ok(hash)
        }

        #[glue::readonly]
        pub fn validate_block_hash(&self, block: Block) -> anyhow::Result<bool> {
            let mut _block = block.clone();
            _block.id = "".to_string();
            let block_hash = self.get_block_hash(_block)?;
            Ok(block_hash == block.id)
        }
    }

    // ------- transaction & transaction receipt ------
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct Transaction {
        #[serde(skip_serializing_if = "Option::is_none")]
        pub transaction_hash: Option<String>,
        pub from_address: String,
        pub contract_address: String,
        pub package_name: String,
        pub contract_name: String,
        pub struct_name: String,
        pub function_name: String,
        pub parameters: Vec<Value>,
        pub publickeys: Vec<String>,
        pub signatures: Vec<String>,
        pub timestamp: i64,
    }

    #[glue::storage_item]
    pub struct TransactionReceipt {
        pub transaction_hash: String,
        pub transaction_index: i64,
        pub block_id: String,
        pub from_address: String,
        pub package_name: String,
        pub contract_address: String,
        pub contract_name: String,
        pub struct_name: String,
        pub function_name: String,
        pub parameters: Vec<Value>,
        pub publickeys: Vec<String>,
        pub signatures: Vec<String>,
        pub status: bool,
        pub result: Value,
    }

    #[glue::storage]
    pub struct TransactionReceipts {
        pub receipts_map: glue::collections::Map<String, TransactionReceipt>, // transaction hash -> receipt
    }

    impl TransactionReceipts {
        #[glue::constructor]
        pub fn new() -> Self {
            Self {
                receipts_map: glue::collections::Map::default(),
            }
        }

        // ----------read only----------
        #[glue::readonly]
        pub fn get_transaction_receipt(
            &self,
            transaction_hash: String,
        ) -> anyhow::Result<Option<TransactionReceipt>> {
            match self.receipts_map.get(&transaction_hash) {
                Some(receipt) => Ok(Some(receipt.clone())),
                None => Ok(None),
            }
        }

        // ---------- atomic ----------
        #[glue::atomic]
        pub fn create_transaction_receipt(
            &mut self,
            receipt: TransactionReceipt,
        ) -> anyhow::Result<()> {
            self.receipts_map
                .insert(&receipt.transaction_hash.clone(), &receipt.clone());
            Ok(())
        }

        #[glue::atomic]
        pub fn create_transaction_receipts(
            &mut self,
            receipts: Vec<TransactionReceipt>,
        ) -> anyhow::Result<()> {
            for receipt in receipts {
                self.receipts_map
                    .insert(&receipt.transaction_hash.clone(), &receipt.clone());
            }
            Ok(())
        }
    }

    // ------- utils -------
    #[glue::wasm_bind]
    pub fn keccak256(input: String) -> String {
        let mut hasher = sha3::Keccak256::new();
        hasher.update(input.as_bytes());
        let result: String = hasher
            .finalize()
            .iter()
            .map(|b| format!("{:02x}", b))
            .collect();
        return result;
    }
}

#[cfg(test)]
mod test_blocktree {
    use super::*;

    #[glue::test]
    fn test_get_block_template() {
        primechain_pow_blocktree::set_block_tree_instance(
            primechain_pow_blocktree::BlockTree::new(),
        );
        let blocktree = primechain_pow_blocktree::get_block_tree_instance();

        let first_block = blocktree.get_block_template().unwrap();
        assert_eq!(
            first_block.parent_id,
            primechain_pow_blocktree::ROOT_PARENT_ID
        );
        assert_eq!(first_block.height, 1);
        assert_eq!(first_block.difficulty_score, 0);
        assert_eq!(first_block.difficulty_score_overall, 0);
        assert_eq!(first_block.transactions, "[]".to_string());
        assert_eq!(
            first_block.merkle,
            primechain_pow_blocktree::_keccak256("[]".to_string())
        );
        assert_eq!(first_block.timestamp, 0);
        assert_eq!(first_block.nonce, 0);
        assert_eq!(first_block.multiplier, 0);

        // connect this block, then get the block template again
        blocktree.save_block_to_disk(first_block.clone()).unwrap();
        blocktree.connect_block(first_block.clone(), true).unwrap();

        let second_block = blocktree.get_block_template().unwrap();
        assert_eq!(second_block.parent_id, first_block.id);
        assert_eq!(second_block.height, 2);
        assert_eq!(second_block.difficulty_score, 0);
        assert_eq!(second_block.difficulty_score_overall, 0);
        assert_eq!(second_block.transactions, "[]".to_string());
        assert_eq!(
            second_block.merkle,
            primechain_pow_blocktree::_keccak256("[]".to_string())
        );
        assert_eq!(second_block.timestamp, 0);
        assert_eq!(second_block.nonce, 0);
        assert_eq!(second_block.multiplier, 0);
    }

    #[glue::test]
    fn test_get_current_best_block() {
        primechain_pow_blocktree::set_block_tree_instance(
            primechain_pow_blocktree::BlockTree::new(),
        );
        let blocktree = primechain_pow_blocktree::get_block_tree_instance();

        // if there is no best block, it should return None
        let best_block = blocktree.get_current_best_block().unwrap();
        assert_eq!(best_block, None);

        // insert a block and set it as best block
        let block = blocktree.get_block_template().unwrap();
        blocktree.save_block_to_disk(block.clone()).unwrap();
        blocktree.connect_block(block.clone(), true).unwrap();

        let best_block = blocktree.get_current_best_block().unwrap().unwrap();
        assert_eq!(best_block, block);
    }

    #[glue::test]
    fn test_get_block() {
        primechain_pow_blocktree::set_block_tree_instance(
            primechain_pow_blocktree::BlockTree::new(),
        );
        let blocktree = primechain_pow_blocktree::get_block_tree_instance();

        // if there is no block, it should return None
        let block = blocktree.get_block("non-exist".to_string()).unwrap();
        assert_eq!(block, None);

        // insert a block
        let block = blocktree.get_block_template().unwrap();
        blocktree.save_block_to_disk(block.clone()).unwrap();

        let saved_block = blocktree.get_block(block.id.clone()).unwrap().unwrap();
        assert_eq!(block, saved_block);
    }

    #[glue::test]
    fn test_get_blocks() {
        primechain_pow_blocktree::set_block_tree_instance(
            primechain_pow_blocktree::BlockTree::new(),
        );
        let blocktree = primechain_pow_blocktree::get_block_tree_instance();
        // if there is no block, it should return empty vec
        let blocks = blocktree.get_blocks(vec![]).unwrap();
        assert_eq!(blocks, vec![]);

        // connect 5 blocks
        //     1
        //     |
        //     2    <- locator
        //     |
        //     3
        //     |
        //     4
        //     |
        //     5
        let mut blocks = vec![];
        for _ in 0..5 {
            let block = blocktree.get_block_template().unwrap();
            blocktree.save_block_to_disk(block.clone()).unwrap();
            blocktree.connect_block(block.clone(), true).unwrap();
            blocks.push(block);
        }

        // get blocks
        // path should be [2, 3, 4, 5]
        let locator = vec![blocks[0].id.clone(), blocks[1].id.clone()];
        let path = blocktree.get_blocks(locator).unwrap();
        println!("{:?}", path);
        assert_eq!(path.len(), 4);
    }
    
    #[glue::test]
    fn test_validate_block() {
        primechain_pow_blocktree::set_block_tree_instance(
            primechain_pow_blocktree::BlockTree::new(),
        );
        let blocktree = primechain_pow_blocktree::get_block_tree_instance();

        let mut valid_block = blocktree.get_block_template().unwrap();
        valid_block.difficulty_score = 1;
        valid_block.difficulty_score_overall = 1;
        // add one transaction
        let transaction = primechain_pow_blocktree::Transaction {
            transaction_hash: Some("hash".to_string()),
            from_address: "from_address".to_string(),
            contract_address: "contract_address".to_string(),
            package_name: "package_name".to_string(),
            contract_name: "contract_name".to_string(),
            struct_name: "struct_name".to_string(),
            function_name: "function_name".to_string(),
            parameters: vec![],
            publickeys: vec![],
            signatures: vec![],
            timestamp: 0,
        };
        valid_block.transactions = serde_json::to_string(&vec![transaction]).unwrap();
        valid_block.merkle = primechain_pow_blocktree::_keccak256(valid_block.transactions.clone());
        // set block id with block hash
        valid_block.id = "".to_string();
        valid_block.id = blocktree.get_block_hash(valid_block.clone()).unwrap();
        assert!(blocktree.validate_block(valid_block.clone()).is_ok());

        // invalid block: invalid id, invalid height, invalid difficulty score, invalid overall difficulty score
        let mut invalid_block = valid_block.clone();
        invalid_block.id = "".to_string();
        assert!(blocktree.validate_block(invalid_block.clone()).is_err());

        let mut invalid_block = valid_block.clone();
        invalid_block.id = "abcd".to_string();
        assert!(blocktree.validate_block(invalid_block.clone()).is_err());

        invalid_block = valid_block.clone();
        invalid_block.height = 0;
        assert!(blocktree.validate_block(invalid_block.clone()).is_err());

        invalid_block = valid_block.clone();
        invalid_block.difficulty_score = 0;
        assert!(blocktree.validate_block(invalid_block.clone()).is_err());

        invalid_block = valid_block.clone();
        invalid_block.difficulty_score_overall = 0;
        assert!(blocktree.validate_block(invalid_block.clone()).is_err());
    }

    #[glue::test]
    fn test_generate_locator() {
        primechain_pow_blocktree::set_block_tree_instance(
            primechain_pow_blocktree::BlockTree::new(),
        );
        let blocktree = primechain_pow_blocktree::get_block_tree_instance();

        // if there is no best block, it should return empty vec
        let locator = blocktree.generate_locator().unwrap();
        assert!(locator.is_empty());

        // create a long chain of blocks, and generate locator
        let mut blocks: Vec<primechain_pow_blocktree::Block> = Vec::new();
        for _ in 0..3000 {
            let block = blocktree.get_block_template().unwrap();
            blocktree.save_block_to_disk(block.clone()).unwrap();
            blocktree.connect_block(block.clone(), true).unwrap();
            blocks.push(block);
        }

        let locator = blocktree.generate_locator().unwrap();

        // verify the locator
        let target_block_index = blocks.len() - 1;
        let mut shift = 1;
        let mut factor = 1;
        for i in 0..std::cmp::min(locator.len(), primechain_pow_blocktree::MAX_LOCATOR_LENGTH) {
            assert_eq!(
                locator[i],
                blocks[target_block_index + 1 - shift].id.to_string()
            );
            if i >= 10 {
                factor *= 2;
            }
            shift += factor;
        }
    }

    #[glue::test]
    fn test_get_reorganization_path() {
        primechain_pow_blocktree::set_block_tree_instance(primechain_pow_blocktree::BlockTree::new());
        let blocktree = primechain_pow_blocktree::get_block_tree_instance();

        // Block Tree Structure:
        // block1 is the root block, created from ROOT_PARENT_ID
        // block2 and block3 are children of block1
        // block4 is a child of block3
        // block5 is a child of block4
        //
        //      block1
        //      /     \
        //   block2   block3
        //              |
        //            block4
        //             |
        //            block5
        let block1 = blocktree.get_block_template().unwrap();
        blocktree.save_block_to_disk(block1.clone()).unwrap();
        blocktree.connect_block(block1.clone(), true).unwrap();

        let block2 = blocktree.get_block_template().unwrap();
        blocktree.save_block_to_disk(block2.clone()).unwrap();

        let mut block3 = blocktree.get_block_template().unwrap();
        // manually change block3 id to to avoid block id collision with block2
        block3.id = blocktree.get_block_hash(block3.clone()).unwrap();
        block3.id = format!("{}_{}", block3.id, "test");
        blocktree.save_block_to_disk(block3.clone()).unwrap();
        blocktree.connect_block(block3.clone(), true).unwrap();

        let block4 = blocktree.get_block_template().unwrap();
        blocktree.save_block_to_disk(block4.clone()).unwrap();
        blocktree.connect_block(block4.clone(), true).unwrap();

        let block5 = blocktree.get_block_template().unwrap();
        blocktree.save_block_to_disk(block5.clone()).unwrap();
        blocktree.connect_block(block5.clone(), true).unwrap();

        // reorg from block2 to block5
        let reorg_path = blocktree
            .get_reorganization_path(block2.id.clone(), block5.id.clone())
            .unwrap();
        println!("rust roll_back_path: {:?}", reorg_path.roll_back_path);
        println!("rust common_ancestor: {:?}", reorg_path.common_ancestor);
        println!("rust roll_forward_path: {:?}", reorg_path.roll_forward_path);
        assert_eq!(reorg_path.common_ancestor, block1.id.to_string());
        assert_eq!(reorg_path.roll_back_path, vec![block2.id.clone()]);
        assert_eq!(
            reorg_path.roll_forward_path,
            vec![block3.id.clone(), block4.id.clone(), block5.id.clone()]
        );
    }

    #[glue::test]
    fn test_save_block_to_disk() {
        primechain_pow_blocktree::set_block_tree_instance(primechain_pow_blocktree::BlockTree::new());
        let blocktree = primechain_pow_blocktree::get_block_tree_instance();

        let block = blocktree.get_block_template().unwrap();
        blocktree.save_block_to_disk(block.clone()).unwrap();

        let saved_block = blocktree.get_block(block.id.clone()).unwrap().unwrap();
        assert_eq!(block, saved_block);
    }

    #[glue::test]
    fn test_connect_block() {
        primechain_pow_blocktree::set_block_tree_instance(primechain_pow_blocktree::BlockTree::new());
        let blocktree = primechain_pow_blocktree::get_block_tree_instance();

        let block = blocktree.get_block_template().unwrap();
        blocktree.save_block_to_disk(block.clone()).unwrap();
        blocktree.connect_block(block.clone(), true).unwrap();

        assert_eq!(blocktree.best_block_id.get(), block.id);
        let best_block = blocktree.get_current_best_block().unwrap().unwrap();
        assert_eq!(best_block, block);
    }
}

#[cfg(test)]
mod test_transaction_receipts {
    use super::*;

    #[glue::test]
    fn test_get_and_create_transaction_receipt() {
        primechain_pow_blocktree::set_transaction_receipts_instance(
            primechain_pow_blocktree::TransactionReceipts::new(),
        );
        let receipts = primechain_pow_blocktree::get_transaction_receipts_instance();

        let receipt = primechain_pow_blocktree::TransactionReceipt {
            transaction_hash: "hash".to_string(),
            transaction_index: 0,
            block_id: "block_id".to_string(),
            from_address: "from_address".to_string(),
            contract_address: "contract_address".to_string(),
            package_name: "package_name".to_string(),
            contract_name: "contract_name".to_string(),
            struct_name: "struct_name".to_string(),
            function_name: "function_name".to_string(),
            parameters: vec![],
            publickeys: vec![],
            signatures: vec![],
            status: true,
            result: serde_json::json!({}),
        };

        receipts
            .create_transaction_receipt(receipt.clone())
            .unwrap();
        let saved_receipt = receipts
            .get_transaction_receipt(receipt.transaction_hash.clone())
            .unwrap()
            .unwrap();
        assert_eq!(receipt, saved_receipt);
    }

    #[glue::test]
    fn test_create_transaction_receipts() {
        primechain_pow_blocktree::set_transaction_receipts_instance(
            primechain_pow_blocktree::TransactionReceipts::new(),
        );
        let receipts = primechain_pow_blocktree::get_transaction_receipts_instance();

        let receipt1 = primechain_pow_blocktree::TransactionReceipt {
            transaction_hash: "hash1".to_string(),
            transaction_index: 0,
            block_id: "block_id".to_string(),
            from_address: "from_address".to_string(),
            contract_address: "contract_address".to_string(),
            package_name: "package_name".to_string(),
            contract_name: "contract_name".to_string(),
            struct_name: "struct_name".to_string(),
            function_name: "function_name".to_string(),
            parameters: vec![],
            publickeys: vec![],
            signatures: vec![],
            status: true,
            result: serde_json::json!({}),
        };

        let receipt2 = primechain_pow_blocktree::TransactionReceipt {
            transaction_hash: "hash2".to_string(),
            transaction_index: 0,
            block_id: "block_id".to_string(),
            from_address: "from_address".to_string(),
            contract_address: "contract_address".to_string(),
            package_name: "package_name".to_string(),
            contract_name: "contract_name".to_string(),
            struct_name: "struct_name".to_string(),
            function_name: "function_name".to_string(),
            parameters: vec![],
            publickeys: vec![],
            signatures: vec![],
            status: true,
            result: serde_json::json!({}),
        };

        receipts
            .create_transaction_receipts(vec![receipt1.clone(), receipt2.clone()])
            .unwrap();
        let saved_receipt1 = receipts
            .get_transaction_receipt(receipt1.transaction_hash.clone())
            .unwrap()
            .unwrap();
        let saved_receipt2 = receipts
            .get_transaction_receipt(receipt2.transaction_hash.clone())
            .unwrap()
            .unwrap();
        assert_eq!(receipt1, saved_receipt1);
        assert_eq!(receipt2, saved_receipt2);
    }

}
