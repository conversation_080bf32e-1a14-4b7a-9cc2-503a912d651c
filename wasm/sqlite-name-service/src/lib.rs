#![allow(unused_imports, unused_variables, dead_code)]

use wasm_py_bridge_macro::wasm_bind;

use crate::model::implement::name_service::{NameService, NameServiceModel};
use crate::model::name_service::INameServiceModel;

mod tests;
pub mod model;


#[wasm_bind]
fn add_package_name(package_name: String) -> bool {
    // check package name
    if !_check_package_name(package_name.clone()) {
        return false;
    }

    // insert package name
    let name_service_model: Box<dyn INameServiceModel> = Box::new(NameServiceModel::new());
    let name_service = NameService {
        package_name,
    };
    let result: rusqlite::Result<usize> = name_service_model.insert(&name_service);
    match result {
        Ok(_) => true,
        Err(_) => false,
    }
}

#[wasm_bind]
fn add_many_package_name(package_name: Vec<String>) -> bool {
    // check package name
    for name in package_name.clone() {
        if !_check_package_name(name.clone()) {
            return false;
        }
    }

    // insert package name
    let name_service_model: Box<dyn INameServiceModel> = Box::new(NameServiceModel::new());
    let mut name_services: Vec<NameService> = vec![];
    for name in package_name.clone() {
        let name_service = NameService {
            package_name: name,
        };
        name_services.push(name_service);
    }
    let result: rusqlite::Result<usize> = name_service_model.insert_many(&name_services);
    match result {
        Ok(_) => true,
        Err(_) => false,
    }
}

#[wasm_bind]
fn check_package_name(package_name: String) -> bool {
    // check cargo package name
    is_valid_package_name(package_name.clone())
        // check duplicate vgraph package name
        && is_duplicate_vgraph_package_name(package_name.clone())
}

// check cargo package name
fn is_valid_package_name(name: String) -> bool {
    // Check if the name is empty
    if name.is_empty() {
        return false;
    }

    // Check the length constraint: no more than 64 characters
    if name.len() > 64 {
        return false;
    }

    // Check if it starts with a lowercase letter
    if let Some(first_char) = name.chars().next() {
        if !first_char.is_ascii_lowercase() {
            return false;
        }
    } else {
        // Empty string is not a valid crate name
        return false;
    }

    // Check if it contains only lowercase letters, digits, or hyphens
    if !name.chars().all(|c| c.is_ascii_lowercase() || c.is_ascii_digit() || c == '-' || c == '_') {
        return false;
    }

    // Check if it contains consecutive underscores or hyphens
    if name.contains("--") || name.contains("__") || name.starts_with('-') || name.ends_with('-') {
        return false;
    }

    true
}

// check duplicate vgraph package name from db
fn is_duplicate_vgraph_package_name(package_name: String) -> bool {
    let name_service_model: Box<dyn INameServiceModel> = Box::new(NameServiceModel::new());

    let name_service = NameService {
        package_name,
    };
    let result: Option<NameService> = name_service_model.get(name_service.package_name.clone());
    match result {
        Some(_) => false,
        None => true,
    }
}

