#[cfg(test)]
mod test {
    use crate::is_valid_package_name;

    #[test]
    fn test_valid_crate_names() {
        let valid_names = vec![
            "valid_crate",
            "another_valid_crate",
            "valid123crate",
            "valid-crate",
        ];
        for name in valid_names {
            assert!(is_valid_package_name(name.to_string()));
        }
    }

    #[test]
    fn test_invalid_crate_names() {
        let invalid_names = vec![
            "", // Empty string
            "InvalidCrate", // Starts with an uppercase letter
            "valid__crate", // Consecutive underscores
            "valid--crate", // Consecutive hyphens
            "-invalid-crate", // Starts with a hyphen
            "invalid-crate-", // Ends with a hyphen
            "crate_name_with_a_very_long_name_that_exceeds_sixty_four_characters_which_is_not_allowed",
        ];
        for name in invalid_names {
            assert!(!is_valid_package_name(name.to_string()));
        }
    }
}