use std::sync::MutexGuard;
use super::sqlite::get_conn;
use rusqlite::*;

// exec_trans executes a function in a transaction
pub fn exec_trans<F>(func: F) -> Result<()>
where
    F: FnOnce(&Transaction) -> Result<()>,
{
    // get connection and start a transaction
    let mut conn: MutexGuard<Connection> = get_conn();
    let tx: Transaction = conn.transaction()?;

    // execute the function
    let result: Result<()> = func(&tx);

    match result {
        Ok(()) => tx.commit(), // commit transaction
        Err(e) => {
            tx.rollback()?; // rollback transaction
            Err(e) // return error
        }
    }
}