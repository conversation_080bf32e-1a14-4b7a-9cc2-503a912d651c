use std::sync::{<PERSON>te<PERSON>, MutexGuard};

use rusqlite::{Connection, params, ToSql};
use serde::{Deserialize, Serialize};

use crate::model::name_service::INameServiceModel;
use crate::model::sqlite::{get_conn, ICreateTable};
use crate::model::trans::exec_trans;

#[derive(Debug, Serialize, Deserialize)]
pub struct NameService {
    pub package_name: String,
}


pub struct NameServiceModel;

impl NameServiceModel {
    pub fn new() -> Self {
        Self {}
    }
}

impl ICreateTable for NameServiceModel {
    fn create_table(&self, conn: &Mutex<Connection>) {
        let sql = "
        CREATE TABLE IF NOT EXISTS name_service (
            package_name TEXT PRIMARY KEY
        )
        ";

        let conn: MutexGuard<Connection> = conn.lock().unwrap();
        conn.execute(sql, []).unwrap();
    }
}

impl INameServiceModel for NameServiceModel {
    fn insert(&self, item: &NameService) -> rusqlite::Result<usize> {
        let sql = "
        INSERT INTO name_service (package_name)
        VALUES (?1)
        ";
        let sql_params = params![
            item.package_name
        ];

        let conn = get_conn();
        conn.execute(&sql, sql_params)
    }

    fn get(&self, package_name: String) -> Option<NameService> {
        let sql = "SELECT package_name FROM name_service WHERE package_name = ?1";
        let sql_params = params![package_name];

        let conn = get_conn();
        let mut stmt = match conn.prepare(&sql) {
            Ok(stmt) => stmt,
            Err(_) => return None,
        };

        let result = stmt.query_row(sql_params, |row| Ok({
            let package_name: String = row.get(0).unwrap();
            NameService {
                package_name,
            }
        }));

        match result {
            Ok(name_service) => Some(name_service),
            Err(_) => None,
        }
    }

    fn insert_many(&self, items: &Vec<NameService>) -> rusqlite::Result<usize> {
        let mut count = 0;

        exec_trans(|tx| {
            if items.is_empty() {
                return Ok(());
            }

            let sql_prefix = "INSERT INTO name_service (package_name) VALUES ";
            let mut sql_values = Vec::new();
            let mut params = Vec::new();

            for (index, item) in items.iter().enumerate() {
                sql_values.push(format!("(?{})", index + 1));
                params.push(&item.package_name as &dyn ToSql);
            }

            let sql = sql_prefix.to_owned() + &sql_values.join(", ");
            tx.execute(&sql, params.as_slice())?;
            count = items.len();
            Ok(())
        })?;

        Ok(count)
    }
}

impl NameService {
    pub fn new(package_name: String) -> Self {
        Self {
            package_name,
        }
    }
}