use std::sync::Mutex;

use lazy_static::lazy_static;
use rusqlite::*;

use crate::model::implement::name_service::{NameService, NameServiceModel};

lazy_static! {
    static ref CONN: Mutex<Connection> = create_db_connection();
}

pub fn get_conn() -> std::sync::MutexGuard<'static, Connection> {
    CONN.lock().expect("Failed to lock the connection")
}

// reset_conn reset the connection, will be used in unit test
pub fn reset_conn() {
    let new_conn = create_db_connection();
    let mut conn_guard = CONN.lock().unwrap();
    *conn_guard = new_conn.into_inner().unwrap();
}

fn create_db_connection() -> Mutex<Connection> {
    let conn_str: String = std::env::var("conn_str").unwrap_or("vgraph.db".to_string());

    let conn: Connection = Connection::open(conn_str).unwrap();
    let result: Mutex<Connection> = Mutex::new(conn);
    // init database
    // create tables
    create_tables(&result);

    result
}

fn create_tables(conn: &Mutex<Connection>) {
    // create table interfaces
    let create_table_interfaces: Vec<Box<dyn ICreateTable>> = vec![
        Box::new(NameServiceModel::new()),
    ];

    // create tables
    for create_table_interface in create_table_interfaces {
        create_table_interface.create_table(&conn);
    }
}

// define a interface create table
pub trait ICreateTable {
    fn create_table(&self, conn: &Mutex<Connection>);
}
