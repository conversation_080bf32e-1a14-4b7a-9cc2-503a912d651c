#[glue::contract(package = "abstract_blocktree")]
mod blocktree_utils {
    use sha3::Digest;

    // #[glue::storage]
    // pub struct Utils {}

    // impl Utils {
    //     #[glue::constructor]
    //     pub fn new() -> Self {
    //         Self {}
    //     }

    //     #[glue::readonly]
    //     pub fn keccak256(&self, input: String) -> String {
    //         let mut hasher = sha3::Keccak256::new();
    //         hasher.update(input.as_bytes());
    //         let result: String = hasher.finalize().iter().map(|b| format!("{:02x}", b)).collect();
    //         return result;
    //     }
    // }

    #[glue::wasm_bind]
    pub fn keccak256(input: String) -> String {
        let mut hasher = sha3::Keccak256::new();
        hasher.update(input.as_bytes());
        let result: String = hasher.finalize().iter().map(|b| format!("{:02x}", b)).collect();
        return result;
    }
}

#[glue::contract]
mod abstract_blocktree {
    use serde::{Deserialize, Serialize};
    use serde_json::Value;
    // use lazy_static::lazy_static;
    use uuid::Uuid;

    use super::*;

    // messages to be sent to the client
    const ROOT_PARENT_ID: &str = "00000000-0000-0000-0000-000000000000";
    const MAX_LOCATOR_LENGTH: usize = 32;
    const SUCCESS: &str = "success"; // update db successfully
    const ERROR: &str = "error"; // error message
    const LOCATOR: &str = "locator"; // send locator to the node that broadcasts the new block
    const _BLOCK_SAME_AS_BEST: &str = "block same as best"; // new block same as best block
    const BLOCK_DIFFICULTY_LESS_OR_EQUAL: &str = "block difficulty less or equal"; // new block difficulty less or equal to best block

    // lazy_static! {
    //     pub static ref NIL_BLOCK: AbstractBlock = AbstractBlock {
    //         id: ROOT_PARENT_ID.to_string(),
    //         parent_id: ROOT_PARENT_ID.to_string(),
    //         difficulty_score: 0,
    //         difficulty_score_overall: 0,
    //         height: 0,
    //         transactions: "[]".to_string(),
    //         // merkle: blocktree_utils::get_utils_instance().keccak256("[]".to_string()),
    //         merkle: blocktree_utils::_keccak256("[]".to_string()),
    //         timestamp: 0,
    //         nonce: 0,
    //         multiplier: 1,
    //     };
    // }

    #[glue::storage_item]
    pub struct AbstractBlock {
        pub id: String,
        pub parent_id: String,
        pub difficulty_score: i64,
        pub difficulty_score_overall: i64,
        pub height: i64,
        pub transactions: String,
        pub merkle: String,
        pub timestamp: i64,
        pub nonce: i64,
        pub multiplier: i64,
    }

    impl AbstractBlock {
        pub fn validate(&self) -> bool {
            // validate block
            // here, we just verify some fields of the block, in the future, there will be more validation logic here
            // let mut block_tree = abstract_blocktree::BlockTree::new();

            // trivial validation
            if self.id == Uuid::nil().to_string() {
                return false;
            }
            if self.difficulty_score <= 0 {
                return false;
            }
            if self.difficulty_score_overall <= 0 {
                return false;
            }
            if self.height <= 0 {
                return false;
            }
            if blocktree_utils::_keccak256(self.transactions.clone()) != self.merkle {
                return false;
            }

            // if its parent is in the block tree, check the parent
            if self.parent_id != Uuid::nil().to_string() {
                match get_block_tree_instance().get(self.parent_id.clone()) {
                    // match block_tree.get(self.parent_id.clone()) {
                    Ok(parent_block) => {
                        // check the difficulty score overall
                        if parent_block.difficulty_score_overall + self.difficulty_score != self.difficulty_score_overall {
                            return false;
                        }
                        // check the height
                        if self.height != parent_block.height + 1 {
                            return false;
                        }
                    }
                    Err(_) => {}
                };
            } else {
                // if the block's parent is the root parent, check the height and difficulty score overall
                if self.height != 1 {
                    return false;
                }
                if self.difficulty_score != self.difficulty_score_overall {
                    return false;
                }
            }
            // if the block passes all tests, return true
            true
        }
    }

    #[glue::storage_item]
    pub struct TransactionReceipt {
        pub transaction_hash: String,
        pub transaction_index: i64,
        pub block_id: String,
        pub from_address: String,
        pub contract_address: String,
        pub package_name: String,
        pub contract_name: String,
        pub struct_name: String,
        pub function_name: String,
        pub parameters: Vec<Value>,
        pub publickeys: Vec<String>,
        pub signatures: Vec<String>,
        pub status: bool,
        pub result: Value,
    }

    #[derive(Debug, Serialize, Deserialize)]
    pub struct ReorganizationPath {
        pub roll_back_path: Vec<String>,
        pub common_ancestor: String,
        pub roll_forward_path: Vec<String>,
    }

    #[derive(Debug, Serialize, Deserialize)]
    pub struct Transaction {
        #[serde(skip_serializing_if = "Option::is_none")]
        pub transaction_hash: Option<String>,
        pub from_address: String,
        pub contract_address: String,
        pub package_name: String,
        pub contract_name: String,
        pub struct_name: String,
        pub function_name: String,
        pub parameters: Vec<Value>,
        pub publickeys: Vec<String>,
        pub signatures: Vec<String>,
        pub timestamp: i64,
    }

    #[glue::storage]
    pub struct BlockTree {
        pub root_block_id: glue::StorageField<String>,
        pub best_block_id: glue::StorageField<String>,
        pub blocktree_map: glue::collections::Map<String, AbstractBlock>,
    }

    #[glue::storage]
    pub struct TransactionMap {
        pub transaction_receipt_map: glue::collections::Map<String, TransactionReceipt>,
    }

    impl TransactionMap {
        #[glue::constructor]
        pub fn new() -> Self {
            Self {
                transaction_receipt_map: glue::collections::Map::new(),
            }
        }

        #[glue::atomic]
        pub fn insert(&mut self, id: String, transaction_receipt: TransactionReceipt) -> anyhow::Result<()> {
            if self.contains(id.clone()) {
                return Err(anyhow::anyhow!("TransactionReceipt with id {} already exists", id));
            }
            self.transaction_receipt_map.insert(&id, &transaction_receipt);
            Ok(())
        }

        #[glue::readonly]
        pub fn get(&self, id: String) -> anyhow::Result<TransactionReceipt> {
            match self.transaction_receipt_map.get(&id) {
                Some(receipt) => Ok(receipt),
                None => Err(anyhow::anyhow!("TransactionReceipt with id {} not found", id)),
            }
        }

        #[glue::atomic]
        pub fn remove(&mut self, id: String) {
            self.transaction_receipt_map.remove(&id);
        }

        #[glue::readonly]
        pub fn contains(&self, id: String) -> bool {
            self.transaction_receipt_map.contains(&id)
        }

        #[glue::readonly]
        pub fn size(&self) -> u32 {
            self.transaction_receipt_map.size()
        }

        #[glue::atomic]
        pub fn create(&mut self, transaction_receipt: TransactionReceipt) -> anyhow::Result<bool> {
            let id = transaction_receipt.transaction_hash.clone();
            // reject if transaction id already exists
            if self.contains(id.clone()) {
                return Ok(false);
            }
            match self.insert(id, transaction_receipt) {
                Ok(_) => Ok(true),
                Err(_e) => {
                    Ok(false)
                }
            }
        }
    }

    impl BlockTree {
        #[glue::constructor]
        pub fn new() -> Self {
            let mut init_map = glue::collections::Map::new();
            // init create root parent block and set best block
            init_map.insert(&ROOT_PARENT_ID.to_string(), &AbstractBlock {
                id: ROOT_PARENT_ID.to_string(),
                parent_id: ROOT_PARENT_ID.to_string(),
                difficulty_score: 0,
                difficulty_score_overall: 0,
                height: 0,
                transactions: "[]".to_string(),
                merkle: blocktree_utils::_keccak256("[]".to_string()),
                timestamp: 0,
                nonce: 0,
                multiplier: 1,
            });
            Self {
                root_block_id: glue::StorageField::new(&"".to_string()),
                best_block_id: glue::StorageField::new(&ROOT_PARENT_ID.to_string()),
                blocktree_map: init_map,
            }
        }

        #[glue::atomic]
        pub fn insert(&mut self, id: String, block: AbstractBlock) -> anyhow::Result<()> {
            if self.contains(id.clone()) {
                return Err(anyhow::anyhow!("Block with id {} already exists", id));
            }
            self.blocktree_map.insert(&id, &block);
            Ok(())
        }

        #[glue::atomic]
        pub fn update(&mut self, id: String, block: AbstractBlock) -> anyhow::Result<()> {
            self.blocktree_map.insert(&id, &block);
            Ok(())
        }

        #[glue::readonly]
        pub fn get(&self, id: String) -> anyhow::Result<AbstractBlock> {
            match self.blocktree_map.get(&id) {
                Some(block) => Ok(block),
                None => Err(anyhow::anyhow!("Block with id {} not found", id)),
            }
        }

        #[glue::atomic]
        pub fn remove(&mut self, id: String) {
            self.blocktree_map.remove(&id);
        }

        #[glue::readonly]
        pub fn contains(&self, id: String) -> bool {
            self.blocktree_map.contains(&id)
        }

        #[glue::readonly]
        pub fn size(&self) -> u32 {
            // root parent block should not be counted
            self.blocktree_map.size() - 1
        }

        #[glue::atomic]
        pub fn set_best_block_id(&mut self, id: String) -> anyhow::Result<()> {
            self.best_block_id.set(&id);
            Ok(())
        }

        #[glue::readonly]
        pub fn get_best_block_id(&self) -> anyhow::Result<String> {
            Ok(self.best_block_id.get().clone())
        }

        #[glue::readonly]
        pub fn get_root_block_id(&self) -> anyhow::Result<String> {
            Ok(self.root_block_id.get().clone())
        }

        #[glue::atomic]
        pub fn initialize_root_block_if_not_set(&mut self, block: AbstractBlock) -> anyhow::Result<()> {
            if block.parent_id == ROOT_PARENT_ID && self.root_block_id.get() == "" {
                self.root_block_id.set(&block.id.clone());
            }
            Ok(())
        }

        #[glue::readonly]
        pub fn get_best_block(&self) -> anyhow::Result<AbstractBlock> {
            match self.blocktree_map.get(&self.best_block_id.get()) {
                Some(block) => Ok(block),
                None => Err(anyhow::anyhow!("Best block not found")),
            }
        }

        #[glue::atomic]
        pub fn new_block(&mut self, parent_block_id: String, transactions: Vec<Transaction>, merkle: String, timestamp: i64, nonce: i64, multiplier: i64) -> anyhow::Result<AbstractBlock> {
            // check if transactions merkle is correct
            // if blocktree_utils::get_utils_instance().keccak256(serde_json::to_string(&transactions).unwrap()) != merkle {
            if blocktree_utils::_keccak256(serde_json::to_string(&transactions).unwrap()) != merkle {
                return Err(anyhow::anyhow!("Error: wrong merkle"));
            }
            let parent_block: AbstractBlock = self.get(parent_block_id.clone()).unwrap();

            // insert block
            let current_difficulty = 1;
            let block = AbstractBlock {
                id: Uuid::new_v4().to_string(),
                parent_id: parent_block.id,
                difficulty_score: current_difficulty,
                difficulty_score_overall: current_difficulty + parent_block.difficulty_score_overall,
                height: parent_block.height + 1,
                transactions: serde_json::to_string(&transactions).unwrap(),
                merkle: merkle.clone(),
                timestamp: timestamp,
                nonce: nonce,
                multiplier: multiplier,
            };

            // init set root block id
            self.initialize_root_block_if_not_set(block.clone())?;

            // get best block tree
            let best_block_id: String = self.get_best_block_id()?;
            let best_block: AbstractBlock = self.get(best_block_id.clone())?;

            // compare current and best block difficulty_score_overall
            // if greater, insert block and update best block (use transaction)
            // if not, insert block only
            let _ = self.insert(block.id.clone(), block.clone());
            if block.difficulty_score_overall > best_block.difficulty_score_overall {
                let _ = self.set_best_block_id(block.id.clone());
            }

            Ok(block)
        }

        #[glue::readonly]
        pub fn reorganization_path(&self, old_best_block_id: String, new_best_block_id: String) -> anyhow::Result<ReorganizationPath> {
            if old_best_block_id == new_best_block_id {
                return Ok(ReorganizationPath {
                    roll_back_path: [].to_vec(),
                    common_ancestor: old_best_block_id,
                    roll_forward_path: [].to_vec(),
                });
            }
            let mut block1: AbstractBlock = self.get(old_best_block_id.clone())?;
            let mut block2: AbstractBlock = self.get(new_best_block_id.clone())?;

            let mut roll_back_path: Vec<String> = Vec::new();
            let mut roll_forward_path: Vec<String> = Vec::new();

            // Move the higher block down to the same height as another block
            while block1.height > block2.height {
                roll_back_path.push(block1.id.clone());
                block1 = self.get(block1.parent_id.clone())?;
            }

            while block2.height > block1.height {
                roll_forward_path.push(block2.id.clone());
                block2 = self.get(block2.parent_id.clone())?;
            }

            // Starting at the same height, traverse upwards until you find a common ancestor
            while block1.id != block2.id {
                roll_back_path.push(block1.id.clone());
                roll_forward_path.push(block2.id.clone());
                block1 = self.get(block1.parent_id.clone())?;
                block2 = self.get(block2.parent_id.clone())?;
            }

            // reverse roll_forward_path inplace
            roll_forward_path.reverse();

            let reorganization_path: ReorganizationPath = ReorganizationPath {
                roll_back_path,
                common_ancestor: block1.id.clone(),
                roll_forward_path,
            };

            Ok(reorganization_path)
        }

        // not necessary, can be replaced by get_best_block().difficulty_score_overall
        #[glue::readonly]
        pub fn get_best_block_difficulty_score_overall(&self) -> anyhow::Result<i64> {
            let best_block: AbstractBlock = self.get_best_block()?;
            Ok(best_block.difficulty_score_overall)
        }

        // generate_locator is a function to generate locator
        // locator is a list of block ids, starts from the current block
        // and climbs up the block tree with exponentially increasing jumps after first 10 blocks, e.g. 1, 2, 4, 8, 16, 32, 64, 128, 256, 512
        // until it hits the root block or the max locator length is reached
        #[glue::readonly]
        pub fn generate_locator(&self, block_id: String) -> anyhow::Result<Vec<String>> {
            let mut locator: Vec<String> = Vec::new();
            let mut step = 1; // set step to climb
            let mut counter = 1;
            let mut current_block_id = block_id;

            while current_block_id != ROOT_PARENT_ID && counter < MAX_LOCATOR_LENGTH {
                // push current block id
                locator.push(current_block_id.clone());

                // update current block id, climb up `step` blocks
                for _ in 0..step {
                    current_block_id = match self.get(current_block_id.clone()) {
                        Ok(block) => block.parent_id,
                        Err(_e) => break,
                    };
                }

                if counter >= 10 {
                    // double steps, exponential growth
                    step *= 2;
                }
                counter += 1;
            }

            Ok(locator)
        }

        // receive_new_block is a function to handle new block data received from other node
        // handle logic:
        // 1. validate block data
        // 2. update block data in db:
        //   - if new block is the same as this node's best block, do nothing
        //   - if new block's difficulty overall is less than this node's best block, do nothing(abandon it)
        //   - if new block has the same difficulty score overall as this node's best block, insert it to the block tree, not update best block
        //   - if new block's difficulty overall is greater than this node's best block, do the following:
        //     1. if new block is the child of this node's best block, insert it into the block tree, and update best block
        //     2. if new block is not the child of this node's best block, send Locator message to client

        // return a message to main app
        // messages -> please go to consts.rs to see the message list
        #[glue::atomic]
        // fn receive_new_block(&mut self, block: BlockTree) -> anyhow::Result<String> {
        pub fn receive_new_block(&mut self, block: AbstractBlock) -> anyhow::Result<String> {
            // validate block
            if !block.validate() {
                return Ok(ERROR.to_string());
            }
            let best_block_id = self.best_block_id.get().clone();

            // if the new block is the same as this node's best block, do nothing
            if block.id == best_block_id {
                return Ok(ERROR.to_string());
            };

            self.initialize_root_block_if_not_set(block.clone())?;

            // get this node's best block's difficulty score overall
            // let current_best_block_difficulty_score_overall = get_best_block_difficulty_score_overall(best_block_id_str);
            let current_best_block_difficulty_score_overall = self.get_best_block_difficulty_score_overall()?;
            // if the new block has the same difficulty overall as this node's best block, insert it to the block tree, not update best block
            if block.difficulty_score_overall <= current_best_block_difficulty_score_overall {
                if self.contains(block.parent_id.clone()) {
                    // if its parent block exists, insert it into the block tree
                    match self.insert(block.id.clone(), block.clone()) {
                        Ok(_) => {}
                        Err(_) => {
                            println!("Failed to insert new block to block tree");
                            return Ok(ERROR.to_string());
                        }
                    }
                }
                // else abandon it
                return Ok(BLOCK_DIFFICULTY_LESS_OR_EQUAL.to_string());
            }

            // if the new block's difficulty overall is greater than this node's best block, do the following:
            //   1. if new block is the child of this node's best block, insert it into the block tree, and update best block
            //   2. if new block is not the child of this node's best block, send Locator message to client

            let best_block = self.get_best_block()?;

            // if new block is the child of this node's best block, append it to the block tree, and update best block
            if block.parent_id == best_block.id && block.height == best_block.height + 1 {
                // update block tree
                self.set_best_block_id(block.id.clone())?;
                match self.insert(block.id.clone(), block.clone()) {
                    Ok(_) => {
                        return Ok(SUCCESS.to_string());
                    }
                    Err(_) => {
                        println!("Failed to insert new block to block tree");
                        return Ok(ERROR.to_string());
                    }
                }
            } else {
                // if new block is not the child of this node's best block, or height don't match, reorganize the block tree, and update best block
                // send "LOCATOR" message to app, app will invoke `generate_locator` to get this node's locator
                // then the locator will be sent to the node that broadcasts the new block
                // the node that broadcasts the new block will handle locator, send "SYNC_BLOCKS" message to this node
                // finally, this node will handle the new blocks data, and update block tree in `receive_sync_blocks`
                return Ok(LOCATOR.to_string());
            }
        }

        #[glue::readonly]
        pub fn receive_locator(&self, locator: Vec<String>) -> Vec<AbstractBlock> {
            // find common ancestor of this node's best block and the other node's best block
            let best_block: AbstractBlock = self.get_best_block().unwrap();
            let mut common_ancestor_id = String::new();

            for locator_block_id in locator {
                match self.get(locator_block_id.clone()) {
                    Ok(locator_block) => {
                        // if the locator block is in the block tree, check if it's the common ancestor
                        if locator_block.difficulty_score_overall > best_block.difficulty_score_overall {
                            // if the locator block's difficulty score overall is greater than this node's best block, skip
                            continue;
                        } else {
                            // if the locator block's difficulty score overall is less than or equal to this node's best block
                            // climb up from best block to find common ancestor
                            let mut current_block_id = best_block.id.clone();
                            while current_block_id != locator_block.id
                                && current_block_id != ROOT_PARENT_ID
                            {
                                let current_block = match self.get(current_block_id.clone()) {
                                    Ok(block) => block,
                                    Err(_) => {
                                        println!("Failed to get block from model");
                                        return Vec::new();
                                    }
                                };
                                current_block_id = current_block.parent_id;
                            }
                            if current_block_id == locator_block.id {
                                // if the locator block is the common ancestor, set it as common ancestor
                                common_ancestor_id = current_block_id.to_string();
                                break;
                            }
                        }
                    }
                    Err(_) => {
                        // if the locator block is not in the block tree, continue
                        continue;
                    }
                };
            }

            // if common ancestor is not found, set it to root block
            if common_ancestor_id.is_empty() {
                common_ancestor_id = match self.get_root_block_id() {
                    Ok(id) => id,
                    Err(_) => {
                        println!("Failed to get root block");
                        return Vec::new();
                    }
                };
            }

            // find and return path from common ancestor to this node's best block
            let mut path: Vec<AbstractBlock> = Vec::new();
            let mut current_block_id = best_block.id.clone();
            let common_ancestor = match self.get(common_ancestor_id.clone()) {
                Ok(block) => block,
                Err(_) => {
                    println!("Failed to get common ancestor from model");
                    return Vec::new();
                }
            };
            while current_block_id != common_ancestor_id {
                match self.get(current_block_id.clone()) {
                    Ok(current_block) => {
                        current_block_id = current_block.parent_id.clone();
                        path.push(current_block);
                    }
                    Err(_) => {
                        println!("Failed to get block from model");
                        return Vec::new();
                    }
                };
            }
            if current_block_id == common_ancestor_id {
                path.push(common_ancestor);
            }

            path.reverse();
            path
        }

        #[glue::atomic]
        pub fn receive_sync_blocks(&mut self, blocks: Vec<AbstractBlock>) -> String {
            println!("\n\nBlocks data: {:?}\n\n", blocks);

            // verify if each block is valid
            for block in blocks.iter() {
                if !block.validate() {
                    println!("Invalid blocks data: invalid block");
                    return ERROR.to_string();
                }
            }

            println!("Valid blocks data\n");

            // verify if blocks are in the same chain
            if blocks
                .windows(2)
                .any(|window| window[0].id != window[1].parent_id)
            {
                println!("Invalid blocks data: blocks are not in the same chain");
                return ERROR.to_string();
            }

            println!("Blocks are in the same chain\n");

            let common_ancestor = match blocks.first() {
                Some(block) => block,
                None => {
                    println!("Invalid blocks data");
                    return ERROR.to_string();
                }
            };
            let common_ancestor_id = common_ancestor.id.clone();
            println!("Common ancestor: {:?}\n", common_ancestor);

            // verify if common ancestor is in the block tree
            if self.get(common_ancestor_id.clone()).is_err()
                && common_ancestor.parent_id != ROOT_PARENT_ID
            {
                println!("Invalid common ancestor");
                return ERROR.to_string();
            }

            println!("Common ancestor is in the block tree\n");

            // update block tree use transaction
            // transaction:
            //   1. insert the blocks to the block tree
            //   2. insert best block if not exists, otherwise update best block
            for block in blocks.iter() {
                if self.get(block.id.clone()).is_ok() {
                    if let Err(_) = self.update(block.id.clone(), block.clone()) {
                        return ERROR.to_string();
                    }
                } else {
                    if let Err(_) = self.insert(block.id.clone(), block.clone()) {
                        return ERROR.to_string();
                    }
                }
                self.initialize_root_block_if_not_set(block.clone()).unwrap();
            }
            // 2. use the last block in the blocks to set best block
            self.set_best_block_id(blocks.last().unwrap().id.clone()).unwrap();
            return SUCCESS.to_string();
        }
    }
}


#[cfg(test)]
mod tests {
    use std::cmp::min;

    use lazy_static::lazy_static;

    use super::*;

    pub const ROOT_PARENT_ID: &str = "00000000-0000-0000-0000-000000000000";
    pub const MAX_LOCATOR_LENGTH: usize = 32;
    // pub const EMPTY_MERKLE:  &str = "518674ab2b227e5f11e9084f615d57663cde47bce1ba168b4c19c7ee22a73d70";
    lazy_static! {
        static ref EMPTY_MERKLE: String = "518674ab2b227e5f11e9084f615d57663cde47bce1ba168b4c19c7ee22a73d70".to_string();
    }

    #[glue::test]
    fn test_new_block() {
        abstract_blocktree::set_block_tree_instance(abstract_blocktree::BlockTree::new());
        let block_tree = abstract_blocktree::get_block_tree_instance();
        // let mut best_block = abstract_blocktree::BestBlock::new();
        // block_tree.new_block(ROOT_PARENT_ID.to_string(), Vec::new(), "518674ab2b227e5f11e9084f615d57663cde47bce1ba168b4c19c7ee22a73d70".to_string(), 0, 0, 1);
        let block1 = block_tree.new_block(ROOT_PARENT_ID.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        let get_block1 = block_tree.get(block1.id.clone()).unwrap();
        assert_eq!(block1, get_block1);
        assert_eq!(block1.parent_id.to_string(), ROOT_PARENT_ID);
        assert_eq!(block1.height, 1);

        let block2 = block_tree.new_block(block1.id.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        assert_eq!(block2.parent_id.to_string(), block1.id.to_string());
        assert_eq!(block2.height, 2);
        assert_eq!(
            block2.difficulty_score_overall,
            block2.difficulty_score + block1.difficulty_score_overall
        );

        let block3 = block_tree.new_block(block1.id.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        assert_eq!(block3.parent_id.to_string(), block1.id.to_string());
        assert_eq!(block3.height, 2);
        assert_eq!(
            block3.difficulty_score_overall,
            block3.difficulty_score + block1.difficulty_score_overall
        );
        let block4 = block_tree.new_block(block3.id.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        let get_block4 = block_tree.get(block4.id.clone()).unwrap();
        assert_eq!(block4.parent_id.to_string(), block3.id.to_string());
        assert_eq!(block4.height, 3);
        assert_eq!(
            block4.difficulty_score_overall,
            block4.difficulty_score + block3.difficulty_score_overall
        );
        assert_eq!(block4, get_block4);

        let size = block_tree.size();
        assert_eq!(size, 4);
    }

    #[glue::test]
    fn test_root_block() {
        abstract_blocktree::set_block_tree_instance(abstract_blocktree::BlockTree::new());
        let block_tree = abstract_blocktree::get_block_tree_instance();

        let block1 = block_tree.new_block(ROOT_PARENT_ID.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        assert_eq!(block_tree.get_root_block_id().unwrap(), block1.id.clone());
        let _block2 = block_tree.new_block(block1.id.clone(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        assert_eq!(block_tree.get_root_block_id().unwrap(), block1.id.clone());
    }

    #[glue::test]
    fn test_best_block() {
        abstract_blocktree::set_block_tree_instance(abstract_blocktree::BlockTree::new());
        let block_tree = abstract_blocktree::get_block_tree_instance();

        let block1 = block_tree.new_block(ROOT_PARENT_ID.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        let block2 = block_tree.new_block(block1.id.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();

        // block 3 is a fork of block 1
        // so best block should be block2
        let block3 = block_tree.new_block(block1.id.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        assert_eq!(block2.id.to_string(), block_tree.get_best_block_id().unwrap());
        assert_eq!(block2, block_tree.get_best_block().unwrap());
        let block4 = block_tree.new_block(block3.id.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();

        // best block should be block4
        let best_block_id = block_tree.get_best_block_id().unwrap();
        assert_eq!(best_block_id, block4.id.to_string());
        assert_eq!(block4, block_tree.get_best_block().unwrap());
    }

    #[glue::test]
    fn test_reorganization_path() {
        abstract_blocktree::set_block_tree_instance(abstract_blocktree::BlockTree::new());
        let block_tree = abstract_blocktree::get_block_tree_instance();

        let block1 = block_tree.new_block(ROOT_PARENT_ID.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        println!("Block 1 id: {:?}", block1.id.clone());
        let block2 = block_tree.new_block(block1.id.clone(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        println!("Block 2 id: {:?}", block2.id.clone());
        let block3 = block_tree.new_block(block1.id.clone(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        println!("Block 3 id: {:?}", block3.id.clone());
        let block4 = block_tree.new_block(block3.id.clone(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        println!("Block 4 id: {:?}", block4.id.clone());
        let block5 = block_tree.new_block(block4.id.clone(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        println!("Block 5 id: {:?}", block5.id.clone());

        // Block Tree Structure:
        // block1 is the root block, created from ROOT_PARENT_ID
        // block2 and block3 are children of block1
        // block4 is a child of block3
        // block5 is a child of block4
        //
        //      block1
        //      /     \
        //   block2   block3
        //              |
        //            block4
        //             |
        //            block5

        print!(
            "      block1\n      /     \\\n   block2   block3\n              |\n            block4\n              |\n            block5\n"
        );

        let start_block_id = block2.id.clone();
        let end_block_id = block5.id.clone();

        let reorg_path = block_tree.reorganization_path(start_block_id, end_block_id).unwrap();
        println!("rust roll_back_path: {:?}", reorg_path.roll_back_path);
        println!("rust common_ancestor: {:?}", reorg_path.common_ancestor);
        println!("rust roll_forward_path: {:?}", reorg_path.roll_forward_path);
        assert_eq!(reorg_path.common_ancestor, block1.id.clone());
        assert_eq!(reorg_path.roll_back_path.len(), 1);
        assert_eq!(reorg_path.roll_forward_path.len(), 3);
    }

    #[glue::test]
    fn test_get_best_block_difficulty_score_overall() {
        let mut block_tree = abstract_blocktree::BlockTree::new();

        let block1 = block_tree.new_block(ROOT_PARENT_ID.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        let _block2 = block_tree.new_block(block1.id.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        let block3 = block_tree.new_block(block1.id.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        let block4 = block_tree.new_block(block3.id.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();

        // best block difficulty score overall should be block4's difficulty score overall
        let best_block_difficulty_score_overall = block_tree.get_best_block_difficulty_score_overall().unwrap();
        assert_eq!(
            best_block_difficulty_score_overall,
            block4.difficulty_score_overall
        );
    }

    #[glue::test]
    fn test_receive_new_block() {
        //
        //      block1
        //        |
        //      block2
        //      /    \
        //  block4   block3
        //             |
        //            block5
        //             ...
        //            block7
        //
        abstract_blocktree::set_block_tree_instance(abstract_blocktree::BlockTree::new());
        let block_tree = abstract_blocktree::get_block_tree_instance();

        let block1 = block_tree.new_block(ROOT_PARENT_ID.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        let block2 = block_tree.new_block(block1.id.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();

        let block3 = abstract_blocktree::AbstractBlock {
            id: "00000000-0000-0000-0000-000000000003".to_string(),
            parent_id: block2.id.clone(),
            difficulty_score: 1,
            difficulty_score_overall: block2.difficulty_score_overall + 1,
            height: 3,
            transactions: "[]".to_string(),
            merkle: EMPTY_MERKLE.clone(),
            timestamp: 0,
            nonce: 0,
            multiplier: 1,
        };
        let message = block_tree.receive_new_block(block3.clone()).unwrap();
        assert_eq!(message, "success");
        assert_eq!(block_tree.size(), 3);
        let best_block = block_tree.get_best_block().unwrap();
        assert_eq!(best_block, block3.clone());

        let block4 = abstract_blocktree::AbstractBlock {
            id: "00000000-0000-0000-0000-000000000004".to_string(),
            parent_id: block2.id.clone(),
            difficulty_score: 1,
            difficulty_score_overall: block2.difficulty_score_overall + 1,
            height: 3,
            transactions: "[]".to_string(),
            merkle: EMPTY_MERKLE.clone(),
            timestamp: 0,
            nonce: 0,
            multiplier: 1,
        };
        // block tree receive block4, blocktree size should be 4
        let message = block_tree.receive_new_block(block4.clone()).unwrap();
        assert_eq!(message, "block difficulty less or equal");
        assert_eq!(block_tree.size(), 4);
        // best block should be block3
        let best_block = block_tree.get_best_block().unwrap();
        assert_eq!(best_block, block3.clone());

        let err_block = abstract_blocktree::AbstractBlock {
            id: "00000000-0000-0000-0000-000000000004".to_string(),
            parent_id: block3.id.clone(),
            difficulty_score: 1,
            difficulty_score_overall: block2.difficulty_score_overall + 1,
            height: 3,
            transactions: "[]".to_string(),
            merkle: EMPTY_MERKLE.clone(),
            timestamp: 0,
            nonce: 0,
            multiplier: 1,
        };
        // block tree receive block4, blocktree size should be 4
        let message = block_tree.receive_new_block(err_block.clone()).unwrap();
        assert_eq!(message, "error");
        assert_eq!(block_tree.size(), 4);

        let block5 = abstract_blocktree::AbstractBlock {
            id: "00000000-0000-0000-0000-000000000005".to_string(),
            parent_id: block3.id.clone(),
            difficulty_score: 1,
            difficulty_score_overall: block3.difficulty_score_overall + 1,
            height: 4,
            transactions: "[]".to_string(),
            merkle: EMPTY_MERKLE.clone(),
            timestamp: 0,
            nonce: 0,
            multiplier: 1,
        };
        let message = block_tree.receive_new_block(block5.clone()).unwrap();
        assert_eq!(message, "success");
        assert_eq!(block_tree.size(), 5);
        let best_block = block_tree.get_best_block().unwrap();
        assert_eq!(best_block, block5.clone());

        // receive a higher block, but not a child of current best block
        let block7 = abstract_blocktree::AbstractBlock {
            id: "00000000-0000-0000-0000-000000000007".to_string(),
            parent_id: "00000000-0000-0000-0000-000000000006".to_string(),
            difficulty_score: 1,
            difficulty_score_overall: block5.difficulty_score_overall + 2,
            height: 6,
            transactions: "[]".to_string(),
            merkle: EMPTY_MERKLE.clone(),
            timestamp: 0,
            nonce: 0,
            multiplier: 1,
        };
        let message = block_tree.receive_new_block(block7.clone()).unwrap();
        assert_eq!(message, "locator");
        assert_eq!(block_tree.size(), 5);
    }

    #[glue::test]
    fn test_generate_locator() {
        abstract_blocktree::set_block_tree_instance(abstract_blocktree::BlockTree::new());
        let block_tree = abstract_blocktree::get_block_tree_instance();

        let mut last_block_id = ROOT_PARENT_ID.to_string();
        let mut blocks = Vec::new();
        // create a long chain of blocks
        for _ in 0..3000 {
            let new_block = block_tree.new_block(last_block_id, Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
            last_block_id = new_block.id.clone();
            blocks.push(new_block);
        }

        // generate locator
        let target_block_index = blocks.len() - 1;
        let target_block_id = blocks[target_block_index].id.clone();
        let locator = block_tree.generate_locator(target_block_id).unwrap();

        assert!(!locator.is_empty());
        let mut shift = 1;
        let mut factor = 1;
        println!("locator: {:?} \n", locator);

        for i in 0..min(locator.len(), MAX_LOCATOR_LENGTH) {
            assert_eq!(
                locator[i],
                blocks[target_block_index + 1 - shift].id
            );
            if i >= 10 {
                factor *= 2;
            }
            shift += factor;
        }
    }

    #[glue::test]
    fn test_locator_sync_blocks() {
        // create block_tree
        abstract_blocktree::set_block_tree_instance(abstract_blocktree::BlockTree::new());
        let block_tree = abstract_blocktree::get_block_tree_instance();
        // generate blocks [block1, block2, block3, block4]
        let block1 = block_tree.new_block(ROOT_PARENT_ID.to_string(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        let block2 = block_tree.new_block(block1.id.clone(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        let block3 = block_tree.new_block(block2.id.clone(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();

        let block4 = block_tree.new_block(block3.id.clone(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        // generate locator from block_tree and then remove block4
        let locator = block_tree.generate_locator(block4.id.clone()).unwrap();
        println!("block_tree locator : {:?}", locator);
        // locator will be [block4.id, block3.id, block2.id, block1.id]
        assert_eq!(locator, [block4.id.clone(), block3.id.clone(), block2.id.clone(), block1.id.clone()]);
        // remove block4 and best block will be block3
        block_tree.remove(block4.id.clone());
        block_tree.set_best_block_id(block3.id.clone()).unwrap();

        // generate blocks 6,7,8,9 -> [block1, block2, block3, block6, block7, block8, block9]
        let block6 = block_tree.new_block(block3.id.clone(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        let block7 = block_tree.new_block(block6.id.clone(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        let block8 = block_tree.new_block(block7.id.clone(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();
        let block9 = block_tree.new_block(block8.id.clone(), Vec::new(), EMPTY_MERKLE.clone(), 0, 0, 1).unwrap();

        // block_tree receive locator, the common ancestor is block3
        // find and return path from common ancestor to best block
        // generate block path will be [block3, block6, block7, block8, block9]
        let block_path = block_tree.receive_locator(locator.clone());
        println!("block_path: {:?}", block_path);
        assert_eq!(block_path, [block3.clone(), block6.clone(), block7.clone(), block8.clone(), block9.clone()]);

        // test receive_sync_blocks
        block_tree.remove(block9.id.clone());
        block_tree.remove(block8.id.clone());
        block_tree.remove(block7.id.clone());
        block_tree.remove(block6.id.clone());
        block_tree.set_best_block_id(block3.id.clone()).unwrap();

        // receive_sync_blocks
        // block_tree -> [block1, block2, block3, block6, block7, block8, block9]
        let result = block_tree.receive_sync_blocks(block_path.clone());
        println!("result: {:?}", result);
        assert_eq!(result, "success");
        assert_eq!(block_tree.size(), 7);
        assert_eq!(block_tree.get_best_block().unwrap(), block9.clone());
    }
}
