TMP_DIR := ./tmp
RESULT_DIR := ./result
WASM_FILES := $(RESULT_DIR)/*.wasm
TMP_WASM_FILES := $(TMP_DIR)/*.wasm
MOVE_TARGET_DIR := ../..

build:
	@cp -aLR dependency/ dependency-nix/
	@git add dependency-nix/ > /dev/null 2>&1
	nix build . || true
	@git reset dependency-nix/ > /dev/null 2>&1
	@rm -rf dependency-nix/
	@mkdir -p $(TMP_DIR)
	@echo "Copy wasm files to $(MOVE_TARGET_DIR)"
	@cp $(WASM_FILES) $(TMP_DIR)/
	@chmod 775 $(TMP_WASM_FILES)
	@mv -f $(TMP_WASM_FILES) $(MOVE_TARGET_DIR)
	@rm -rf $(TMP_DIR)
	@echo "Build success"

clean:
	@rm -rf $(TMP_DIR)
	@rm -rf $(RESULT_DIR)
	@echo "Clean success"

all: build
