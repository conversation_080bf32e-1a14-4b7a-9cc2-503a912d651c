#![allow(unused_variables, unused_mut, dead_code, unused_imports)]

use std::string::String;

use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use wasm_py_bridge_macro::wasm_bind;

use crate::model::implement::token::{Token, TokenModel};
use crate::model::token::ITokenModel;

pub mod model;

#[wasm_bind]
fn print_env() {
    println!("----- print_env -----");
    let mut env_vars: Vec<_> = std::env::vars().collect();

    env_vars.sort();
    for (k, v) in env_vars {
        println!("{} = {}", k, v);
    }
    println!("-------- end --------");
}

#[wasm_bind]
// function create new token with token_id as parameter
fn new_token(token_id: String) -> Result<()> {
    let token_model = TokenModel::new();
    token_model.create_table(&token_id);
    Ok(())
}

#[wasm_bind]
fn issue(token_id: String, account: String, amount: i64) -> Result<()> {
    if amount <= 0 {
        return Err(anyhow!("Issue amount must be greater than 0"));
    }
    // get account and token_id
    _new_token(token_id.to_string())?;
    // Add amount for account
    add_amount(&token_id, &account, amount)
}

#[wasm_bind]
fn transfer(token_id: String, from: String, to: String, amount: i64) -> Result<()> {
    if amount <= 0 {
        return Err(anyhow!("Transfer amount must be greater than 0"));
    }
    // get string of account and token_id
    _new_token(token_id.to_string())?;
    // transfer token amount from account to account
    if !check_amount(&token_id, &from, amount) {
        // Insufficient balance
        return Err(anyhow!("Insufficient sending address balance"));
    }
    add_amount(&token_id, &from, -amount)?;
    add_amount(&token_id, &to, amount)?;
    Ok(())
}

#[wasm_bind]
fn balance(token_id: String, account: String) -> Result<i64> {
    // get string of account and token_id
    _new_token(token_id.to_string())?;
    // get balance
    let balance = get_amount(&token_id, &account);
    Ok(balance)
}

fn get_amount(token_id: &str, account: &str) -> i64 {
    let token_model = TokenModel::new();
    let token = token_model.get(token_id, account, None);
    match token {
        Some(token) => token.amount,
        None => 0,
    }
}

fn add_amount(token_id: &str, account: &str, amount: i64) -> Result<()> {
    if !check_amount(token_id, account, -amount) {
        return Err(anyhow!("Insufficient balance"));
    }
    let token_model = TokenModel::new();
    let token = token_model.get(token_id, account, None);
    match token {
        Some(mut token) => {
            token.amount += amount;
            token_model.update(token_id, &token, None)?;
        }
        None => {
            let token = Token {
                address: account.to_string(),
                amount,
            };
            token_model.insert(token_id, &token, None)?;
        }
    }
    Ok(())
}

fn check_amount(token_id: &str, account: &str, amount: i64) -> bool {
    if amount <= 0 {
        return true;
    }
    let token_model = TokenModel::new();
    let token = token_model.get(token_id, account, None);
    match token {
        Some(token1) => {
            return token1.amount >= amount;
        }
        None => false,
    }
}

#[cfg(test)]
mod test {
    use super::*;

    fn reset_conn() {
        let conn_str = "vgraph.db";
        std::fs::remove_file(conn_str).unwrap_or(());
    }

    #[test]
    fn test_new_token() {
        reset_conn();
        let token_id = "test_new_token";
        _new_token(token_id.to_string()).expect("Create token failed");
        let token_model = TokenModel::new();
        assert_eq!(token_model.table_exists(token_id), true);
    }

    #[test]
    fn test_add_amount() {
        reset_conn();
        let token_id = "test_add_amount";
        let account = "account";
        let amount = 100;
        _new_token(token_id.to_string()).expect("Create token failed");
        add_amount(token_id, account, amount).expect("Add amount failed");
        let token_model = TokenModel::new();
        let token = token_model.get(token_id, account, None);
        match token {
            Some(token) => assert_eq!(token.amount, amount),
            None => assert_eq!(false, true),
        }
    }

    #[test]
    fn test_issue() {
        reset_conn();
        let token_id = "test_issue";
        let account = "test_issue_account";
        let amount = 100;
        _new_token(token_id.to_string()).expect("Create token failed");
        _issue(token_id.to_string(), account.to_string(), amount).expect("Issue failed");
        let token_model = TokenModel::new();
        let token = token_model.get(token_id, account, None);
        match token {
            Some(token) => assert_eq!(token.amount, amount),
            None => assert_eq!(false, true),
        }
    }

    #[test]
    fn test_transfer() {
        reset_conn();
        let token_id = "test_transfer";
        let from = "test_transfer_from";
        let to = "test_transfer_to";
        let amount = 100;
        _issue(token_id.to_string(), from.to_string(), amount).expect("Issue failed");
        let token_model = TokenModel::new();
        let token_from_balance = get_amount(token_id, from);
        let token_to_balance = get_amount(token_id, to);
        assert_eq!(token_from_balance, amount);
        assert_eq!(token_to_balance, 0);

        _transfer(token_id.to_string(), from.to_string(), to.to_string(), amount)
            .expect("Transfer failed");
        let token_from_balance = get_amount(token_id, from);
        let token_to_balance = get_amount(token_id, to);
        assert_eq!(token_from_balance, 0);
        assert_eq!(token_to_balance, amount);
    }
}