use rusqlite::Transaction;

use crate::model::implement::token::Token;

pub trait ITokenModel {
    fn count(&self, token_id: &str) -> i64;
    fn insert(&self, token_id: &str, item: &Token, tx: Option<&Transaction>) -> rusqlite::Result<usize>;
    fn get(&self, token_id: &str, address: &str, tx: Option<&Transaction>) -> Option<Token>;
    fn update(&self, token_id: &str, item: &Token, tx: Option<&Transaction>) -> rusqlite::Result<usize>;
    fn delete(&self, token_id: &str, address: &str, tx: Option<&Transaction>) -> rusqlite::Result<usize>;
}