use super::sqlite::get_conn;
use rusqlite::*;

// exec_trans executes a function in a transaction
pub fn execute_sqlite_transactions<F>(func: F) -> Result<()>
where
    F: FnOnce(&Transaction) -> Result<()>,
{
    // get connection and start a transaction
    let mut conn = get_conn();
    let tx = conn.transaction()?;

    // execute the function
    let result = func(&tx);

    match result {
        Ok(()) => tx.commit(), // commit transaction
        Err(e) => {
            tx.rollback()?; // rollback transaction
            Err(e) // return error
        }
    }
}