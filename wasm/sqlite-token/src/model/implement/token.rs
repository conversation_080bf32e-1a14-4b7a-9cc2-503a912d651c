use rusqlite::{params, Transaction};
use serde::{Deserialize, Serialize};

use crate::model::sqlite::get_conn;
use crate::model::token::ITokenModel;

#[derive(Debug, Serialize, Deserialize)]
pub struct Token {
    pub address: String,
    pub amount: i64,
}

pub struct TokenModel;

impl TokenModel {
    pub fn new() -> Self {
        Self {}
    }

    pub fn create_table(&self, token_id: &str) {
        let sql = format!(
            "CREATE TABLE IF NOT EXISTS {} (
            address TEXT PRIMARY KEY,
            amount INTEGER
        )", token_id);

        let conn = get_conn();
        match conn.execute(&sql, params![]) {
            Ok(_) => (),
            Err(err) => println!("create block tree table error: {:?}", err),
        };
    }

    pub fn table_exists(&self, token_id: &str) -> bool {
        let sql = format!(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='{}';",
            token_id
        );

        let conn = get_conn();
        let mut stmt = match conn.prepare(&sql) {
            Ok(stmt) => stmt,
            Err(_) => return false,
        };
        let mut rows = match stmt.query([]) {
            Ok(rows) => rows,
            Err(_) => return false,
        };

        rows.next().is_ok()
    }
}

impl ITokenModel for TokenModel {
    fn count(&self, token_id: &str) -> i64 {
        let sql = format!("SELECT COUNT(*) FROM {}", token_id);
        let conn = get_conn();
        let mut stmt = conn.prepare(&sql).unwrap();
        let mut rows = stmt.query(params![]).unwrap();
        let count: i64 = rows.next().unwrap().unwrap().get(0).unwrap();
        count
    }

    fn insert(&self, token_id: &str, item: &Token, tx: Option<&Transaction>) -> rusqlite::Result<usize> {
        let sql = format!("INSERT INTO {} (address, amount) VALUES (?1, ?2)", token_id);
        let conn = get_conn();
        match tx {
            Some(tx) => tx.execute(&sql, params![item.address, item.amount]),
            None => conn.execute(&sql, params![item.address, item.amount]),
        }
    }

    fn get(&self, token_id: &str, address: &str, tx: Option<&Transaction>) -> Option<Token> {
        let sql = format!("SELECT * FROM {} WHERE address = ?1", token_id);
        let sql_params = params![address];

        match tx {
            // if given transaction context, use transaction
            Some(tx) => {
                let mut stmt = match tx.prepare(&sql) {
                    Ok(stmt) => stmt,
                    Err(_) => return None,
                };

                let iter = stmt.query_row(sql_params, |row| {
                    Ok(Token {
                        address: row.get(0)?,
                        amount: row.get(1)?,
                    })
                });

                match iter {
                    Ok(item) => Some(item),
                    Err(_) => None,
                }
            }
            None => {
                let conn = get_conn();

                let mut stmt = match conn.prepare(&sql) {
                    Ok(stmt) => stmt,
                    Err(_) => return None,
                };

                let iter = stmt.query_row(sql_params, |row| {
                    Ok(Token {
                        address: row.get(0)?,
                        amount: row.get(1)?,
                    })
                });

                match iter {
                    Ok(item) => Some(item),
                    Err(_) => None,
                }
            }
        }
    }

    fn update(&self, token_id: &str, item: &Token, tx: Option<&Transaction>) -> rusqlite::Result<usize> {
        let sql = format!("UPDATE {} SET amount = ?1 WHERE address = ?2", token_id);
        let conn = get_conn();
        match tx {
            Some(tx) => tx.execute(&sql, params![item.amount, item.address]),
            None => conn.execute(&sql, params![item.amount, item.address]),
        }
    }

    fn delete(&self, token_id: &str, address: &str, tx: Option<&Transaction>) -> rusqlite::Result<usize> {
        let sql = format!("DELETE FROM {} WHERE address = ?1", token_id);
        let conn = get_conn();
        match tx {
            Some(tx) => tx.execute(&sql, params![address]),
            None => conn.execute(&sql, params![address]),
        }
    }
}


// unit test
#[cfg(test)]
mod tests {
    use std::fs::remove_file;

    use crate::model::sqlite::reset_conn;

    use super::*;

    #[test]
    fn test_token_model() {
        let conn_str = "vgraph.db";
        remove_file(conn_str).unwrap_or(());
        reset_conn(); // reset connection
        let token_model = TokenModel::new();

        let token_id = "test_token";
        let token = Token {
            address: "test_address".to_string(),
            amount: 100,
        };

        token_model.create_table(token_id);
        assert_eq!(token_model.table_exists(token_id), true);
        assert_eq!(token_model.count(token_id), 0);
        token_model.insert(token_id, &token, None).unwrap();
        assert_eq!(token_model.count(token_id), 1);
        let token_get = token_model.get(token_id, "test_address", None).unwrap();
        assert_eq!(token_get.address, "test_address");
        assert_eq!(token_get.amount, 100);
        let token_update = Token {
            address: "test_address".to_string(),
            amount: 200,
        };
        token_model.update(token_id, &token_update, None).unwrap();
        let token_get = token_model.get(token_id, "test_address", None).unwrap();
        assert_eq!(token_get.amount, 200);
        token_model.delete(token_id, "test_address", None).unwrap();
        assert_eq!(token_model.count(token_id), 0);
    }
}

