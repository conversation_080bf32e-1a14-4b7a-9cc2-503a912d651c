[package]
name = "token"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
rusqlite = { version = "0.29.0", features = ["bundled", "wasm32-wasi-vfs"] }
wasm-py-bridge-macro = { path = "../wasm-py-bridge-macro" }
wasm-utils = { path = "../wasm-utils" }

serde = { version = "=1.0.193", features = ["derive"] }
serde_json = "=1.0.108"
lazy_static = "=1.4.0"
anyhow = "=1.0.75"

[lib]
crate-type = ["cdylib", "rlib"]
