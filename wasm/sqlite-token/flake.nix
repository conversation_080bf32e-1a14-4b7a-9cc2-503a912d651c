{
  description = "Rust wasm32-wasi build with wasi-sdk";

  inputs = {
    flake-utils.url = "github:numtide/flake-utils";
    rust-overlay = {
      url = "github:oxalica/rust-overlay";
      inputs.nixpkgs.follows = "nixpkgs";
    };
    nixpkgs.url = "nixpkgs/nixos-23.11";
  };

  outputs = { self, nixpkgs, flake-utils, rust-overlay }:
    flake-utils.lib.eachSystem [ "x86_64-linux" "aarch64-darwin" "aarch64-linux" "x86_64-darwin" ]
      (system: let
        overlays = [ rust-overlay.overlays.default ];
        pkgs = import nixpkgs { inherit system overlays; };
        rust = pkgs.rust-bin.fromRustupToolchainFile ./rust-toolchain.toml;
        hostPlatform = pkgs.stdenv.hostPlatform;
        wasiSdkDerivation = { fetchurl, runtimeShellPackage, stdenv, stdenvNoCC, unzip, }:
          let
            common-src = builtins.fromJSON (builtins.readFile ./nix-repo.json);
            wasi-sdk-key = if hostPlatform.isDarwin then "wasi-sdk_darwin" else "wasi-sdk_linux";
            wasi-sdk-src = fetchurl common-src."${wasi-sdk-key}";
          in
          stdenvNoCC.mkDerivation {
            name = "wasi-sdk";
            srcs = [ wasi-sdk-src ];
            setSourceRoot = "sourceRoot=$(echo wasi-sdk-*)";
            nativeBuildInputs = [ unzip ];
            buildInputs = [ runtimeShellPackage ];
            cc_for_build = "${stdenv.cc}/bin/cc";
            installPhase = ''
              cp -a . $out
              chmod -R u+w $out

              mkdir -p $out/env
              cat <<EOF > $out/env/setup-env.sh
              export WASI_SDK_PATH=$out
              export WASI_SYSROOT=$out/share/wasi-sysroot
              export CC="$out/bin/clang --sysroot=$out/share/wasi-sysroot"
              export AR="$out/bin/llvm-ar"
              export CC_wasm32_wasi="$out/bin/clang --sysroot=$out/share/wasi-sysroot"
              export CARGO_TARGET_WASM32_WASI_LINKER="$out/bin/clang"
              export LIBSQLITE3_FLAGS="-USQLITE_TEMP_STORE -DSQLITE_TEMP_STORE=3 -USQLITE_THREADSAFE -DSQLITE_THREADSAFE=0 -DSQLITE_OMIT_LOCALTIME -DSQLITE_OMIT_LOAD_EXTENSION -DLONGDOUBLE_TYPE=double"
              export RUSTFLAGS="-C link-arg=-Wl,--export-dynamic,--no-entry"
              EOF

              chmod +x $out/env/setup-env.sh
            '';
            dontFixup = true;
            allowedReferences = [ "out" runtimeShellPackage stdenv.cc ];
          };

        wasi-sdk = wasiSdkDerivation { inherit (pkgs) fetchurl runtimeShellPackage stdenv stdenvNoCC unzip; };
      in {
        packages = {
          rust-wasm32-wasi = pkgs.rustPlatform.buildRustPackage {
            pname = "rust-wasm32-wasi";
            version = "1.0.0";
            src = ./.;
            cargoLock = {
              lockFile = ./Cargo.lock;
            };
            nativeBuildInputs = [ rust wasi-sdk ];

            postPatch = ''
              substituteInPlace Cargo.toml --replace "dependency/" "dependency-nix/"
            '';

            checkPhase = "echo 'skip checkPhase'";

            buildPhase = ''
              source ${wasi-sdk}/env/setup-env.sh;
              mkdir -p $out;
              cargo build --verbose --lib --target=wasm32-wasi;
              cp target/wasm32-wasi/debug/*.wasm $out;
            '';

            installPhase = "echo 'skip installPhase'";
          };

          wasi-sdk = wasi-sdk;
        };
        defaultPackage = self.packages.${system}.rust-wasm32-wasi;
      });
}
