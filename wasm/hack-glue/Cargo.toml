[package]
resolver = "2"
name = "hack-glue"
version = "0.1.0"
edition = "2021"

[dependencies]
glue-macro = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
regex = { workspace = true }
uuid = { workspace = true }

[dev-dependencies]
serial_test = { workspace = true }

[workspace]
members = ["glue-macro"]
exclude = []

[workspace.dependencies]
syn = { version = "2.0.61", features = ["full", "fold"] }
quote = "1.0.33"
proc-macro2 = "1.0.82"
serde = { version = "1.0.193", features = ["derive"] }
serde_json = "1.0.108"
regex = "1.10.4"
anyhow = "1.0.75"
uuid = { version = "1.8.0", features = ["serde", "v4", "fast-rng"] }
serial_test = "3.1.1"

# Local Dependencies
glue-macro = { path = "glue-macro" }

[profile.release]
panic = "abort"
lto = true
