use std::cmp::max;
use std::collections::HashSet;

use proc_macro2::Ident;
use quote::{format_ident, quote, ToTokens};
use syn::{Item, parse_str};
use syn::punctuated::Punctuated;
use syn::token::Comma;

use crate::utils::{camel_to_snake_case, find_macro};

pub fn process_impl(gen_code: &mut proc_macro2::TokenStream, module_name: &Ident, items: &mut Vec<Item>) {
    let mut readonly_atomic_macro_count = 0;
    let mut constructor_macro_count = 0;
    for item in items.iter_mut() {
        match item {
            // Impl block
            Item::Impl(impl_block) => {
                // Traverse all items in the impl block
                for mut item in &mut impl_block.items {
                    match item {
                        // If the item is a function
                        syn::ImplItem::Fn(ref mut method) => {
                            // Linking module_name and impl struct name to method's name.
                            let struct_name = format_ident!("{}", impl_block.self_ty.to_token_stream().to_string());
                            let fn_name = method.sig.ident.clone();
                            let export_name = format_ident!("{}", fn_name);

                            // Match method macro
                            for attr in &method.attrs {
                                if find_macro(attr, "constructor") { // hack_glue::constructor
                                    constructor_macro_count += 1;
                                    // Get old function argument list and return type
                                    let args = &method.sig.inputs;
                                    let args_names = args.iter().map(|arg| {
                                        if let syn::FnArg::Typed(arg) = arg {
                                            arg.pat.to_token_stream()
                                        } else {
                                            quote! {}
                                        }
                                    }).collect::<Vec<_>>();

                                    gen_code.extend(quote! {
                                            #[hack_glue::wasm_bind]
                                            fn #export_name(#args) {
                                                let instance = get_instance();
                                                *instance = #struct_name::new(#(#args_names),*);
                                            }
                                        });

                                    let mut block_code_string: String = method.block.to_token_stream().to_string();

                                    // Apply the changes to the block
                                    method.block = parse_str(block_code_string.as_str()).unwrap();
                                } else if find_macro(attr, "atomic") || find_macro(attr, "readonly") { // hack_glue::atomic and hack_glue::readonly
                                    readonly_atomic_macro_count += 1;
                                    // Get args without self
                                    let args = &method.sig.inputs.iter()
                                        .skip(1).collect::<Punctuated<_, Comma>>();

                                    let args_names = args.iter().map(|arg| {
                                        if let syn::FnArg::Typed(arg) = arg {
                                            arg.pat.to_token_stream()
                                        } else {
                                            quote! {}
                                        }
                                    }).collect::<Vec<_>>();

                                    let fn_output = &method.sig.output;

                                    let atomic_fn = quote! {
                                            #[hack_glue::wasm_bind]
                                            pub extern fn #export_name(#args) #fn_output {
                                                get_instance().#fn_name(#(#args_names),*)
                                            }
                                        };
                                    gen_code.extend(atomic_fn);
                                }
                            }
                        }
                        _ => {}
                    }
                }
            }
            _ => {}
        }
    }
    // Check if there are more than one constructor macro
    if constructor_macro_count > 1 {
        panic!("Only one constructor function is allowed in a contract");
    } else if constructor_macro_count == 0 {
        panic!("One constructor function is required in the contract");
    }

    // Check if there are more than one readonly or atomic macro
    if readonly_atomic_macro_count == 0 {
        panic!("At least one atomic or readonly macro is required in the contract");
    }
}
