#![allow(unused_variables, unused_mut, dead_code, unused_imports)]

use proc_macro::TokenStream;
use quote::{format_ident, quote, ToTokens};
use regex::Regex;
use std::sync::atomic::{AtomicBool, Ordering};
use syn::parse::{Parse, Parser};
use syn::punctuated::Punctuated;
use syn::token::Comma;
use syn::{parse_macro_input, ImplItemFn, ItemMod, ItemStruct, Visibility};
use syn::{Block, FnArg, Ident, ItemFn, PatType, ReturnType};

mod utils;
mod struct_handle;
mod impl_handle;

static CONTRACT_MACRO_HAS_RUN: AtomicBool = AtomicBool::new(false);

#[proc_macro_attribute]
pub fn contract(args: TokenStream, item: TokenStream) -> TokenStream {
    if CONTRACT_MACRO_HAS_RUN.compare_exchange(false, true, Ordering::SeqCst, Ordering::SeqCst).is_err() {
        panic!("One rust project only can have one contract!");
    }

    let mut module = parse_macro_input!(item as ItemMod);
    let mut gen_code = quote! {};
    let module_name = module.ident.clone();
    // Make module visibility public
    module.vis = Visibility::Public(Default::default());
    let module_name_str = module_name.to_string();

    // Insert env code
    gen_code.extend(quote! {
        #[allow(dead_code)]
        #[inline]
        pub fn env() -> hack_glue::env::Environment {
            hack_glue::env::get_env().clone()
        }
    });

    // Traverse all items in the module
    if let Some((_, items)) = &mut module.content {
        // Process the struct block
        struct_handle::process_struct(&mut gen_code, &module_name, items);

        // Process the impl block
        impl_handle::process_impl(&mut gen_code, &module_name, items);
    }


    let parsed_file: syn::File = syn::parse2(gen_code).unwrap();
    module.content.as_mut().map(|(_, items)| {
        items.extend(parsed_file.items);
    });

    let expanded = quote! {
        #module
    };

    // print code
    // println!("{}", TokenStream::from(expanded.clone()).to_string());

    TokenStream::from(expanded)
}


#[proc_macro_attribute]
pub fn storage(_attr: TokenStream, item: TokenStream) -> TokenStream {
    let mut item_struct = parse_macro_input!(item as ItemStruct);

    // Create the derive attribute
    let serialize_attr = syn::parse_quote!(#[derive(Default)]);
    item_struct.attrs.push(serialize_attr);

    // Generate the output tokens from the modified struct
    TokenStream::from(quote!(#item_struct))
}

#[proc_macro_attribute]
pub fn storage_item(_attr: TokenStream, item: TokenStream) -> TokenStream {
    let mut item_struct = parse_macro_input!(item as ItemStruct);

    // Create the derive attribute
    let serialize_attr = syn::parse_quote!(#[derive(serde::Serialize, serde::Deserialize, Eq, PartialEq, Clone, Debug, Default)]);
    item_struct.attrs.push(serialize_attr);

    // Generate the output tokens from the modified struct
    TokenStream::from(quote!(#item_struct))
}


#[proc_macro_attribute]
pub fn test(attr: TokenStream, item: TokenStream) -> TokenStream {
    let input = parse_macro_input!(item as ItemFn);

    let syn::ItemFn { attrs, vis, sig, block } = input;

    let new_block = quote! {
        {
            hack_glue::env::set_test_env();
            #block
        }
    };

    let output = quote! {
        #(#attrs)*
        #[test]
        #[serial_test::serial]
        #vis #sig
        #new_block
    };

    output.into()
}


#[proc_macro_attribute]
pub fn constructor(args: TokenStream, item: TokenStream) -> TokenStream {
    let input_fn = parse_macro_input!(item as ImplItemFn);

    // Error if method name is not new
    if input_fn.sig.ident != "new" {
        return TokenStream::from(quote! {
            compile_error!("The constructor function must be named `new`");
        });
    }


    let expanded = quote! {
        #input_fn
    };

    // print code
    // println!("{}", TokenStream::from(expanded.clone()).to_string());

    TokenStream::from(expanded)
}

#[proc_macro_attribute]
pub fn atomic(_attr: TokenStream, item: TokenStream) -> TokenStream {
    item
}

#[proc_macro_attribute]
pub fn readonly(_attr: TokenStream, item: TokenStream) -> TokenStream {
    item
}

// Define a procedural macro for binding Rust functions WebAssembly to the universal interface
#[proc_macro_attribute]
pub fn wasm_bind(_attr: TokenStream, item: TokenStream) -> TokenStream {
    // Parse the input function
    let input_fn: ItemFn = parse_macro_input!(item as ItemFn);

    // Extract relevant parts of the function
    let fn_name: &Ident = &input_fn.sig.ident;
    let closure_fn_name: Ident = format_ident!("{}", fn_name);
    let fn_inputs: &Punctuated<FnArg, Comma> = &input_fn.sig.inputs;
    let fn_output: &ReturnType = &input_fn.sig.output;
    let fn_block: &Box<Block> = &input_fn.block;

    // Process function arguments to create struct fields
    let mut struct_fields = Vec::new();
    let mut field_names: Vec<Ident> = Vec::new();
    let mut args: Vec<&FnArg> = Vec::new();
    // processing arguments
    for (i, arg) in fn_inputs.iter().enumerate() {
        args.push(arg);

        if let FnArg::Typed(PatType { ty, .. }) = arg {
            // Generate struct fields and field names
            // gen field name, like "p0", "p1", "p2"...
            let field_name = format_ident!("p{}", i.to_string());

            struct_fields.push(quote! { pub #field_name: #ty });
            field_names.push(field_name);
        }
    }

    // Convert function name to camel case for the params struct
    let fn_name_camel = fn_name
        .to_string()
        .split('_')
        .map(|word| {
            word.chars().enumerate().map(|(j, c)| {
                if j == 0 {
                    // Capitalize the first letter of each word
                    c.to_uppercase().to_string()
                } else {
                    // Other characters remain unchanged
                    c.to_string()
                }
            }).collect::<String>()
        })
        .collect::<String>();

    // Construct a struct for function parameters
    let params_struct_name: Ident = format_ident!("_{}Params", fn_name_camel);

    // construct params struct
    let mut params_struct_code = quote! {};
    // optional params code
    let mut params_env_code = quote! {
    };
    params_struct_code.extend(quote! {
        #[derive(serde::Serialize, serde::Deserialize)]
        struct #params_struct_name {
            #(#struct_fields),*
        }
    });
    // Code for handling parameters
    params_env_code.extend(quote! {
        let func_args: hack_glue::FuncArgs<#params_struct_name> = hack_glue::json_ptr_to_object(input_ptr).unwrap();
        hack_glue::env::set_env(func_args.envs);
        let params = func_args.args;
    });


    // Generate code for function return
    let mut return_code = quote! {};
    match fn_output {
        // for handling different return types
        ReturnType::Default => {
            // no return value
            return_code.extend(quote! {
                let #closure_fn_name = |#fn_inputs| #fn_output {
                    #fn_block
                };

                #closure_fn_name(#(params.#field_names),*);
                let serializable_result = hack_glue::SerializableResult::default();
                hack_glue::object_to_json_ptr(&serializable_result).unwrap()
            });
        }
        ReturnType::Type(_, type_box) => {
            // has return value

            // Call the hidden function with the struct as input
            return_code.extend(quote! {
                let #closure_fn_name = |#fn_inputs| #fn_output {
                    #fn_block
                };

                let result_values = #closure_fn_name(#(params.#field_names),*);
            });

            // Extract the return type name
            let mut return_type_name = quote! { #type_box }.to_string();
            let regex = Regex::new(r"<.*?>").unwrap();
            return_type_name = regex.replace_all(&return_type_name, "").to_string();

            // Check if the return type is a Result
            if return_type_name.contains("Result") {
                return_code.extend(quote! {
                    let serializable_result = hack_glue::SerializableResult::from(result_values);
                });
            } else {
                // If the return type is not a Result, wrap it in a Result
                return_code.extend(quote! {
                    let serializable_result = hack_glue::SerializableResult::from(Ok(result_values));
                });
            }

            // Convert the return value to a JSON string
            return_code.extend(quote! {
                hack_glue::object_to_json_ptr(&serializable_result).unwrap()
            });
        }
    }

    // Generate the final code
    // Expose the function to WebAssembly with the original name
    // Convert the function parameters to a struct
    // Call the hidden function with the struct as input
    let gen = quote! {
        #[cfg(target_arch = "wasm32")]
        #params_struct_code

        #[cfg(target_arch = "wasm32")]
        #[no_mangle]
        pub extern "C" fn #fn_name(input_ptr: *mut std::ffi::c_char) -> *mut std::ffi::c_char {
            #params_env_code
            #return_code
        }
    };

    gen.into()
}
