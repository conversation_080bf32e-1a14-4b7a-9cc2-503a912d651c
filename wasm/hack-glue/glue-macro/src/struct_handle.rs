use proc_macro2::Ident;
use quote::{format_ident, quote, ToTokens};
use syn::{parse_str, Item};

use crate::utils::{camel_to_snake_case, extract_between_angle_brackets, extract_map_types, extract_vec_type, find_macro};

pub fn process_struct(gen_code: &mut proc_macro2::TokenStream, module_name: &Ident, items: &mut Vec<Item>) {
    // Check the storage struct macro, must have one and only one
    let mut count_storage_macro: u32 = 0;
    for item in items.iter_mut() {
        match item {
            // Struct block
            Item::Struct(ref mut struct_block) => {
                // If the struct has a storage attribute
                for attr in &struct_block.attrs {
                    if find_macro(attr, "storage") { // hack_glue::storage
                        count_storage_macro += 1;
                        if count_storage_macro > 1 {
                            panic!("Only one storage struct is allowed in a contract");
                        }
                        let struct_name = &struct_block.ident;

                        let mut params_list_key_type_code = quote! {};

                        // get filed name and type
                        for field in &mut struct_block.fields {
                            let field_name = field.ident.as_ref().unwrap();
                            let field_type = &field.ty;
                            // If type is not hack_glue::collections::Map
                            let key_name = format!("{}", field_name);
                            let key_type_struct_name = format_ident!("{}_{}", struct_name, field_name);

                            let field_type_string = field_type.to_token_stream().to_string();
                            if !field_type_string.contains("StorageField") && !field_type_string.contains("Map") && !field_type_string.contains("Vec") {
                                panic!("not support storage type, glue only support hack_glue::StorageField or type in hack_glue::collections");
                            }

                            params_list_key_type_code.extend(quote! {
                                #[allow(non_camel_case_types)]
                                pub struct #key_type_struct_name;
                                impl hack_glue::StorageKey for #key_type_struct_name {
                                    fn key() -> String {
                                        format!("{}-{}", env().contract_address, #key_name)
                                    }
                                }
                            });

                            if field_type_string.contains("Map") {
                                // modify filed type,map key maybe a tuple
                                let map_key_value_types = extract_map_types(&field_type_string);
                                if let Some((map_key_type, map_value_type)) = map_key_value_types {
                                    field.ty = parse_str(&format!("hack_glue::collections::Map<{}, {}, {}>", map_key_type, map_value_type, key_type_struct_name).to_string()).unwrap();
                                } else {
                                    panic!("Map key value types not found")
                                }
                            } else if field_type_string.contains("Vec") {
                                // modify filed type
                                let map_key_value_types = extract_vec_type(&field_type_string);
                                if let Some(value_type) = map_key_value_types {
                                    field.ty = parse_str(&format!("hack_glue::collections::Vec<{}, {}>", value_type, key_type_struct_name).to_string()).unwrap();
                                } else {
                                    panic!("Vec value types not found")
                                }
                            } else if field_type_string.contains("StorageField") {
                                // get the content in <>
                                let mut storage_field_type = extract_between_angle_brackets(&field_type_string).unwrap();
                                field.ty = parse_str(&format!("hack_glue::StorageField<{}, {}>", storage_field_type, key_type_struct_name).to_string()).unwrap();
                            }
                        }

                        gen_code.extend(quote! {
                                    #params_list_key_type_code

                                    static mut INSTANCE: std::cell::UnsafeCell<Option<#struct_name>> = std::cell::UnsafeCell::new(None);

                                    #[allow(dead_code)]
                                    #[inline]
                                    pub fn get_instance<'a>() ->  &'a mut #struct_name {
                                         unsafe {
                                            if (*INSTANCE.get()).is_none() {
                                                *INSTANCE.get() = Some(#struct_name::default());
                                            }
                                            (&mut *INSTANCE.get()).as_mut().unwrap()
                                        }
                                    }

                                    // only use in test to register contract
                                    #[cfg(not(target_arch = "wasm32"))]
                                    #[allow(dead_code)]
                                    pub fn set_instance(instance: #struct_name) {
                                        unsafe
                                        {
                                            *INSTANCE.get() = Some(instance);
                                        }
                                    }

                                });
                    }
                }
            }
            _ => {}
        }
    }
    if count_storage_macro == 0 {
        panic!("There is no storage structure in the contract, there should be one and only one storage structure in the contract");
    }
}
