use std::collections::HashMap;

use regex::Regex;

pub fn find_macro(attr: &syn::Attribute, name: &str) -> bool {
    if let Some(last) = attr.path().segments.last() {
        return last.ident == name && attr.path().segments.iter()
            .map(|seg| seg.ident.to_string()).collect::<Vec<_>>() == vec!["hack_glue", name];
    }
    false
}


pub fn extract_map_types(map_str: &str) -> Option<(String, String)> {
    let re = Regex::new(r".*Map\s*<\s*(.+)\s*>\s*").unwrap();
    if let Some(captures) = re.captures(map_str) {
        let types_str = captures.get(1)?.as_str();
        let (key_type, value_type) = split_key_value(types_str)?;
        return Some((key_type.trim().to_string(), value_type.trim().to_string()));
    }
    None
}

fn split_key_value(types_str: &str) -> Option<(&str, &str)> {
    let mut brackets = 0;
    for (i, ch) in types_str.chars().enumerate() {
        match ch {
            '<' | '(' | '[' => brackets += 1,
            '>' | ')' | ']' => brackets -= 1,
            ',' if brackets == 0 => return Some((types_str[..i].trim(), types_str[i + 1..].trim())),
            _ => {}
        }
    }
    None
}

pub fn extract_vec_type(vec_str: &str) -> Option<String> {
    let re = Regex::new(r".*Vec\s*<\s*(.+)\s*>\s*").unwrap();
    if let Some(captures) = re.captures(vec_str) {
        let type_str = captures.get(1)?.as_str();
        return Some(type_str.trim().to_string());
    }
    None
}

pub fn camel_to_snake_case(input: &str) -> String {
    let mut snake_case = String::new();
    let mut prev_char_was_upper = false;

    for (i, c) in input.chars().enumerate() {
        if c.is_uppercase() {
            if i > 0 && !prev_char_was_upper {
                snake_case.push('_');
            }
            snake_case.push(c.to_lowercase().next().unwrap());
            prev_char_was_upper = true;
        } else {
            snake_case.push(c);
            prev_char_was_upper = false;
        }
    }

    snake_case
}

pub fn extract_between_angle_brackets(input: &str) -> Option<String> {
    let mut start = None;
    let mut count = 0;

    for (i, c) in input.char_indices() {
        if c == '<' {
            if start.is_none() {
                start = Some(i);
            }
            count += 1;
        } else if c == '>' {
            count -= 1;
            if count == 0 {
                return start.map(|s| input[s + 1..i].to_string());
            }
        }
    }

    None
}


#[cfg(test)]
mod test {
    use super::*;

    #[test]
    fn test_extract_map_types() {
        let inputs_and_expected = vec![
            ("glue :: collections :: Map < (String, String), i32 >", Some(("(String, String)", "i32"))),
            ("hack_glue::collections::Map<(String, String), (i32, i32)>", Some(("(String, String)", "(i32, i32)"))),
            ("Map<String, i32>", Some(("String", "i32"))),
            ("Map<String, HA<i32, i32>>", Some(("String", "HA<i32, i32>"))),
            ("Map<String,（HA<i32, i32>,HA<i32, i32>)>", Some(("String", "（HA<i32, i32>,HA<i32, i32>)"))),
        ];

        for (input, expected) in inputs_and_expected {
            let result = extract_map_types(input);
            assert_eq!(
                result.map(|(k, v)| (k.to_string(), v.to_string())),
                expected.map(|(k, v)| (k.to_string(), v.to_string())),
                "Failed on input: {}",
                input
            );
        }
    }

    #[test]
    fn test_extract_vec_type() {
        let inputs_and_expected = vec![
            ("Vec<String>", Some("String")),
            ("Vec< (String, String) >", Some("(String, String)")),
            ("hack_glue::collections::Vec<i32>", Some("i32")),
            ("Vec<HA<i32, i32>>", Some("HA<i32, i32>")),
            ("Vec<(HA<i32, i32>, HA<i32, i32>)>", Some("(HA<i32, i32>, HA<i32, i32>)")),
        ];

        for (input, expected) in inputs_and_expected {
            let result = extract_vec_type(input);
            assert_eq!(
                result.map(|v| v.to_string()),
                expected.map(|v| v.to_string()),
                "Failed on input: {}",
                input
            );
        }
    }

    #[test]
    fn test_camel_to_snake_case() {
        let camel_case = "camelCaseString";
        let snake_case = camel_to_snake_case(camel_case);
        assert_eq!(snake_case, "camel_case_string");

        let camel_case_with_capital = "CamelCaseString";
        let snake_case_capital = camel_to_snake_case(camel_case_with_capital);
        assert_eq!(snake_case_capital, "camel_case_string");
    }

    #[test]
    fn test_extract_between_angle_brackets() {
        let input = "Vec<(String, i32)>";
        let result = extract_between_angle_brackets(input);
        assert_eq!(result, Some("(String, i32)".to_string()));

        let input = "Vec<String>";
        let result = extract_between_angle_brackets(input);
        assert_eq!(result, Some("String".to_string()));

        let input = "Vec<i32>";
        let result = extract_between_angle_brackets(input);
        assert_eq!(result, Some("i32".to_string()));

        let input = "Vec<HA<i32, i32>>";
        let result = extract_between_angle_brackets(input);
        assert_eq!(result, Some("HA<i32, i32>".to_string()));

        let input = "Vec<(HA<i32, i32>, HA<i32, i32>)>";
        let result = extract_between_angle_brackets(input);
        assert_eq!(result, Some("(HA<i32, i32>, HA<i32, i32>)".to_string()));
    }
}
