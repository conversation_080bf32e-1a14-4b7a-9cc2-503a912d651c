#![allow(unused_imports, dead_code, unused_variables)]

use std::collections::HashMap;
#[cfg(target_arch = "wasm32")]
use std::ffi::c_char;
use std::marker::PhantomData;
use std::sync::{LazyLock, Mutex};

#[cfg(target_arch = "wasm32")]
use crate::{deallocate_c_char, get_string, string_to_c_char};

pub static CHANGE_LIST: LazyLock<Mutex<HashMap<String, Option<String>>>> = LazyLock::new(|| {
    Mutex::new(HashMap::new())
});


#[cfg(not(target_arch = "wasm32"))]
pub(crate) fn clear_change_list() {
    let mut storage = CHANGE_LIST.lock().unwrap();
    storage.clear();
}

#[cfg(target_arch = "wasm32")]
extern "C" {
    fn get_slot(key: *mut c_char) -> *mut c_char;
    fn readonly_panic();
}

#[inline]
pub(crate) fn storage_put(key: &str, value: &str) {
    #[cfg(not(target_arch = "wasm32"))]
    {
        let mut storage = CHANGE_LIST.lock().unwrap();
        // fix insert success but return None value for test env
        // HashMap.insert returns None if the key is not present in the map
        let _is_insert = storage.insert(String::from(key), Option::from(String::from(value))).is_none();
    }

    #[cfg(target_arch = "wasm32")]
    unsafe {
        let env = crate::env::get_env();
        if env.readonly {
            readonly_panic();
            panic!("storage is readonly");
        }

        let mut storage = CHANGE_LIST.lock().unwrap();
        storage.insert(key.to_string(), Some(value.to_string()));
    }
}

#[inline]
pub(crate) fn storage_get(key: &str) -> Option<String> {
    #[cfg(not(target_arch = "wasm32"))]
    {
        let storage = CHANGE_LIST.lock().unwrap();
        return storage.get(key).cloned().unwrap_or(None);
    }

    #[cfg(target_arch = "wasm32")]
    unsafe {
        let mut storage = CHANGE_LIST.lock().unwrap();
        if let Some(value) = storage.get(key).cloned() {
            return value;
        }

        let key_ptr = string_to_c_char(key);
        let env = crate::env::get_env();

        let value_ptr = get_slot(key_ptr);
        let value = get_string(value_ptr);
        deallocate_c_char(value_ptr);

        storage.insert(key.to_string(), value.clone());

        value
    }

}

#[inline]
pub(crate) fn storage_pop(key: &str) -> Option<String> {
    #[cfg(not(target_arch = "wasm32"))]
    {
        let mut storage = CHANGE_LIST.lock().unwrap();
        return storage.remove(key).unwrap_or(None);
    }

    #[cfg(target_arch = "wasm32")]
    unsafe {
        let env = crate::env::get_env();
        if env.readonly {
            readonly_panic();
            panic!("storage is readonly");
        }

        let mut storage = CHANGE_LIST.lock().unwrap();
        let value = storage.get(key).cloned();
        storage.insert(key.to_string(), None);

        match value {
            Some(value) => value,
            None => {
                // if value is not in the change list, get it from the storage
                let key_ptr = string_to_c_char(key);
                let value_ptr = get_slot(key_ptr);
                let value = get_string(value_ptr);
                deallocate_c_char(value_ptr);

                value
            }
        }
    }

}

/// Define a storage field used in hack_glue::storage struct field
pub struct StorageField<V: serde::Serialize + for<'a> serde::Deserialize<'a>, KeyPrefix: StorageKey = ()> {
    _marker: PhantomData<fn() -> (V, KeyPrefix)>,
    key: String,
}

impl<V: serde::Serialize + for<'a> serde::Deserialize<'a>, KeyPrefix: StorageKey> Default for StorageField<V, KeyPrefix> {
    #[inline]
    fn default() -> Self {
        Self {
            _marker: PhantomData,
            key: KeyPrefix::key(),
        }
    }
}

impl<V: serde::Serialize + for<'a> serde::Deserialize<'a>, KeyPrefix: StorageKey> StorageField<V, KeyPrefix> {
    #[inline]
    pub fn new(key: &V) -> Self {
        let field = Self {
            _marker: PhantomData,
            key: KeyPrefix::key(),
        };
        field.set(key);
        field
    }

    #[inline]
    pub fn set(&self, value: &V) {
        storage_put(&self.key, &serde_json::to_string(value).unwrap());
    }

    #[inline]
    pub fn get(&self) -> V {
        let value = storage_get(&self.key).unwrap();
        serde_json::from_str(&value).unwrap()
    }
}


pub trait StorageKey {
    /// Returns the storage key.
    fn key() -> String;
}

impl StorageKey for () {
    #[inline]
    fn key() -> String {
        "".to_string()
    }
}
