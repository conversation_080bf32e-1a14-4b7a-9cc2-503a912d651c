#[cfg(target_arch = "wasm32")]
use std::ffi::c_char;
use std::fmt::Debug;

use serde_json::{to_value, Value};

#[cfg(target_arch = "wasm32")]
use crate::{get_string, string_to_c_char};

#[cfg(target_arch = "wasm32")]
extern "C" {
    fn delegate_call(key: *mut c_char) -> *mut c_char;
}

#[derive(serde::Serialize, serde::Deserialize, Debug, Default)]
pub struct CallBuilder {
    contract_address: String,
    function_name: String,
    args: Vec<Value>,
}

#[derive(serde::Serialize, serde::Deserialize, Debug)]
pub struct CallResult<R: serde::Serialize> {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub value: Option<R>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub err: Option<String>,
}

/// `build_call` function initializes a `CallBuilder` which is used to build and configure
/// the necessary information for making a contract call. This method is especially useful
/// in the context of interacting with a smart contract through delegated calls.
///
/// The `CallBuilder` allows you to set key parameters for a contract call, such as the
/// contract address, contract name, structure name, function name, and arguments.
/// It supports method chaining, making the process of configuring the call easier and clearer.
///
/// This is typically used in conjunction with `call` and `call_no_return` to actually
/// execute the contract function.
///
/// # Example
///
/// ```rust
/// use hack_glue::call::build_call;
///
/// let call = build_call()
///     .contract_address("3e2a3d6055f044e587e53899b8f4c045")
///     .function_name("balance")
///     .push_arg("Account123");
///
/// // Call the contract function and retrieve the balance
/// let balance: Result<i64, anyhow::Error> = call.call();
/// ```
///
/// In this example:
/// - `build_call` initializes the builder with default values.
/// - `contract_address` and `function_name` are used
///   to specify the target contract and the function to be called.
/// - `push_arg` is used to pass arguments to the function.
/// - Finally, `call()` is used to execute the function and return the result.
/// - If no return value is expected, `call_no_return()` can be used instead.
pub fn build_call() -> CallBuilder {
    CallBuilder::default()
}

impl CallBuilder {
    #[inline]
    pub fn contract_address(mut self, contract_address: &str) -> Self {
        self.contract_address = contract_address.to_string();
        self
    }

    #[inline]
    pub fn function_name(mut self, function_name: &str) -> Self {
        self.function_name = function_name.to_string();
        self
    }

    #[inline]
    pub fn push_arg<T>(mut self, arg: T) -> Self
    where
        T: serde::Serialize,
    {
        if let Ok(val) = to_value(arg) {
            self.args.push(val);
        }
        self
    }

    #[inline]
    fn perform_call<R>(&self) -> anyhow::Result<CallResult<R>>
    where
        R: serde::Serialize + for<'a> serde::Deserialize<'a>,
    {
        #[cfg(target_arch = "wasm32")]
        {
            let key = string_to_c_char(&serde_json::to_string(&self)?);
            let result = unsafe { delegate_call(key) };
            let result_str = get_string(result).unwrap_or_default();
            let call_result: CallResult<R> = serde_json::from_str(&result_str)?;
            Ok(call_result)
        }
        #[cfg(not(target_arch = "wasm32"))]
        {
            Err(anyhow::anyhow!("CallBuilder::call is only available in wasm32 target"))
        }
    }

    #[inline]
    pub fn call<R>(&self) -> anyhow::Result<R>
    where
        R: serde::Serialize + for<'a> serde::Deserialize<'a>,
    {
        let call_result = self.perform_call::<R>()?;
        match call_result.value {
            Some(val) => Ok(val),
            None => Err(anyhow::anyhow!(call_result.err.unwrap_or_else(|| "Unknown error".to_string()))),
        }
    }

    #[inline]
    pub fn call_no_return(&self) -> anyhow::Result<()> {
        let call_result = self.perform_call::<()>()?;
        if let Some(err) = call_result.err {
            Err(anyhow::anyhow!(err))
        } else {
            Ok(())
        }
    }
}