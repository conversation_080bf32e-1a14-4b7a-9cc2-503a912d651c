use std::collections::HashMap;
use std::sync::{LazyLock, RwLock};

#[derive(<PERSON><PERSON>, <PERSON>bug, Eq, <PERSON>ialEq, Default)]
pub struct Environment {
    // block env
    pub block_id: String,
    pub block_height: u64,
    // transaction env
    pub transaction_hash: String,
    pub transaction_index: u32,
    pub transaction_from_address: String,
    pub transaction_timestamp: u64,
    // contract env
    pub contract_address: String,
    // contract configs
    pub readonly: bool,
    pub register: bool,
    // TODO: add public key, private key, etc.
}

static ENV: LazyLock<RwLock<Environment>> = LazyLock::new(|| {
    RwLock::new(Environment::default())
});

#[cfg(not(target_arch = "wasm32"))]
pub fn set_test_env() {
    // use default env
    set_env(HashMap::new());
    // clear change list
    crate::storages::clear_change_list();
}

pub fn set_env(envs: HashMap<String, String>) {
    let default_uuid = || uuid::Uuid::new_v4().simple().to_string();
    let get_or_default = |key: &str, default: &str| envs.get(key).unwrap_or(&default.to_string()).to_string();
    let parse_or_default = |key: &str, default: &str| envs.get(key).unwrap_or(&default.to_string()).parse().unwrap();
    let parse_bool_or_default = |key: &str, default: bool| envs.get(key).map_or(default, |v| v.parse().unwrap_or(default));

    *ENV.write().unwrap() = Environment {
        block_id: get_or_default("block_id", &default_uuid()),
        block_height: parse_or_default("block_height", "1"),
        transaction_hash: get_or_default("transaction_hash", &default_uuid()),
        transaction_index: parse_or_default("transaction_index", "1") as u32,
        transaction_from_address: get_or_default("transaction_from_address", &default_uuid()),
        transaction_timestamp: parse_or_default("transaction_timestamp", "0"),
        contract_address: get_or_default("contract_address", &default_uuid()),
        readonly: parse_bool_or_default("readonly", false),
        register: parse_bool_or_default("register", false),
    }
}

#[inline]
pub fn get_env<'a>() -> std::sync::RwLockReadGuard<'a, Environment> {
    ENV.read().unwrap()
}

impl Environment {
    #[cfg(not(target_arch = "wasm32"))]
    pub fn equal_test_env(&self, test_env: &TestEnvironment) -> bool {
        self.block_id == test_env.block_id
            && self.block_height == test_env.block_height
            && self.transaction_hash == test_env.transaction_hash
            && self.transaction_index == test_env.transaction_index
            && self.transaction_from_address == test_env.transaction_from_address
            && self.transaction_timestamp == test_env.transaction_timestamp
    }
}


#[cfg(not(target_arch = "wasm32"))]
#[derive(Clone, Debug, Eq, PartialEq, Default)]
pub struct TestEnvironment {
    // block env
    pub block_id: String,
    pub block_height: u64,
    // transaction env
    pub transaction_hash: String,
    pub transaction_index: u32,
    pub transaction_from_address: String,
    pub transaction_timestamp: u64,
}

#[cfg(not(target_arch = "wasm32"))]
pub struct TestEnvironmentBuilder {
    block_id: Option<String>,
    block_height: Option<u64>,
    transaction_hash: Option<String>,
    transaction_index: Option<u32>,
    transaction_from_address: Option<String>,
    transaction_timestamp: Option<u64>,
}

#[cfg(not(target_arch = "wasm32"))]
impl TestEnvironmentBuilder {
    pub fn new() -> Self {
        Self {
            block_id: None,
            block_height: None,
            transaction_hash: None,
            transaction_index: None,
            transaction_from_address: None,
            transaction_timestamp: None,
        }
    }

    pub fn block_id(mut self, block_id: &str) -> Self {
        self.block_id = Some(block_id.to_string());
        self
    }

    pub fn block_height(mut self, block_height: u64) -> Self {
        self.block_height = Some(block_height);
        self
    }

    pub fn transaction_hash(mut self, transaction_hash: &str) -> Self {
        self.transaction_hash = Some(transaction_hash.to_string());
        self
    }

    pub fn transaction_index(mut self, transaction_index: u32) -> Self {
        self.transaction_index = Some(transaction_index);
        self
    }

    pub fn transaction_from_address(mut self, transaction_from_address: &str) -> Self {
        self.transaction_from_address = Some(transaction_from_address.to_string());
        self
    }

    pub fn transaction_timestamp(mut self, transaction_timestamp: u64) -> Self {
        self.transaction_timestamp = Some(transaction_timestamp);
        self
    }

    pub fn build(self) -> TestEnvironment {
        TestEnvironment {
            block_id: self.block_id.unwrap_or_default(),
            block_height: self.block_height.unwrap_or(0),
            transaction_hash: self.transaction_hash.unwrap_or_default(),
            transaction_index: self.transaction_index.unwrap_or(0),
            transaction_from_address: self.transaction_from_address.unwrap_or_default(),
            transaction_timestamp: self.transaction_timestamp.unwrap_or(0),
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
pub fn update_test_env(new_env: &TestEnvironment) {
    let mut env = ENV.write().unwrap();
    env.block_id = new_env.block_id.clone();
    env.block_height = new_env.block_height;
    env.transaction_hash = new_env.transaction_hash.clone();
    env.transaction_index = new_env.transaction_index;
    env.transaction_from_address = new_env.transaction_from_address.clone();
    env.transaction_timestamp = new_env.transaction_timestamp;
}

#[cfg(not(target_arch = "wasm32"))]
pub fn set_from_test_env(test_env: &TestEnvironment) {
    let mut env = ENV.write().unwrap();
    env.block_id = test_env.block_id.clone();
    env.block_height = test_env.block_height;
    env.transaction_hash = test_env.transaction_hash.clone();
    env.transaction_index = test_env.transaction_index;
    env.transaction_from_address = test_env.transaction_from_address.clone();
    env.transaction_timestamp = test_env.transaction_timestamp;
}

