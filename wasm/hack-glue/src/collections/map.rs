use std::hash::Hash;
use std::marker::PhantomData;

use crate::{key_hash, storage_get, storage_pop, storage_put};
use crate::storages::StorageKey;

pub struct Map<K: Hash + serde::Serialize, V: serde::Serialize + for<'a> serde::Deserialize<'a>, KeyPrefix: StorageKey = ()> {
    _marker: PhantomData<fn() -> (K, V, KeyPrefix)>,
    cache_size: u32,
    key_prefix: String,
    size_key: String,
}

impl<K, V, KeyPrefix> Default for Map<K, V, KeyPrefix>
where
    K: Hash + serde::Serialize,
    for<'de> V: serde::Serialize + serde::Deserialize<'de>,
    KeyPrefix: StorageKey,
{
    fn default() -> Self {
        Self::new()
    }
}

impl<K, V, KeyPrefix> Map<K, V, KeyPrefix>
where
    K: Hash + serde::Serialize,
    for<'de> V: serde::Serialize + serde::Deserialize<'de>,
    KeyPrefix: StorageKey,
{
    pub fn new() -> Self {
        let key_prefix = KeyPrefix::key();
        let size_key = format!("{}*size", key_prefix);
        let mut new = Self {
            _marker: PhantomData,
            cache_size: 0,
            key_prefix,
            size_key,
        };
        if crate::env::get_env().register {
            new.set_size(0);
        } else {
            new.cache_size = new.get_size();
        }
        new
    }

    #[inline]
    pub fn insert(&mut self, key: &K, value: &V) {
        let hash_value = key_hash(key);
        let key_string = format!("{}-{}", self.key_prefix, hash_value);
        let value_string = serde_json::to_string(&value).unwrap();

        let insert_flag = !self.contains(key);

        storage_put(&key_string, &value_string);

        if insert_flag {
            let size = self.size();
            self.set_size(size + 1);
        }
    }

    #[inline]
    pub fn get(&self, key: &K) -> Option<V> {
        let hash_value = key_hash(key);
        let key_string = format!("{}-{}", self.key_prefix, hash_value);
        let value_string = storage_get(&key_string);
        if value_string.is_none() {
            return None;
        }

        let value = serde_json::from_str(&value_string.unwrap());
        if value.is_err() {
            return None;
        }

        Some(value.unwrap())
    }

    #[inline]
    pub fn take(&mut self, key: &K) -> Option<V> {
        let hash_value = key_hash(key);
        let key_string = format!("{}-{}", self.key_prefix, hash_value);
        let value_string = storage_pop(&key_string);
        if value_string.is_none() {
            return None;
        }

        let value = serde_json::from_str(&value_string.unwrap());
        if value.is_err() {
            return None;
        }

        let size = self.size();
        self.set_size(size - 1);

        Some(value.unwrap())
    }

    #[inline]
    pub fn contains(&self, key: &K) -> bool {
        let hash_value = key_hash(key);
        let key_string = format!("{}-{}", self.key_prefix, hash_value);
        storage_get(&key_string).is_some()
    }

    #[inline]
    pub fn remove(&mut self, key: &K) {
        let hash_value = key_hash(key);
        let key_string = format!("{}-{}", self.key_prefix, hash_value);
        let result = storage_pop(&key_string);
        if result.is_some() {
            let size = self.size();
            self.set_size(size - 1);
        }
    }

    /// glue vector use it to remove
    #[inline]
    pub(crate) fn remove_not_change_size(&mut self, key: &K) {
        let hash_value = key_hash(key);
        let key_string = format!("{}-{}", self.key_prefix, hash_value);
        let _ = storage_pop(&key_string);
    }

    #[inline]
    pub(crate) fn set_size(&mut self, size: u32) {
        storage_put(&self.size_key, &size.to_string());
        self.cache_size = size;
    }

    #[inline]
    pub fn size(&self) -> u32 {
        return self.cache_size;
    }

    #[inline]
    fn get_size(&self) -> u32 {
        let size_string = storage_get(&self.size_key);
        if size_string.is_none() {
            return 0;
        }
        return size_string.unwrap().parse().unwrap();
    }

    // TODO clear
}
