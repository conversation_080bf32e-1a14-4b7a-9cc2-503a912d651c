use std::collections::HashMap;
use std::ffi::{c_char, CStr, CString};
use std::hash::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>};
use std::ptr;

use serde::{Deserialize, Serialize};

#[inline]
pub fn string_to_c_char(string: &str) -> *mut c_char {
    CString::new(string)
        .map(|c_string| c_string.into_raw())
        .unwrap_or_else(|_| ptr::null_mut())
}

#[no_mangle]
pub extern "C" fn allocate_c_char(len: usize) -> *mut c_char {
    let buffer = vec![0; len];
    let raw_ptr = buffer.as_ptr() as *mut c_char;
    std::mem::forget(buffer);
    raw_ptr
}


#[no_mangle]
pub extern "C" fn deallocate_c_char(ptr: *mut c_char) {
    unsafe {
        let _ = CString::from_raw(ptr);
    }
}

#[inline]
pub fn get_string(ptr: *mut c_char) -> Option<String> {
    // if None
    if ptr.is_null() {
        return None;
    }
    let c_str = unsafe { CStr::from_ptr(ptr) };
    let str_slice = c_str.to_str().unwrap();
    Some(str_slice.to_string())
}

#[inline]
pub fn write_string(string: &str) -> *mut c_char {
    CString::new(string)
        .map(|c_string| c_string.into_raw())
        .unwrap_or_else(|_| ptr::null_mut())
}

#[inline]
pub fn object_to_json_ptr<T: serde::Serialize>(object: T) -> anyhow::Result<*mut c_char> {
    let path_json = serde_json::to_string(&object)?;
    Ok(write_string(&path_json))
}

#[inline]
pub fn json_ptr_to_object<T: serde::de::DeserializeOwned>(ptr: *mut c_char) -> anyhow::Result<T> {
    let c_string = unsafe { CString::from_raw(ptr) };
    let path_json = c_string.to_str()?;
    let object = serde_json::from_str(path_json)?;
    Ok(object)
}


#[derive(serde::Serialize, serde::Deserialize, Debug)]
pub struct SerializableResult {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub value: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub err: Option<String>,
    pub change_list: HashMap<String, Option<String>>,
}

impl SerializableResult
{
    #[inline]
    pub fn from<T: serde::Serialize>(result: Result<T, anyhow::Error>) -> Self {
        let change_list = crate::storages::CHANGE_LIST.lock().unwrap().clone();
        match result {
            Ok(value) => SerializableResult {
                value: Option::from(serde_json::to_string(&Some(value)).unwrap()),
                err: None,
                change_list,
            },
            Err(err) => SerializableResult {
                value: None,
                err: Some(err.to_string()),
                change_list,
            },
        }
    }

    #[inline]
    pub fn default() -> Self {
        let change_list = crate::storages::CHANGE_LIST.lock().unwrap().clone();
        SerializableResult {
            value: None,
            err: None,
            change_list,
        }
    }
}

#[inline]
/// Generates a human-readable or numeric hash for a key.
/// If the key's string representation is short enough, it's used directly.
/// Otherwise, a numeric hash is returned with prefix @.
pub fn key_hash<K: Hash + serde::Serialize>(key: &K) -> String {
    let key_string = serde_json::to_string(&key);
    match key_string {
        Ok(key) => {
            // if key is number convert to string and return
            if key.parse::<i64>().is_ok() {
                return key;
            }
            // if key is String and len < 19, use key directly
            if key.starts_with('"') && key.ends_with('"') && key.len() < 19 {
                return key[1..key.len() - 1].to_string();
            } else if key.starts_with("[") && key.ends_with("]") {
                let vec = serde_json::from_str::<Vec<String>>(&key);
                match vec {
                    Ok(vec) => {
                        let temp_key = vec.join("-");
                        if temp_key.len() < 19 {
                            return temp_key;
                        }
                    }
                    Err(_) => {}
                }
            }
        }
        Err(_) => {}
    }

    let mut hasher = DefaultHasher::new();
    key.hash(&mut hasher);
    // if key is too long, use hash value, prefix with "@"
    format!("@{}", hasher.finish().to_string())
}

#[derive(Serialize, Deserialize, Debug)]
pub struct FuncArgs<T> {
    pub args: T,
    pub envs: HashMap<String, String>,
}


#[cfg(test)]
mod test {
    use super::*;

    #[test]
    fn test_key_hash() {
        let key = "hello";
        let hash = key_hash(&key);
        assert_eq!(hash, "hello");

        let key = "hello world";
        let hash = key_hash(&key);
        assert_eq!(hash, "hello world");

        let key = vec!["hello".to_string(), "world".to_string()];
        let hash = key_hash(&key);
        assert_eq!(hash, "hello-world");

        let key = vec!["hello".to_string(), "world".to_string(), "123".to_string()];
        let hash = key_hash(&key);
        assert_eq!(hash, "hello-world-123");

        let key = ("123", "hello".to_string());
        let hash = key_hash(&key);
        assert_eq!(hash, "123-hello");

        let key = ("123", "hello".to_string(), "world".to_string());
        let hash = key_hash(&key);
        assert_eq!(hash, "123-hello-world");

        let key: i32 = 123;
        let hash = key_hash(&key);
        assert_eq!(hash, "123");

        let key: i64 = 1234567890123456789i64;
        let hash = key_hash(&key);
        assert_eq!(hash, "1234567890123456789");

        let key: u32 = 123u32;
        let hash = key_hash(&key);
        assert_eq!(hash, "123");
    }
}