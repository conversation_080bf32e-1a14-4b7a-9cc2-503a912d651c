use rusqlite::{Connection, Result};
use std::sync::Mutex;
use std::os::raw::c_char;

#[no_mangle]
#[export_name = "_start"]
pub extern "C" fn main() {
    println!("empty main func");
}

#[no_mangle]
pub extern "C" fn get_env_from_db(conn: &Mutex<Connection>) {
// pub extern "C" fn set_env_from_db() {
    println!("set env from db in wasm");
    // let conn = create_db_connection();
    // let conn = (&conn).lock().unwrap();
    let conn = conn.lock().unwrap();
    println!("get_env conn: {:?}", conn);
    
    let mut stmt = conn.prepare("SELECT key, value FROM env").unwrap();
    let env_iter = stmt.query_map([], |row| {
        Ok((
            row.get::<_, String>(0)?,
            row.get::<_, String>(1)?,
        ))
    }).unwrap();

    for env in env_iter {
        let (key, value) = env.unwrap();
        std::env::set_var(key.clone(), value.clone());
        println!("{}: {}", key, value);
    }
}

#[no_mangle]
pub extern "C" fn test_env() {
    println!("test print env in wasm");
    let mut env_vars: Vec<_> = std::env::vars().collect();
    env_vars.sort();
    for (k, v) in env_vars {
        println!("{} = {}", k, v);
    }
}

#[no_mangle]
pub extern "C" fn create_db_connection() -> Box<Mutex<Connection>> {
    let conn_str = std::env::var("DB_NAME").unwrap();
    let conn = Connection::open(conn_str).unwrap();
    let conn = Mutex::new(conn);
    return Box::new(conn);
}

