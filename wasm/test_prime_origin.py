import pytest
from wasmtime import Store, Module, Engine, Linker, WasiConfig


def prime_origin(origin, difficulty):
    wasi = WasiConfig()
    wasi.preopen_dir('.', '.')
    wasi.inherit_stdout()
    wasi.inherit_argv()
    wasi.inherit_env()

    engine = Engine()
    module = Module.from_file(engine, "wasm/test_prime_origin.wasm")
    linker = Linker(engine)
    linker.define_wasi()

    store = Store(engine)
    store.set_wasi(wasi)

    instance = linker.instantiate(store, module)

    print("\t--------- wasi test ---------")
    allocate = instance.exports(store)["allocate"]
    deallocate = instance.exports(store)["deallocate"]
    memory = instance.exports(store)["memory"]
    verify_work = instance.exports(store)["verify_work"]

    test_origin = origin
    test_difficulty = difficulty

    origin_ptr = allocate(store, len(test_origin))
    writeString(memory, store, test_origin, origin_ptr)

    print("wasi num:", getString(memory, store, origin_ptr))

    print("\t--------- wasi stdout is_prime begin ---------")
    wasiResult = verify_work(store, origin_ptr, test_difficulty)
    print("\t--------- wasi stdout is_prime end ---------")

    print("wasi result:", wasiResult)
    return wasiResult


def writeString(mem, store, input_str, ptr):
    # write into memory
    mem.write(store, bytearray(input_str.encode()), ptr)
    # write 0 as NULL to terminate C-string
    mem.data_ptr(store)[ptr + len(input_str)] = 0


def getString(mem, store, ptr):
    # loop to read output string
    output_str = ''
    i = 0
    while True:
        # C-string terminates by NULL.
        if mem.data_ptr(store)[ptr + i] == 0:
            break
        char = chr(mem.data_ptr(store)[ptr + i])
        output_str += char
        i += 1
    return output_str


def test_prime_origin():
    origin = "16104852924139568456006626349957181947614826079179167293804058866767923317510414698411827609927680"
    shift_difficulty = 11.69245762 * (2 ** 24)
    shift_difficulty = int(shift_difficulty)
    prime_origin(origin, shift_difficulty)
