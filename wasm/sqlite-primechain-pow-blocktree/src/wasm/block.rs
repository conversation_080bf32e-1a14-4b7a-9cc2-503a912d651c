use anyhow::{anyhow, Result};
use wasm_py_bridge_macro::wasm_bind;

use crate::consts::ROOT_PARENT_ID;
use crate::model::{
    block_tree::IBlockTreeModel,
    best_block::IBestBlockModel,
    implement::block_tree::{BlockTree, BlockTreeModel},
    implement::best_block::BestBlockModel,
};


#[wasm_bind]
pub fn get_block_template(chain_id: String) -> Result<BlockTree> {
    match BlockTree::create_block_template(&chain_id) {
        Ok(block_tree) => Ok(block_tree),
        Err(e) => Err(anyhow!("Failed to get block template: {:?}", e))
    }
}

#[wasm_bind]
pub fn get_current_best_block(chain_id: String) -> Result<Option<BlockTree>> {
    let blocktree_model: Box<dyn IBlockTreeModel> = Box::new(BlockTreeModel::new());
    let best_block_model: Box<dyn IBestBlockModel> = Box::new(BestBlockModel::new());
    let best_block_id = match best_block_model.get(&chain_id, None)? {
        Some(best_block_id) => best_block_id,
        None => ROOT_PARENT_ID.to_string(),
    };

    if best_block_id == ROOT_PARENT_ID.to_string() {
        return Ok(None);
    } else {
        match blocktree_model.get(&chain_id, best_block_id, None) {
            Ok(block) => Ok(block),
            Err(e) => Err(anyhow!("Failed to get current best block: {:?}", e))
        }
    }
}

#[wasm_bind]
pub fn get_block(chain_id: String, block_id: String) -> Result<Option<BlockTree>> {
    let blocktree_model: Box<dyn IBlockTreeModel> = Box::new(BlockTreeModel::new());
    match blocktree_model.get(&chain_id, block_id, None) {
        Ok(block) => Ok(block),
        Err(e) => Err(anyhow!("Failed to get block: {:?}", e))
    }
}

#[wasm_bind]
pub fn get_blocks(chain_id: String, locator: Vec<String>, limit: i64) -> Result<Vec<BlockTree>> {
    let blocktree_model: Box<dyn IBlockTreeModel> = Box::new(BlockTreeModel::new());
    let best_block = match _get_current_best_block(chain_id.clone())? {
        Some(best_block) => best_block,
        None => return Ok(Vec::new()),
    };

    // find common ancestor
    let mut common_ancestor_id = String::new();
    for locator_block_id in locator {
        match blocktree_model.get(&chain_id, locator_block_id, None)? {
            Some(locator_block) => {
                if locator_block.height > best_block.height {
                    // skip locator block that is higher than best block
                    continue;
                } else {
                    let mut current_block_id = best_block.id.clone();
                    while current_block_id != locator_block.id && current_block_id != ROOT_PARENT_ID
                    {
                        let current_block = match blocktree_model.get(
                            &chain_id,
                            current_block_id.clone(),
                            None,
                        )? {
                            Some(block) => block,
                            None => return Ok(Vec::new()),
                        };
                        current_block_id = current_block.parent_id;
                    }
                    if current_block_id == locator_block.id {
                        common_ancestor_id = locator_block.id;
                        break;
                    }
                }
            },
            None => {
                // if the locator block is not in the block tree, continue
                continue;
            },
        }
    }

    // if common ancestor is not found, set it to genesis block
    let common_ancestor = match common_ancestor_id.is_empty() {
        // if common ancestor is not found, set it to genesis block
        true => match blocktree_model.get_genesis_block(&chain_id, None)? {
            Some(genesis_block) => genesis_block,
            None => return Err(anyhow!("Failed to get genesis block")),
        },
        false => match blocktree_model.get(&chain_id, common_ancestor_id, None)? {
            Some(common_ancestor) => common_ancestor,
            None => return Err(anyhow!("Failed to get common ancestor")),
        },
    };

    // find and return path from common ancestor to this node's best block (limit is the maximum number of blocks to return)
    // common ancestor is not included in the path
    let mut path: Vec<BlockTree> = Vec::new();
    let mut current_block_id = best_block.id.clone();
    while current_block_id != common_ancestor.id && path.len() < limit as usize {
        match blocktree_model.get(&chain_id, current_block_id.clone(), None)? {
            Some(current_block) => {
                current_block_id = current_block.parent_id.clone();
                path.push(current_block.clone());
            }
            None => return Err(anyhow!("Failed to get block")),
        }
    }

    path.reverse();
    Ok(path)
}

#[wasm_bind]
pub fn validate_block(chain_id: String, block: BlockTree) -> Result<()> {
    match block.validate(&chain_id) {
        Ok(_) => Ok(()),
        Err(e) => return Err(anyhow!("Failed to validate block: {:?}", e)),
    }
}

#[wasm_bind]
pub fn save_block_to_disk(chain_id: String, block: BlockTree) -> Result<()> {
    let blocktree_model: Box<dyn IBlockTreeModel> = Box::new(BlockTreeModel::new());
    match blocktree_model.insert(&chain_id, &block, None) {
        Ok(_) => Ok(()),
        Err(e) => Err(anyhow!("Failed to save block to disk: {:?}", e))
    }
}

#[wasm_bind]
pub fn connect_block(chain_id: String, block: BlockTree, is_genesis: bool) -> Result<()> {
    if !is_genesis {
        match block.validate(&chain_id) {
            Ok(_) => (),
            Err(e) => return Err(anyhow!("Failed to validate block: {:?}", e))
        }
    }

    // if there is no best block, insert it; else, update it
    let best_block_model: Box<dyn IBestBlockModel> = Box::new(BestBlockModel::new());
    match best_block_model.get(&chain_id, None)? {
        Some(_) => match best_block_model.update(&chain_id, block.id, None) {
            Ok(_) => (),
            Err(e) => return Err(anyhow!("Failed to update best block: {:?}", e))
        },
        None => match best_block_model.insert(&chain_id, block.id, None) {
            Ok(_) => (),
            Err(e) => return Err(anyhow!("Failed to insert best block: {:?}", e))
        },
    }
    Ok(())
}