use wasm_py_bridge_macro::wasm_bind;

use anyhow::{Result, anyhow};

use crate::wasm::block::{_get_current_best_block, _get_block};
use crate::difficulty::{target_get_initial, target_get_next};

#[wasm_bind]
fn next_difficulty(chain_id: String) -> Result<String> {
    let best_block = match _get_current_best_block(chain_id.clone())? {
        Some(best_block) => best_block,
        None => return Ok(target_get_initial().to_string()),
    };

    if (best_block.height == 0) || (best_block.height == 1){
        return Ok(target_get_initial().to_string());
    }

    let parent_block = match _get_block(chain_id.clone(), best_block.parent_id.to_string())? {
        Some(parent_block) => parent_block,
        None => return Err(anyhow!("Failed to get parent block")),
    };

    let n_difficulty: i32;
    if best_block.difficulty_score as i32 > 1 {
        n_difficulty = best_block.difficulty_score as i32;
    } else {
        // 7 * pow(2,24)
        n_difficulty = target_get_initial();
    }

    let n_difficulty_next = target_get_next(n_difficulty, best_block.timestamp/1000000000, parent_block.timestamp/1000000000);

    Ok(n_difficulty_next.to_string())
}