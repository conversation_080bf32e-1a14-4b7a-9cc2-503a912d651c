use anyhow::{anyhow, Result};
use wasm_py_bridge_macro::wasm_bind;

use crate::model::{
    implement::block_tree::{BlockTree, BlockTreeModel},
    block_tree::IBlockTreeModel,
};
use crate::reorg_path::ReorganizationPath;

#[wasm_bind]
pub fn get_reorganization_path(
    chain_id: String,
    old_best_block_id: String,
    new_best_block_id: String,
) -> Result<ReorganizationPath> {
    let _block_tree_model: Box<dyn IBlockTreeModel> = Box::new(BlockTreeModel::new());

    if old_best_block_id == new_best_block_id {
        return Ok(ReorganizationPath {
            roll_back_path: [].to_vec(),
            common_ancestor: old_best_block_id,
            roll_forward_path: [].to_vec(),
        });
    }

    let mut block1: BlockTree =
        match _block_tree_model.get(&chain_id, old_best_block_id.clone(), None)? {
            Some(block) => block,
            None => return Err(anyhow!("Block not found: {}", old_best_block_id)),
        };

    let mut block2: BlockTree =
        match _block_tree_model.get(&chain_id, new_best_block_id.clone(), None)? {
            Some(block) => block,
            None => return Err(anyhow!("Block not found: {}", new_best_block_id)),
        };

    let mut roll_back_path: Vec<String> = Vec::new();
    let mut roll_forward_path: Vec<String> = Vec::new();

    // Move the higher block down to the same height as another block
    while block1.height > block2.height {
        roll_back_path.push(block1.id.clone());
        block1 = match _block_tree_model.get(&chain_id, block1.parent_id.clone(), None)? {
            Some(block) => block,
            None => return Err(anyhow!("Block not found: {}", block1.parent_id)),
        };
    }

    while block2.height > block1.height {
        roll_forward_path.push(block2.id.clone());
        block2 = match _block_tree_model.get(&chain_id, block2.parent_id.clone(), None)? {
            Some(block) => block,
            None => return Err(anyhow!("Block not found: {}", block2.parent_id)),
        };
    }

    // Starting at the same height, traverse upwards until you find a common ancestor
    while block1.id != block2.id {
        roll_back_path.push(block1.id.clone());
        roll_forward_path.push(block2.id.clone());

        block1 = match _block_tree_model.get(&chain_id, block1.parent_id.clone(), None)? {
            Some(block) => block,
            None => return Err(anyhow!("Block not found: {}", block1.parent_id)),
        };

        block2 = match _block_tree_model.get(&chain_id, block2.parent_id.clone(), None)? {
            Some(block) => block,
            None => return Err(anyhow!("Block not found: {}", block2.parent_id)),
        };
    }

    // reverse roll_forward_path in place
    roll_forward_path.reverse();

    Ok(ReorganizationPath {
        roll_back_path,
        common_ancestor: block1.id,
        roll_forward_path,
    })
}
