use wasm_py_bridge_macro::wasm_bind;

use anyhow::{Result, anyhow};

use crate::model::{
    implement::transaction_receipt::{TransactionReceiptModel, TransactionReceipt},
    transaction_receipt::ITransactionReceipt
};

#[wasm_bind]
fn create_transaction_receipt(chain_id: String, tx_receipt: TransactionReceipt) -> Result<()> {
    let transaction_receipt_model: Box<dyn ITransactionReceipt> =
        Box::new(TransactionReceiptModel::new());

    match transaction_receipt_model.insert(&chain_id, &tx_receipt, None) {
        Ok(_) => Ok(()),
        Err(e) => Err(anyhow!("{:?}", e))
    }
}

#[wasm_bind]
fn get_transaction_receipt(chain_id: String, transaction_hash: String) -> Result<Option<TransactionReceipt>> {
    let transaction_receipt_model: Box<dyn ITransactionReceipt> =
        Box::new(TransactionReceiptModel::new());

    match transaction_receipt_model.get(&chain_id, transaction_hash, None) {
        Ok(transaction_receipt) => Ok(transaction_receipt),
        Err(e) => Err(anyhow!("Error getting transaction receipt: {:?}", e))
    }
}