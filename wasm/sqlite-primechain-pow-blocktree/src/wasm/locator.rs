use wasm_py_bridge_macro::wasm_bind;

use anyhow::Result;

use crate::{
    consts::{MAX_LOCATOR_LENGTH, ROOT_PARENT_ID},
    model::{
        implement::{
            best_block::BestBlockModel,
            block_tree::BlockTreeModel,
        },
        best_block::IBestBlockModel,
        block_tree::IBlockTreeModel,
    },
};

#[wasm_bind]
pub fn generate_locator(chain_id: String) -> Result<Vec<String>> {
    let best_block_model: Box<dyn IBestBlockModel> = Box::new(BestBlockModel::new());
    let blocktree_model: Box<dyn IBlockTreeModel> = Box::new(BlockTreeModel::new());

    // start from the best block
    let mut current_block_id = match best_block_model.get(&chain_id, None)? {
        Some(best_block_id) => best_block_id,
        None => ROOT_PARENT_ID.to_string(),
    };

    let mut locator = Vec::new();
    let mut step = 1;
    let mut counter = 1;

    while current_block_id != ROOT_PARENT_ID && counter < MAX_LOCATOR_LENGTH {
        locator.push(current_block_id.clone());

        for _ in 0..step {
            current_block_id =
                match blocktree_model.get(&chain_id, current_block_id.clone(), None)? {
                    Some(block) => block.parent_id,
                    None => break,
                };
        }

        if counter >= 10 {
            // double steps, exponential growth
            step *= 2;
        }
        counter += 1;
    }

    Ok(locator)
}
