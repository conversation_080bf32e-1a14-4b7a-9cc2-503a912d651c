use rusqlite::Transaction;

use crate::model::implement::block_tree::BlockTree;

pub trait IBlockTreeModel {
    fn count(&self, chain_id: &str, transaction: Option<&Transaction>) -> rusqlite::Result<i64>;
    fn insert(&self, chain_id: &str, block: &BlockTree, transaction: Option<&Transaction>) -> rusqlite::Result<usize>;
    fn get(&self, chain_id: &str, block_id: String, transaction: Option<&Transaction>) -> rusqlite::Result<Option<BlockTree>>;
    fn update(&self, chain_id: &str, item: &BlockTree, transaction: Option<&Transaction>) -> rusqlite::Result<usize>;
    fn delete(&self, chain_id: &str, block_id: String, transaction: Option<&Transaction>) -> rusqlite::Result<usize>;
    fn get_genesis_block(&self, chain_id: &str, transaction: Option<&Transaction>) -> rusqlite::Result<Option<BlockTree>>;
}