use rusqlite::Transaction;

use crate::model::implement::transaction_receipt::TransactionReceipt;

pub trait ITransactionReceipt {
    fn count(&self, chain_id: &str, transaction: Option<&Transaction>) -> rusqlite::Result<i64>;
    fn insert(&self, chain_id: &str, transaction_receipt: &TransactionReceipt, transaction: Option<&Transaction>) -> rusqlite::Result<usize>;
    fn get(&self, chain_id: &str, transaction_hash: String, transaction: Option<&Transaction>) -> rusqlite::Result<Option<TransactionReceipt>>;
    fn update(&self, chain_id: &str, transaction_receipt: &TransactionReceipt, transaction: Option<&Transaction>) -> rusqlite::Result<usize>;
    fn delete(&self, chain_id: &str, transaction_hash: String, transaction: Option<&Transaction>) -> rusqlite::Result<usize>;
}