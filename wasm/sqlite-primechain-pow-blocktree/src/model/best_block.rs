use rusqlite::Transaction;

pub trait IBestBlockModel {
    fn count(&self, chain_id: &str, transaction: Option<&Transaction>) -> rusqlite::Result<i64>;
    fn insert(&self, chain_id: &str, block_id: String, transaction: Option<&Transaction>) -> rusqlite::Result<usize>;
    fn get(&self, chain_id: &str, transaction: Option<&Transaction>) -> rusqlite::Result<Option<String>>;
    fn update(&self, chain_id: &str, block_id: String, transaction: Option<&Transaction>) -> rusqlite::Result<usize>;
    fn delete(&self, chain_id: &str, transaction: Option<&Transaction>) -> rusqlite::Result<usize>;
}