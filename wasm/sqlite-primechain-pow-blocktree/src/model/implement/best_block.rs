use rusqlite::{params, Transaction};
use serde::{Deserialize, Serialize};

use crate::model::best_block::IBestBlockModel;
use crate::model::sqlite::get_conn;

#[derive(Debug, Serialize, Deserialize)]
pub struct BestBlock {
    pub block_id: String,
}

pub struct BestBlockModel;

impl BestBlockModel {
    pub fn new() -> Self {
        Self {}
    }

    pub fn create_table(&self, chain_id: &str) -> rusqlite::Result<()> {
        let table_name = format!("{}_best_block", chain_id);
        let sql = format!(
            "CREATE TABLE IF NOT EXISTS {} (
                id INTEGER PRIMARY KEY,
                block_id TEXT NOT NULL
            )",
            table_name
        );

        let conn = get_conn();
        match conn.execute(&sql, params![]) {
            Ok(_) => Ok(()),
            Err(e) => Err(e),
        }
    }
}

impl IBestBlockModel for BestBlockModel {
    fn count(&self, chain_id: &str, transaction: Option<&Transaction>) -> rusqlite::Result<i64> {
        let table_name = format!("{}_best_block", chain_id);
        let sql = format!("SELECT COUNT(*) FROM {}", table_name);
        let sql_params = rusqlite::params![];

        let query_fn = |conn: &rusqlite::Connection| -> rusqlite::Result<i64> {
            let mut stmt = conn.prepare(&sql)?;
            stmt.query_row(sql_params, |row| row.get(0))
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn get(
        &self,
        chain_id: &str,
        transaction: Option<&Transaction>,
    ) -> rusqlite::Result<Option<String>> {
        let table_name = format!("{}_best_block", chain_id);
        let sql = format!("SELECT block_id FROM {} WHERE id = ?1", table_name);
        let sql_params = rusqlite::params![1];

        let query_fn = |conn: &rusqlite::Connection| -> rusqlite::Result<Option<String>> {
            let mut stmt = conn.prepare(&sql)?;
            let iter = stmt.query_row(sql_params, |row| row.get(0));

            match iter {
                Ok(block_id) => Ok(Some(block_id)),
                Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
                Err(e) => Err(e),
            }
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn insert(
        &self,
        chain_id: &str,
        block_id: String,
        transaction: Option<&Transaction>,
    ) -> rusqlite::Result<usize> {
        let table_name = format!("{}_best_block", chain_id);
        let sql = format!("INSERT INTO {} (id, block_id) VALUES (?1, ?2)", table_name);
        let sql_params = rusqlite::params![1, block_id];

        let query_fn = |conn: &rusqlite::Connection| -> rusqlite::Result<usize> {
            conn.execute(&sql, sql_params)
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn update(
        &self,
        chain_id: &str,
        block_id: String,
        transaction: Option<&Transaction>,
    ) -> rusqlite::Result<usize> {
        let table_name = format!("{}_best_block", chain_id);
        let sql = format!("UPDATE {} SET block_id = ?1 WHERE id = ?2", table_name);
        let sql_params = rusqlite::params![block_id, 1];

        let query_fn = |conn: &rusqlite::Connection| -> rusqlite::Result<usize> {
            conn.execute(&sql, sql_params)
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn delete(&self, chain_id: &str, transaction: Option<&Transaction>) -> rusqlite::Result<usize> {
        let table_name = format!("{}_best_block", chain_id);
        let sql = format!("DELETE FROM {}", table_name);
        let sql_params = rusqlite::params![];

        let query_fn = |conn: &rusqlite::Connection| -> rusqlite::Result<usize> {
            conn.execute(&sql, sql_params)
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }
}
