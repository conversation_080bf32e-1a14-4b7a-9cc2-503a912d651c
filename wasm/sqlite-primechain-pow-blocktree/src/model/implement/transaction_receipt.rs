use rusqlite::{params, Connection, Transaction};
use serde::{Deserialize, Serialize};
use serde_json::Value;

use crate::model::transaction_receipt::ITransactionReceipt;
use crate::model::sqlite::get_conn;

#[derive(PartialEq, Clone, Debug, Serialize, Deserialize)]
pub struct TransactionReceipt {
    pub transaction_hash: String,
    pub transaction_index: i64,
    pub block_id: String,
    pub from_address: String,
    pub contract_name: String,
    pub function_name: String,
    pub parameters: Vec<Value>,
    pub publickeys: Vec<String>,
    pub signatures: Vec<String>,
    pub status: bool,
    pub result: Value,
}

pub struct TransactionReceiptModel;

impl TransactionReceiptModel {
    pub fn new() -> Self {
        Self {}
    }

    pub fn create_table(&self, chain_id: &str) -> rusqlite::Result<()> {
        let table_name = format!("{}_transaction_receipt", chain_id);
        let sql = format!(
            "CREATE TABLE IF NOT EXISTS {} (
                transaction_hash TEXT PRIMARY KEY,
                transaction_index INTEGER,
                block_id TEXT,
                from_address TEXT,
                contract_name TEXT,
                function_name TEXT,
                parameters TEXT,
                publickeys TEXT,
                signatures TEXT,
                status BOOLEAN,
                result TEXT
            )",
            table_name
        );

        let conn = get_conn();
        match conn.execute(&sql, params![]) {
            Ok(_) => Ok(()),
            Err(e) => Err(e),
        }
    }
}

impl ITransactionReceipt for TransactionReceiptModel {
    fn count(&self, chain_id: &str, transaction: Option<&Transaction>) -> rusqlite::Result<i64> {
        let table_name = format!("{}_transaction_receipt", chain_id);
        let sql = format!("SELECT COUNT(*) FROM {}", table_name);
        let sql_params = params![];

        let query_fn = |conn: &Connection| -> rusqlite::Result<i64> {
            let mut stmt = conn.prepare(&sql)?;
            stmt.query_row(sql_params, |row| row.get(0))
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn insert(
        &self,
        chain_id: &str,
        transaction_receipt: &TransactionReceipt,
        transaction: Option<&Transaction>,
    ) -> rusqlite::Result<usize> {
        let table_name = format!("{}_transaction_receipt", chain_id);
        let sql = format!(
            "INSERT INTO {} (
            transaction_hash,
            transaction_index,
            block_id,
            from_address,
            contract_name,
            function_name,
            parameters,
            publickeys,
            signatures,
            status,
            result
        ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11)",
            table_name
        );

        let sql_params = params![
            transaction_receipt.transaction_hash,
            transaction_receipt.transaction_index,
            transaction_receipt.block_id,
            transaction_receipt.from_address,
            transaction_receipt.contract_name,
            transaction_receipt.function_name,
            serde_json::to_string(&transaction_receipt.parameters).unwrap(),
            serde_json::to_string(&transaction_receipt.publickeys).unwrap(),
            serde_json::to_string(&transaction_receipt.signatures).unwrap(),
            transaction_receipt.status,
            serde_json::to_string(&transaction_receipt.result).unwrap(),
        ];

        let query_fn =
            |conn: &Connection| -> rusqlite::Result<usize> { conn.execute(&sql, sql_params) };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn get(
        &self,
        chain_id: &str,
        transaction_hash: String,
        transaction: Option<&Transaction>,
    ) -> rusqlite::Result<Option<TransactionReceipt>> {
        let table_name = format!("{}_transaction_receipt", chain_id);
        let sql = format!("SELECT * FROM {} WHERE transaction_hash = ?1", table_name);
        let sql_params = params![transaction_hash];

        let query_fn = |conn: &Connection| -> rusqlite::Result<Option<TransactionReceipt>> {
            let mut stmt = conn.prepare(&sql)?;
            let iter = stmt.query_row(sql_params, |row| {
                let parameters_json_str: String = row.get(6)?;
                let publickeys_json_str: String = row.get(7)?;
                let signatures_json_str: String = row.get(8)?;
                let result_json_str: String = row.get(10)?;

                Ok(TransactionReceipt {
                    transaction_hash: row.get(0)?,
                    transaction_index: row.get(1)?,
                    block_id: row.get(2)?,
                    from_address: row.get(3)?,
                    contract_name: row.get(4)?,
                    function_name: row.get(5)?,
                    parameters: serde_json::from_str(&parameters_json_str).unwrap(),
                    publickeys: serde_json::from_str(&publickeys_json_str).unwrap(),
                    signatures: serde_json::from_str(&signatures_json_str).unwrap(),
                    status: row.get(9)?,
                    result: serde_json::from_str(&result_json_str).unwrap(),
                })
            });

            match iter {
                Ok(receipt) => Ok(Some(receipt)),
                Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
                Err(err) => Err(err),
            }
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn update(
        &self,
        chain_id: &str,
        transaction_receipt: &TransactionReceipt,
        transaction: Option<&Transaction>,
    ) -> rusqlite::Result<usize> {
        let table_name = format!("{}_transaction_receipt", chain_id);
        let sql = format!(
            "UPDATE {} SET
        transaction_index = ?2,
        block_id = ?3,
        from_address = ?4,
        contract_name = ?5,
        function_name = ?6,
        parameters = ?7,
        publickeys = ?8,
        signatures = ?9,
        status = ?10,
        result = ?11
        WHERE transaction_hash = ?1",
            table_name
        );

        let sql_params = params![
            transaction_receipt.transaction_hash,
            transaction_receipt.transaction_index,
            transaction_receipt.block_id,
            transaction_receipt.from_address,
            transaction_receipt.contract_name,
            transaction_receipt.function_name,
            serde_json::to_string(&transaction_receipt.parameters).unwrap(),
            serde_json::to_string(&transaction_receipt.publickeys).unwrap(),
            serde_json::to_string(&transaction_receipt.signatures).unwrap(),
            transaction_receipt.status,
            serde_json::to_string(&transaction_receipt.result).unwrap(),
        ];

        let query_fn =
            |conn: &Connection| -> rusqlite::Result<usize> { conn.execute(&sql, sql_params) };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn delete(&self, chain_id: &str, transaction_hash: String, transaction: Option<&Transaction>) -> rusqlite::Result<usize> {
        let table_name = format!("{}_transaction_receipt", chain_id);
        let sql = format!( "DELETE FROM {} WHERE transaction_hash = ?1", table_name);
        let sql_params = params![transaction_hash];

        let query_fn =
            |conn: &Connection| -> rusqlite::Result<usize> { conn.execute(&sql, sql_params) };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }
}
