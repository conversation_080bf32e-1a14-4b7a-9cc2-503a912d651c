use rusqlite::{params, Connection, Transaction};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use anyhow::anyhow;

use crate::consts::ROOT_PARENT_ID;
use crate::wasm::utils::_keccak256;
use crate::model::sqlite::get_conn;
use crate::model::{
    block_tree::IBlockTreeModel,
    best_block::IBestBlockModel,
    implement::best_block::BestBlockModel,
};

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct BlockTree {
    pub id: String,
    pub parent_id: String,
    pub difficulty_score: f64,
    pub difficulty_score_overall: f64,
    pub height: i64,
    pub transactions: String,
    pub merkle: String,
    pub timestamp: i64,
    pub nonce: i64,
    pub multiplier: i64,
}

impl BlockTree {
    // create block template
    // will be used when creating a candidate block
    pub fn create_block_template(chain_id: &str) -> rusqlite::Result<BlockTree> {
        let blocktree_model: Box<dyn IBlockTreeModel> = Box::new(BlockTreeModel::new());
        let best_block_model: Box<dyn IBestBlockModel> = Box::new(BestBlockModel::new());

        // get current best block
        let best_block_id = match best_block_model.get(chain_id, None)? {
            Some(best_block_id) => best_block_id,
            None => ROOT_PARENT_ID.to_string(),
        };
        
        if best_block_id == ROOT_PARENT_ID.to_string() {
            // there's no block in the table
            return Ok(BlockTree {
                id: Uuid::new_v4().to_string(),
                parent_id: ROOT_PARENT_ID.to_string(),
                difficulty_score: 0.0,
                difficulty_score_overall: 0.0,
                height: 1,
                transactions: "".to_string(),
                merkle: "".to_string(),
                timestamp: 0,
                nonce: 0,
                multiplier: 0,
            });
        } else {
            // get the best block
            let best_block = blocktree_model.get(chain_id, best_block_id, None)?;
            match best_block {
                Some(best_block) => {
                    Ok(BlockTree {
                        id: Uuid::new_v4().to_string(),
                        parent_id: best_block.id,
                        difficulty_score: 0.0,
                        difficulty_score_overall: best_block.difficulty_score_overall,
                        height: best_block.height + 1,
                        transactions: "".to_string(),
                        merkle: _keccak256("[]".to_string()),
                        timestamp: 0,
                        nonce: 0,
                        multiplier: 0,
                    })
                }
                None => {
                    // best block not found
                    return Err(rusqlite::Error::QueryReturnedNoRows);
                
                }
            }
        }
    }

    pub fn validate(&self, chain_id: &str) -> anyhow::Result<()> {
        // ----- block -----
        // validation: trivial validation
        if self.id == Uuid::nil().to_string() {
            return Err(anyhow!("Block id is nil."));
        }
        if self.height <= 0 {
            return Err(anyhow!("Block height is invalid."));
        }
        if self.difficulty_score <= 0 as f64 {
            return Err(anyhow!("Block difficulty score is invalid."));
        }
        if self.difficulty_score_overall <= 0 as f64 {
            return Err(anyhow!("Block overall difficulty score is invalid."));
        }

        // validation: parent block
        // TODO: when use block hash, we need to validate the parent block hash
        let blocktree_model: Box<dyn IBlockTreeModel> = Box::new(BlockTreeModel::new());
        if self.parent_id != ROOT_PARENT_ID {
            let parent_block = blocktree_model.get(chain_id, self.parent_id.clone(), None)?;
            match parent_block {
                Some(parent_block) => {
                    if parent_block.height + 1 != self.height {
                        return Err(anyhow!("Block height is invalid."));
                    }
                    if self.difficulty_score_overall != parent_block.difficulty_score_overall + self.difficulty_score {
                        return Err(anyhow!("Block overall difficulty score is invalid."));
                    }
                }
                None => {}
            }
        } else {
            if self.height != 1 {
                return Err(anyhow!("Block height is invalid."));
            }
        }

        Ok(())
    }
}

pub struct BlockTreeModel;

impl BlockTreeModel {
    pub fn new() -> Self {
        Self {}
    }

    pub fn create_table(&self, chain_id: &str) -> rusqlite::Result<()> {
        let table_name = format!("{}_block_tree", chain_id);
        let sql = format!(
            "CREATE TABLE IF NOT EXISTS {} (
                id TEXT PRIMARY KEY,
                parent_id TEXT NOT NULL,
                difficulty_score REAL NOT NULL,
                difficulty_score_overall REAL NOT NULL,
                height INTEGER NOT NULL,
                transactions TEXT,
                merkle TEXT,
                timestamp INTEGER,
                nonce INTEGER,
                multiplier INTEGER
            )",
            table_name
        );

        let conn = get_conn();
        match conn.execute(&sql, params![]) {
            Ok(_) => Ok(()),
            Err(e) => Err(e),
        }
    }
}

impl IBlockTreeModel for BlockTreeModel {
    fn count(&self, chain_id: &str, transaction: Option<&Transaction>) -> rusqlite::Result<i64> {
        let table_name = format!("{}_block_tree", chain_id);
        let sql = format!("SELECT COUNT(*) FROM {}", table_name);
        let sql_params = params![];

        let query_fn = |conn: &Connection| -> rusqlite::Result<i64> {
            let mut stmt = conn.prepare(&sql)?;
            stmt.query_row(sql_params, |row| row.get(0))
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn get(&self, chain_id: &str, block_id: String, transaction: Option<&Transaction>) -> rusqlite::Result<Option<BlockTree>> {
        let table_name = format!("{}_block_tree", chain_id);
        let sql = format!("SELECT * FROM {} WHERE id = ?", table_name);
        let sql_params = params![block_id];

        let query_fn = |conn: &Connection| -> rusqlite::Result<Option<BlockTree>> {
            let mut stmt = conn.prepare(&sql)?;
            let iter = stmt.query_row(sql_params, |row| {
                Ok(BlockTree {
                    id: row.get(0)?,
                    parent_id: row.get(1)?,
                    difficulty_score: row.get(2)?,
                    difficulty_score_overall: row.get(3)?,
                    height: row.get(4)?,
                    transactions: row.get(5)?,
                    merkle: row.get(6)?,
                    timestamp: row.get(7)?,
                    nonce: row.get(8)?,
                    multiplier: row.get(9)?,
                })
            });

            match iter {
                Ok(block_tree) => Ok(Some(block_tree)),
                Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
                Err(e) => Err(e),
            }
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn insert(&self, chain_id: &str, block: &BlockTree, transaction: Option<&Transaction>) -> rusqlite::Result<usize> {
        let table_name = format!("{}_block_tree", chain_id);
        let sql = format!(
            "INSERT INTO {} (id, parent_id, difficulty_score, difficulty_score_overall, height, transactions, merkle, timestamp, nonce, multiplier) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            table_name
        );
        let sql_params = params![
            block.id,
            block.parent_id,
            block.difficulty_score,
            block.difficulty_score_overall,
            block.height,
            block.transactions,
            block.merkle,
            block.timestamp,
            block.nonce,
            block.multiplier,
        ];

        let query_fn = |conn: &Connection| -> rusqlite::Result<usize> {
            conn.execute(&sql, sql_params)
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn update(&self, chain_id: &str, item: &BlockTree, transaction: Option<&Transaction>) -> rusqlite::Result<usize> {
        let table_name = format!("{}_block_tree", chain_id);
        let sql = format!(
            "UPDATE {} SET parent_id = ?, difficulty_score = ?, difficulty_score_overall = ?, height = ?, transactions = ?, merkle = ?, timestamp = ?, nonce = ?, multiplier = ? WHERE id = ?",
            table_name
        );
        let sql_params = params![
            item.parent_id,
            item.difficulty_score,
            item.difficulty_score_overall,
            item.height,
            item.transactions,
            item.merkle,
            item.timestamp,
            item.nonce,
            item.multiplier,
            item.id,
        ];

        let query_fn = |conn: &Connection| -> rusqlite::Result<usize> {
            conn.execute(&sql, sql_params)
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn delete(&self, chain_id: &str, block_id: String, transaction: Option<&Transaction>) -> rusqlite::Result<usize> {
        let table_name = format!("{}_block_tree", chain_id);
        let sql = format!("DELETE FROM {} WHERE id = ?", table_name);
        let sql_params = params![block_id];

        let query_fn = |conn: &Connection| -> rusqlite::Result<usize> {
            conn.execute(&sql, sql_params)
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }

    fn get_genesis_block(&self, chain_id: &str, transaction: Option<&Transaction>) -> rusqlite::Result<Option<BlockTree>> {
        let table_name = format!("{}_block_tree", chain_id);
        let sql = format!("SELECT * FROM {} WHERE height = 1", table_name);
        let sql_params = params![];

        let query_fn = |conn: &Connection| -> rusqlite::Result<Option<BlockTree>> {
            let mut stmt = conn.prepare(&sql)?;
            let iter = stmt.query_row(sql_params, |row| {
                Ok(BlockTree {
                    id: row.get(0)?,
                    parent_id: row.get(1)?,
                    difficulty_score: row.get(2)?,
                    difficulty_score_overall: row.get(3)?,
                    height: row.get(4)?,
                    transactions: row.get(5)?,
                    merkle: row.get(6)?,
                    timestamp: row.get(7)?,
                    nonce: row.get(8)?,
                    multiplier: row.get(9)?,
                })
            });

            match iter {
                Ok(block_tree) => Ok(Some(block_tree)),
                Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
                Err(e) => Err(e),
            }
        };

        match transaction {
            Some(transaction) => query_fn(transaction),
            None => {
                let conn = get_conn();
                query_fn(&conn)
            }
        }
    }
}