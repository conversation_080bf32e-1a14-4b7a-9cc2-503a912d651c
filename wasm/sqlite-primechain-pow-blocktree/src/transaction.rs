use crate::wasm::utils::_keccak256;
use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Transaction {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub transaction_hash: Option<String>,
    pub from_address: String,
    pub contract_name: String,
    pub function_name: String,
    pub parameters: Vec<Value>,
    pub publickeys: Vec<String>,
    pub signatures: Vec<String>,
    pub timestamp: i64,
}

pub fn create_transaction(
    from_address: String,
    contract_name: String,
    function_name: String,
    parameters: Vec<Value>,
    publickeys: Vec<String>,
    signatures: Vec<String>,
    timestamp: i64,
) -> Transaction {
    let mut tx = Transaction {
        transaction_hash: None,
        from_address,
        contract_name,
        function_name,
        parameters,
        publickeys,
        signatures,
        timestamp,
    };
    // serialize the transaction to json
    let tx_json = serde_json::to_string(&tx).unwrap();

    tx.transaction_hash = Some(_keccak256(tx_json));

    tx
}
