pub mod consts;
pub mod difficulty;
pub mod model;
pub mod reorg_path;
pub mod transaction;
pub mod wasm;

use anyhow::Result;
use model::implement::transaction_receipt::TransactionReceiptModel;
use wasm_py_bridge_macro::wasm_bind;

use crate::model::implement::{best_block::BestBlockModel, block_tree::BlockTreeModel};

#[wasm_bind]
pub fn initialize_chain_tables(chain_id: String) -> Result<()> {
    let blocktree_model = BlockTreeModel::new();
    let best_block_model = BestBlockModel::new();
    let transaction_receipt_model = TransactionReceiptModel::new();

    blocktree_model.create_table(&chain_id)?;
    best_block_model.create_table(&chain_id)?;
    transaction_receipt_model.create_table(&chain_id)?;
    Ok(())
}
