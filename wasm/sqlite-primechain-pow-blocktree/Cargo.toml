[package]
name = "sqlite-primechain-pow-blocktree"
version = "0.1.0"
edition = "2021"

[dependencies]
wasm-py-bridge-macro = { path = "../wasm-py-bridge-macro" }
wasm-utils = { path = "../wasm-utils" }

rusqlite = { version = "=0.29.0", features = [
    "bundled",
    "wasm32-wasi-vfs",
    "serde_json",
    "uuid",
] }
uuid = { version = "=1.6.1", features = ["serde", "v4", "fast-rng"] }
anyhow = "=1.0.75"
serde = { version = "=1.0.193", features = ["derive"] }
serde_json = "=1.0.108"
lazy_static = "=1.4.0"
sha3 = "=0.10.8"

[lib]
crate-type = ["cdylib", "rlib"]

[dev-dependencies]
reqwest = { version = "0.11", features = [
    "json",
] } # reqwest with JSON parsing support
futures = "0.3" # for our async / await blocks
tokio = { version = "1.12.0", features = ["full"] } # for our async runtime
