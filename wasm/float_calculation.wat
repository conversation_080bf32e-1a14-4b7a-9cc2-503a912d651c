(module
  (func (export "min_f")
    (param $a f32)
    (param $b f32)
    (result f32)
    local.get $a
    local.get $b
    f32.min
  )
  (func (export "max_f")
    (param $a f32)
    (param $b f32)
    (result f32)
    local.get $a
    local.get $b
    f32.max
  )
  (func (export "div")
    (param $a f32)
    (param $b f32)
    (result f32)
    local.get $a
    local.get $b
    f32.div
  )
  (func (export "add")
    (param $a f32)
    (param $b f32)
    (result f32)
    local.get $a
    local.get $b
    f32.add
  )
  (func (export "isNaN")
    (param $value f32)
    (result i32)
    local.get $value
    local.get $value
    f32.ne
  )
  (func (export "sqrt")
    (param $value f32)
    (result f32)
    local.get $value
    f32.sqrt
  )
  (func (export "abs_f")
    (param $value f32)
    (result f32)
    local.get $value
    f32.abs
  )
  (func (export "neg")
    (param $value f32)
    (result f32)
    local.get $value
    f32.neg
  )
  (func (export "ceil")
    (param $value f32)
    (result f32)
    local.get $value
    f32.ceil
  )
  (func (export "floor")
    (param $value f32)
    (result f32)
    local.get $value
    f32.floor
  )
  (func (export "trunc")
    (param $value f32)
    (result f32)
    local.get $value
    f32.trunc
  )
)
