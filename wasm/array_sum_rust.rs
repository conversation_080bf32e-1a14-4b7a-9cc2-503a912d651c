// follow  https://radu-matei.com/blog/practical-guide-to-wasm-memory/
/// Allocate memory into the module's linear memory
/// and return the offset to the start of the block.
#[no_mangle]
pub extern fn alloc(len: usize) -> *mut u8 {
    // create a new mutable buffer with capacity `len`
    let mut buf = Vec::with_capacity(len);
    // take a mutable pointer to the buffer
    let ptr = buf.as_mut_ptr();
    // take ownership of the memory block and
    // ensure that its destructor is not
    // called when the object goes out of scope
    // at the end of the function
    std::mem::forget(buf);
    // return the pointer so the runtime
    // can write data at this offset
    return ptr;
}

/// Given a pointer to the start of a byte array and
/// its length, return the sum of its elements.
#[no_mangle]
pub unsafe extern fn array_sum(ptr: *mut u8, len: usize) -> u8 {
    // create a Vec<u8> from the pointer to the
    // linear memory and the length
    let data = Vec::from_raw_parts(ptr, len, len);
    // actually compute the sum and return it
    data.iter().sum()
}

//Compiling this Rust program to a WebAssembly target (cargo build --target wasm32-unknown-unknown, or --target wasm32-wasi), the output is a .wasm module
//Convert *.wasm and *.wat to each other
//This can be easily done with the WebAssembly Binary Toolkit.
// Download the release from https://github.com/WebAssembly/wabt/releases and unzip itc
// # parse binary file test.wasm and write text file test.wat
// $ bin/wasm2wat test.wasm -o test.wat
