import os

import pytest

if os.path.basename(os.getcwd()) == "wasm":
    os.chdir("..")

import nacl.signing
import nacl.encoding
import nacl.exceptions

from wasmtime import Store, Module, Instance, Func, FuncType, Engine, Linker, WasiConfig
# from pyvsystems import testnet_chain, Account
# from pyvsystems.crypto import sign, verifySignature
import base58

str2bytes = lambda s: s.encode('latin-1')
bytes2str = lambda b: ''.join(map(chr, b))
str2list = lambda s: [c for c in s]
list2bytes = lambda s: b''.join(s)


def gen_sign(msg: str) -> (bytes, str):
    seed_hash = b'\xd8wx\x85\xc0\xbf\xaeG\xa8\x03\x93\xcb\x08\xfa,p\x82e-\xed\x939=\xc2T\xf6\xbekE8\x881'
    # Generate a signature key using the same seed
    signing_key = nacl.signing.SigningKey(seed_hash, encoder=nacl.encoding.RawEncoder)
    signed = signing_key.sign(str2bytes(msg), encoder=nacl.encoding.RawEncoder)
    signed_b58 = base58.b58encode(signed.signature)
    verify_key = signing_key.verify_key
    verify_key_b58 = base58.b58encode(verify_key.encode())

    return signed_b58, bytes2str(verify_key_b58)


def verify_sign(publicKey: str, msg: bytes, signature: bytes) -> bool:
    try:
        verify_key = nacl.signing.VerifyKey(base58.b58decode(publicKey), encoder=nacl.encoding.RawEncoder)
        original_message = verify_key.verify(msg, base58.b58decode(signature), encoder=nacl.encoding.RawEncoder)
        print("The message is authentic:", original_message)
        return True
    except nacl.exceptions.BadSignatureError:
        print("The signature was forged or tampered with!")
        return False


def test_sign():
    print("--------- python gen pubKey & signed string ---------")
    message = 'testmessage'
    signatureBytes, publicKey = gen_sign(message)
    signature = signatureBytes.decode()

    print('publicKey:', publicKey)
    print('signature:', signature)
    print('message:', message)

    publicKeyBytes = base58.b58decode(publicKey)
    print("publicKey bytes [", ", ".join(map(str, publicKeyBytes)), "]", sep="")
    signatureBytes1 = base58.b58decode(signature)
    print("signature bytes [", ", ".join(map(str, signatureBytes1)), "]", sep="")

    # --------- WASI CONFIG ---------
    wasi = WasiConfig()
    wasi.preopen_dir('.', '.')
    wasi.inherit_stdout()
    # wasi.argv = ['arga', 'bb']
    wasi.inherit_argv()
    wasi.inherit_env()
    wasi.env = [['signature', signature], ['pub_key', publicKey], ['message', message]]

    engine = Engine()
    module = Module.from_file(engine, "wasm/test_sign.wasm")
    linker = Linker(engine)
    linker.define_wasi()

    store = Store(engine)
    store.set_wasi(wasi)

    instance = linker.instantiate(store, module)

    print("--------- python test ---------")

    # custom ed25519 verify
    verifyResult = verify_sign(publicKey.encode(), message.encode(), signatureBytes)

    print('python VerifyResult', verifyResult)

    print("--------- wasi test ---------")
    allocate = instance.exports(store)["allocate"]
    deallocate = instance.exports(store)["deallocate"]
    memory = instance.exports(store)["memory"]
    verify = instance.exports(store)["verify"]

    publicKeyPtr = allocate(store, len(publicKey))
    writeString(memory, store, publicKey, publicKeyPtr)
    messagePtr = allocate(store, len(message))
    writeString(memory, store, message, messagePtr)
    signedStringPtr = allocate(store, len(signature))
    writeString(memory, store, signature, signedStringPtr)

    print("\t--------- wasi stdout verify begin ---------")
    wasiVerifyResult = verify(store) == 1
    print("\t--------- wasi stdout rust verify end ---------")

    print("wasi VerifyResult:", wasiVerifyResult)


def writeString(mem, store, input_str, ptr):
    # write into memory
    mem.write(store, bytearray(input_str.encode()), ptr)
    # write 0 as NULL to terminate C-string
    mem.data_ptr(store)[ptr + len(input_str)] = 0


def getString(mem, store, ptr):
    # loop to read output string
    output_str = ''
    i = 0
    while True:
        # C-string terminates by NULL.
        if mem.data_ptr(store)[ptr + i] == 0:
            break
        char = chr(mem.data_ptr(store)[ptr + i])
        output_str += char
        i += 1
    return output_str
