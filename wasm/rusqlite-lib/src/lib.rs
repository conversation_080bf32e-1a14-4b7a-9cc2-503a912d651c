use rusqlite::{Connection, Result};
use std::sync::Mutex;
use std::ffi::{CStr};
use std::mem;
use std::os::raw::{c_char, c_void};

#[derive(Debug)]
struct Person {
    id: i32,
    name: String,
    age: i32,
}

// _start func is needed for wasm32-wasip1 target
#[no_mangle]
#[export_name = "_start"]
pub extern "C" fn main() -> Result<()> {
    Ok(())
}


#[no_mangle]
pub extern "C" fn create_db_connection() -> Box<Mutex<Connection>> {
    let conn_str = ":memory:";
    let conn = Connection::open(conn_str).unwrap();
    // let conn = Connection::open_in_memory().unwrap();
    let conn = Mutex::new(conn);
    return Box::new(conn);
}

#[no_mangle]
pub extern fn allocate(size: usize) -> *mut c_void {
  let mut buffer = Vec::with_capacity(size);
  let pointer = buffer.as_mut_ptr();
  mem::forget(buffer);

  pointer as *mut c_void
}

#[no_mangle]
pub extern fn deallocate(pointer: *mut c_void, capacity: usize) {
  unsafe {
    let _ = Vec::from_raw_parts(pointer, 0, capacity);
  }
}

fn get_string(ptr: *mut c_char) -> Result<String> {
    let c_str = unsafe { CStr::from_ptr(ptr).to_bytes() };
    let string = std::str::from_utf8(c_str).unwrap();
    Ok(string.to_string())
}

#[no_mangle]
pub extern "C" fn add_person(conn: &Mutex<Connection>, name_ptr: *mut c_char, age: i32) {
    let conn = conn.lock().unwrap();

    let name_str = get_string(name_ptr).unwrap();

    conn.execute(
        "CREATE TABLE IF NOT EXISTS person (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            age INTEGER NOT NULL
        )",
        [],
    ).unwrap();

    conn.execute(
        "INSERT INTO person (name, age) VALUES (?1, ?2)",
        (name_str, age),
    ).unwrap();
    ()
}


#[no_mangle]
pub extern "C" fn get_person_age(conn: &Mutex<Connection>, n: i32) -> i32 {
    let conn = conn.lock().unwrap();
    let mut stmt = conn.prepare("SELECT age FROM person WHERE id = ?1").unwrap();
    let age: i32 = stmt.query_row([n], |row| row.get(0)).unwrap();
    println!("get_person_age age of person {} is {:?}", n, age);
    age
}


#[no_mangle]
pub extern "C" fn get_person_data(conn: &Mutex<Connection>, n: i32) -> Result<(String, i32)> {
    let conn = conn.lock().unwrap();
    let mut stmt = conn.prepare("SELECT name, age FROM person WHERE id = ?1").unwrap();
    let row = stmt.query_row([n], |row| {
        let name = row.get(0)?;
        let age = row.get(1)?;
        println!("get_person name of person {} is {:?}, age is {:?}", n, name, age);
        Ok((name, age))
    });

    match row {
        Ok(result) => Ok(result),
        Err(e) => Err(e),
    }
}


