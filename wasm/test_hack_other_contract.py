import pytest

from vm import ContractTester, resetTesterState

tokenClient = ContractTester(
    address="6985493A43B94EBF9904341AEAD740F4",
    wasmName="token_lmdb_demo",
)

hackClient = ContractTester(
    address="6F5E6E4610DE489A95CC94741DA0B55D",
    wasmName="hack_contract"
)


@pytest.fixture(autouse=True)
def clean_db():
    resetTesterState()


@pytest.fixture(autouse=True)
def register_contract():
    tokenClient.constructor("vgraph_token")
    hackClient.constructor()


def test_hack_contract():
    tokenClient.execute("issue", None, "Alice", 1234)
    hackClient.execute("hack", None, "Alice", 5678)
    # check if the hack is successful
    result, err = tokenClient.executeReadOnly("balance", int, "Alice")
    assert err is None
    assert result == 1234
