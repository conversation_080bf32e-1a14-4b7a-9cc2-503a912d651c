# Import necessary modules for testing, data handling, WebAssembly operations, and timing.
import os
import unittest

from wasmtime import Store, Module, Engine, Linker, WasiConfig

# setup environment, import local modules and set basePath
if os.path.basename(os.getcwd()) == "wasm":
    os.chdir("..")
from wasm import wasm_py_bridge

# Define a test class for testing WebAssembly bindings using Python's unittest framework.
class TestNameService(unittest.TestCase):
    engine = None
    linker = None

    @classmethod
    def setUpClass(cls):
        # Setup for the test class, initializing the WebAssembly environment.
        cls.engine = Engine()
        cls.module = Module.from_file(cls.engine, "wasm/sqlite_name_service.wasm")
        cls.linker = Linker(cls.engine)
        cls.linker.define_wasi()

    def setUp(self):
        # Setup for each test case, configuring the WebAssembly instance.
        wasi = WasiConfig()
        wasi.preopen_dir('.', '.')
        wasi.inherit_stdout()
        wasi.inherit_env()
        self.store = Store(self.engine)
        self.store.set_wasi(wasi)
        self.instance = self.linker.instantiate(self.store, self.module)
        # clean vgraph.db if exists
        if os.path.exists("vgraph.db"):
            os.remove("vgraph.db")

    def test_check_package_name(self):
        isSuccess, err = wasm_py_bridge.invoke(self.store, self.instance, "check_package_name", bool, "test_pack")
        self.assertEqual(isSuccess, True)

    def test_add_package_name(self):
        isSuccess, err = wasm_py_bridge.invoke(self.store, self.instance, "add_package_name", bool, "1")
        self.assertEqual(isSuccess, False)

    def test_check_package_name_special_chars(self):
        isSuccess, err = wasm_py_bridge.invoke(self.store, self.instance, "check_package_name", bool, "test@pack")
        self.assertEqual(isSuccess, False)

    def test_check_package_name_long(self):
        long_package_name = "a" * 65
        isSuccess, err = wasm_py_bridge.invoke(self.store, self.instance, "check_package_name", bool,
                                                long_package_name)
        self.assertEqual(isSuccess, False)

    def test_check_package_name_empty(self):
        isSuccess, err = wasm_py_bridge.invoke(self.store, self.instance, "check_package_name", bool, "")
        self.assertEqual(isSuccess, False)

    def test_check_package_name_duplicate(self):
        isSuccess, err = wasm_py_bridge.invoke(self.store, self.instance, "add_package_name", bool, "test-pack")
        self.assertEqual(isSuccess, True)
        isSuccess, err = wasm_py_bridge.invoke(self.store, self.instance, "check_package_name", bool, "test-pack")
        self.assertEqual(isSuccess, False)

    def test_add_package_name_duplicate(self):
        isSuccess, err = wasm_py_bridge.invoke(self.store, self.instance, "add_package_name", bool, "test_pack")
        self.assertEqual(isSuccess, True)
        isSuccess, err = wasm_py_bridge.invoke(self.store, self.instance, "add_package_name", bool, "test_pack")
        self.assertEqual(isSuccess, False)

    def test_add_many_package_name(self):
        # package name array
        package_names = ["test_pack1", "test_pack2", "test_pack3"]
        # check them not exist
        for package_name in package_names:
            isSuccess, err = wasm_py_bridge.invoke(self.store, self.instance, "check_package_name", bool, package_name)
            self.assertEqual(isSuccess, True)

        isSuccess, err = wasm_py_bridge.invoke(self.store, self.instance, "add_many_package_name", bool, package_names)
        self.assertEqual(isSuccess, True)

        # check them exist
        for package_name in package_names:
            isSuccess, err = wasm_py_bridge.invoke(self.store, self.instance, "check_package_name", bool, package_name)
            self.assertEqual(isSuccess, False)


if __name__ == '__main__':
    unittest.main()  # Executing the tests if the script is run directly.
