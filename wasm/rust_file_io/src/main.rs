use std::fs::File;
use std::io::{self, Read, Write};

fn read_file(file_path: &str) -> io::Result<String> {
    let mut file = File::open(file_path)?;
    let mut contents = String::new();
    file.read_to_string(&mut contents)?;
    Ok(contents)
}

fn write_file(file_path: &str, content: &str) -> io::Result<()> {
    let mut file = File::create(file_path)?;
    file.write_all(content.as_bytes())?;
    Ok(())
}

fn main() {
    let file_path = "example.txt";

    // write file
    let content = "Hello, World!";
    if let Err(err) = write_file(file_path, content) {
        eprintln!("Failed to write file: {}", err);
        return;
    }
    println!("File written successfully!");

    // read file
    match read_file(file_path) {
        Ok(contents) => {
            println!("File content:\n{}", contents);
        }
        Err(err) => {
            eprintln!("Failed to read file: {}", err);
        }
    }
}
