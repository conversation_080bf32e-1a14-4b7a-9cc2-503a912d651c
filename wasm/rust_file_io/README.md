
## Usage:

1. [Install the Rust toolchain here](https://www.rust-lang.org/tools/install)

4. give permission to `build.sh` and run it to build

   ```shell
   chmod u+x build.sh
   
   ./build.sh
   ```

5. The wasm file will be copy to the `wasm` folder. Then you can run it with your webassembly runtime.

   For example, [wasmtime](https://wasmtime.dev/)

   ```shell
   wasmtime run rust_file_io.wasm --dir=.
   ```
