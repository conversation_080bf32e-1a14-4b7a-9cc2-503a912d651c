[package]
name = "test-sign"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
#curve25519-dalek = "=4.1.1"
bs58 = "0.5.0"
#rusqlite = { version = "0.29.0", features = ["bundled", "wasm32-wasi-vfs"] }
rand = "0.8.5"
anyhow = "1.0.75"
#hex = "0.4.3"
#rand_core = "0.6.4"
ed25519-dalek = "=2.1.1"

[lib]
crate-type = ["cdylib", "rlib"]
