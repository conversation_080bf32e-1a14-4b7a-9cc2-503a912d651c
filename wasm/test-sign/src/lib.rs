// #![allow(unused_variables, unused_mut, dead_code, unused_imports)]
use std::mem;
use std::env;
use std::os::raw::c_void;
use ed25519_dalek::{Signature, Verifier, VerifyingKey};
use bs58;


#[no_mangle]
pub extern fn verify() -> bool {
    // get envs
    let public_key_b58 = env::var("pub_key").expect("pub_key Environment variable does not exist");
    let signature_b58 = env::var("signature").expect("signature Environment variable does not exist");
    let message_str = env::var("message").expect("message Environment variable does not exist");

    // print envs
    println!("public_key_b58: {:?}", public_key_b58);
    println!("signature_b58: {:?}", signature_b58);
    println!("message_str: {:?}", message_str);

    // decode b58
    let public_key_vec = &bs58::decode(public_key_b58).into_vec().expect("Failed to decode Base58");
    if public_key_vec.len() != 32 {
        println!("Invalid public key length: expected 32 bytes, got {}", public_key_vec.len());
    }
    let public_key_bytes = public_key_vec.as_slice();

    let signature_vec = bs58::decode(signature_b58).into_vec().expect("Failed to decode Base58");
    if signature_vec.len() != 64 {
        println!("Invalid signature length: expected 64 bytes, got {}", signature_vec.len());
    }
    let signature_bytes = signature_vec.as_slice();

    // print bytes
    println!("public_key_bytes: {:?}", public_key_bytes);
    println!("signature_bytes: {:?}", signature_bytes);

    // Convert your signature bytes into a Signature object.
    let signature = Signature::try_from(signature_bytes)
        .expect("Failed to create signature");

    // Convert your public key bytes into a PublicKey object.
    let public_key = VerifyingKey::try_from(public_key_bytes)
        .expect("Failed to create public key");

    // Verify the signature.
    match public_key.verify(message_str.as_bytes(), &signature) {
        Ok(_) => {
            println!("Signature is valid.");
            true
        }
        Err(_) => {
            println!("Signature is invalid.");
            false
        }
    }
}

#[no_mangle]
#[export_name = "_start"]
pub extern "C" fn main() {
    println!("empty main func");
}

#[no_mangle]
pub extern fn allocate(size: usize) -> *mut c_void {
    let mut buffer = Vec::with_capacity(size);
    let pointer = buffer.as_mut_ptr();
    mem::forget(buffer);

    pointer as *mut c_void
}

#[no_mangle]
pub extern fn deallocate(pointer: *mut c_void, capacity: usize) {
    unsafe {
        let _ = Vec::from_raw_parts(pointer, 0, capacity);
    }
}
