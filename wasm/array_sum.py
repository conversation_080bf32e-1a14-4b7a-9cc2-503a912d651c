from kivy.logger import Logger
from wasmtime import Store, Module, Instance, Trap, MemoryType, Memory, Limits, WasmtimeError
class ArraySum:

    def copyMemory(store,array, instance):
        ptr = instance.exports(store)["alloc"](store,len(array))
        memory = instance.exports(store)["memory"]
        for i in range(len(array)):
            memory.data_ptr(store)[ptr+i] = array[i]
        return ptr
    def arraySum(self,store,array, instance):
        from wasm.array_sum import ArraySum
        ptr = ArraySum.copyMemory(store, array, instance)
        res = instance.exports(store)["array_sum"](store,ptr,len(array))
        Logger.info(f'Result={res}')
