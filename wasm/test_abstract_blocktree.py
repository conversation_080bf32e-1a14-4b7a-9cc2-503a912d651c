import dataclasses
from typing import List, Any

import pytest
import serde

from common.utils import keccak256
from vm import resetTesterState, ContractTester

contractName = "abstract_blocktree"
contractAddress = "2e2a3d60-55f0-44e5-87e5-3899b8f4c048".replace("-", "")

contractClient = ContractTester(
    address=contractAddress,
    packageName="abstract_blocktree",
    wasmName=contractName,
)

rootParentId = "00000000-0000-0000-0000-000000000000"


@pytest.fixture(autouse=True)
def clean_db():
    resetTesterState()


@pytest.fixture(autouse=True)
def register_contract():
    # default new init contract
    contractClient.constructor("BlockTree")


@serde.serde
@dataclasses.dataclass
class ReorganizationPath:
    roll_back_path: List[str]
    common_ancestor: str
    roll_forward_path: List[str]

    def __str__(self) -> str:
        return (
            f"ReorganizationPath(roll_back_path={self.roll_back_path}, common_ancestor={self.common_ancestor}, "
            f"roll_forward_path={self.roll_forward_path})"
        )


@serde.serde
@dataclasses.dataclass
class TransactionReceipt:
    transaction_hash: str
    transaction_index: int
    block_id: str
    from_address: str
    contract_address: str
    package_name: str
    contract_name: str  # mod name
    struct_name: str
    function_name: str
    parameters: List[Any]
    publickeys: List[str]
    signatures: List[str]
    status: bool
    result: Any

    def __str__(self) -> str:
        return (
            f"TransactionReceipt(transaction_hash={self.transaction_hash}, transaction_index={self.transaction_index}, "
            f"block_id={self.block_id}, from_address={self.from_address}, contract_address={self.contract_address}, "
            f"packageName={self.package_name}, contract_name={self.contract_name}, struct_name={self.struct_name}, "
            f"function_name={self.function_name}, parameters={self.parameters}, publickeys={self.publickeys}, "
            f"signatures={self.signatures}, status={self.status}, result={self.result})"
        )


@serde.serde
@dataclasses.dataclass
class AbstractBlock:
    id: str
    parent_id: str
    difficulty_score: int
    difficulty_score_overall: int
    height: int
    transactions: str
    merkle: str
    timestamp: int
    nonce: int
    multiplier: int


def test_lmdb(clean_db, register_contract):
    empty_hash = getHash("[]")
    assert empty_hash == "518674ab2b227e5f11e9084f615d57663cde47bce1ba168b4c19c7ee22a73d70"

    result, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, rootParentId, [], empty_hash, 0, 0, 1)

    assert err is None


def getHash(input):
    return keccak256(input)


def test_new_block(clean_db, register_contract):
    # create root block1
    rootParentId = "00000000-0000-0000-0000-000000000000"
    empty_hash = getHash("[]")
    block1, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, rootParentId, [], empty_hash, 0, 0, 1)
    assert block1.parent_id == rootParentId
    assert block1.height == 1

    # create block2
    block2ParentId = block1.id
    block2, err = contractClient.execute(
        "BlockTree", "new_block", AbstractBlock, block2ParentId, [], empty_hash, 0, 0, 1
    )
    assert block2.parent_id == block2ParentId
    assert block2.height == 2
    assert block2.difficulty_score_overall == block2.difficulty_score + block1.difficulty_score_overall

    # create block3
    block3ParentId = block1.id
    block3, err = contractClient.execute(
        "BlockTree", "new_block", AbstractBlock, block3ParentId, [], empty_hash, 0, 0, 1
    )
    assert block3.parent_id == block3ParentId
    assert block3.height == 2
    assert block3.difficulty_score_overall == block3.difficulty_score + block1.difficulty_score_overall

    # create block4
    block4ParentId = block3.id
    block4, err = contractClient.execute(
        "BlockTree", "new_block", AbstractBlock, block4ParentId, [], empty_hash, 0, 0, 1
    )
    assert block4.parent_id == block4ParentId
    assert block4.height == 3
    assert block4.difficulty_score_overall == block4.difficulty_score + block3.difficulty_score_overall


def test_best_block(clean_db, register_contract):
    # create root block1
    rootParentId = "00000000-0000-0000-0000-000000000000"
    empty_hash = getHash("[]")
    block1, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, rootParentId, [], empty_hash, 0, 0, 1)
    bestBlockId, err = contractClient.executeReadOnly("BlockTree", "get_best_block_id", str)
    assert bestBlockId == block1.id

    # create block2
    block2ParentId = block1.id
    block2, err = contractClient.execute(
        "BlockTree", "new_block", AbstractBlock, block2ParentId, [], empty_hash, 0, 0, 1
    )
    bestBlockId, err = contractClient.executeReadOnly("BlockTree", "get_best_block_id", str)
    assert bestBlockId == block2.id

    # create block3
    block3ParentId = block1.id
    block3, err = contractClient.execute(
        "BlockTree", "new_block", AbstractBlock, block3ParentId, [], empty_hash, 0, 0, 1
    )
    bestBlockId, err = contractClient.executeReadOnly("BlockTree", "get_best_block_id", str)
    assert bestBlockId == block2.id
    # create block4
    block4ParentId = block3.id
    block4, err = contractClient.execute(
        "BlockTree", "new_block", AbstractBlock, block4ParentId, [], empty_hash, 0, 0, 1
    )
    # test best block
    bestBlockId, err = contractClient.executeReadOnly("BlockTree", "get_best_block_id", str)
    assert bestBlockId == block4.id


def test_reorganization_path(clean_db, register_contract):
    # Block Tree Structure:
    # block1 is the root block, created from ROOT_PARENT_ID
    # block2 and block3 are children of block1
    # block4 is a child of block3
    # block5 is a child of block4
    #
    #      block1
    #      /     \
    #   block2   block3
    #              |
    #            block4
    #             |
    #            block5

    rootParentId = "00000000-0000-0000-0000-000000000000"
    empty_hash = getHash("[]")
    block1, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, rootParentId, [], empty_hash, 0, 0, 1)
    block2, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, block1.id, [], empty_hash, 0, 0, 1)
    block3, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, block1.id, [], empty_hash, 0, 0, 1)
    block4, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, block3.id, [], empty_hash, 0, 0, 1)
    block5, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, block4.id, [], empty_hash, 0, 0, 1)
    print(
        "      block1\n      /     \\\n   block2   block3\n              |\n            block4\n              |\n            block5\n"
    )

    # start = block2
    # end = block5
    reorgPath, err = contractClient.executeReadOnly(
        "BlockTree", "reorganization_path", ReorganizationPath, block2.id, block5.id
    )
    assert reorgPath.roll_back_path == [block2.id]
    assert reorgPath.common_ancestor == block1.id
    assert reorgPath.roll_forward_path == [block3.id, block4.id, block5.id]


def test_generate_locator(clean_db, register_contract):
    maxLocatorSize = 32
    lastBlockId = "00000000-0000-0000-0000-000000000000"
    blocks = []
    empty_hash = getHash("[]")
    # insert some blocks in the tree
    for _ in range(50):
        block, err = contractClient.execute(
            "BlockTree", "new_block", AbstractBlock, lastBlockId, [], empty_hash, 0, 0, 1
        )
        blocks.append(block)
        lastBlockId = block.id

    # generate locator
    targetBlockIndex = len(blocks) - 1
    targetBlockId = blocks[targetBlockIndex].id
    locator, err = contractClient.executeReadOnly("BlockTree", "generate_locator", List[str], targetBlockId)
    print(f"locator: {locator}")

    # check locator
    assert maxLocatorSize >= len(locator) > 0

    shift = 1
    factor = 1

    for i in range(min(len(locator), maxLocatorSize)):
        assert locator[i] == blocks[targetBlockIndex + 1 - shift].id
        if i >= 10:
            factor *= 2
        shift += factor


def test_receive_new_block(clean_db, register_contract):
    #
    #      block1
    #        |
    #      block2
    #      /    \
    #  block4   block3
    #             |
    #            block5
    #             ...
    #            block7
    #
    # create root block1
    rootParentId = "00000000-0000-0000-0000-000000000000"
    empty_hash = getHash("[]")
    block1, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, rootParentId, [], empty_hash, 0, 0, 1)
    block2, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, block1.id, [], empty_hash, 0, 0, 1)
    block3 = AbstractBlock(
        id="00000000-0000-0000-0000-000000000003",
        parent_id=block2.id,
        difficulty_score=1,
        difficulty_score_overall=block2.difficulty_score_overall + 1,
        height=3,
        transactions="[]",
        merkle=empty_hash,
        timestamp=0,
        nonce=0,
        multiplier=1,
    )
    message, err = contractClient.execute("BlockTree", "receive_new_block", str, block3)
    assert message == "success"
    size, err = contractClient.executeReadOnly("BlockTree", "size", int)
    assert size == 3
    best_block, err = contractClient.executeReadOnly("BlockTree", "get_best_block", AbstractBlock)
    assert best_block == block3

    # receive block4
    block4 = AbstractBlock(
        id="00000000-0000-0000-0000-000000000004",
        parent_id=block2.id,
        difficulty_score=1,
        difficulty_score_overall=block2.difficulty_score_overall + 1,
        height=3,
        transactions="[]",
        merkle=empty_hash,
        timestamp=0,
        nonce=0,
        multiplier=1,
    )
    # block tree receive block4, blocktree size should be 4
    message, err = contractClient.execute("BlockTree", "receive_new_block", str, block4)
    assert message == "block difficulty less or equal"
    size, err = contractClient.executeReadOnly("BlockTree", "size", int)
    assert size == 4
    # best block should be block3
    best_block, err = contractClient.executeReadOnly("BlockTree", "get_best_block", AbstractBlock)
    assert best_block == block3

    error_block = AbstractBlock(
        id="00000000-0000-0000-0000-000000000004",
        parent_id=block3.id,
        difficulty_score=1,
        difficulty_score_overall=block2.difficulty_score_overall + 1,
        height=3,
        transactions="[]",
        merkle=empty_hash,
        timestamp=0,
        nonce=0,
        multiplier=1,
    )
    message, err = contractClient.execute("BlockTree", "receive_new_block", str, error_block)
    assert message == "error"
    size, err = contractClient.executeReadOnly("BlockTree", "size", int)
    assert size == 4

    # receive block5
    block5 = AbstractBlock(
        id="00000000-0000-0000-0000-000000000005",
        parent_id=block3.id,
        difficulty_score=1,
        difficulty_score_overall=block3.difficulty_score_overall + 1,
        height=4,
        transactions="[]",
        merkle=empty_hash,
        timestamp=0,
        nonce=0,
        multiplier=1,
    )
    message, err = contractClient.execute("BlockTree", "receive_new_block", str, block5)
    assert message == "success"
    size, err = contractClient.executeReadOnly("BlockTree", "size", int)
    assert size == 5
    best_block, err = contractClient.executeReadOnly("BlockTree", "get_best_block", AbstractBlock)
    assert best_block == block5

    # receive a higher block, but not a child of current best block
    block7 = AbstractBlock(
        id="00000000-0000-0000-0000-000000000007",
        parent_id="00000000-0000-0000-0000-000000000006",
        difficulty_score=1,
        difficulty_score_overall=block5.difficulty_score_overall + 2,
        height=6,
        transactions="[]",
        merkle=empty_hash,
        timestamp=0,
        nonce=0,
        multiplier=1,
    )
    message, err = contractClient.execute("BlockTree", "receive_new_block", str, block7)
    assert message == "locator"
    size, err = contractClient.executeReadOnly("BlockTree", "size", int)
    assert size == 5


def test_locator_sync_blocks(clean_db, register_contract):
    # generate blocks [block1, block2, block3, block4]
    rootParentId = "00000000-0000-0000-0000-000000000000"
    empty_hash = getHash("[]")
    block1, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, rootParentId, [], empty_hash, 0, 0, 1)
    block2, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, block1.id, [], empty_hash, 0, 0, 1)
    block3, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, block2.id, [], empty_hash, 0, 0, 1)
    block4, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, block3.id, [], empty_hash, 0, 0, 1)
    # generate locator from block_tree and then remove block4
    locator, err = contractClient.executeReadOnly("BlockTree", "generate_locator", List[str], block4.id)
    print(f"block_tree locator : {locator}")
    # locator will be [block4.id, block3.id, block2.id, block1.id]
    assert locator == [block4.id, block3.id, block2.id, block1.id]
    # remove block4 and best block will be block3
    result, err = contractClient.execute("BlockTree", "remove", None, block4.id)
    assert err is None
    result, err = contractClient.execute("BlockTree", "set_best_block_id", None, block3.id)
    assert err is None
    # generate blocks 6,7,8,9 -> [block1, block2, block3, block6, block7, block8, block9]
    block6, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, block3.id, [], empty_hash, 0, 0, 1)
    block7, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, block6.id, [], empty_hash, 0, 0, 1)
    block8, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, block7.id, [], empty_hash, 0, 0, 1)
    block9, err = contractClient.execute("BlockTree", "new_block", AbstractBlock, block8.id, [], empty_hash, 0, 0, 1)

    # block_tree receive locator, the common ancestor is block3
    # find and return path from common ancestor to best block
    # generate block path will be [block3, block6, block7, block8, block9]
    block_path, err = contractClient.executeReadOnly("BlockTree", "receive_locator", List[AbstractBlock], locator)
    print(f"block_path: {block_path}")
    assert block_path == [block3, block6, block7, block8, block9]

    # test receive_sync_blocks
    result, err = contractClient.execute("BlockTree", "remove", None, block9.id)
    result, err = contractClient.execute("BlockTree", "remove", None, block8.id)
    result, err = contractClient.execute("BlockTree", "remove", None, block7.id)
    result, err = contractClient.execute("BlockTree", "remove", None, block6.id)
    result, err = contractClient.execute("BlockTree", "set_best_block_id", None, block3.id)
    # receive_sync_blocks
    # block_tree -> [block1, block2, block3, block6, block7, block8, block9]
    result, err = contractClient.execute("BlockTree", "receive_sync_blocks", str, block_path)
    assert result == "success"
    size, err = contractClient.executeReadOnly("BlockTree", "size", int)
    assert size == 7
    best_block, err = contractClient.executeReadOnly("BlockTree", "get_best_block", AbstractBlock)
    assert best_block == block9


def test_get_none_transaction_receipt(clean_db, register_contract):
    result, err = contractClient.executeReadOnly("TransactionMap", "get", TransactionReceipt, "abcdefg")
    assert result is None


def test_create_and_get_transaction_receipt(clean_db, register_contract):
    tx_receipt = TransactionReceipt(
        transaction_hash="6b5a862716deebb2f2f98f06b00d511efc8b44a21ec0a708e7346f3e0081fac4",
        transaction_index=0,
        block_id="9731c9e4-2ff0-480f-b5b9-5c4411dc4a12",
        from_address="ATvJmXWLfq1z41f5cHKCae72cZdSZzSnvC9",
        contract_address="3e2a3d6055f044e587e53899b8f4c045",
        package_name="token",
        contract_name="token",
        struct_name="Token",
        function_name="issue",
        parameters=["Alice", 1000],
        publickeys=["addd"],
        signatures=["xxxxxxx"],
        status=True,
        result=None,
    )
    ok, err = contractClient.execute("TransactionMap", "create", bool, tx_receipt)
    if ok and err is not None:
        result, err = contractClient.executeReadOnly("TransactionMap", "get", TransactionReceipt, "abcdefg")
        assert result == tx_receipt
