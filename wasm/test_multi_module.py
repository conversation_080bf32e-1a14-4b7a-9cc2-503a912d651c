import pytest
from wasmtime import Store, Module, Instance, Func, FuncType, Engine, Linker, WasiConfig, ValType, Config


def test_multi_module():
    wasi = WasiConfig()
    wasi.preopen_dir('.', '.')
    wasi.inherit_stdout()

    engine = Engine()
    linking1_module = Module.from_file(engine, "wasm/multi_module.wasm")
    linker = Linker(engine)
    linker.define_wasi()

    # Create a `Store` to hold instances, and configure wasi state
    store = Store(engine)
    store.set_wasi(wasi)

    linking1 = linker.instantiate(store, linking1_module)
    for foo in linking1_module.exports:
        print(foo.name)

    run = linking1.exports(store)["load_rusqlite_run"]
    run(store)
