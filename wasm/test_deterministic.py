# Test floating point operations deterministic of wasmtime
# Need to add the cranelift_nan_canonicalization setter
# in wasmtime/_config.py with scripts/patch_wasmtime_nan_config.sh

import math
import platform
import struct

from wasmtime import Config, Engine, Instance, Linker, Module, Store, WasiConfig

machine = platform.machine().lower()
print(f"machine: {machine}")

arch = ""
if "x86" in machine or "amd64" in machine:
    arch = "x86"
elif "arm" in arch or "aarch" in arch:
    arch = "arm"


def initWasm() -> (Instance, Store):
    wasi = WasiConfig()
    wasi.preopen_dir(".", ".")
    wasi.inherit_stdout()

    engineConfig = Config()
    engineConfig.cranelift_nan_canonicalization = True

    engine = Engine(engineConfig)
    linking1_module = Module.from_file(engine, "wasm/float_calculation.wat")
    linker = Linker(engine)
    linker.define_wasi()

    store = Store(engine)
    store.set_wasi(wasi)

    linking1 = linker.instantiate(store, linking1_module)

    return linking1, store


def floatToStr(value: float) -> str:
    """
    Converts a floating-point number to its IEEE 754 single-precision binary string representation.

    Args:
        value (float): The floating-point number to convert.

    Returns:
        str: The binary string representation of the floating-point number.
    """
    packed = struct.pack("f", value)
    bits = struct.unpack("I", packed)[0]
    sign = (bits >> 31) & 0x1
    exponent = (bits >> 23) & 0xFF
    fraction = bits & 0x7FFFFF

    sign = f"{sign:b}"
    exponent = f"{exponent:b}".zfill(8)
    fraction = f"{fraction:b}".zfill(23)
    bytes_string = f"{sign} {exponent} {fraction}"
    print(f"Float bytes: {bytes_string}")
    return bytes_string


def strToFloat(binary_str: str) -> float:
    """
    Converts a IEEE 754 single-precision binary string representation to a floating-point number.

    Args:
        binary_str (str): The binary string representation of the floating-point number.

    Returns:
        float: The floating-point number.
    """
    binary_str = binary_str.replace(" ", "").strip()
    bytes_value = int(binary_str, 2).to_bytes(4, byteorder="big")
    float_value = struct.unpack("!f", bytes_value)[0]
    print(f"{type(float_value)} strToFloat value: {float_value}")
    return float_value


def testFloatConvert():
    assert strToFloat("0 10000001 01000000000000000000000") == 5.0
    # nan is not equal to any value even nan itself in IEEE 754
    assert strToFloat("0 11111111 10000000000000000000000") != strToFloat("0 11111111 10000000000000000000000")
    assert math.isnan(strToFloat("0 11111111 10000000000000000000000"))
    assert math.isnan(strToFloat("0 11111111 10000000000110000000000"))
    # -inf
    assert strToFloat("1 11111111 00000000000000000000000") == float("-inf")
    # inf
    assert strToFloat("0 11111111 00000000000000000000000") == float("inf")
    assert floatToStr(0.5) == "0 01111110 00000000000000000000000"
    assert floatToStr(5.0) == "0 10000001 01000000000000000000000"
    assert floatToStr(0.3) == "0 01111101 00110011001100110011010"
    assert floatToStr(7.7) == "0 10000001 11101100110011001100110"
    assert floatToStr(float("nan")) == "0 11111111 10000000000000000000000"
    assert floatToStr(float("-nan")) == "1 11111111 10000000000000000000000"
    assert floatToStr(float("inf")) == "0 11111111 00000000000000000000000"
    assert floatToStr(float("-inf")) == "1 11111111 00000000000000000000000"


def testMin():
    linking, store = initWasm()
    minF = linking.exports(store)["min_f"]

    def execute(a, b):
        result = minF(store, a, b)
        print(f"----------min {a}  {b} = {result}")
        return floatToStr(result)

    value = execute(0.0, 0.0)
    assert value == "0 00000000 00000000000000000000000"
    value = execute(0.0, 3.14)
    assert value == "0 00000000 00000000000000000000000"
    value = execute(3.14, 0.0)
    assert value == "0 00000000 00000000000000000000000"
    value = execute(3.14, 3.14)
    assert value == "0 10000000 10010001111010111000011"
    value = execute(float("nan"), 0.0)
    assert value == "0 11111111 10000000000000000000000"
    value = execute(0.0, float("nan"))
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("nan"), 3.14)
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("nan"), -3.14)
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("nan"), float("-nan"))
    # assert value == "1 11111111 10000000000000000000000" if arch == "x86" else "0 11111111 10000000000000000000000"
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("-nan"), float("nan"))
    # assert value == "0 11111111 10000000000000000000000" if arch == "x86" else "1 11111111 10000000000000000000000"
    assert value == "0 11111111 10000000000000000000000"
    value = execute(2.5, strToFloat("1 11111111 10001100000000010000000"))
    # assert value == "1 11111111 10001100000000010000000"
    assert value == "0 11111111 10000000000000000000000"
    # different nan to compare
    value = execute(strToFloat("1 11111111 10001100000000010000000"), strToFloat("0 11111111 10001100000000010000111"))
    # assert value == "0 11111111 10001100000000010000111" if arch == "x86" else "1 11111111 10001100000000010000000"
    assert value == "0 11111111 10000000000000000000000"


def testMax():
    linking, store = initWasm()
    maxF = linking.exports(store)["max_f"]

    def execute(a, b):
        result = maxF(store, a, b)
        print(f"----------max {a}  {b} = {result}")
        return floatToStr(result)

    value = execute(0.0, 0.0)
    assert value == "0 00000000 00000000000000000000000"
    value = execute(0.0, 3.14)
    assert value == "0 10000000 10010001111010111000011"
    value = execute(3.14, 0.0)
    assert value == "0 10000000 10010001111010111000011"
    value = execute(3.14, 3.14)
    assert value == "0 10000000 10010001111010111000011"
    value = execute(float("nan"), 0.0)
    assert value == "0 11111111 10000000000000000000000"
    value = execute(0.0, float("nan"))
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("nan"), 3.14)
    assert value == "0 11111111 10000000000000000000000"
    value = execute(3.14, float("nan"))
    assert value == "0 11111111 10000000000000000000000"
    value = execute(-3.14, float("nan"))
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("nan"), float("nan"))
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("nan"), float("-nan"))
    # assert value == "1 11111111 10000000000000000000000" if arch == "x86" else "0 11111111 10000000000000000000000"
    assert value == "0 11111111 10000000000000000000000"
    value = execute(strToFloat("1 11111111 10001100000000010000000"), strToFloat("0 11111111 10001100000000010000111"))
    # assert value == "0 11111111 10001100000000010000111" if arch == "x86" else "1 11111111 10001100000000010000000"
    assert value == "0 11111111 10000000000000000000000"


def testAdd():
    linking, store = initWasm()
    add = linking.exports(store)["add"]

    def execute(a, b):
        result = add(store, a, b)
        print(f"----------add {a} + {b} = {result}")
        return floatToStr(result)

    value = execute(3.0, 7.0)
    assert value == "0 10000010 01000000000000000000000"
    value = execute(3.0, 0.0)
    assert value == "0 10000000 10000000000000000000000"
    value = execute(0.1, 0.2)  # 0.30000001192092896
    assert value == "0 01111101 00110011001100110011010"
    value = execute(1.1, 2.2)  # 3.3000001907348633
    assert value == "0 10000000 10100110011001100110100"
    value = execute(3.0, float("nan"))
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("-nan"), 3.0)
    # assert value == "1 11111111 10000000000000000000000"
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("-nan"), float("nan"))
    # assert value == "1 11111111 10000000000000000000000"
    assert value == "0 11111111 10000000000000000000000"
    value = execute(1.5, 2.5)
    assert value == "0 10000001 00000000000000000000000"
    value = execute(-1.5, 2.5)
    assert value == "0 01111111 00000000000000000000000"
    value = execute(1.5, -2.5)
    assert value == "1 01111111 00000000000000000000000"
    value = execute(-1.5, -2.5)
    assert value == "1 10000001 00000000000000000000000"
    value = execute(0.0, -0.0)
    assert value == "0 00000000 00000000000000000000000"
    value = execute(-0.0, 0.0)
    assert value == "0 00000000 00000000000000000000000"


def testDiv():
    linking, store = initWasm()
    div = linking.exports(store)["div"]

    def execute(a, b):
        result = div(store, a, b)
        print(f"----------div {a} / {b} = {result}")
        return floatToStr(result)

    value = execute(1.0, 4.0)
    assert value == "0 01111101 00000000000000000000000"
    value = execute(0.1, 0.5)  # 0.20000000298023224
    assert value == "0 01111100 10011001100110011001101"
    value = execute(0.1, 0.3)  # 0.3333333134651184
    assert value == "0 01111101 01010101010101010101010"
    value = execute(float("nan"), 4.0)
    assert value == "0 11111111 10000000000000000000000"
    value = execute(1.0, float("nan"))
    assert value == "0 11111111 10000000000000000000000"
    value = execute(0.0, 4.0)
    assert value == "0 00000000 00000000000000000000000"
    value = execute(-0.0, 4.0)
    assert value == "1 00000000 00000000000000000000000"
    value = execute(4.0, 0.0)
    assert value == "0 11111111 00000000000000000000000"
    value = execute(4.0, -0.0)
    assert value == "1 11111111 00000000000000000000000"
    value = execute(0.0, 0.0)
    # assert value == "1 11111111 10000000000000000000000" if arch == "x86" else "0 11111111 10000000000000000000000"
    assert value == "0 11111111 10000000000000000000000"
    value = execute(0.0, -0.0)
    # assert value == "1 11111111 10000000000000000000000" if arch == "x86" else "0 11111111 10000000000000000000000"
    assert value == "0 11111111 10000000000000000000000"
    value = execute(-0.0, 0.0)
    # assert value == "1 11111111 10000000000000000000000" if arch == "x86" else "0 11111111 10000000000000000000000"
    assert value == "0 11111111 10000000000000000000000"
    value = execute(-0.0, -0.0)
    # assert value == "1 11111111 10000000000000000000000" if arch == "x86" else "0 11111111 10000000000000000000000"
    assert value == "0 11111111 10000000000000000000000"


def testNan():
    linking, store = initWasm()
    testNaN = linking.exports(store)["isNaN"]
    div = linking.exports(store)["div"]

    def execute(value):
        result = testNaN(store, value)
        print(f"----------isNaN({value}) = {result}")
        return result == 1

    assert execute(0.0) == False
    assert execute(-0.0) == False
    assert execute(3.14) == False
    assert execute(float("nan")) == True
    assert execute(float("-nan")) == True
    assert execute(div(store, 4.0, -0.0)) == False
    assert execute(div(store, 0.0, 0.0)) == True
    assert execute(div(store, 3.14, 0.0)) == False
    assert execute(div(store, 3.14, 3.14)) == False
    assert execute(div(store, float("nan"), 3.14)) == True
    assert execute(div(store, 3.14, float("nan"))) == True
    assert execute(div(store, float("nan"), float("nan"))) == True


def testSqrt():
    linking, store = initWasm()
    sqrt = linking.exports(store)["sqrt"]

    def execute(value):
        result = sqrt(store, value)
        print(f"----------sqrt({value}) = {result}")
        return floatToStr(result)

    value = execute(4.0)
    assert value == "0 10000000 00000000000000000000000"
    value = execute(3.14)
    assert value == "0 01111111 11000101101000100001011"
    value = execute(0.0)
    assert value == "0 00000000 00000000000000000000000"
    value = execute(-0.0)
    assert value == "1 00000000 00000000000000000000000"
    value = execute(float("nan"))
    assert value == "0 11111111 10000000000000000000000"
    value = execute(strToFloat("1 11111111 10001100000000010000000"))
    # assert value == "1 11111111 10001100000000010000000"
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("inf"))
    assert value == "0 11111111 00000000000000000000000"
    value = execute(float("-inf"))
    # assert value == "1 11111111 10000000000000000000000" if arch == "x86" else "0 11111111 10000000000000000000000"
    assert value == "0 11111111 10000000000000000000000"


def testAbs():
    linking, store = initWasm()
    absF = linking.exports(store)["abs_f"]

    def execute(value):
        result = absF(store, value)
        print(f"----------abs({value}) = {result}")
        return floatToStr(result)

    value = execute(4.0)
    assert value == "0 10000001 00000000000000000000000"
    value = execute(-4.0)
    assert value == "0 10000001 00000000000000000000000"
    value = execute(3.14)
    assert value == "0 10000000 10010001111010111000011"
    value = execute(-3.14)
    assert value == "0 10000000 10010001111010111000011"
    value = execute(0.0)
    assert value == "0 00000000 00000000000000000000000"
    value = execute(-0.0)
    assert value == "0 00000000 00000000000000000000000"
    value = execute(float("-nan"))
    assert value == "0 11111111 10000000000000000000000"
    value = execute(strToFloat("1 11111111 10001100000000010000000"))
    assert value == "0 11111111 10001100000000010000000"
    value = execute(float("inf"))
    assert value == "0 11111111 00000000000000000000000"
    value = execute(float("-inf"))
    assert value == "0 11111111 00000000000000000000000"


def testCeil():
    linking, store = initWasm()
    ceil = linking.exports(store)["ceil"]

    def execute(value):
        result = ceil(store, value)
        print(f"----------ceil({value}) = {result}")
        return floatToStr(result)

    value = execute(4.0)
    assert value == "0 10000001 00000000000000000000000"
    value = execute(3.14)
    assert value == "0 10000001 00000000000000000000000"
    value = execute(0.0)
    assert value == "0 00000000 00000000000000000000000"
    value = execute(-0.0)
    assert value == "1 00000000 00000000000000000000000"
    value = execute(-3.14)
    assert value == "1 10000000 10000000000000000000000"
    value = execute(float("nan"))
    assert value == "0 11111111 10000000000000000000000"
    value = execute(strToFloat("1 11111111 10001100000000010000000"))
    # assert value == "1 11111111 10001100000000010000000"
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("inf"))
    assert value == "0 11111111 00000000000000000000000"
    value = execute(float("-inf"))
    assert value == "1 11111111 00000000000000000000000"


def testFloor():
    linking, store = initWasm()
    floor = linking.exports(store)["floor"]

    def execute(value):
        result = floor(store, value)
        print(f"----------floor({value}) = {result}")
        return floatToStr(result)

    value = execute(4.0)
    assert value == "0 10000001 00000000000000000000000"
    value = execute(3.14)
    assert value == "0 10000000 10000000000000000000000"
    value = execute(0.0)
    assert value == "0 00000000 00000000000000000000000"
    value = execute(-0.0)
    assert value == "1 00000000 00000000000000000000000"
    value = execute(-3.14)
    assert value == "1 10000001 00000000000000000000000"
    value = execute(float("nan"))
    assert value == "0 11111111 10000000000000000000000"
    value = execute(strToFloat("1 11111111 10001100000000010000000"))
    # assert value == "1 11111111 10001100000000010000000"
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("inf"))
    assert value == "0 11111111 00000000000000000000000"
    value = execute(float("-inf"))
    assert value == "1 11111111 00000000000000000000000"


def testTrunc():
    linking, store = initWasm()
    trunc = linking.exports(store)["trunc"]

    def execute(value):
        result = trunc(store, value)
        print(f"----------trunc({value}) = {result}")
        return floatToStr(result)

    value = execute(4.0)
    assert value == "0 10000001 00000000000000000000000"
    value = execute(3.14)
    assert value == "0 10000000 10000000000000000000000"
    value = execute(0.0)
    assert value == "0 00000000 00000000000000000000000"
    value = execute(-0.0)
    assert value == "1 00000000 00000000000000000000000"
    value = execute(-3.14)
    assert value == "1 10000000 10000000000000000000000"
    value = execute(float("nan"))
    assert value == "0 11111111 10000000000000000000000"
    value = execute(strToFloat("1 11111111 10001100000000010000000"))
    # assert value == "1 11111111 10001100000000010000000"
    assert value == "0 11111111 10000000000000000000000"
    value = execute(float("inf"))
    assert value == "0 11111111 00000000000000000000000"
    value = execute(float("-inf"))
    assert value == "1 11111111 00000000000000000000000"
