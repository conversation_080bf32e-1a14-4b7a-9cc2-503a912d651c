import pytest
from wasmtime import Store, Module, Instance, Func, FuncType, Engine, Linker, WasiConfig


def test_prime():
    wasi = WasiConfig()
    wasi.preopen_dir('.', '.')
    wasi.inherit_stdout()
    # wasi.argv = ['arga', 'bb']
    wasi.inherit_argv()
    wasi.inherit_env()

    engine = Engine()
    module = Module.from_file(engine, "wasm/test_prime.wasm")
    linker = Linker(engine)
    linker.define_wasi()

    store = Store(engine)
    store.set_wasi(wasi)

    instance = linker.instantiate(store, module)

    print("--------- wasi test ---------")
    allocate = instance.exports(store)["allocate"]
    deallocate = instance.exports(store)["deallocate"]
    memory = instance.exports(store)["memory"]
    is_prime = instance.exports(store)["is_prime"]

    num = "30432527221704537086371993251530170531786747066637051"
    # num = "1000000007"
    # num = "10"
    # num = "233"
    rand_times = 100

    numPtr = allocate(store, len(num))
    writeString(memory, store, num, numPtr)

    print("wasi num:", getString(memory, store, numPtr))

    print("\t--------- wasi stdout is_prime begin ---------")
    wasiResult = is_prime(store, numPtr, rand_times) == 1
    print("\t--------- wasi stdout is_prime end ---------")

    print("wasi is_prime Result:", wasiResult)


def writeString(mem, store, input_str, ptr):
    # write into memory
    mem.write(store, bytearray(input_str.encode()), ptr)
    # write 0 as NULL to terminate C-string
    mem.data_ptr(store)[ptr + len(input_str)] = 0


def getString(mem, store, ptr):
    # loop to read output string
    output_str = ''
    i = 0
    while True:
        # C-string terminates by NULL.
        if mem.data_ptr(store)[ptr + i] == 0:
            break
        char = chr(mem.data_ptr(store)[ptr + i])
        output_str += char
        i += 1
    return output_str
