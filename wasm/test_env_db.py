import pytest
from wasmtime import Store, Module, Instance, Func, FuncType, Engine, Linker, WasiConfig, ValType, Config

import sqlite3


def test_env_db():
    wasi = WasiConfig()
    wasi.preopen_dir('.', '.')
    wasi.inherit_stdout()
    wasi.inherit_env()
    tokenName = 'TEST_V_GRAPH'
    wasi.env = [['DB_NAME', 'env.db']]
    

    engine = Engine()
    module = Module.from_file(engine, "wasm/test_env_db.wasm")
    linker = Linker(engine)
    linker.define_wasi()

    # Create a `Store` to hold instances, and configure wasi state
    store = Store(engine)
    store.set_wasi(wasi)

    instance = linker.instantiate(store, module)
    createConnection = instance.exports(store)["create_db_connection"]
    getEnv = instance.exports(store)["get_env_from_db"]
    testEnv = instance.exports(store)["test_env"]

    conn = createConnection(store)

    print('------------------set env 1-------------------')
    pySetDbEnv('TOKEN_ID', tokenName)
    getEnv(store, conn)
    print('------------------set env 2-------------------')
    pySetDbEnv('TOKEN_ID_2', 'TEST_TOKEN_2')
    getEnv(store, conn)
    print('------------------test env-------------------')
    testEnv(store)


def pySetDbEnv(key, value):
    print(f'key {key}, value {value}')
    conn = sqlite3.connect('env.db')
    table = 'env'
    cursor = conn.cursor()
    cursor.execute('''CREATE TABLE IF NOT EXISTS env (
                    id INTEGER PRIMARY KEY,
                    key TEXT NOT NULL UNIQUE,
                    value TEXT NOT NULL
        )''')
    cursor.execute("SELECT value FROM env WHERE key=?", (key,))
    data = cursor.fetchone()
    print(f'data {data}')
    if data is not None:
        cursor.execute("UPDATE env SET value=? WHERE key=?", (value, key))
    else:
        cursor.execute("INSERT INTO env (key, value) VALUES (?, ?)", (key, value))
    # cursor.execute(f"INSERT OR REPLACE INTO {table} (key, value) VALUES (?, ?)", (key, value))
    conn.commit()
    cursor.close()
    conn.close()
