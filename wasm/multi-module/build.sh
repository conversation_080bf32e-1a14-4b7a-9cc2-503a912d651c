# set WASI_SDK_PATH to the correct location in your system

WASI_SDK_PATH=

export WASI_SYSROOT="${WASI_SDK_PATH}/share/wasi-sysroot"
export CC="${WASI_SDK_PATH}/bin/clang --sysroot=${WASI_SYSROOT}"
export AR="${WASI_SDK_PATH}/bin/llvm-ar"
export CC_wasm32_wasi="${CC}"
export CARGO_TARGET_WASM32_WASI_LINKER="${WASI_SDK_PATH}/bin/clang"

export LIBSQLITE3_FLAGS="\
    -USQLITE_TEMP_STORE \
    -DSQLITE_TEMP_STORE=3 \
    -USQLITE_THREADSAFE \
    -DSQLITE_THREADSAFE=0 \
    -DSQLITE_OMIT_LOCALTIME \
    -DSQLITE_OMIT_LOAD_EXTENSION \
    -DLONGDOUBLE_TYPE=double"

# for rust lib
export RUSTFLAGS='-C link-arg=-Wl,--export-dynamic'


rustup target add wasm32-wasip1

cargo build --target "wasm32-wasip1"

cp ./target/wasm32-wasip1/debug/multi_module.wasm ..
