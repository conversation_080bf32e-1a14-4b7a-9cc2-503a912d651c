import copy
import os
from dataclasses import dataclass
from typing import Any, Type, Optional, TypeVar, Tuple, Union
from typing import List, Dict

from kivy import Logger
from serde import serde
from serde.json import to_json, from_json
from wasmtime import Instance, Store, WasiConfig, Engine, Module, Linker

# The environment variables that are preserved and passed to the WASM module.
preserveEnv = {
    'conn_str': 'vgraph.db'
}

T = TypeVar('T')


@serde
@dataclass
class SerializableResult:
    value: Optional[str] = None
    err: Optional[str] = None


class WasmClient:
    def __init__(self, store, instance):
        self.store = store
        self.instance = instance

    def invoke(self, funcName: str, returnType: Optional[Type] = None, *args) -> Any:
        return invoke(self.store, self.instance, funcName, returnType, *args)


def invoke(store: Store, instance: Instance, funcName: str, returnType: Optional[Type] = None, *args) \
        -> Union[Tuple[Optional[Any], Optional[str]], Optional[str]]:
    """
    Invoke a specified function within a WebAssembly module,
    handling the serialization and deserialization of arguments and the return value.

    This function serializes arguments into a JSON string format, passes them to the specified WASM function,
    and then deserializes the response back into a common json c-char pointer.

    :param store: The WebAssembly Store object.
    :param instance: The WebAssembly Instance object containing exported functions.
    :param funcName: The name of the function to be invoked in the WASM module.
    :param returnType: The expected return type of the WASM function. Used for deserialization.
    :param args: The arguments to be passed to the function.
    :return: The result from the WASM function, deserialized into a Python object.
            The type of this object is determined by 'returnType', if specified, or inferred from the deserialized data.
    """
    func = instance.exports(store)[funcName]

    # helper functions
    allocateCChar = instance.exports(store)["allocate_c_char"]
    deallocateCChar = instance.exports(store)["deallocate_c_char"]
    memory = instance.exports(store)["memory"]

    # deserialize args tuple to json string
    argsJson = to_json(args)
    # allocate memory for json string
    argsJsonPtr = allocateCChar(store, len(argsJson) + 1)
    # write json string into memory
    writeStr(memory, store, argsJson, argsJsonPtr)

    # invoke function with args json string
    resultPtr = func(store, argsJsonPtr)

    # no need to deallocate memory for arg json string
    # because it auto managed by the rust code

    # read result json string from memory
    resultJson = getStr(memory, store, resultPtr)
    # deallocate memory for resultPtr
    deallocateCChar(store, resultPtr)

    serializableResult: SerializableResult = from_json(SerializableResult, resultJson)
    if returnType is None:
        return serializableResult.err

    value = None
    if serializableResult.value is not None:
        value = from_json(returnType, serializableResult.value)

    return value, serializableResult.err


def writeStr(mem, store, inputStr, ptr):
    # write into memory
    mem.write(store, bytearray(inputStr.encode()), ptr)
    # write 0 as NULL to terminate C-string
    mem.data_ptr(store)[ptr + len(inputStr)] = 0


def getStr(mem, store, ptr):
    output_chars = []
    i = 0
    while True:
        char_val = mem.data_ptr(store)[ptr + i]
        if char_val == 0:  # C-string terminates by NULL.
            break
        output_chars.append(chr(char_val))
        i += 1
    return ''.join(output_chars)


def executeContractFunc(envs: Optional[Dict[str, str]], contractName, funcName, returnType, *args) \
        -> Tuple[Any, Optional[str]]:
    """
    Execute a specified function within a WebAssembly module (with once instance and store),
    handling the serialization and deserialization of arguments and the return value.

    If you want to execute the function in block level,
    you should pass the block envs and transaction envs by using mergeEnv.
    """
    wasi = WasiConfig()
    wasi.preopen_dir('.', '.')
    wasi.inherit_stdout()
    wasi.inherit_env()
    finalEnv: Dict = copy.deepcopy(preserveEnv)

    # env filter: only preserve the envs that are not in the preserveEnv
    if envs is not None:
        for k, v in envs.items():
            if k not in finalEnv:
                finalEnv[k] = v

    wasi.env = convertToIterable(finalEnv)
    engine = Engine()
    wasmFilePath = f"contract/base/{contractName}.wasm"
    if not os.path.exists(wasmFilePath):
        Logger.error(f"Contract Executor: Contract file {wasmFilePath} not found.")
        return None, f"Contract file {wasmFilePath} not found."
    try:
        module = Module.from_file(engine, wasmFilePath)
        linker = Linker(engine)
        linker.define_wasi()

        store = Store(engine)
        store.set_wasi(wasi)
        instance = linker.instantiate(store, module)

        wasmClient = WasmClient(store, instance)

        result, err = wasmClient.invoke(funcName, returnType, *args)
        if err is not None:
            Logger.error(f"Contract Executor: Contract error: {err}")
            return None, f"{err}"
        return result, None
    except Exception as e:
        Logger.error(f"Contract Executor: Internal error: {e}")
        return None, f"{e}"


def mergeEnv(blockEnv: Optional[Dict[str, str]], transactionEnv: Optional[Dict[str, str]]) -> Dict:
    """
    Merge the environment variables from the block and the transaction.
    """
    finalEnv: Dict = {}

    # env filter: only preserve the envs that are not in the finalEnv

    if blockEnv is not None:
        for k, v in blockEnv.items():
            if k not in finalEnv:
                finalEnv[k] = v

    if transactionEnv is not None:
        for k, v in transactionEnv.items():
            if k not in finalEnv:
                finalEnv[k] = v

    return finalEnv


def convertToIterable(data: Dict) -> List[List]:
    return [[k, v] for k, v in data.items()]
