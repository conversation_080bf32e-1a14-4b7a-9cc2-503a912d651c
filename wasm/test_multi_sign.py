# import os
#
# if os.path.basename(os.getcwd()) == "wasm":
#     os.chdir("..")

import hashlib

import nacl.signing
import nacl.encoding
import nacl.exceptions
import pytest

from wasmtime import Store, Module, Instance, Func, FuncType, Engine, Linker, WasiConfig
import base58

str2bytes = lambda s: s.encode('latin-1')
bytes2str = lambda b: ''.join(map(chr, b))
str2list = lambda s: [c for c in s]
list2bytes = lambda s: b''.join(s)


def gen_sign(seed: bytes, msg: str) -> (bytes, str):
    # Generate a signature key using the same seed
    signing_key = nacl.signing.SigningKey(seed, encoder=nacl.encoding.RawEncoder)
    signed = signing_key.sign(str2bytes(msg), encoder=nacl.encoding.RawEncoder)
    signed_b58 = base58.b58encode(signed.signature)
    verify_key = signing_key.verify_key
    verify_key_b58 = base58.b58encode(verify_key.encode())

    return bytes2str(signed_b58), bytes2str(verify_key_b58)


# def verify_sign(publicKey: str, msg: bytes, signature: bytes) -> bool:
#     try:
#         verify_key = nacl.signing.VerifyKey(base58.b58decode(publicKey), encoder=nacl.encoding.RawEncoder)
#         original_message = verify_key.verify(msg, base58.b58decode(signature), encoder=nacl.encoding.RawEncoder)
#         print("The message is authentic:", original_message)
#         return True
#     except nacl.exceptions.BadSignatureError:
#         print("The signature was forged or tampered with!")
#         return False


def test_multi_sign():
    print("--------- python gen pubKey & signed string ---------")
    message = 'testmessage'
    message_hash = base58.b58encode(hashlib.sha256(message.encode()).digest()).decode()
    seed1 = b'v+\x08G$\xae\xa8\x11\xd4\x15\xc5\x96c\xc1\x18\xd3\xa8\x00\x00\xd4;\r\xea\x6d_\x93\x16~\xd5@\x17\xb9'
    seed2 = b'\x9e\xbe)\x82(\xc4\xab\x14\x92\xc4\xae\x92MGr\xd8\xe1\x8a\xad\xf2\x18\x96\x336\x1emU\x12fZ\xa8\x91'
    seed3 = b'y\x92\xcc\x01\x02\xf2g\xf5\xa3\x16J;\xd1\xb3\x80\xbe\xac?\xad\x12G\xc2\x16\xd6m\x8c\x1e?/\xd8\xda\x03'

    signature1, publicKey1 = gen_sign(seed1, message_hash)
    signature2, publicKey2 = gen_sign(seed2, message_hash)
    signature3, publicKey3 = gen_sign(seed3, message_hash)

    # correct signature1 is 59WMumti59dYY5tjRhi4bjGNM6ABWiKLpGEPb9kmdzV8mGz82QnmT7Zp5xGjEBQ9dENyCQqTsWgZ5wB8TDhNhFLk
    # when one sign is not verified, two sign verify, the mult-verify result is true
    signature1 = "59WMumti59dYY5tjRhi4bjGNM6ABWiKLpGEPb9kmdzV8mGz82QnmT7Zp5xGjEBQ9dENyCQqTsWgZ5wB8TDhNhFLj"

    print('publicKey1:', publicKey1)
    print('publicKey2:', publicKey2)
    print('publicKey3:', publicKey3)
    print('signature1:', signature1)
    print('signature2:', signature2)
    print('signature3:', signature3)
    print('message_hash:', message_hash)

    # --------- WASI CONFIG ---------
    wasi = WasiConfig()
    wasi.preopen_dir('.', '.')
    wasi.inherit_stdout()
    # wasi.argv = ['arga', 'bb']
    wasi.inherit_argv()
    wasi.inherit_env()
    wasi.env = [['signature1', signature1], ['pub_key1', publicKey1],
                ['signature2', signature2], ['pub_key2', publicKey2],
                ['signature3', signature3], ['pub_key3', publicKey3],
                ['message', message_hash]]

    engine = Engine()
    module = Module.from_file(engine, "wasm/multi_sign.wasm")
    linker = Linker(engine)
    linker.define_wasi()

    store = Store(engine)
    store.set_wasi(wasi)

    instance = linker.instantiate(store, module)

    # print("--------- python test ---------")

    # custom ed25519 verify
    # verifyResult = verify_sign(publicKey.encode(), message.encode(), signatureBytes)

    # print('python VerifyResult', verifyResult)

    print("--------- wasi test ---------")
    allocate = instance.exports(store)["allocate"]
    deallocate = instance.exports(store)["deallocate"]
    memory = instance.exports(store)["memory"]
    verify = instance.exports(store)["verify"]

    # publicKeyPtr1 = allocate(store, len(publicKey1))
    # writeString(memory, store, publicKey1, publicKeyPtr1)
    # messagePtr1 = allocate(store, len(message))
    # writeString(memory, store, message, messagePtr1)
    # signedStringPtr1 = allocate(store, len(signature1))
    # writeString(memory, store, signature1, signedStringPtr1)

    print("\t--------- wasi stdout verify begin ---------")
    wasiVerifyResult = verify(store) == 1
    print("\t--------- wasi stdout rust verify end ---------")

    print("wasi VerifyResult:", wasiVerifyResult)


def writeString(mem, store, input_str, ptr):
    # write into memory
    mem.write(store, bytearray(input_str.encode()), ptr)
    # write 0 as NULL to terminate C-string
    mem.data_ptr(store)[ptr + len(input_str)] = 0


def getString(mem, store, ptr):
    # loop to read output string
    output_str = ''
    i = 0
    while True:
        # C-string terminates by NULL.
        if mem.data_ptr(store)[ptr + i] == 0:
            break
        char = chr(mem.data_ptr(store)[ptr + i])
        output_str += char
        i += 1
    return output_str
