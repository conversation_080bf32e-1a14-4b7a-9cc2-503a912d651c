use std::ffi::{c_char, c_void};
use std::mem;

use num_bigint::{BigInt};
use num_traits::{Num, One, ToPrimitive, Zero};


const DIFFICULTY_UNITY: u64 = 1 << 24;

pub extern fn get_difficulty_unity() -> u64 {
    DIFFICULTY_UNITY
}

// Fermat probable primality test (2-PRP): 2 ** (n-1) = 1 (mod n)
#[no_mangle]
pub extern fn fermat_probable_primality_test(n: &BigInt) -> u64 {
    // Base 2
    let r = BigInt::from(2).modpow(&(n - BigInt::one()), n);

    if r == BigInt::one() || *n == BigInt::zero() {
        DIFFICULTY_UNITY.to_u64().expect("Failed to convert to u64")
    } else {
        // Calculate fractional difficulty
        let result = ((n - &r) * &DIFFICULTY_UNITY) / n;
        result.to_u64().expect("Failed to convert to u64")
    }
}

// Twin prime chain scale
#[no_mangle]
pub extern fn twin_prime_chain_scale(origin: &BigInt) -> u64 {
    let mut delta = BigInt::from(-1i32);
    let mut scale = 0u64;
    let mut current_origin = origin.clone();

    loop {
        let p = &current_origin + &delta;
        let difficulty = fermat_probable_primality_test(&p);
        // println!("Difficulty: {}", difficulty);
        scale += difficulty;

        if difficulty < DIFFICULTY_UNITY {
            break; // End of twin prime chain
        }
        if delta > Zero::zero() {
            current_origin <<= 1;
        }
        delta = delta * -1;
    }

    scale
}



#[no_mangle]
fn verify_submitted_work(origin: &BigInt, difficulty: u64) -> bool {
    // Calculate the scale of the twin prime chain
    let scale = twin_prime_chain_scale(&origin);

    // Compare the scale with the difficulty
    // divide by 2^24
    // let real_difficulty = scale as f64 / (1 << 24) as f64;
    // println!("Submitted origin difficulty: {}", real_difficulty);

    scale >= difficulty
}



#[no_mangle]
// Interface for wasm to call verify_submitted_work function with number_ptr and difficulty
pub extern fn verify_work(number_ptr: *mut c_char, difficulty: u64) -> bool {
    // convert the number_ptr to a string and then to a BigInt
    let c_str = unsafe {
        assert!(!number_ptr.is_null());
        std::ffi::CStr::from_ptr(number_ptr)
    };

    let number = c_str.to_str().unwrap();
    let input = BigInt::from_str_radix(number, 10).unwrap();

    verify_submitted_work(&input, difficulty)
}




#[no_mangle]
pub extern fn allocate(size: usize) -> *mut c_void {
    let mut buffer = Vec::with_capacity(size);
    let pointer = buffer.as_mut_ptr();
    mem::forget(buffer);

    pointer as *mut c_void
}

#[no_mangle]
pub extern fn deallocate(pointer: *mut c_void, capacity: usize) {
    unsafe {
        let _ = Vec::from_raw_parts(pointer, 0, capacity);
    }
}
