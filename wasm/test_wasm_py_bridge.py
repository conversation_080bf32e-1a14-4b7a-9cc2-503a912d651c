# Import necessary modules for testing, data handling, WebAssembly operations, and timing.
import os
import unittest
from dataclasses import dataclass
from timeit import default_timer as timer
from typing import List, Dict

from serde import serde  # is pyserde not serde
from wasmtime import Store, Module, Engine, Linker, WasiConfig

from wasm import wasm_py_bridge


# Define a test class for testing WebAssembly bindings using Python's unittest framework.
class TestWasmBind(unittest.TestCase):
    engine = None
    linker = None

    @classmethod
    def setUpClass(cls):
        # Setup for the test class, initializing the WebAssembly environment.
        cls.engine = Engine()
        path = "wasm/test_wasm_py_bridge.wasm"
        if os.getcwd().split("/")[-1] == "wasm":
            path = path.lstrip("wasm/")
        cls.module = Module.from_file(cls.engine, path)
        cls.linker = Linker(cls.engine)
        cls.linker.define_wasi()

    def setUp(self):
        # Setup for each test case, configuring the WebAssembly instance.
        wasi = WasiConfig()
        wasi.preopen_dir('.', '.')
        wasi.inherit_stdout()
        wasi.inherit_env()
        self.store = Store(self.engine)
        self.store.set_wasi(wasi)
        self.instance = self.linker.instantiate(self.store, self.module)
        self.wasm_client = wasm_py_bridge.WasmClient(self.store, self.instance)

    # Test methods for various functionalities in the WebAssembly module.
    def test_print_hello_world(self):
        # Testing the 'print_hello_world' function.
        err = self.wasm_client.invoke("print_hello_world")
        self.assertIsNone(err)
        print("print_hello_world test passed")

    def test_get_string(self):
        # Testing the 'get_string' function and verifying its return value.
        result, err = self.wasm_client.invoke("get_string", str)
        self.assertIsNone(err)
        self.assertEqual(result, "get string")
        print("get_string test passed with result:", result)

    def test_compute_f64(self):
        # Testing a function that performs a floating-point computation.
        result, err = self.wasm_client.invoke("compute_f64", float, 1.1, 2.3)
        self.assertIsNone(err)
        self.assertEqual(result, 3.4)
        print("compute_f64 test passed with result:", result)

    def test_conditional_response_true(self):
        # Testing a function that returns different strings based on a boolean flag.
        result, err = self.wasm_client.invoke("conditional_response", str, 12, True)
        self.assertIsNone(err)
        self.assertEqual(result, "Student, Age: 12")
        print("conditional_response test passed with result:", result)

    def test_conditional_response_false(self):
        result, err = self.wasm_client.invoke("conditional_response", str, 32, False)
        self.assertIsNone(err)
        self.assertEqual(result, "Non-student")
        print("conditional_response test passed with result:", result)

    def test_update_user_info(self):
        # Testing a function that updates user information, using data classes for structured data.
        @serde
        @dataclass
        class UserInfo:
            username: str
            scores: List[int]
            metadata: Dict[str, str]

        user_info = UserInfo(username="Alice", scores=[95, 85, 75],
                             metadata={"role": "admin", "email": "<EMAIL>"})
        result, err = self.wasm_client.invoke("update_user_info", UserInfo, user_info)
        after_update = UserInfo(username="ALICE", scores=[105, 95, 85],
                                metadata={"role": "admin!", "email": "<EMAIL>!"})
        self.assertIsNone(err)
        self.assertEqual(result, after_update)
        print("update_user_info test passed with result:", result.__dict__)

    def test_format_latest_education(self):
        # Testing a function that formats the latest education details from structured data.
        @serde
        @dataclass
        class EducationDetail:
            year: int
            institution: str

        @serde
        @dataclass
        class PersonalInfo:
            id: int
            name: str

        @serde
        @dataclass
        class EducationRecord:
            personal_info: PersonalInfo
            education_history: List[EducationDetail]

        personal_info = PersonalInfo(id=123, name="Alice")
        education_history = [EducationDetail(year=2018, institution="University A"),
                             EducationDetail(year=2020, institution="University B")]
        record = EducationRecord(personal_info=personal_info, education_history=education_history)
        result, err = self.wasm_client.invoke("format_latest_education", str, record)
        self.assertIsNone(err)
        self.assertEqual(result, "Alice attended University B in 2020")
        print("format_latest_education test passed with result:", result)

    def test_process_structs(self):
        # Testing a function that processes custom data structures.
        @serde
        @dataclass
        class StructA:
            field_a: str

        @serde
        @dataclass
        class StructB:
            field_b: int

        result, err = self.wasm_client.invoke("process_structs", str,
                                              StructA(field_a="test_string"), StructB(field_b=2))
        self.assertIsNone(err)
        self.assertEqual(result,
                         r"""StructA: StructA { field_a: "test_string" }, StructB: StructB { field_b: 2 }""")
        print("process_structs test passed with result:", result)

    def test_process_map_list(self):
        # Testing functions that handle complex data types like maps and lists.
        @serde
        @dataclass
        class CustomType:
            value: int

        map_data = {"key1": CustomType(1), "key2": CustomType(2)}
        list_data = [CustomType(3), CustomType(4)]

        map_result, map_err = self.wasm_client.invoke("process_map", Dict[str, CustomType], map_data)
        list_result, list_err = self.wasm_client.invoke("process_list", List[CustomType], list_data)
        self.assertIsNone(map_err)
        self.assertEqual(map_result, {'key1': CustomType(2), 'key2': CustomType(3)})
        self.assertIsNone(list_err)
        self.assertEqual(list_result, [CustomType(4), (CustomType(5))])
        print("Map and list processing tests passed")

    def test_performance_large_data(self):
        # Testing the performance of a function when handling large data sets.
        large_scores = list(range(100000))

        @serde
        @dataclass
        class UserInfo:
            username: str
            scores: List[int]
            metadata: Dict[str, str]

        user_info = UserInfo(username="Alice", scores=large_scores, metadata={"test": "data"})
        start_time = timer()
        self.wasm_client.invoke("update_user_info", UserInfo, user_info)
        end_time = timer()
        print(f"Performance test for large data: {end_time - start_time}")

    def test_result_ok(self):
        result, err = self.wasm_client.invoke("return_result_ok_full", str)
        self.assertIsNone(err)
        self.assertIsNotNone(result)
        self.assertEqual(result, "return result")
        result1, err1 = self.wasm_client.invoke("return_result_ok_short", str)
        self.assertIsNone(err1)
        self.assertIsNotNone(result1)
        self.assertEqual(result1, "return result")

    def test_result_err(self):
        result, err = self.wasm_client.invoke("return_result_err_full", str)
        self.assertIsNone(result)
        self.assertIsNotNone(err)
        self.assertEqual(err, "return error")
        result1, err1 = self.wasm_client.invoke("return_result_err_short", str)
        self.assertIsNone(result1)
        self.assertIsNotNone(err1)
        self.assertEqual(err1, "return error")

    def test_return_empty_ok(self):
        err = self.wasm_client.invoke("return_empty_ok", None)
        self.assertIsNone(err)

    def test_return_empty_err(self):
        err = self.wasm_client.invoke("return_empty_err", None)
        self.assertIsNotNone(err)
        self.assertEqual(err, "return error")

    def test_result_complicate_nested(self):
        @serde
        @dataclass
        class Book:
            title: str
            publication_year: int
            author: str

        @serde
        @dataclass
        class Review:
            reviewer: str
            score: int  # Assuming score is an integer out of 10
            comment: str

        @serde
        @dataclass
        class LibraryCatalog:
            books: List[Book]
            book_reviews: Dict[str, List[Review]]
            author_books: Dict[str, List[Book]]

        book1 = Book(title="Rust Programming", publication_year=2021, author="John Doe")
        book2 = Book(title="Advanced Rust", publication_year=2022, author="Jane Smith")

        review1 = Review(reviewer="Alice Johnson", score=8, comment="Great introduction to Rust.")
        review2 = Review(reviewer="Bob Brown", score=9, comment="Excellent coverage of advanced topics.")

        library_catalog = LibraryCatalog(
            books=[book1, book2],
            book_reviews={
                book1.title: [review1],
                book2.title: [review2],
            },
            author_books={
                book1.author: [book1],
                book2.author: [book2],
            }
        )

        result, err = self.wasm_client.invoke("return_result_ok_complicate_nested", LibraryCatalog)
        self.assertIsNone(err)
        self.assertIsNotNone(result)
        self.assertEqual(result, library_catalog)


if __name__ == '__main__':
    unittest.main()  # Executing the tests if the script is run directly.
