
## Usage:

1. [Install the Rust toolchain here](https://www.rust-lang.org/tools/install)

2. download wasi-sdk

   https://github.com/WebAssembly/wasi-sdk/releases

3. unzip the wasi-sdk and use the path to set `WASI_SDK_PATH` in `build.sh`

   For example:

   ```shell
   WASI_SDK_PATH=/Users/<USER>/develop/SDK/wasm/wasi-sdk-20.0
   ```

4. give permission to `build.sh` and run it to build

   ```shell
   chmod u+x build.sh
   
   ./build.sh
   ```

5. The wasm file will be copy to the `wasm` folder. Then you can run it with your webassembly runtime.

   For example, [wasmtime](https://wasmtime.dev/)

   ```shell
   wasmtime run rusqlite.wasm
   ```
