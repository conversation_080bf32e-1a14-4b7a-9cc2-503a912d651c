#![allow(dead_code)]

use std::{mem, ptr};
use std::ffi::{c_char, CStr, CString};
use std::os::raw::c_void;

use anyhow::Result;
use serde::{Deserialize, Serialize};

mod test;

#[no_mangle]
pub extern "C" fn allocate(size: usize) -> *mut c_void {
    if size == 0 {
        return ptr::null_mut();
    }

    let mut buffer = Vec::with_capacity(size);
    let pointer = buffer.as_mut_ptr();
    mem::forget(buffer);

    pointer as *mut c_void
}

#[no_mangle]
pub extern "C" fn deallocate(pointer: *mut c_void, capacity: usize) {
    if pointer.is_null() || capacity == 0 {
        return;
    }

    unsafe {
        let ptr = pointer as *mut u8;
        let _buffer = Vec::from_raw_parts(ptr, 0, capacity);
    }
}

#[no_mangle]
pub extern "C" fn allocate_c_char(len: usize) -> *mut c_char {
    let buffer = vec![0; len];
    let raw_ptr = buffer.as_ptr() as *mut c_char;
    std::mem::forget(buffer);
    raw_ptr
}

#[no_mangle]
pub extern "C" fn deallocate_c_char(ptr: *mut c_char) {
    unsafe {
        let _ = CString::from_raw(ptr);
    }
}

pub fn get_string(ptr: *mut c_char) -> Result<String> {
    let c_str = unsafe { CStr::from_ptr(ptr) };
    let str_slice = c_str.to_str()?;
    Ok(str_slice.to_string())
}

pub fn write_string(string: &str) -> *mut c_char {
    CString::new(string)
        .map(|c_string| c_string.into_raw())
        .unwrap_or_else(|_| ptr::null_mut())
}

pub fn object_to_json_ptr<T: Serialize>(object: T) -> Result<*mut c_char> {
    let path_json = serde_json::to_string(&object)?;
    Ok(write_string(&path_json))
}

pub fn json_ptr_to_object<T: serde::de::DeserializeOwned>(ptr: *mut c_char) -> Result<T> {
    let c_string = unsafe { CString::from_raw(ptr) };
    let path_json = c_string.to_str()?;
    let object = serde_json::from_str(path_json)?;
    Ok(object)
}

#[derive(Serialize, Deserialize, Debug)]
pub struct SerializableResult {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub value: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub err: Option<String>,
}

impl SerializableResult
{
    pub fn from<T: Serialize>(result: Result<T, anyhow::Error>) -> Self {
        match result {
            Ok(value) => SerializableResult {
                value: Option::from(serde_json::to_string(&Some(value)).unwrap()),
                err: None,
            },
            Err(err) => SerializableResult {
                value: None,
                err: Some(err.to_string()),
            },
        }
    }
}