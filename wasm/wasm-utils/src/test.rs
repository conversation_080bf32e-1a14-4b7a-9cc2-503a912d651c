#[cfg(test)]
mod tests {
    use std::ffi::CString;

    use serde::{Deserialize, Serialize};

    use crate::*;

    #[derive(Serialize, Deserialize, PartialEq, Debug)]
    struct TestStruct {
        id: i32,
        name: String,
    }

    #[test]
    fn test_get_string() {
        let original = CString::new("Hello, world!").expect("CString::new failed");
        let ptr = original.into_raw();
        let string = get_string(ptr).expect("Failed to get string");
        assert_eq!(string, "Hello, world!");
    }

    #[test]
    fn test_write_string() {
        let original = "Hello, world!";
        let ptr = write_string(original);
        assert!(!ptr.is_null());
    }

    #[test]
    fn test_json_serialization() {
        let test_object = TestStruct { id: 1, name: "Test".to_string() };
        let ptr = object_to_json_ptr(&test_object).expect("Serialization failed");
        let deserialized: TestStruct = json_ptr_to_object(ptr).expect("Deserialization failed");
        assert_eq!(test_object, deserialized);
    }

    #[test]
    fn test_from_result_ok() {
        let test_object = TestStruct { id: 1, name: "Test".to_string() };
        let ptr = object_to_json_ptr(&test_object).expect("Serialization failed");
        let result: Result<TestStruct> = json_ptr_to_object(ptr);
        let serializable_result = SerializableResult::from(result);
        let json = serde_json::to_string(&serializable_result).unwrap();
        assert_eq!(r#"{"value":"{\"id\":1,\"name\":\"Test\"}"}"#, json);
    }

    #[test]
    fn test_from_result_err1() {
        let ptr = write_string("invalid json");
        let result: Result<TestStruct> = json_ptr_to_object(ptr);
        let serializable_result = SerializableResult::from(result);
        let json = serde_json::to_string(&serializable_result).unwrap();
        assert_eq!(r#"{"err":"expected value at line 1 column 1"}"#, json);
    }

    #[test]
    fn test_from_result_err2() {
        let result: Result<TestStruct> = Err(anyhow::anyhow!("test error"));
        let serializable_result = SerializableResult::from(result);
        let json = serde_json::to_string(&serializable_result).unwrap();
        assert_eq!(r#"{"err":"test error"}"#, json);
    }

    #[test]
    fn test_serialize_none() {
        let serializable_result = SerializableResult { value: None, err: None };
        let json = serde_json::to_string(&serializable_result).unwrap();
        assert_eq!(r#"{}"#, json);
    }

    #[test]
    fn test_serialize_different_type() {
        let result: Result<i32> = Ok(42);
        let serializable_result = SerializableResult::from(result);
        let json = serde_json::to_string(&serializable_result).unwrap();
        assert_eq!(r#"{"value":"42"}"#, json);
    }

    #[test]
    fn test_serialize_nested_struct() {
        #[derive(Serialize, Deserialize, Debug)]
        struct NestedStruct {
            nested: TestStruct,
        }

        let nested_object = NestedStruct {
            nested: TestStruct { id: 1, name: "Nested Test".to_string() },
        };
        let result: Result<NestedStruct> = Ok(nested_object);
        let serializable_result = SerializableResult::from(result);
        let json = serde_json::to_string(&serializable_result).unwrap();
        assert_eq!(r#"{"value":"{\"nested\":{\"id\":1,\"name\":\"Nested Test\"}}"}"#, json);
    }

    #[test]
    fn test_error_variety() {
        let custom_error = std::io::Error::new(std::io::ErrorKind::Other, "custom error");
        let result: Result<TestStruct> = Err(anyhow::Error::new(custom_error));
        let serializable_result = SerializableResult::from(result);
        let json = serde_json::to_string(&serializable_result).unwrap();
        assert_eq!(r#"{"err":"custom error"}"#, json);
    }

    #[test]
    fn test_error_details() {
        let detailed_error = anyhow::anyhow!("detailed error with additional info: {}", 42);
        let result: Result<TestStruct> = Err(detailed_error);
        let serializable_result = SerializableResult::from(result);
        let json = serde_json::to_string(&serializable_result).unwrap();
        assert_eq!(r#"{"err":"detailed error with additional info: 42"}"#, json);
    }
}
