from dataclasses import dataclass
from typing import List, Any, Optional, Dict

from serde import serde  # is pyserde not serde
from serde.json import to_json, to_dict

from wasm import wasm_py_bridge


@serde
@dataclass
class TransactionReceipt:
    transaction_hash: str
    transaction_index: int
    block_id: str
    from_address: str
    contract_name: str
    function_name: str
    parameters: List[Any]
    publickeys: List[str]
    signatures: List[str]
    status: bool
    result: Any

    def __str__(self) -> str:
        return (
            f"TransactionReceipt(transaction_hash={self.transaction_hash}, transaction_index={self.transaction_index}, "
            f"block_id={self.block_id}, from_address={self.from_address}, contract_name={self.contract_name}, "
            f"function_name={self.function_name}, parameters={self.parameters}, publickeys={self.publickeys}, "
            f"signatures={self.signatures}, status={self.status}, result={self.result})")


@serde
@dataclass
class Transaction:
    transaction_hash: Optional[str]
    from_address: str
    contract_name: str
    function_name: str
    parameters: List[Any]
    publickeys: List[str]
    signatures: List[str]
    timestamp: int

    def __str__(self) -> str:
        return (f"Transaction(transaction_hash={self.transaction_hash}, from_address={self.from_address}, "
                f"contract_name={self.contract_name}, function_name={self.function_name}, parameters={self.parameters}, "
                f"publickeys={self.publickeys}, signatures={self.signatures}, timestamp={self.timestamp})")

    def toHashableStr(self) -> str:
        # hidden transaction_hash, publickeys and signatures
        transactionDict = to_dict(self)
        skipKeys = ["transaction_hash", "publickeys", "signatures"]
        for key in skipKeys:
            if key in transactionDict:
                transactionDict.pop(key, None)

        return to_json(transactionDict)

    def toEnvironment(self, transaction_index: str) -> Dict[str, str]:
        result = {
            "transaction_hash": self.transaction_hash,
            "transaction_index": str(transaction_index),
            "transaction_timestamp": str(self.timestamp),
            "transaction_from_address": self.from_address,
        }
        for i, public_key in enumerate(self.publickeys):
            result[f"publickey{i}"] = public_key

        for i, signature in enumerate(self.signatures):
            result[f"signature{i}"] = signature

        return result


@serde
@dataclass
class BlockTree:
    id: str
    parent_id: str
    difficulty_score: float
    difficulty_score_overall: float
    height: int
    transactions: str
    merkle: str
    timestamp: int
    nonce: int
    multiplier: int

    def __str__(self) -> str:
        return (f"BlockTree(id={self.id}, parent_id={self.parent_id}, difficulty_score={self.difficulty_score}, "
                f"difficulty_score_overall={self.difficulty_score_overall}, height={self.height}), timestamp={self.timestamp}")

    def toEnvironment(self) -> Dict[str, str]:
        return {
            "block_id": self.id,
            "block_height": str(self.height)
        }


@serde
@dataclass
class ReorganizationPath:
    roll_back_path: List[str]
    common_ancestor: str
    roll_forward_path: List[str]

    def __str__(self) -> str:
        return (f"ReorganizationPath(roll_back_path={self.roll_back_path}, common_ancestor={self.common_ancestor}, "
                f"roll_forward_path={self.roll_forward_path})")


from typing import Optional, List
import os
import unittest

from wasmtime import Store, Module, Engine, Linker, WasiConfig


class TestPrimechainPowBlockTree(unittest.TestCase):
    dbPath = "vgraph.db"
    chainId = "test_chain"
    sleepInterval = 1

    def setUp(self) -> None:
        self.powWasmClient = self.initializeWasmClient("wasm/sqlite_primechain_pow_blocktree.wasm")

        # clean vgraph.db if exists
        if os.path.exists(self.dbPath):
            os.remove(self.dbPath)

        # init pow tables
        self.powWasmClient.invoke(
            "initialize_chain_tables", None,
            self.chainId
        )

    def tearDown(self) -> None:
        # clean vgraph.db if exists
        if os.path.exists(self.dbPath):
            os.remove(self.dbPath)

    def initializeWasmClient(self, wasmFile: str) -> wasm_py_bridge.WasmClient:
        engine = Engine()
        module = Module.from_file(engine, wasmFile)
        linker = Linker(engine)
        linker.define_wasi()

        wasi_config = WasiConfig()
        wasi_config.preopen_dir('.', '.')
        wasi_config.inherit_stdout()
        wasi_config.inherit_env()
        store = Store(engine)
        store.set_wasi(wasi_config)
        instance = linker.instantiate(store, module)
        wasmClient = wasm_py_bridge.WasmClient(store, instance)
        return wasmClient

    def test_save_block_to_disk(self):
        block, err = self.powWasmClient.invoke('get_block_template', BlockTree, self.chainId)
        self.assertIsNone(err)

        err = self.powWasmClient.invoke('save_block_to_disk', None, self.chainId, block)

        # get block
        block1, err = self.powWasmClient.invoke('get_block', Optional[BlockTree], self.chainId, block.id)
        self.assertIsNone(err)
        self.assertEqual(block, block1)

    def test_connect_block(self):
        block, err = self.powWasmClient.invoke('get_block_template', BlockTree, self.chainId)
        self.assertIsNone(err)

        err = self.powWasmClient.invoke('save_block_to_disk', None, self.chainId, block)
        self.assertIsNone(err)

        err = self.powWasmClient.invoke('connect_block', None, self.chainId, block, True)
        self.assertIsNone(err)

        # get current best block
        block1, err = self.powWasmClient.invoke('get_current_best_block', Optional[BlockTree], self.chainId)
        self.assertIsNone(err)
        self.assertEqual(block, block1)

    def test_get_current_best_block(self):
        # initial best block is None
        block, err = self.powWasmClient.invoke('get_current_best_block', Optional[BlockTree], self.chainId)
        self.assertIsNone(err)
        self.assertIsNone(block)

        # try to connect one block
        block, err = self.powWasmClient.invoke('get_block_template', BlockTree, self.chainId)
        self.assertIsNone(err)

        err = self.powWasmClient.invoke('save_block_to_disk', None, self.chainId, block)
        self.assertIsNone(err)

        err = self.powWasmClient.invoke('connect_block', None, self.chainId, block, True)
        self.assertIsNone(err)

        # get current best block
        block1, err = self.powWasmClient.invoke('get_current_best_block', Optional[BlockTree], self.chainId)
        self.assertIsNone(err)
        self.assertEqual(block, block1)

    def test_get_block(self):
        block, err = self.powWasmClient.invoke('get_block_template', BlockTree, self.chainId)
        self.assertIsNone(err)

        err = self.powWasmClient.invoke('save_block_to_disk', None, self.chainId, block)

        # get block
        block1, err = self.powWasmClient.invoke('get_block', Optional[BlockTree], self.chainId, block.id)
        self.assertIsNone(err)
        self.assertEqual(block, block1)

    def test_generate_locator(self):
        # if there is no best block, the locator should be empty
        locator, err = self.powWasmClient.invoke('generate_locator', List[str], self.chainId)
        self.assertIsNone(err)
        self.assertEqual(locator, [])

        # create a long chain of blocks, and generate locator
        blocks = []
        for _ in range(500):
            block, err = self.powWasmClient.invoke('get_block_template', BlockTree, self.chainId)
            self.assertIsNone(err)
            err = self.powWasmClient.invoke('save_block_to_disk', None, self.chainId, block)
            self.assertIsNone(err)
            err = self.powWasmClient.invoke('connect_block', None, self.chainId, block, True)
            self.assertIsNone(err)
            blocks.append(block)

        locator, err = self.powWasmClient.invoke('generate_locator', List[str], self.chainId)
        self.assertIsNone(err)

        # verify the locator
        targetBlockIndex = len(blocks) - 1
        shift = 1
        factor = 1
        maxLocatorLength = 32

        for i in range(min(len(locator), maxLocatorLength)):
            self.assertEqual(locator[i], blocks[targetBlockIndex + 1 - shift].id)
            if i >= 10:
                factor *= 2
            shift += factor

    def test_reorganization_path(self):
        # Block Tree Structure:
        # block1 is the root block, created from ROOT_PARENT_ID
        # block2 and block3 are children of block1
        # block4 is a child of block3
        # block5 is a child of block4
        #
        #      block1
        #      /     \
        #   block2   block3
        #              |
        #            block4
        #             |
        #            block5

        block1, err = self.powWasmClient.invoke('get_block_template', BlockTree, self.chainId)
        self.assertIsNone(err)
        err = self.powWasmClient.invoke('save_block_to_disk', None, self.chainId, block1)
        self.assertIsNone(err)
        err = self.powWasmClient.invoke('connect_block', None, self.chainId, block1, True)
        self.assertIsNone(err)

        block2, err = self.powWasmClient.invoke('get_block_template', BlockTree, self.chainId)
        self.assertIsNone(err)
        err = self.powWasmClient.invoke('save_block_to_disk', None, self.chainId, block2)
        self.assertIsNone(err)

        block3, err = self.powWasmClient.invoke('get_block_template', BlockTree, self.chainId)
        self.assertIsNone(err)
        err = self.powWasmClient.invoke('save_block_to_disk', None, self.chainId, block3)
        self.assertIsNone(err)
        err = self.powWasmClient.invoke('connect_block', None, self.chainId, block3, True)
        self.assertIsNone(err)

        block4, err = self.powWasmClient.invoke('get_block_template', BlockTree, self.chainId)
        self.assertIsNone(err)
        err = self.powWasmClient.invoke('save_block_to_disk', None, self.chainId, block4)
        self.assertIsNone(err)
        err = self.powWasmClient.invoke('connect_block', None, self.chainId, block4, True)
        self.assertIsNone(err)

        block5, err = self.powWasmClient.invoke('get_block_template', BlockTree, self.chainId)
        self.assertIsNone(err)
        err = self.powWasmClient.invoke('save_block_to_disk', None, self.chainId, block5)
        self.assertIsNone(err)
        err = self.powWasmClient.invoke('connect_block', None, self.chainId, block5, True)
        self.assertIsNone(err)

        print(
            "      block1\n      /     \\\n   block2   block3\n              |\n            block4\n              |\n            block5\n")

        # reorganization path from block2 to block5
        # common ancestor: block1
        # roll back path: [block2]
        # roll forward path: [block3, block4, block5]
        reorgPath, err = self.powWasmClient.invoke('get_reorganization_path', ReorganizationPath, self.chainId,
                                                   block2.id, block5.id)
        self.assertIsNone(err)
        self.assertEqual(reorgPath.common_ancestor, block1.id)
        self.assertEqual(reorgPath.roll_back_path, [block2.id])
        self.assertEqual(reorgPath.roll_forward_path, [block3.id, block4.id, block5.id])


if __name__ == "__main__":
    unittest.main()
