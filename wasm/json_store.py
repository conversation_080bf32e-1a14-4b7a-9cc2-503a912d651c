from kivy.app import App
from kivy.logger import Logger
from kivy.storage.jsonstore import JsonStore


def answer():
    Logger.info('answer in python module')
    return 42

def setJsonStore():
    vgraphStore = JsonStore(App.get_running_app().user_data_dir + '/vgraph.json')
    vgraphStore.put("testKey", testAttribute='testValue')
    Logger.info('json_store.py: setJsonStore testAttribute="testValue"')

def getJsonStore():
    vgraphStore = JsonStore(App.get_running_app().user_data_dir + '/vgraph.json')
    if vgraphStore.exists("testKey"):
        for attribute in vgraphStore.get("testKey"):
            value = vgraphStore.get("testKey")[attribute]
            Logger.info(f'json_store.py: getJsonStore {attribute} value is: {value}')
