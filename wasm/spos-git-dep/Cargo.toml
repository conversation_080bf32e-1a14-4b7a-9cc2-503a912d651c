[package]
name = "spos-git-dep"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
glue = { branch = "nix-git-glue", git = "https://zshimonz:<EMAIL>/virtualeconomy/vgraph.git"}
token = { branch = "nix-git-token", git = "https://zshimonz:<EMAIL>/virtualeconomy/vgraph.git"}

serde = { version = "=1.0.193", features = ["derive"] }
serde_json = "1.0.108"
lazy_static = "1.4.0"
anyhow = "1.0.75"
sha3 = "0.10.8"
uuid = { version = "1.6.1", features = ["serde", "v4", "fast-rng"] }

[dev-dependencies]
serial_test = "3.1.1"

[lib]
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"
