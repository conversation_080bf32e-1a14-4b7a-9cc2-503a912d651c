{
  description = "Rust wasm32-wasi build with wasi-sdk";

  inputs = {
    flake-utils.url = "github:numtide/flake-utils";
    rust-overlay = {
      url = "github:oxalica/rust-overlay";
      inputs.nixpkgs.follows = "nixpkgs";
    };
    nixpkgs.url = "nixpkgs/nixos-23.11";
  };

  outputs = { self, nixpkgs, flake-utils, rust-overlay }:
    flake-utils.lib.eachSystem [ "x86_64-linux" "aarch64-darwin" "aarch64-linux" "x86_64-darwin" ]
      (system: let
        overlays = [ rust-overlay.overlays.default ];
        pkgs = import nixpkgs { inherit system overlays; };
        rust = pkgs.rust-bin.fromRustupToolchainFile ./rust-toolchain.toml;
      in {
        packages = {
          rust-wasm32-wasi = pkgs.rustPlatform.buildRustPackage {
            pname = "rust-wasm32-wasi";
            version = "1.0.0";
            src = ./.;
            cargoLock = {
              lockFile = ./Cargo.lock;
               outputHashes = {
                  "glue-0.1.0" = "sha256-3y5lyWRoW0NpgWhClm4OtuhVlD3lub1xMyZfcMCrxbo=";
                  "token-0.1.0" = "sha256-cdt5dwpksGgWbOz2UZsguSTqso0NtjXW2k5uHpWEJB4=";
               };
            };
            nativeBuildInputs = [ rust ];

            checkPhase = "echo 'skip checkPhase'";

            buildPhase = ''
              mkdir -p $out;
              cargo build --release --target wasm32-wasi;
              cp target/wasm32-wasi/release/*.wasm $out;
            '';

            installPhase = "echo 'skip installPhase'";
          };
        };
        defaultPackage = self.packages.${system}.rust-wasm32-wasi;
      });
}