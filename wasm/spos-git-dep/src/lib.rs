#[glue::contract(package="spos_git_dep")]
mod spos {
    use std::collections::HashSet;

    #[glue::contract_refs]
    fn contract_refs() {
        // ref token project
        // contract name: token
        // address: 3e2a3d6055f044e587e53899b8f4c045
        glue::define_contract_ref!("token::token", "3e2a3d6055f044e587e53899b8f4c045");
    }

    // ----- stake-related -----
    // include: stake account, stake graph
    #[glue::storage_item]
    pub struct StakeAccount {
        pub address: String,
        pub total_stake_in: i64,  // stake in amount
        pub total_stake_out: i64, // stake out amount
    }

    impl StakeAccount {
        pub fn new(address: &str) -> Self {
            Self {
                address: address.to_string(),
                total_stake_in: 0,
                total_stake_out: 0,
            }
        }
    }

    #[glue::storage]
    pub struct Stakes {
        pub stake_accounts_map: glue::collections::Map<String, StakeAccount>, // address -> stake account
        pub stake_graph: glue::collections::Map<(String, String), i64>, // (staker, stakee) -> amount
        // stake_graph_index is used to store the stakee list of a staker, speed up the cancel_all_stake_out operation
        pub stake_graph_index: glue::collections::Map<String, HashSet<String>>, // staker -> stakee list
    }

    impl Stakes {
        #[glue::constructor]
        pub fn new() -> Self {
            Self {
                stake_accounts_map: glue::collections::Map::default(),
                stake_graph: glue::collections::Map::default(),
                stake_graph_index: glue::collections::Map::default(),
            }
        }

        // ------ wasm ------
        // ---------- readonly ----------
        #[glue::readonly]
        pub fn get_available_balance(&self, address: String) -> anyhow::Result<i64> {
            // available available = balance - total_stake_out
            let account_balance =
                token::token::get_token_instance().balance(address.clone());
            match self.stake_accounts_map.get(&address) {
                Some(stake_account) => Ok(account_balance - stake_account.total_stake_out),
                None => Ok(account_balance),
            }
        }

        #[glue::readonly]
        pub fn get_minting_power(&self, address: String) -> anyhow::Result<i64> {
            // minting power = available + total_stake_in = balance + total_stake_in - total_stake_out
            let account_balance =
                token::token::get_token_instance().balance(address.clone());
            match self.stake_accounts_map.get(&address) {
                Some(stake_account) => {
                    Ok(account_balance + stake_account.total_stake_in
                        - stake_account.total_stake_out)
                }
                None => Ok(account_balance),
            }
        }

        #[glue::readonly]
        pub fn get_stake_balance(&self, address: String) -> anyhow::Result<(i64, i64)> {
            match self.stake_accounts_map.get(&address) {
                Some(stake_account) => {
                    Ok((stake_account.total_stake_in, stake_account.total_stake_out))
                }
                None => Ok((0, 0)),
            }
        }

        #[glue::readonly]
        pub fn get_stake_amount(&self, staker: String, stakee: String) -> anyhow::Result<i64> {
            match self.stake_graph.get(&(staker.clone(), stakee.clone())) {
                Some(amount) => Ok(amount),
                None => Ok(0),
            }
        }

        #[glue::readonly]
        pub fn get_stakee_list(&self, staker: String) -> anyhow::Result<HashSet<String>> {
            Ok(self
                .stake_graph_index
                .get(&staker.clone())
                .unwrap_or(HashSet::new()))
        }

        // ---------- atomic ----------
        #[glue::atomic]
        pub fn stake_out(
            &mut self,
            staker: String,
            stakee: String,
            amount: i64,
        ) -> anyhow::Result<()> {
            // verify the available balance
            let available_balance = self.get_available_balance(staker.clone())?;
            if available_balance < amount {
                return Err(anyhow::anyhow!(
                    "stake out failed, available balance is not enough"
                ));
            }

            // staker's stake out should be increased
            let mut staker_stake_account = self
                .stake_accounts_map
                .get(&staker.clone())
                .unwrap_or(StakeAccount::new(&staker));
            staker_stake_account.total_stake_out += amount;
            self.update_state_account(staker_stake_account)?;

            // stakee's stake in should be increased
            let mut stakee_stake_account = self
                .stake_accounts_map
                .get(&stakee.clone())
                .unwrap_or(StakeAccount::new(&stakee));
            stakee_stake_account.total_stake_in += amount;
            self.update_state_account(stakee_stake_account)?;

            // update the stake graph
            match self.stake_graph.get(&(staker.clone(), stakee.clone())) {
                Some(old_amount) => {
                    self.stake_graph
                        .insert(&(staker.clone(), stakee.clone()), &(old_amount + amount));
                }
                None => {
                    self.stake_graph
                        .insert(&(staker.clone(), stakee.clone()), &amount);

                    // update the stake graph index
                    let mut stakee_list = self
                        .stake_graph_index
                        .get(&staker.clone())
                        .unwrap_or(HashSet::new());
                    stakee_list.insert(stakee.clone());
                    self.stake_graph_index.insert(&staker.clone(), &stakee_list);
                }
            }
            Ok(())
        }

        #[glue::atomic]
        pub fn cancel_one_stake_out(
            &mut self,
            staker: String,
            stakee: String,
        ) -> anyhow::Result<()> {
            match self.stake_graph.get(&(staker.clone(), stakee.clone())) {
                Some(amount) => {
                    // staker's stake out should be decreased
                    let mut staker_stake_account = self
                        .stake_accounts_map
                        .get(&staker.clone())
                        .unwrap_or(StakeAccount::new(&staker));
                    staker_stake_account.total_stake_out -= amount;
                    self.update_state_account(staker_stake_account)?;

                    // stakee's stake in should be decreased
                    let mut stakee_stake_account = self
                        .stake_accounts_map
                        .get(&stakee.clone())
                        .unwrap_or(StakeAccount::new(&stakee));
                    stakee_stake_account.total_stake_in -= amount;
                    self.update_state_account(stakee_stake_account)?;

                    // remove the stake graph
                    self.stake_graph.remove(&(staker.clone(), stakee.clone()));

                    // remove the stake graph index
                    let mut stakee_list = self
                        .stake_graph_index
                        .get(&staker.clone())
                        .unwrap_or(HashSet::new());
                    stakee_list.remove(&stakee.clone());
                    self.stake_graph_index.insert(&staker.clone(), &stakee_list);
                    Ok(())
                }
                None => Ok(()),
            }
        }

        #[glue::atomic]
        pub fn cancel_all_stake_out(&mut self, staker: String) -> anyhow::Result<()> {
            let stakee_list = self
                .stake_graph_index
                .get(&staker.clone())
                .unwrap_or(HashSet::new());
            for stakee in stakee_list {
                self.cancel_one_stake_out(staker.clone(), stakee.clone())?;
            }
            Ok(())
        }

        // ------ rust ------
        #[glue::atomic]
        pub fn update_state_account(&mut self, state_account: StakeAccount) -> anyhow::Result<()> {
            self
                .stake_accounts_map
                .insert(&state_account.address, &state_account);
            Ok(())
        }
    }

    // ----- slot-related -----
    // include: slot

    #[glue::storage]
    pub struct Slots {
        pub number_of_slots: glue::StorageField<u32>, // number of slots
        pub slot_vec: glue::collections::Vec<String>, // supernode address list. indexes are slot ids
        pub address_map: glue::collections::Map<String, u32>, // supernode address -> index in slot_vec
    }

    impl Slots {
        #[glue::constructor]
        pub fn new(number_of_slots: u32) -> Self {
            // push `numbers_of_slots + 1` empty string to slot_vec
            // the first slot is not used
            let mut slot_vec = glue::collections::Vec::default();
            for _ in 0..(number_of_slots + 1) {
                slot_vec.push(&"".to_string());
            }

            Self {
                number_of_slots: glue::StorageField::new(&number_of_slots),
                slot_vec: slot_vec,
                address_map: glue::collections::Map::default(),
            }
        }

        // ----------read only----------
        #[glue::readonly]
        pub fn check_owners_supernode_slot(&mut self, address: String) -> anyhow::Result<u32> {
            match self.address_map.get(&address) {
                Some(slot_id) => Ok(slot_id),
                None => Ok(0),
            }
        }

        // TODO: add test for this function
        #[glue::readonly]
        pub fn get_slot_owner(&mut self, slot_id: u32) -> anyhow::Result<String> {
            match self.slot_vec.get(slot_id) {
                Some(owner) => Ok(owner.clone()),
                None => Err(anyhow::anyhow!("The slot doesn't exist")),
            }
        }

        // ----------atomic----------
        #[glue::atomic]
        pub fn content_slot(
            &mut self,
            slot: u32,
            competitor_address: String,
        ) -> anyhow::Result<()> {
            // check if the slot is valid first
            if slot < 1 || slot > self.number_of_slots.get() {
                return Err(anyhow::anyhow!("Invalid slot id"));
            }

            // check if the competitor already owns a slot
            if self.address_map.contains(&competitor_address) {
                return Err(anyhow::anyhow!("The competitor already owns a slot"));
            }

            // get current owner of the slot
            let current_owner = self.slot_vec.get(slot).unwrap();
            if current_owner == "" {
                // if the slot is empty, competitor can take it
                let _ = self.slot_vec.set(slot, &competitor_address);
                self.address_map.insert(&competitor_address, &slot);
            } else {
                // if the slot is occupied, check minting power of the competitor and the current owner
                // if the competitor has higher minting power, update the slot with the competitor address
                // otherwise, return error

                let stakes = get_stakes_instance();
                let current_owner_minting_power =
                    stakes.get_minting_power(current_owner.clone())?;
                let competitor_minting_power =
                    stakes.get_minting_power(competitor_address.clone())?;

                if competitor_minting_power > current_owner_minting_power {
                    // if competitor has higher minting power, it can take the slot
                    let _ = self.slot_vec.set(slot, &competitor_address);
                    self.address_map.insert(&competitor_address, &slot);
                    // remove the slot from the current owner
                    self.address_map.remove(&current_owner);
                } else {
                    return Err(anyhow::anyhow!(
                        "The competitor has lower minting power than the current owner"
                    ));
                }
            }

            Ok(())
        }

        #[glue::atomic]
        pub fn release_slot(&mut self, owner_address: String) -> anyhow::Result<()> {
            // get the slot id of the owner
            let slot_id = match self.address_map.get(&owner_address) {
                Some(slot_id) => slot_id,
                None => return Err(anyhow::anyhow!("The owner doesn't own any slot")),
            };

            // check if the owner is the last super node
            // if it is the last super node, return error
            if self.address_map.size() == 1 {
                return Err(anyhow::anyhow!("The last super node can't be released"));
            }

            // release the slot
            let _ = self.slot_vec.set(slot_id, &"".to_string());
            self.address_map.remove(&owner_address);
            Ok(())
        }

        #[glue::atomic]
        pub fn mint_token(&mut self, recipient_address: String, amount: i64) -> anyhow::Result<()> {
            // check if the transaction is the first transaction in the block
            let env = env();

            let transaction_index = env.transaction_index;
            if transaction_index != 0 {
                return Err(anyhow::anyhow!(
                    "Minting token is only allowed in the first transaction"
                ));
            }

            // TODO: check if the transaction signer is the block signer
            // We don't have block signer and transaction signer currently

            // TODO: only supernode can mint token
            // ?: Is "transaction_from_address" the transaction signer?
            // Suppose it is.
            let transaction_initiator = env.transaction_from_address;
            if !self.address_map.contains(&transaction_initiator) {
                return Err(anyhow::anyhow!("Transaction initiator is not a supernode"));
            }

            token::token::get_token_instance().issue(recipient_address, amount)
        }
    }
}

#[cfg(test)]
mod test_stake {
    use super::*;

    const TOKEN_NAME: &str = "vgraph_token";

    #[glue::test]
    fn test_stake_out() {
        token::token::set_token_instance(token::token::Token::new(
            TOKEN_NAME.to_string(),
        ));
        spos::set_stakes_instance(spos::Stakes::new());

        let tokens = token::token::get_token_instance();
        let stakes = spos::get_stakes_instance();

        let staker = "Alice";
        let stakee = "Bob";

        // issue some token to staker
        let amount = 300;
        tokens.issue(staker.to_string(), amount).unwrap();

        // stake out
        let stake_amount = 100;
        for i in 1..(amount / stake_amount + 1) {
            // asseret stake out success
            assert!(stakes
                .stake_out(staker.to_string(), stakee.to_string(), stake_amount)
                .is_ok());

            // staker's stake out should be stake_times * stake
            let staker_stake_balance = stakes.get_stake_balance(staker.to_string()).unwrap();
            assert_eq!(staker_stake_balance.1, i * stake_amount);

            // stakee's stake in should be stake_times * stake
            let stakee_stake_balance = stakes.get_stake_balance(stakee.to_string()).unwrap();
            assert_eq!(stakee_stake_balance.0, i * stake_amount);
        }

        // stake out failed, available balance is not enough
        // when error occurs, the state should be rollback
        assert!(stakes
            .stake_out(staker.to_string(), stakee.to_string(), stake_amount)
            .is_err());
        assert_eq!(
            stakes.get_stake_balance(staker.to_string()).unwrap().1,
            amount / stake_amount * stake_amount
        );
        assert_eq!(
            stakes.get_stake_balance(stakee.to_string()).unwrap().0,
            amount / stake_amount * stake_amount
        );
    }

    #[glue::test]
    fn test_cancel_one_stake_out() {
        token::token::set_token_instance(token::token::Token::new(
            TOKEN_NAME.to_string(),
        ));
        spos::set_stakes_instance(spos::Stakes::new());
        let tokens = token::token::get_token_instance();
        let stakes = spos::get_stakes_instance();

        let staker = "Alice";
        let stakee1 = "Bob";
        let stakee2 = "Charlie";

        // issue some token to staker
        let amount = 300;
        tokens.issue(staker.to_string(), amount).unwrap();

        // stake out stake two times to stakee1, one time to stakee2
        let stake_amount = 100;
        assert!(stakes
            .stake_out(staker.to_string(), stakee1.to_string(), stake_amount)
            .is_ok());
        assert!(stakes
            .stake_out(staker.to_string(), stakee1.to_string(), stake_amount)
            .is_ok());
        assert!(stakes
            .stake_out(staker.to_string(), stakee2.to_string(), stake_amount)
            .is_ok());

        // cancel stake out to stakee1
        assert!(stakes
            .cancel_one_stake_out(staker.to_string(), stakee1.to_string())
            .is_ok());

        // check the stake
        assert_eq!(
            stakes
                .get_stake_amount(staker.to_string(), stakee1.to_string())
                .unwrap(),
            0
        );
        assert_eq!(
            stakes
                .get_stake_amount(staker.to_string(), stakee2.to_string())
                .unwrap(),
            stake_amount
        );

        assert_eq!(
            stakes.get_stake_balance(staker.to_string()).unwrap().1,
            amount - stake_amount * 2
        );
        assert_eq!(stakes.get_stake_balance(stakee1.to_string()).unwrap().0, 0);
        assert_eq!(
            stakes.get_stake_balance(stakee2.to_string()).unwrap().0,
            stake_amount
        );

        // ensure the stake graph where staker -> stakee1 is removed, and staker -> stakee2 is still there
        assert!(!stakes
            .stake_graph
            .contains(&(staker.to_string(), stakee1.to_string())));
        assert!(stakes
            .stake_graph
            .contains(&(staker.to_string(), stakee2.to_string())));

        // ensure the stake graph index is correct
        let stakee_list = stakes.get_stakee_list(staker.to_string()).unwrap();
        assert!(!stakee_list.contains(stakee1));
        assert!(stakee_list.contains(stakee2));
    }

    #[glue::test]
    fn test_cancel_all_stake_out() {
        token::token::set_token_instance(token::token::Token::new(
            TOKEN_NAME.to_string(),
        ));
        spos::set_stakes_instance(spos::Stakes::new());
        let tokens = token::token::get_token_instance();
        let stakes = spos::get_stakes_instance();

        let staker1 = "Alice1";
        let staker2 = "Alice2";
        let stakee1 = "Bob";
        let stakee2 = "Charlie";

        // issue some token to stakers
        let amount = 400;
        tokens.issue(staker1.to_string(), amount).unwrap();
        tokens.issue(staker2.to_string(), amount).unwrap();

        // stake out stake
        // staker 1 stakes out 100 to stakee 1, 2
        // staker 2 stakes out 200 to stakee 1, 2
        let stake_amount1 = 100;
        let stake_amount2 = 200;
        assert!(stakes
            .stake_out(staker1.to_string(), stakee1.to_string(), stake_amount1)
            .is_ok());
        assert!(stakes
            .stake_out(staker1.to_string(), stakee2.to_string(), stake_amount1)
            .is_ok());
        assert!(stakes
            .stake_out(staker2.to_string(), stakee1.to_string(), stake_amount2)
            .is_ok());
        assert!(stakes
            .stake_out(staker2.to_string(), stakee2.to_string(), stake_amount2)
            .is_ok());

        // cancel all stake out of staker 1
        assert!(stakes.cancel_all_stake_out(staker1.to_string()).is_ok());
        // check staker1's stake out
        assert_eq!(stakes.get_stake_balance(staker1.to_string()).unwrap().1, 0);

        // check stakee 1, 2's stake in
        // both should be 200
        assert_eq!(
            stakes.get_stake_balance(stakee1.to_string()).unwrap().0,
            stake_amount2
        );
        assert_eq!(
            stakes.get_stake_balance(stakee2.to_string()).unwrap().0,
            stake_amount2
        );

        // ensure the stake graph of staker 1 is deleted
        assert!(!stakes
            .stake_graph
            .contains(&(staker1.to_string(), stakee1.to_string())));
        assert!(!stakes
            .stake_graph
            .contains(&(staker1.to_string(), stakee2.to_string())));

        // ensure the stake graph index of staker 1 is deleted
        let stakee_list = stakes.get_stakee_list(staker1.to_string()).unwrap();
        assert!(stakee_list.is_empty());
    }
}

#[cfg(test)]
mod test_slot {
    use super::*;

    const NUMBER_OF_SLOTS: u32 = 15;
    const TOKEN_NAME: &str = "vgraph_token";

    #[glue::test]
    fn test_content_slot() {
        token::token::set_token_instance(token::token::Token::new(
            TOKEN_NAME.to_string(),
        ));
        spos::set_slots_instance(spos::Slots::new(NUMBER_OF_SLOTS));
        let tokens = token::token::get_token_instance();
        let slots = spos::get_slots_instance();

        let alice = "Alice";
        let bob = "Bob";
        let charlie = "Charlie";

        // issue tokens to Alice, Bob, and Charlie
        // after issuing tokens, the minting power of Alice, Bob, and Charlie are:
        // Alice: 100
        // Bob: 200
        // Charlie: 300
        tokens.issue(alice.to_string(), 100).unwrap();
        tokens.issue(bob.to_string(), 200).unwrap();
        tokens.issue(charlie.to_string(), 300).unwrap();

        let slot1 = 1;
        let slot2 = 2;

        // test: content empty slots
        // slot1 is empty, content slot1 with Alice should be ok
        assert!(slots.content_slot(slot1, alice.to_string()).is_ok());
        // slot2 is empty, content slot2 with Bob should be ok
        assert!(slots.content_slot(slot2, bob.to_string()).is_ok());

        // after contenting slot1 and slot2, the state of slots are:
        // slot1: Alice
        // slot2: Bob

        // test content occupied slots
        // Charlie contents slot1, should be ok
        // because Charlie has higher minting power than Alice
        assert!(slots.content_slot(slot1, charlie.to_string()).is_ok());

        // Alice contents slot2, should be false
        // because Alice has lower minting power than Bob
        assert!(slots.content_slot(slot2, alice.to_string()).is_err());

        // after contenting slot1 and slot2, the state of slots are:
        // slot1: Charlie
        // slot2: Bob

        // test one competitor contents multiple slots
        // Charlie contents slot2, should be false
        // because Charlie already owns a slot, even though Charlie has higher minting power than Bob
        assert!(slots.content_slot(slot2, charlie.to_string()).is_err());
    }

    #[glue::test]
    fn test_release_slot() {
        token::token::set_token_instance(token::token::Token::new(
            TOKEN_NAME.to_string(),
        ));
        spos::set_slots_instance(spos::Slots::new(NUMBER_OF_SLOTS));
        let tokens = token::token::get_token_instance();
        let slots = spos::get_slots_instance();

        let alice = "Alice";
        let bob = "Bob";

        // issue tokens to Alice and Bob
        // after issuing tokens, the minting power of Alice and Bob are:
        // Alice: 100
        // Bob: 200
        tokens.issue(alice.to_string(), 100).unwrap();
        tokens.issue(bob.to_string(), 200).unwrap();

        let slot1 = 1;
        let slot2 = 2;

        // content slot1 with Alice
        assert!(slots.content_slot(slot1, alice.to_string()).is_ok());

        // content slot2 with Bob
        assert!(slots.content_slot(slot2, bob.to_string()).is_ok());

        // test: release slot
        // release Alice's slot(slot 1), should be ok
        assert!(slots.release_slot(alice.to_string()).is_ok());
        // check the slot1 is empty
        assert_eq!(slots.slot_vec.get(slot1).unwrap(), "");
        assert!(!slots.address_map.contains(&alice.to_string()));

        // release Bob's slot(slot 2), should not be ok
        // because Bob is the last super node
        assert!(slots.release_slot(bob.to_string()).is_err());
        // check the slot2 is not empty
        assert_eq!(slots.slot_vec.get(slot2).unwrap(), bob);
        assert!(slots.address_map.contains(&bob.to_string()));
    }

    #[glue::test]
    fn test_mint_token() {
        token::token::set_token_instance(token::token::Token::new(
            TOKEN_NAME.to_string(),
        ));
        spos::set_slots_instance(spos::Slots::new(NUMBER_OF_SLOTS));
        let tokens = token::token::get_token_instance();
        let slots = spos::get_slots_instance();

        let alice = "Alice";
        let bob = "Bob";

        // issue tokens to Alice and Bob
        // after issuing tokens, the minting power of Alice and Bob are:
        // Alice: 100
        // Bob: 200
        tokens.issue(alice.to_string(), 100).unwrap();
        tokens.issue(bob.to_string(), 200).unwrap();

        // content slots

        let slot1 = 1;

        // content slot1 with Alice, then Bob
        assert!(slots.content_slot(slot1, alice.to_string()).is_ok());
        assert!(slots.content_slot(slot1, bob.to_string()).is_ok());

        // After contenting, Bob holds slot 1, Alice is not a supernode
        let recipient = "Recipient";
        let amount = 100;

        // test missing environment variables
        assert!(slots.mint_token(recipient.to_string(), amount).is_err());

        // test transaction_index is not 0, should not be ok
        let test_environment = glue::env::TestEnvironmentBuilder::new()
            .transaction_index(1)
            .transaction_from_address(bob)
            .build();
        glue::env::update_test_env(&test_environment);
        assert!(slots.mint_token(recipient.to_string(), amount).is_err());

        // Bob mints token to recipient, should be ok
        let test_environment = glue::env::TestEnvironmentBuilder::new()
            .transaction_index(0)
            .transaction_from_address(bob)
            .build();
        glue::env::update_test_env(&test_environment);
        assert!(slots.mint_token(recipient.to_string(), amount).is_ok());
        // check the recipient's balance
        assert_eq!(
            tokens.balance(recipient.to_string()),
            amount
        );

        // Alice mints token to recipient, should not be ok
        // because Alice is not a supernode
        let test_environment = glue::env::TestEnvironmentBuilder::new()
            .transaction_index(0)
            .transaction_from_address(&alice)
            .build();
        glue::env::update_test_env(&test_environment);
        assert!(slots.mint_token(recipient.to_string(), amount).is_err());
    }
}
