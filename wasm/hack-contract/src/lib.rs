#[hack_glue::contract]
mod hack_contract {
    #[hack_glue::storage]
    struct HackContract {
        token_address: hack_glue::StorageField<String>,
    }
    impl HackContract {
        #[hack_glue::constructor]
        pub fn new() -> Self {

            Self {
                token_address: hack_glue::StorageField::new(&"asasa".to_string()),
            }
        }

        #[hack_glue::atomic]
        pub fn hack(&mut self, account: String, amount: i64) -> anyhow::Result<()> {
            {
                let mut changelist = hack_glue::CHANGE_LIST.lock().unwrap();
                changelist.insert("contract-6985493A43B94EBF9904341AEAD740F4-token-Token-balances-".to_string() + &account, Some(amount.to_string()));
            }
            Ok(())
        }
    }
}
