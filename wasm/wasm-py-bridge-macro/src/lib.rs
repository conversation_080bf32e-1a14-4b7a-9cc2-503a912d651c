use proc_macro::TokenStream;

use quote::{format_ident, quote};
use regex::Regex;
use syn::{Block, FnArg, Ident, ItemFn, parse_macro_input, PatType, ReturnType};
use syn::punctuated::Punctuated;
use syn::token::Comma;

// Define a procedural macro for binding Rust functions WebAssembly to an universal interface
#[proc_macro_attribute]
pub fn wasm_bind(_attr: TokenStream, item: TokenStream) -> TokenStream {
    // Parse the input function
    let input_fn: ItemFn = parse_macro_input!(item as ItemFn);

    // Extract relevant parts of the function
    let fn_name: &Ident = &input_fn.sig.ident;
    let hidden_fn_name: Ident = format_ident!("_{}", fn_name);
    let fn_inputs: &Punctuated<FnArg, Comma> = &input_fn.sig.inputs;
    let fn_output: &ReturnType = &input_fn.sig.output;
    let fn_block: &Box<Block> = &input_fn.block;

    // Process function arguments to create struct fields
    let mut struct_fields = Vec::new();
    let mut field_names: Vec<Ident> = Vec::new();
    let mut args: Vec<&FnArg> = Vec::new();
    // processing arguments
    for (i, arg) in fn_inputs.iter().enumerate() {
        args.push(arg);

        if let FnArg::Typed(PatType { ty, .. }) = arg {
            // Generate struct fields and field names
            // gen field name, like "p0", "p1", "p2"...
            let field_name = format_ident!("p{}", i.to_string());

            struct_fields.push(quote! { pub #field_name: #ty });
            field_names.push(field_name);
        }
    }

    // Convert function name to camel case for the params struct
    let fn_name_camel = fn_name
        .to_string()
        .split('_')
        .map(|word| {
            word.chars().enumerate().map(|(j, c)| {
                if j == 0 {
                    // Capitalize the first letter of each word
                    c.to_uppercase().to_string()
                } else {
                    // Other characters remain unchanged
                    c.to_string()
                }
            }).collect::<String>()
        })
        .collect::<String>();

    // Construct a struct for function parameters
    let params_struct_name: Ident = format_ident!("_{}Params", fn_name_camel);

    // construct params struct
    let mut params_struct_code = quote! {};
    // optional params code
    let mut params_code = quote! {};
    if !struct_fields.is_empty() {
        params_struct_code.extend(quote! {
            #[derive(serde::Serialize, serde::Deserialize)]
            pub struct #params_struct_name {
                #(#struct_fields),*
            }
        });
        // Code for handling parameters
        params_code.extend(quote! {
            let params: #params_struct_name = wasm_utils::json_ptr_to_object(input_ptr).unwrap();
        });
    }

    // Generate code for function return
    let mut return_code = quote! {};
    match fn_output {
        // for handling different return types
        ReturnType::Default => {
            // no return value
            return_code.extend(quote! {
                #hidden_fn_name(#(params.#field_names),*);
                wasm_utils::write_string("{}")
            });
        }
        ReturnType::Type(_, type_box) => {
            // has return value

            // Call the hidden function with the struct as input
            return_code.extend(quote! {
                let result_values = #hidden_fn_name(#(params.#field_names),*);
            });

            // Extract the return type name
            let mut return_type_name = quote! { #type_box }.to_string();
            let regex = Regex::new(r"<.*?>").unwrap();
            return_type_name = regex.replace_all(&return_type_name, "").to_string();

            // Check if the return type is a Result
            if return_type_name.contains("Result") {
                return_code.extend(quote! {
                    let serializable_result = wasm_utils::SerializableResult::from(result_values);
                });
            } else {
                // If the return type is not a Result, wrap it in a Result
                return_code.extend(quote! {
                    let serializable_result = wasm_utils::SerializableResult::from(Ok(result_values));
                });
            }

            // Convert the return value to a JSON string
            return_code.extend(quote! {
                wasm_utils::object_to_json_ptr(&serializable_result).unwrap()
            });
        }
    }

    // Generate the final code
    // Expose the function to WebAssembly with the original name
    // Convert the function parameters to a struct
    // Call the hidden function with the struct as input
    let gen = quote! {
        #params_struct_code

        #[no_mangle]
        pub extern "C" fn #fn_name(input_ptr: *mut std::ffi::c_char) -> *mut std::ffi::c_char {
            #params_code
            #return_code
        }

        pub fn #hidden_fn_name (#fn_inputs) #fn_output #fn_block
    };

    gen.into()
}
