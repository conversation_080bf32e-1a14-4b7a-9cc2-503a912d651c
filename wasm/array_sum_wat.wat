(module
  ;; (import "wasm.json_store" "answer" (func $test_call (result i32)))
  ;; (import "wasm.json_store_wat" "setJsonStore" (func $setJsonStore ))
  ;; (import "wasm.json_store_wat" "getJsonStore" (func $getJsonStore ))
  (type (;0;) (func))
  (type (;1;) (func (param i32 i32)))
  (type (;2;) (func (param i32)))
  (type (;3;) (func (param i32) (result i64)))
  (type (;4;) (func (param i32) (result i32)))
  (type (;5;) (func (param i32 i32) (result i32)))
  (func $_ZN5alloc7raw_vec17capacity_overflow17h4b275cb3c10b0a78E (type 0)
    (local i32)
    global.get $__stack_pointer
    i32.const 32
    i32.sub
    local.tee 0
    global.set $__stack_pointer
    local.get 0
    i32.const 28
    i32.add
    i32.const 0
    i32.store
    local.get 0
    i32.const 1048664
    i32.store offset=24
    local.get 0
    i64.const 1
    i64.store offset=12 align=4
    local.get 0
    i32.const 1048624
    i32.store offset=8
    local.get 0
    i32.const 8
    i32.add
    i32.const 1048632
    call $_ZN4core9panicking9panic_fmt17h751be80779d42b53E
    unreachable)
  (func $_ZN4core9panicking9panic_fmt17h751be80779d42b53E (type 1) (param i32 i32)
    (local i32)
    global.get $__stack_pointer
    i32.const 32
    i32.sub
    local.tee 2
    global.set $__stack_pointer
    local.get 2
    i32.const 1
    i32.store8 offset=24
    local.get 2
    local.get 1
    i32.store offset=20
    local.get 2
    local.get 0
    i32.store offset=16
    local.get 2
    i32.const 1048648
    i32.store offset=12
    local.get 2
    i32.const 1048664
    i32.store offset=8
    local.get 2
    i32.const 8
    i32.add
    call $rust_begin_unwind
    unreachable)
  (func $rust_begin_unwind (type 2) (param i32)
    (local i32 i32)
    global.get $__stack_pointer
    i32.const 16
    i32.sub
    local.tee 1
    global.set $__stack_pointer
    block  ;; label = @1
      local.get 0
      i32.load offset=8
      local.tee 2
      br_if 0 (;@1;)
      call $_ZN4core9panicking5panic17h8af046397a2bf65dE
      unreachable
    end
    local.get 1
    local.get 0
    i32.load offset=12
    i32.store offset=8
    local.get 1
    local.get 0
    i32.store offset=4
    local.get 1
    local.get 2
    i32.store
    local.get 1
    call $_ZN3std10sys_common9backtrace26__rust_end_short_backtrace17h53cabafab5b09adaE
    unreachable)
  (func $_ZN4core3ptr102drop_in_place$LT$$RF$core..iter..adapters..copied..Copied$LT$core..slice..iter..Iter$LT$u8$GT$$GT$$GT$17h05fa0f971b46b0e7E (type 2) (param i32))
  (func $_ZN36_$LT$T$u20$as$u20$core..any..Any$GT$7type_id17h13c78596688f67b2E (type 3) (param i32) (result i64)
    i64.const -3679082246147160904)
  (func $_ZN4core9panicking5panic17h8af046397a2bf65dE (type 0)
    (local i32)
    global.get $__stack_pointer
    i32.const 32
    i32.sub
    local.tee 0
    global.set $__stack_pointer
    local.get 0
    i32.const 20
    i32.add
    i32.const 0
    i32.store
    local.get 0
    i32.const 1048664
    i32.store offset=16
    local.get 0
    i64.const 1
    i64.store offset=4 align=4
    local.get 0
    i32.const 43
    i32.store offset=28
    local.get 0
    i32.const 1048664
    i32.store offset=24
    local.get 0
    local.get 0
    i32.const 24
    i32.add
    i32.store
    local.get 0
    i32.const 1048736
    call $_ZN4core9panicking9panic_fmt17h751be80779d42b53E
    unreachable)
  (func $rust_panic (type 0)
    unreachable
    unreachable)
  (func $_ZN8dlmalloc8dlmalloc17Dlmalloc$LT$A$GT$6malloc17ha96fcefbb44d6da5E (type 4) (param i32) (result i32)
    (local i32 i32 i32 i32 i32 i32 i32 i32 i64)
    block  ;; label = @1
      block  ;; label = @2
        block  ;; label = @3
          block  ;; label = @4
            block  ;; label = @5
              local.get 0
              i32.const 245
              i32.lt_u
              br_if 0 (;@5;)
              i32.const 0
              local.set 1
              local.get 0
              i32.const -65587
              i32.ge_u
              br_if 4 (;@1;)
              local.get 0
              i32.const 11
              i32.add
              local.tee 0
              i32.const -8
              i32.and
              local.set 2
              i32.const 0
              i32.load offset=1048764
              local.tee 3
              i32.eqz
              br_if 3 (;@2;)
              i32.const 0
              local.set 4
              block  ;; label = @6
                local.get 2
                i32.const 256
                i32.lt_u
                br_if 0 (;@6;)
                i32.const 31
                local.set 4
                local.get 2
                i32.const 16777215
                i32.gt_u
                br_if 0 (;@6;)
                local.get 2
                i32.const 6
                local.get 0
                i32.const 8
                i32.shr_u
                i32.clz
                local.tee 0
                i32.sub
                i32.shr_u
                i32.const 1
                i32.and
                local.get 0
                i32.const 1
                i32.shl
                i32.sub
                i32.const 62
                i32.add
                local.set 4
              end
              i32.const 0
              local.get 2
              i32.sub
              local.set 1
              block  ;; label = @6
                local.get 4
                i32.const 2
                i32.shl
                i32.const 1049032
                i32.add
                i32.load
                local.tee 0
                i32.eqz
                br_if 0 (;@6;)
                i32.const 0
                local.set 5
                local.get 2
                i32.const 0
                i32.const 25
                local.get 4
                i32.const 1
                i32.shr_u
                i32.sub
                i32.const 31
                i32.and
                local.get 4
                i32.const 31
                i32.eq
                select
                i32.shl
                local.set 6
                i32.const 0
                local.set 7
                loop  ;; label = @7
                  block  ;; label = @8
                    local.get 0
                    i32.load offset=4
                    i32.const -8
                    i32.and
                    local.tee 8
                    local.get 2
                    i32.lt_u
                    br_if 0 (;@8;)
                    local.get 8
                    local.get 2
                    i32.sub
                    local.tee 8
                    local.get 1
                    i32.ge_u
                    br_if 0 (;@8;)
                    local.get 8
                    local.set 1
                    local.get 0
                    local.set 7
                    local.get 8
                    br_if 0 (;@8;)
                    i32.const 0
                    local.set 1
                    local.get 0
                    local.set 7
                    br 4 (;@4;)
                  end
                  local.get 0
                  i32.const 20
                  i32.add
                  i32.load
                  local.tee 8
                  local.get 5
                  local.get 8
                  local.get 0
                  local.get 6
                  i32.const 29
                  i32.shr_u
                  i32.const 4
                  i32.and
                  i32.add
                  i32.const 16
                  i32.add
                  i32.load
                  local.tee 0
                  i32.ne
                  select
                  local.get 5
                  local.get 8
                  select
                  local.set 5
                  local.get 6
                  i32.const 1
                  i32.shl
                  local.set 6
                  local.get 0
                  br_if 0 (;@7;)
                end
                block  ;; label = @7
                  local.get 5
                  i32.eqz
                  br_if 0 (;@7;)
                  local.get 5
                  local.set 0
                  br 3 (;@4;)
                end
                local.get 7
                br_if 3 (;@3;)
              end
              i32.const 0
              local.set 7
              local.get 3
              i32.const 2
              local.get 4
              i32.shl
              local.tee 0
              i32.const 0
              local.get 0
              i32.sub
              i32.or
              i32.and
              local.tee 0
              i32.eqz
              br_if 3 (;@2;)
              local.get 0
              i32.const 0
              local.get 0
              i32.sub
              i32.and
              i32.ctz
              i32.const 2
              i32.shl
              i32.const 1049032
              i32.add
              i32.load
              local.tee 0
              br_if 1 (;@4;)
              br 3 (;@2;)
            end
            block  ;; label = @5
              block  ;; label = @6
                block  ;; label = @7
                  block  ;; label = @8
                    block  ;; label = @9
                      i32.const 0
                      i32.load offset=1048760
                      local.tee 6
                      i32.const 16
                      local.get 0
                      i32.const 11
                      i32.add
                      i32.const -8
                      i32.and
                      local.get 0
                      i32.const 11
                      i32.lt_u
                      select
                      local.tee 2
                      i32.const 3
                      i32.shr_u
                      local.tee 1
                      i32.shr_u
                      local.tee 0
                      i32.const 3
                      i32.and
                      br_if 0 (;@9;)
                      local.get 2
                      i32.const 0
                      i32.load offset=1049160
                      i32.le_u
                      br_if 7 (;@2;)
                      local.get 0
                      br_if 1 (;@8;)
                      i32.const 0
                      i32.load offset=1048764
                      local.tee 0
                      i32.eqz
                      br_if 7 (;@2;)
                      local.get 0
                      i32.const 0
                      local.get 0
                      i32.sub
                      i32.and
                      i32.ctz
                      i32.const 2
                      i32.shl
                      i32.const 1049032
                      i32.add
                      i32.load
                      local.tee 7
                      i32.load offset=4
                      i32.const -8
                      i32.and
                      local.set 1
                      block  ;; label = @10
                        local.get 7
                        i32.load offset=16
                        local.tee 0
                        br_if 0 (;@10;)
                        local.get 7
                        i32.const 20
                        i32.add
                        i32.load
                        local.set 0
                      end
                      local.get 1
                      local.get 2
                      i32.sub
                      local.set 5
                      block  ;; label = @10
                        local.get 0
                        i32.eqz
                        br_if 0 (;@10;)
                        loop  ;; label = @11
                          local.get 0
                          i32.load offset=4
                          i32.const -8
                          i32.and
                          local.get 2
                          i32.sub
                          local.tee 8
                          local.get 5
                          i32.lt_u
                          local.set 6
                          block  ;; label = @12
                            local.get 0
                            i32.load offset=16
                            local.tee 1
                            br_if 0 (;@12;)
                            local.get 0
                            i32.const 20
                            i32.add
                            i32.load
                            local.set 1
                          end
                          local.get 8
                          local.get 5
                          local.get 6
                          select
                          local.set 5
                          local.get 0
                          local.get 7
                          local.get 6
                          select
                          local.set 7
                          local.get 1
                          local.set 0
                          local.get 1
                          br_if 0 (;@11;)
                        end
                      end
                      local.get 7
                      i32.load offset=24
                      local.set 4
                      local.get 7
                      i32.load offset=12
                      local.tee 1
                      local.get 7
                      i32.ne
                      br_if 2 (;@7;)
                      local.get 7
                      i32.const 20
                      i32.const 16
                      local.get 7
                      i32.const 20
                      i32.add
                      local.tee 1
                      i32.load
                      local.tee 6
                      select
                      i32.add
                      i32.load
                      local.tee 0
                      br_if 3 (;@6;)
                      i32.const 0
                      local.set 1
                      br 4 (;@5;)
                    end
                    block  ;; label = @9
                      block  ;; label = @10
                        local.get 0
                        i32.const -1
                        i32.xor
                        i32.const 1
                        i32.and
                        local.get 1
                        i32.add
                        local.tee 2
                        i32.const 3
                        i32.shl
                        local.tee 5
                        i32.const 1048776
                        i32.add
                        i32.load
                        local.tee 0
                        i32.const 8
                        i32.add
                        local.tee 7
                        i32.load
                        local.tee 1
                        local.get 5
                        i32.const 1048768
                        i32.add
                        local.tee 5
                        i32.eq
                        br_if 0 (;@10;)
                        local.get 1
                        local.get 5
                        i32.store offset=12
                        local.get 5
                        local.get 1
                        i32.store offset=8
                        br 1 (;@9;)
                      end
                      i32.const 0
                      local.get 6
                      i32.const -2
                      local.get 2
                      i32.rotl
                      i32.and
                      i32.store offset=1048760
                    end
                    local.get 0
                    local.get 2
                    i32.const 3
                    i32.shl
                    local.tee 2
                    i32.const 3
                    i32.or
                    i32.store offset=4
                    local.get 0
                    local.get 2
                    i32.add
                    local.tee 0
                    local.get 0
                    i32.load offset=4
                    i32.const 1
                    i32.or
                    i32.store offset=4
                    local.get 7
                    return
                  end
                  block  ;; label = @8
                    block  ;; label = @9
                      i32.const 2
                      local.get 1
                      i32.const 31
                      i32.and
                      local.tee 1
                      i32.shl
                      local.tee 5
                      i32.const 0
                      local.get 5
                      i32.sub
                      i32.or
                      local.get 0
                      local.get 1
                      i32.shl
                      i32.and
                      local.tee 0
                      i32.const 0
                      local.get 0
                      i32.sub
                      i32.and
                      i32.ctz
                      local.tee 1
                      i32.const 3
                      i32.shl
                      local.tee 7
                      i32.const 1048776
                      i32.add
                      i32.load
                      local.tee 0
                      i32.const 8
                      i32.add
                      local.tee 8
                      i32.load
                      local.tee 5
                      local.get 7
                      i32.const 1048768
                      i32.add
                      local.tee 7
                      i32.eq
                      br_if 0 (;@9;)
                      local.get 5
                      local.get 7
                      i32.store offset=12
                      local.get 7
                      local.get 5
                      i32.store offset=8
                      br 1 (;@8;)
                    end
                    i32.const 0
                    local.get 6
                    i32.const -2
                    local.get 1
                    i32.rotl
                    i32.and
                    i32.store offset=1048760
                  end
                  local.get 0
                  local.get 2
                  i32.const 3
                  i32.or
                  i32.store offset=4
                  local.get 0
                  local.get 2
                  i32.add
                  local.tee 6
                  local.get 1
                  i32.const 3
                  i32.shl
                  local.tee 1
                  local.get 2
                  i32.sub
                  local.tee 2
                  i32.const 1
                  i32.or
                  i32.store offset=4
                  local.get 0
                  local.get 1
                  i32.add
                  local.get 2
                  i32.store
                  block  ;; label = @8
                    i32.const 0
                    i32.load offset=1049160
                    local.tee 5
                    i32.eqz
                    br_if 0 (;@8;)
                    local.get 5
                    i32.const -8
                    i32.and
                    i32.const 1048768
                    i32.add
                    local.set 1
                    i32.const 0
                    i32.load offset=1049168
                    local.set 0
                    block  ;; label = @9
                      block  ;; label = @10
                        i32.const 0
                        i32.load offset=1048760
                        local.tee 7
                        i32.const 1
                        local.get 5
                        i32.const 3
                        i32.shr_u
                        i32.shl
                        local.tee 5
                        i32.and
                        i32.eqz
                        br_if 0 (;@10;)
                        local.get 1
                        i32.load offset=8
                        local.set 5
                        br 1 (;@9;)
                      end
                      i32.const 0
                      local.get 7
                      local.get 5
                      i32.or
                      i32.store offset=1048760
                      local.get 1
                      local.set 5
                    end
                    local.get 1
                    local.get 0
                    i32.store offset=8
                    local.get 5
                    local.get 0
                    i32.store offset=12
                    local.get 0
                    local.get 1
                    i32.store offset=12
                    local.get 0
                    local.get 5
                    i32.store offset=8
                  end
                  i32.const 0
                  local.get 6
                  i32.store offset=1049168
                  i32.const 0
                  local.get 2
                  i32.store offset=1049160
                  local.get 8
                  return
                end
                local.get 7
                i32.load offset=8
                local.tee 0
                local.get 1
                i32.store offset=12
                local.get 1
                local.get 0
                i32.store offset=8
                br 1 (;@5;)
              end
              local.get 1
              local.get 7
              i32.const 16
              i32.add
              local.get 6
              select
              local.set 6
              loop  ;; label = @6
                local.get 6
                local.set 8
                block  ;; label = @7
                  local.get 0
                  local.tee 1
                  i32.const 20
                  i32.add
                  local.tee 6
                  i32.load
                  local.tee 0
                  br_if 0 (;@7;)
                  local.get 1
                  i32.const 16
                  i32.add
                  local.set 6
                  local.get 1
                  i32.load offset=16
                  local.set 0
                end
                local.get 0
                br_if 0 (;@6;)
              end
              local.get 8
              i32.const 0
              i32.store
            end
            block  ;; label = @5
              local.get 4
              i32.eqz
              br_if 0 (;@5;)
              block  ;; label = @6
                block  ;; label = @7
                  local.get 7
                  i32.load offset=28
                  i32.const 2
                  i32.shl
                  i32.const 1049032
                  i32.add
                  local.tee 0
                  i32.load
                  local.get 7
                  i32.eq
                  br_if 0 (;@7;)
                  local.get 4
                  i32.const 16
                  i32.const 20
                  local.get 4
                  i32.load offset=16
                  local.get 7
                  i32.eq
                  select
                  i32.add
                  local.get 1
                  i32.store
                  local.get 1
                  i32.eqz
                  br_if 2 (;@5;)
                  br 1 (;@6;)
                end
                local.get 0
                local.get 1
                i32.store
                local.get 1
                br_if 0 (;@6;)
                i32.const 0
                i32.const 0
                i32.load offset=1048764
                i32.const -2
                local.get 7
                i32.load offset=28
                i32.rotl
                i32.and
                i32.store offset=1048764
                br 1 (;@5;)
              end
              local.get 1
              local.get 4
              i32.store offset=24
              block  ;; label = @6
                local.get 7
                i32.load offset=16
                local.tee 0
                i32.eqz
                br_if 0 (;@6;)
                local.get 1
                local.get 0
                i32.store offset=16
                local.get 0
                local.get 1
                i32.store offset=24
              end
              local.get 7
              i32.const 20
              i32.add
              i32.load
              local.tee 0
              i32.eqz
              br_if 0 (;@5;)
              local.get 1
              i32.const 20
              i32.add
              local.get 0
              i32.store
              local.get 0
              local.get 1
              i32.store offset=24
            end
            block  ;; label = @5
              block  ;; label = @6
                local.get 5
                i32.const 16
                i32.lt_u
                br_if 0 (;@6;)
                local.get 7
                local.get 2
                i32.const 3
                i32.or
                i32.store offset=4
                local.get 7
                local.get 2
                i32.add
                local.tee 2
                local.get 5
                i32.const 1
                i32.or
                i32.store offset=4
                local.get 2
                local.get 5
                i32.add
                local.get 5
                i32.store
                block  ;; label = @7
                  i32.const 0
                  i32.load offset=1049160
                  local.tee 6
                  i32.eqz
                  br_if 0 (;@7;)
                  local.get 6
                  i32.const -8
                  i32.and
                  i32.const 1048768
                  i32.add
                  local.set 1
                  i32.const 0
                  i32.load offset=1049168
                  local.set 0
                  block  ;; label = @8
                    block  ;; label = @9
                      i32.const 0
                      i32.load offset=1048760
                      local.tee 8
                      i32.const 1
                      local.get 6
                      i32.const 3
                      i32.shr_u
                      i32.shl
                      local.tee 6
                      i32.and
                      i32.eqz
                      br_if 0 (;@9;)
                      local.get 1
                      i32.load offset=8
                      local.set 6
                      br 1 (;@8;)
                    end
                    i32.const 0
                    local.get 8
                    local.get 6
                    i32.or
                    i32.store offset=1048760
                    local.get 1
                    local.set 6
                  end
                  local.get 1
                  local.get 0
                  i32.store offset=8
                  local.get 6
                  local.get 0
                  i32.store offset=12
                  local.get 0
                  local.get 1
                  i32.store offset=12
                  local.get 0
                  local.get 6
                  i32.store offset=8
                end
                i32.const 0
                local.get 2
                i32.store offset=1049168
                i32.const 0
                local.get 5
                i32.store offset=1049160
                br 1 (;@5;)
              end
              local.get 7
              local.get 5
              local.get 2
              i32.add
              local.tee 0
              i32.const 3
              i32.or
              i32.store offset=4
              local.get 7
              local.get 0
              i32.add
              local.tee 0
              local.get 0
              i32.load offset=4
              i32.const 1
              i32.or
              i32.store offset=4
            end
            local.get 7
            i32.const 8
            i32.add
            return
          end
          loop  ;; label = @4
            local.get 0
            i32.load offset=4
            i32.const -8
            i32.and
            local.tee 5
            local.get 2
            i32.ge_u
            local.get 5
            local.get 2
            i32.sub
            local.tee 8
            local.get 1
            i32.lt_u
            i32.and
            local.set 6
            block  ;; label = @5
              local.get 0
              i32.load offset=16
              local.tee 5
              br_if 0 (;@5;)
              local.get 0
              i32.const 20
              i32.add
              i32.load
              local.set 5
            end
            local.get 0
            local.get 7
            local.get 6
            select
            local.set 7
            local.get 8
            local.get 1
            local.get 6
            select
            local.set 1
            local.get 5
            local.set 0
            local.get 5
            br_if 0 (;@4;)
          end
          local.get 7
          i32.eqz
          br_if 1 (;@2;)
        end
        block  ;; label = @3
          i32.const 0
          i32.load offset=1049160
          local.tee 0
          local.get 2
          i32.lt_u
          br_if 0 (;@3;)
          local.get 1
          local.get 0
          local.get 2
          i32.sub
          i32.ge_u
          br_if 1 (;@2;)
        end
        local.get 7
        i32.load offset=24
        local.set 4
        block  ;; label = @3
          block  ;; label = @4
            block  ;; label = @5
              local.get 7
              i32.load offset=12
              local.tee 5
              local.get 7
              i32.ne
              br_if 0 (;@5;)
              local.get 7
              i32.const 20
              i32.const 16
              local.get 7
              i32.const 20
              i32.add
              local.tee 5
              i32.load
              local.tee 6
              select
              i32.add
              i32.load
              local.tee 0
              br_if 1 (;@4;)
              i32.const 0
              local.set 5
              br 2 (;@3;)
            end
            local.get 7
            i32.load offset=8
            local.tee 0
            local.get 5
            i32.store offset=12
            local.get 5
            local.get 0
            i32.store offset=8
            br 1 (;@3;)
          end
          local.get 5
          local.get 7
          i32.const 16
          i32.add
          local.get 6
          select
          local.set 6
          loop  ;; label = @4
            local.get 6
            local.set 8
            block  ;; label = @5
              local.get 0
              local.tee 5
              i32.const 20
              i32.add
              local.tee 6
              i32.load
              local.tee 0
              br_if 0 (;@5;)
              local.get 5
              i32.const 16
              i32.add
              local.set 6
              local.get 5
              i32.load offset=16
              local.set 0
            end
            local.get 0
            br_if 0 (;@4;)
          end
          local.get 8
          i32.const 0
          i32.store
        end
        block  ;; label = @3
          local.get 4
          i32.eqz
          br_if 0 (;@3;)
          block  ;; label = @4
            block  ;; label = @5
              local.get 7
              i32.load offset=28
              i32.const 2
              i32.shl
              i32.const 1049032
              i32.add
              local.tee 0
              i32.load
              local.get 7
              i32.eq
              br_if 0 (;@5;)
              local.get 4
              i32.const 16
              i32.const 20
              local.get 4
              i32.load offset=16
              local.get 7
              i32.eq
              select
              i32.add
              local.get 5
              i32.store
              local.get 5
              i32.eqz
              br_if 2 (;@3;)
              br 1 (;@4;)
            end
            local.get 0
            local.get 5
            i32.store
            local.get 5
            br_if 0 (;@4;)
            i32.const 0
            i32.const 0
            i32.load offset=1048764
            i32.const -2
            local.get 7
            i32.load offset=28
            i32.rotl
            i32.and
            i32.store offset=1048764
            br 1 (;@3;)
          end
          local.get 5
          local.get 4
          i32.store offset=24
          block  ;; label = @4
            local.get 7
            i32.load offset=16
            local.tee 0
            i32.eqz
            br_if 0 (;@4;)
            local.get 5
            local.get 0
            i32.store offset=16
            local.get 0
            local.get 5
            i32.store offset=24
          end
          local.get 7
          i32.const 20
          i32.add
          i32.load
          local.tee 0
          i32.eqz
          br_if 0 (;@3;)
          local.get 5
          i32.const 20
          i32.add
          local.get 0
          i32.store
          local.get 0
          local.get 5
          i32.store offset=24
        end
        block  ;; label = @3
          block  ;; label = @4
            local.get 1
            i32.const 16
            i32.lt_u
            br_if 0 (;@4;)
            local.get 7
            local.get 2
            i32.const 3
            i32.or
            i32.store offset=4
            local.get 7
            local.get 2
            i32.add
            local.tee 0
            local.get 1
            i32.const 1
            i32.or
            i32.store offset=4
            local.get 0
            local.get 1
            i32.add
            local.get 1
            i32.store
            block  ;; label = @5
              local.get 1
              i32.const 256
              i32.lt_u
              br_if 0 (;@5;)
              local.get 0
              local.get 1
              call $_ZN8dlmalloc8dlmalloc17Dlmalloc$LT$A$GT$18insert_large_chunk17hb12990f92538fbbfE
              br 2 (;@3;)
            end
            local.get 1
            i32.const -8
            i32.and
            i32.const 1048768
            i32.add
            local.set 2
            block  ;; label = @5
              block  ;; label = @6
                i32.const 0
                i32.load offset=1048760
                local.tee 5
                i32.const 1
                local.get 1
                i32.const 3
                i32.shr_u
                i32.shl
                local.tee 1
                i32.and
                i32.eqz
                br_if 0 (;@6;)
                local.get 2
                i32.load offset=8
                local.set 1
                br 1 (;@5;)
              end
              i32.const 0
              local.get 5
              local.get 1
              i32.or
              i32.store offset=1048760
              local.get 2
              local.set 1
            end
            local.get 2
            local.get 0
            i32.store offset=8
            local.get 1
            local.get 0
            i32.store offset=12
            local.get 0
            local.get 2
            i32.store offset=12
            local.get 0
            local.get 1
            i32.store offset=8
            br 1 (;@3;)
          end
          local.get 7
          local.get 1
          local.get 2
          i32.add
          local.tee 0
          i32.const 3
          i32.or
          i32.store offset=4
          local.get 7
          local.get 0
          i32.add
          local.tee 0
          local.get 0
          i32.load offset=4
          i32.const 1
          i32.or
          i32.store offset=4
        end
        local.get 7
        i32.const 8
        i32.add
        return
      end
      block  ;; label = @2
        block  ;; label = @3
          block  ;; label = @4
            block  ;; label = @5
              block  ;; label = @6
                block  ;; label = @7
                  block  ;; label = @8
                    block  ;; label = @9
                      block  ;; label = @10
                        block  ;; label = @11
                          block  ;; label = @12
                            block  ;; label = @13
                              i32.const 0
                              i32.load offset=1049160
                              local.tee 0
                              local.get 2
                              i32.ge_u
                              br_if 0 (;@13;)
                              i32.const 0
                              i32.load offset=1049164
                              local.tee 0
                              local.get 2
                              i32.gt_u
                              br_if 4 (;@9;)
                              i32.const 0
                              local.set 1
                              local.get 2
                              i32.const 65583
                              i32.add
                              local.tee 5
                              i32.const 16
                              i32.shr_u
                              memory.grow
                              local.tee 0
                              i32.const -1
                              i32.eq
                              local.tee 7
                              br_if 12 (;@1;)
                              local.get 0
                              i32.const 16
                              i32.shl
                              local.tee 6
                              i32.eqz
                              br_if 12 (;@1;)
                              i32.const 0
                              i32.const 0
                              i32.load offset=1049176
                              i32.const 0
                              local.get 5
                              i32.const -65536
                              i32.and
                              local.get 7
                              select
                              local.tee 8
                              i32.add
                              local.tee 0
                              i32.store offset=1049176
                              i32.const 0
                              i32.const 0
                              i32.load offset=1049180
                              local.tee 1
                              local.get 0
                              local.get 1
                              local.get 0
                              i32.gt_u
                              select
                              i32.store offset=1049180
                              i32.const 0
                              i32.load offset=1049172
                              local.tee 1
                              i32.eqz
                              br_if 1 (;@12;)
                              i32.const 1049184
                              local.set 0
                              loop  ;; label = @14
                                local.get 0
                                i32.load
                                local.tee 5
                                local.get 0
                                i32.load offset=4
                                local.tee 7
                                i32.add
                                local.get 6
                                i32.eq
                                br_if 3 (;@11;)
                                local.get 0
                                i32.load offset=8
                                local.tee 0
                                br_if 0 (;@14;)
                                br 4 (;@10;)
                              end
                            end
                            i32.const 0
                            i32.load offset=1049168
                            local.set 1
                            block  ;; label = @13
                              block  ;; label = @14
                                local.get 0
                                local.get 2
                                i32.sub
                                local.tee 5
                                i32.const 15
                                i32.gt_u
                                br_if 0 (;@14;)
                                i32.const 0
                                i32.const 0
                                i32.store offset=1049168
                                i32.const 0
                                i32.const 0
                                i32.store offset=1049160
                                local.get 1
                                local.get 0
                                i32.const 3
                                i32.or
                                i32.store offset=4
                                local.get 1
                                local.get 0
                                i32.add
                                local.tee 0
                                local.get 0
                                i32.load offset=4
                                i32.const 1
                                i32.or
                                i32.store offset=4
                                br 1 (;@13;)
                              end
                              i32.const 0
                              local.get 5
                              i32.store offset=1049160
                              i32.const 0
                              local.get 1
                              local.get 2
                              i32.add
                              local.tee 6
                              i32.store offset=1049168
                              local.get 6
                              local.get 5
                              i32.const 1
                              i32.or
                              i32.store offset=4
                              local.get 1
                              local.get 0
                              i32.add
                              local.get 5
                              i32.store
                              local.get 1
                              local.get 2
                              i32.const 3
                              i32.or
                              i32.store offset=4
                            end
                            local.get 1
                            i32.const 8
                            i32.add
                            return
                          end
                          i32.const 0
                          i32.load offset=1049204
                          local.tee 0
                          i32.eqz
                          br_if 3 (;@8;)
                          local.get 0
                          local.get 6
                          i32.gt_u
                          br_if 3 (;@8;)
                          br 8 (;@3;)
                        end
                        local.get 0
                        i32.load offset=12
                        br_if 0 (;@10;)
                        local.get 5
                        local.get 1
                        i32.gt_u
                        br_if 0 (;@10;)
                        local.get 1
                        local.get 6
                        i32.lt_u
                        br_if 3 (;@7;)
                      end
                      i32.const 0
                      i32.const 0
                      i32.load offset=1049204
                      local.tee 0
                      local.get 6
                      local.get 0
                      local.get 6
                      i32.lt_u
                      select
                      i32.store offset=1049204
                      local.get 6
                      local.get 8
                      i32.add
                      local.set 5
                      i32.const 1049184
                      local.set 0
                      block  ;; label = @10
                        block  ;; label = @11
                          block  ;; label = @12
                            loop  ;; label = @13
                              local.get 0
                              i32.load
                              local.get 5
                              i32.eq
                              br_if 1 (;@12;)
                              local.get 0
                              i32.load offset=8
                              local.tee 0
                              br_if 0 (;@13;)
                              br 2 (;@11;)
                            end
                          end
                          local.get 0
                          i32.load offset=12
                          i32.eqz
                          br_if 1 (;@10;)
                        end
                        i32.const 1049184
                        local.set 0
                        block  ;; label = @11
                          loop  ;; label = @12
                            block  ;; label = @13
                              local.get 0
                              i32.load
                              local.tee 5
                              local.get 1
                              i32.gt_u
                              br_if 0 (;@13;)
                              local.get 5
                              local.get 0
                              i32.load offset=4
                              i32.add
                              local.tee 5
                              local.get 1
                              i32.gt_u
                              br_if 2 (;@11;)
                            end
                            local.get 0
                            i32.load offset=8
                            local.set 0
                            br 0 (;@12;)
                          end
                        end
                        i32.const 0
                        local.get 6
                        i32.store offset=1049172
                        i32.const 0
                        local.get 8
                        i32.const -40
                        i32.add
                        local.tee 0
                        i32.store offset=1049164
                        local.get 6
                        local.get 0
                        i32.const 1
                        i32.or
                        i32.store offset=4
                        local.get 6
                        local.get 0
                        i32.add
                        i32.const 40
                        i32.store offset=4
                        i32.const 0
                        i32.const 2097152
                        i32.store offset=1049200
                        local.get 1
                        local.get 5
                        i32.const -32
                        i32.add
                        i32.const -8
                        i32.and
                        i32.const -8
                        i32.add
                        local.tee 0
                        local.get 0
                        local.get 1
                        i32.const 16
                        i32.add
                        i32.lt_u
                        select
                        local.tee 7
                        i32.const 27
                        i32.store offset=4
                        i32.const 0
                        i64.load offset=1049184 align=4
                        local.set 9
                        local.get 7
                        i32.const 16
                        i32.add
                        i32.const 0
                        i64.load offset=1049192 align=4
                        i64.store align=4
                        local.get 7
                        local.get 9
                        i64.store offset=8 align=4
                        i32.const 0
                        local.get 8
                        i32.store offset=1049188
                        i32.const 0
                        local.get 6
                        i32.store offset=1049184
                        i32.const 0
                        local.get 7
                        i32.const 8
                        i32.add
                        i32.store offset=1049192
                        i32.const 0
                        i32.const 0
                        i32.store offset=1049196
                        local.get 7
                        i32.const 28
                        i32.add
                        local.set 0
                        loop  ;; label = @11
                          local.get 0
                          i32.const 7
                          i32.store
                          local.get 0
                          i32.const 4
                          i32.add
                          local.tee 0
                          local.get 5
                          i32.lt_u
                          br_if 0 (;@11;)
                        end
                        local.get 7
                        local.get 1
                        i32.eq
                        br_if 8 (;@2;)
                        local.get 7
                        local.get 7
                        i32.load offset=4
                        i32.const -2
                        i32.and
                        i32.store offset=4
                        local.get 1
                        local.get 7
                        local.get 1
                        i32.sub
                        local.tee 0
                        i32.const 1
                        i32.or
                        i32.store offset=4
                        local.get 7
                        local.get 0
                        i32.store
                        block  ;; label = @11
                          local.get 0
                          i32.const 256
                          i32.lt_u
                          br_if 0 (;@11;)
                          local.get 1
                          local.get 0
                          call $_ZN8dlmalloc8dlmalloc17Dlmalloc$LT$A$GT$18insert_large_chunk17hb12990f92538fbbfE
                          br 9 (;@2;)
                        end
                        local.get 0
                        i32.const -8
                        i32.and
                        i32.const 1048768
                        i32.add
                        local.set 5
                        block  ;; label = @11
                          block  ;; label = @12
                            i32.const 0
                            i32.load offset=1048760
                            local.tee 6
                            i32.const 1
                            local.get 0
                            i32.const 3
                            i32.shr_u
                            i32.shl
                            local.tee 0
                            i32.and
                            i32.eqz
                            br_if 0 (;@12;)
                            local.get 5
                            i32.load offset=8
                            local.set 0
                            br 1 (;@11;)
                          end
                          i32.const 0
                          local.get 6
                          local.get 0
                          i32.or
                          i32.store offset=1048760
                          local.get 5
                          local.set 0
                        end
                        local.get 5
                        local.get 1
                        i32.store offset=8
                        local.get 0
                        local.get 1
                        i32.store offset=12
                        local.get 1
                        local.get 5
                        i32.store offset=12
                        local.get 1
                        local.get 0
                        i32.store offset=8
                        br 8 (;@2;)
                      end
                      local.get 0
                      local.get 6
                      i32.store
                      local.get 0
                      local.get 0
                      i32.load offset=4
                      local.get 8
                      i32.add
                      i32.store offset=4
                      local.get 6
                      local.get 2
                      i32.const 3
                      i32.or
                      i32.store offset=4
                      local.get 5
                      local.get 6
                      local.get 2
                      i32.add
                      local.tee 0
                      i32.sub
                      local.set 2
                      block  ;; label = @10
                        local.get 5
                        i32.const 0
                        i32.load offset=1049172
                        i32.eq
                        br_if 0 (;@10;)
                        local.get 5
                        i32.const 0
                        i32.load offset=1049168
                        i32.eq
                        br_if 4 (;@6;)
                        local.get 5
                        i32.load offset=4
                        local.tee 1
                        i32.const 3
                        i32.and
                        i32.const 1
                        i32.ne
                        br_if 5 (;@5;)
                        block  ;; label = @11
                          block  ;; label = @12
                            local.get 1
                            i32.const -8
                            i32.and
                            local.tee 7
                            i32.const 256
                            i32.lt_u
                            br_if 0 (;@12;)
                            local.get 5
                            call $_ZN8dlmalloc8dlmalloc17Dlmalloc$LT$A$GT$18unlink_large_chunk17hbe8d36a9f4060ceeE
                            br 1 (;@11;)
                          end
                          block  ;; label = @12
                            local.get 5
                            i32.const 12
                            i32.add
                            i32.load
                            local.tee 8
                            local.get 5
                            i32.const 8
                            i32.add
                            i32.load
                            local.tee 4
                            i32.eq
                            br_if 0 (;@12;)
                            local.get 4
                            local.get 8
                            i32.store offset=12
                            local.get 8
                            local.get 4
                            i32.store offset=8
                            br 1 (;@11;)
                          end
                          i32.const 0
                          i32.const 0
                          i32.load offset=1048760
                          i32.const -2
                          local.get 1
                          i32.const 3
                          i32.shr_u
                          i32.rotl
                          i32.and
                          i32.store offset=1048760
                        end
                        local.get 7
                        local.get 2
                        i32.add
                        local.set 2
                        local.get 5
                        local.get 7
                        i32.add
                        local.tee 5
                        i32.load offset=4
                        local.set 1
                        br 5 (;@5;)
                      end
                      i32.const 0
                      local.get 0
                      i32.store offset=1049172
                      i32.const 0
                      i32.const 0
                      i32.load offset=1049164
                      local.get 2
                      i32.add
                      local.tee 2
                      i32.store offset=1049164
                      local.get 0
                      local.get 2
                      i32.const 1
                      i32.or
                      i32.store offset=4
                      br 5 (;@4;)
                    end
                    i32.const 0
                    local.get 0
                    local.get 2
                    i32.sub
                    local.tee 1
                    i32.store offset=1049164
                    i32.const 0
                    i32.const 0
                    i32.load offset=1049172
                    local.tee 0
                    local.get 2
                    i32.add
                    local.tee 5
                    i32.store offset=1049172
                    local.get 5
                    local.get 1
                    i32.const 1
                    i32.or
                    i32.store offset=4
                    local.get 0
                    local.get 2
                    i32.const 3
                    i32.or
                    i32.store offset=4
                    local.get 0
                    i32.const 8
                    i32.add
                    local.set 1
                    br 7 (;@1;)
                  end
                  i32.const 0
                  local.get 6
                  i32.store offset=1049204
                  br 4 (;@3;)
                end
                local.get 0
                local.get 7
                local.get 8
                i32.add
                i32.store offset=4
                i32.const 0
                i32.const 0
                i32.load offset=1049172
                local.tee 0
                i32.const 15
                i32.add
                i32.const -8
                i32.and
                local.tee 1
                i32.const -8
                i32.add
                i32.store offset=1049172
                i32.const 0
                local.get 0
                local.get 1
                i32.sub
                i32.const 0
                i32.load offset=1049164
                local.get 8
                i32.add
                local.tee 5
                i32.add
                i32.const 8
                i32.add
                local.tee 6
                i32.store offset=1049164
                local.get 1
                i32.const -4
                i32.add
                local.get 6
                i32.const 1
                i32.or
                i32.store
                local.get 0
                local.get 5
                i32.add
                i32.const 40
                i32.store offset=4
                i32.const 0
                i32.const 2097152
                i32.store offset=1049200
                br 4 (;@2;)
              end
              i32.const 0
              local.get 0
              i32.store offset=1049168
              i32.const 0
              i32.const 0
              i32.load offset=1049160
              local.get 2
              i32.add
              local.tee 2
              i32.store offset=1049160
              local.get 0
              local.get 2
              i32.const 1
              i32.or
              i32.store offset=4
              local.get 0
              local.get 2
              i32.add
              local.get 2
              i32.store
              br 1 (;@4;)
            end
            local.get 5
            local.get 1
            i32.const -2
            i32.and
            i32.store offset=4
            local.get 0
            local.get 2
            i32.const 1
            i32.or
            i32.store offset=4
            local.get 0
            local.get 2
            i32.add
            local.get 2
            i32.store
            block  ;; label = @5
              local.get 2
              i32.const 256
              i32.lt_u
              br_if 0 (;@5;)
              local.get 0
              local.get 2
              call $_ZN8dlmalloc8dlmalloc17Dlmalloc$LT$A$GT$18insert_large_chunk17hb12990f92538fbbfE
              br 1 (;@4;)
            end
            local.get 2
            i32.const -8
            i32.and
            i32.const 1048768
            i32.add
            local.set 1
            block  ;; label = @5
              block  ;; label = @6
                i32.const 0
                i32.load offset=1048760
                local.tee 5
                i32.const 1
                local.get 2
                i32.const 3
                i32.shr_u
                i32.shl
                local.tee 2
                i32.and
                i32.eqz
                br_if 0 (;@6;)
                local.get 1
                i32.load offset=8
                local.set 2
                br 1 (;@5;)
              end
              i32.const 0
              local.get 5
              local.get 2
              i32.or
              i32.store offset=1048760
              local.get 1
              local.set 2
            end
            local.get 1
            local.get 0
            i32.store offset=8
            local.get 2
            local.get 0
            i32.store offset=12
            local.get 0
            local.get 1
            i32.store offset=12
            local.get 0
            local.get 2
            i32.store offset=8
          end
          local.get 6
          i32.const 8
          i32.add
          return
        end
        i32.const 0
        i32.const 4095
        i32.store offset=1049208
        i32.const 0
        local.get 8
        i32.store offset=1049188
        i32.const 0
        local.get 6
        i32.store offset=1049184
        i32.const 0
        i32.const 1048768
        i32.store offset=1048780
        i32.const 0
        i32.const 1048776
        i32.store offset=1048788
        i32.const 0
        i32.const 1048768
        i32.store offset=1048776
        i32.const 0
        i32.const 1048784
        i32.store offset=1048796
        i32.const 0
        i32.const 1048776
        i32.store offset=1048784
        i32.const 0
        i32.const 1048792
        i32.store offset=1048804
        i32.const 0
        i32.const 1048784
        i32.store offset=1048792
        i32.const 0
        i32.const 1048800
        i32.store offset=1048812
        i32.const 0
        i32.const 1048792
        i32.store offset=1048800
        i32.const 0
        i32.const 1048808
        i32.store offset=1048820
        i32.const 0
        i32.const 1048800
        i32.store offset=1048808
        i32.const 0
        i32.const 1048816
        i32.store offset=1048828
        i32.const 0
        i32.const 1048808
        i32.store offset=1048816
        i32.const 0
        i32.const 1048824
        i32.store offset=1048836
        i32.const 0
        i32.const 1048816
        i32.store offset=1048824
        i32.const 0
        i32.const 0
        i32.store offset=1049196
        i32.const 0
        i32.const 1048832
        i32.store offset=1048844
        i32.const 0
        i32.const 1048824
        i32.store offset=1048832
        i32.const 0
        i32.const 1048832
        i32.store offset=1048840
        i32.const 0
        i32.const 1048840
        i32.store offset=1048852
        i32.const 0
        i32.const 1048840
        i32.store offset=1048848
        i32.const 0
        i32.const 1048848
        i32.store offset=1048860
        i32.const 0
        i32.const 1048848
        i32.store offset=1048856
        i32.const 0
        i32.const 1048856
        i32.store offset=1048868
        i32.const 0
        i32.const 1048856
        i32.store offset=1048864
        i32.const 0
        i32.const 1048864
        i32.store offset=1048876
        i32.const 0
        i32.const 1048864
        i32.store offset=1048872
        i32.const 0
        i32.const 1048872
        i32.store offset=1048884
        i32.const 0
        i32.const 1048872
        i32.store offset=1048880
        i32.const 0
        i32.const 1048880
        i32.store offset=1048892
        i32.const 0
        i32.const 1048880
        i32.store offset=1048888
        i32.const 0
        i32.const 1048888
        i32.store offset=1048900
        i32.const 0
        i32.const 1048888
        i32.store offset=1048896
        i32.const 0
        i32.const 1048896
        i32.store offset=1048908
        i32.const 0
        i32.const 1048904
        i32.store offset=1048916
        i32.const 0
        i32.const 1048896
        i32.store offset=1048904
        i32.const 0
        i32.const 1048912
        i32.store offset=1048924
        i32.const 0
        i32.const 1048904
        i32.store offset=1048912
        i32.const 0
        i32.const 1048920
        i32.store offset=1048932
        i32.const 0
        i32.const 1048912
        i32.store offset=1048920
        i32.const 0
        i32.const 1048928
        i32.store offset=1048940
        i32.const 0
        i32.const 1048920
        i32.store offset=1048928
        i32.const 0
        i32.const 1048936
        i32.store offset=1048948
        i32.const 0
        i32.const 1048928
        i32.store offset=1048936
        i32.const 0
        i32.const 1048944
        i32.store offset=1048956
        i32.const 0
        i32.const 1048936
        i32.store offset=1048944
        i32.const 0
        i32.const 1048952
        i32.store offset=1048964
        i32.const 0
        i32.const 1048944
        i32.store offset=1048952
        i32.const 0
        i32.const 1048960
        i32.store offset=1048972
        i32.const 0
        i32.const 1048952
        i32.store offset=1048960
        i32.const 0
        i32.const 1048968
        i32.store offset=1048980
        i32.const 0
        i32.const 1048960
        i32.store offset=1048968
        i32.const 0
        i32.const 1048976
        i32.store offset=1048988
        i32.const 0
        i32.const 1048968
        i32.store offset=1048976
        i32.const 0
        i32.const 1048984
        i32.store offset=1048996
        i32.const 0
        i32.const 1048976
        i32.store offset=1048984
        i32.const 0
        i32.const 1048992
        i32.store offset=1049004
        i32.const 0
        i32.const 1048984
        i32.store offset=1048992
        i32.const 0
        i32.const 1049000
        i32.store offset=1049012
        i32.const 0
        i32.const 1048992
        i32.store offset=1049000
        i32.const 0
        i32.const 1049008
        i32.store offset=1049020
        i32.const 0
        i32.const 1049000
        i32.store offset=1049008
        i32.const 0
        i32.const 1049016
        i32.store offset=1049028
        i32.const 0
        i32.const 1049008
        i32.store offset=1049016
        i32.const 0
        local.get 6
        i32.store offset=1049172
        i32.const 0
        i32.const 1049016
        i32.store offset=1049024
        i32.const 0
        local.get 8
        i32.const -40
        i32.add
        local.tee 0
        i32.store offset=1049164
        local.get 6
        local.get 0
        i32.const 1
        i32.or
        i32.store offset=4
        local.get 6
        local.get 0
        i32.add
        i32.const 40
        i32.store offset=4
        i32.const 0
        i32.const 2097152
        i32.store offset=1049200
      end
      i32.const 0
      local.set 1
      i32.const 0
      i32.load offset=1049164
      local.tee 0
      local.get 2
      i32.le_u
      br_if 0 (;@1;)
      i32.const 0
      local.get 0
      local.get 2
      i32.sub
      local.tee 1
      i32.store offset=1049164
      i32.const 0
      i32.const 0
      i32.load offset=1049172
      local.tee 0
      local.get 2
      i32.add
      local.tee 5
      i32.store offset=1049172
      local.get 5
      local.get 1
      i32.const 1
      i32.or
      i32.store offset=4
      local.get 0
      local.get 2
      i32.const 3
      i32.or
      i32.store offset=4
      local.get 0
      i32.const 8
      i32.add
      return
    end
    local.get 1)
  (func $_ZN8dlmalloc8dlmalloc17Dlmalloc$LT$A$GT$18insert_large_chunk17hb12990f92538fbbfE (type 1) (param i32 i32)
    (local i32 i32 i32 i32)
    i32.const 31
    local.set 2
    block  ;; label = @1
      local.get 1
      i32.const 16777215
      i32.gt_u
      br_if 0 (;@1;)
      local.get 1
      i32.const 6
      local.get 1
      i32.const 8
      i32.shr_u
      i32.clz
      local.tee 2
      i32.sub
      i32.shr_u
      i32.const 1
      i32.and
      local.get 2
      i32.const 1
      i32.shl
      i32.sub
      i32.const 62
      i32.add
      local.set 2
    end
    local.get 0
    i64.const 0
    i64.store offset=16 align=4
    local.get 0
    local.get 2
    i32.store offset=28
    local.get 2
    i32.const 2
    i32.shl
    i32.const 1049032
    i32.add
    local.set 3
    block  ;; label = @1
      block  ;; label = @2
        block  ;; label = @3
          block  ;; label = @4
            block  ;; label = @5
              i32.const 0
              i32.load offset=1048764
              local.tee 4
              i32.const 1
              local.get 2
              i32.shl
              local.tee 5
              i32.and
              i32.eqz
              br_if 0 (;@5;)
              local.get 3
              i32.load
              local.tee 4
              i32.load offset=4
              i32.const -8
              i32.and
              local.get 1
              i32.ne
              br_if 1 (;@4;)
              local.get 4
              local.set 2
              br 2 (;@3;)
            end
            i32.const 0
            local.get 4
            local.get 5
            i32.or
            i32.store offset=1048764
            local.get 3
            local.get 0
            i32.store
            local.get 0
            local.get 3
            i32.store offset=24
            br 3 (;@1;)
          end
          local.get 1
          i32.const 0
          i32.const 25
          local.get 2
          i32.const 1
          i32.shr_u
          i32.sub
          i32.const 31
          i32.and
          local.get 2
          i32.const 31
          i32.eq
          select
          i32.shl
          local.set 3
          loop  ;; label = @4
            local.get 4
            local.get 3
            i32.const 29
            i32.shr_u
            i32.const 4
            i32.and
            i32.add
            i32.const 16
            i32.add
            local.tee 5
            i32.load
            local.tee 2
            i32.eqz
            br_if 2 (;@2;)
            local.get 3
            i32.const 1
            i32.shl
            local.set 3
            local.get 2
            local.set 4
            local.get 2
            i32.load offset=4
            i32.const -8
            i32.and
            local.get 1
            i32.ne
            br_if 0 (;@4;)
          end
        end
        local.get 2
        i32.load offset=8
        local.tee 3
        local.get 0
        i32.store offset=12
        local.get 2
        local.get 0
        i32.store offset=8
        local.get 0
        i32.const 0
        i32.store offset=24
        local.get 0
        local.get 2
        i32.store offset=12
        local.get 0
        local.get 3
        i32.store offset=8
        return
      end
      local.get 5
      local.get 0
      i32.store
      local.get 0
      local.get 4
      i32.store offset=24
    end
    local.get 0
    local.get 0
    i32.store offset=12
    local.get 0
    local.get 0
    i32.store offset=8)
  (func $_ZN8dlmalloc8dlmalloc17Dlmalloc$LT$A$GT$18unlink_large_chunk17hbe8d36a9f4060ceeE (type 2) (param i32)
    (local i32 i32 i32 i32 i32)
    local.get 0
    i32.load offset=24
    local.set 1
    block  ;; label = @1
      block  ;; label = @2
        block  ;; label = @3
          local.get 0
          i32.load offset=12
          local.tee 2
          local.get 0
          i32.ne
          br_if 0 (;@3;)
          local.get 0
          i32.const 20
          i32.const 16
          local.get 0
          i32.const 20
          i32.add
          local.tee 2
          i32.load
          local.tee 3
          select
          i32.add
          i32.load
          local.tee 4
          br_if 1 (;@2;)
          i32.const 0
          local.set 2
          br 2 (;@1;)
        end
        local.get 0
        i32.load offset=8
        local.tee 4
        local.get 2
        i32.store offset=12
        local.get 2
        local.get 4
        i32.store offset=8
        br 1 (;@1;)
      end
      local.get 2
      local.get 0
      i32.const 16
      i32.add
      local.get 3
      select
      local.set 3
      loop  ;; label = @2
        local.get 3
        local.set 5
        block  ;; label = @3
          local.get 4
          local.tee 2
          i32.const 20
          i32.add
          local.tee 3
          i32.load
          local.tee 4
          br_if 0 (;@3;)
          local.get 2
          i32.const 16
          i32.add
          local.set 3
          local.get 2
          i32.load offset=16
          local.set 4
        end
        local.get 4
        br_if 0 (;@2;)
      end
      local.get 5
      i32.const 0
      i32.store
    end
    block  ;; label = @1
      local.get 1
      i32.eqz
      br_if 0 (;@1;)
      block  ;; label = @2
        block  ;; label = @3
          local.get 0
          i32.load offset=28
          i32.const 2
          i32.shl
          i32.const 1049032
          i32.add
          local.tee 4
          i32.load
          local.get 0
          i32.eq
          br_if 0 (;@3;)
          local.get 1
          i32.const 16
          i32.const 20
          local.get 1
          i32.load offset=16
          local.get 0
          i32.eq
          select
          i32.add
          local.get 2
          i32.store
          local.get 2
          br_if 1 (;@2;)
          br 2 (;@1;)
        end
        local.get 4
        local.get 2
        i32.store
        local.get 2
        br_if 0 (;@2;)
        i32.const 0
        i32.const 0
        i32.load offset=1048764
        i32.const -2
        local.get 0
        i32.load offset=28
        i32.rotl
        i32.and
        i32.store offset=1048764
        return
      end
      local.get 2
      local.get 1
      i32.store offset=24
      block  ;; label = @2
        local.get 0
        i32.load offset=16
        local.tee 4
        i32.eqz
        br_if 0 (;@2;)
        local.get 2
        local.get 4
        i32.store offset=16
        local.get 4
        local.get 2
        i32.store offset=24
      end
      local.get 0
      i32.const 20
      i32.add
      i32.load
      local.tee 4
      i32.eqz
      br_if 0 (;@1;)
      local.get 2
      i32.const 20
      i32.add
      local.get 4
      i32.store
      local.get 4
      local.get 2
      i32.store offset=24
      return
    end)
  (func $_ZN8dlmalloc8dlmalloc17Dlmalloc$LT$A$GT$4free17ha4737b7f84970addE (type 2) (param i32)
    (local i32 i32 i32 i32 i32 i32 i32)
    local.get 0
    i32.const -8
    i32.add
    local.tee 1
    local.get 0
    i32.const -4
    i32.add
    i32.load
    local.tee 2
    i32.const -8
    i32.and
    local.tee 0
    i32.add
    local.set 3
    block  ;; label = @1
      block  ;; label = @2
        block  ;; label = @3
          local.get 2
          i32.const 1
          i32.and
          br_if 0 (;@3;)
          local.get 2
          i32.const 3
          i32.and
          i32.eqz
          br_if 1 (;@2;)
          local.get 1
          i32.load
          local.tee 2
          local.get 0
          i32.add
          local.set 0
          block  ;; label = @4
            local.get 1
            local.get 2
            i32.sub
            local.tee 1
            i32.const 0
            i32.load offset=1049168
            i32.ne
            br_if 0 (;@4;)
            local.get 3
            i32.load offset=4
            i32.const 3
            i32.and
            i32.const 3
            i32.ne
            br_if 1 (;@3;)
            i32.const 0
            local.get 0
            i32.store offset=1049160
            local.get 3
            local.get 3
            i32.load offset=4
            i32.const -2
            i32.and
            i32.store offset=4
            local.get 1
            local.get 0
            i32.const 1
            i32.or
            i32.store offset=4
            local.get 1
            local.get 0
            i32.add
            local.get 0
            i32.store
            return
          end
          block  ;; label = @4
            block  ;; label = @5
              local.get 2
              i32.const 256
              i32.lt_u
              br_if 0 (;@5;)
              local.get 1
              i32.load offset=24
              local.set 4
              block  ;; label = @6
                block  ;; label = @7
                  local.get 1
                  i32.load offset=12
                  local.tee 5
                  local.get 1
                  i32.ne
                  br_if 0 (;@7;)
                  local.get 1
                  i32.const 20
                  i32.const 16
                  local.get 1
                  i32.const 20
                  i32.add
                  local.tee 5
                  i32.load
                  local.tee 6
                  select
                  i32.add
                  i32.load
                  local.tee 2
                  br_if 1 (;@6;)
                  i32.const 0
                  local.set 5
                  br 3 (;@4;)
                end
                local.get 1
                i32.load offset=8
                local.tee 2
                local.get 5
                i32.store offset=12
                local.get 5
                local.get 2
                i32.store offset=8
                br 2 (;@4;)
              end
              local.get 5
              local.get 1
              i32.const 16
              i32.add
              local.get 6
              select
              local.set 6
              loop  ;; label = @6
                local.get 6
                local.set 7
                block  ;; label = @7
                  local.get 2
                  local.tee 5
                  i32.const 20
                  i32.add
                  local.tee 6
                  i32.load
                  local.tee 2
                  br_if 0 (;@7;)
                  local.get 5
                  i32.const 16
                  i32.add
                  local.set 6
                  local.get 5
                  i32.load offset=16
                  local.set 2
                end
                local.get 2
                br_if 0 (;@6;)
              end
              local.get 7
              i32.const 0
              i32.store
              br 1 (;@4;)
            end
            block  ;; label = @5
              local.get 1
              i32.const 12
              i32.add
              i32.load
              local.tee 5
              local.get 1
              i32.const 8
              i32.add
              i32.load
              local.tee 6
              i32.eq
              br_if 0 (;@5;)
              local.get 6
              local.get 5
              i32.store offset=12
              local.get 5
              local.get 6
              i32.store offset=8
              br 2 (;@3;)
            end
            i32.const 0
            i32.const 0
            i32.load offset=1048760
            i32.const -2
            local.get 2
            i32.const 3
            i32.shr_u
            i32.rotl
            i32.and
            i32.store offset=1048760
            br 1 (;@3;)
          end
          local.get 4
          i32.eqz
          br_if 0 (;@3;)
          block  ;; label = @4
            block  ;; label = @5
              local.get 1
              i32.load offset=28
              i32.const 2
              i32.shl
              i32.const 1049032
              i32.add
              local.tee 2
              i32.load
              local.get 1
              i32.eq
              br_if 0 (;@5;)
              local.get 4
              i32.const 16
              i32.const 20
              local.get 4
              i32.load offset=16
              local.get 1
              i32.eq
              select
              i32.add
              local.get 5
              i32.store
              local.get 5
              i32.eqz
              br_if 2 (;@3;)
              br 1 (;@4;)
            end
            local.get 2
            local.get 5
            i32.store
            local.get 5
            br_if 0 (;@4;)
            i32.const 0
            i32.const 0
            i32.load offset=1048764
            i32.const -2
            local.get 1
            i32.load offset=28
            i32.rotl
            i32.and
            i32.store offset=1048764
            br 1 (;@3;)
          end
          local.get 5
          local.get 4
          i32.store offset=24
          block  ;; label = @4
            local.get 1
            i32.load offset=16
            local.tee 2
            i32.eqz
            br_if 0 (;@4;)
            local.get 5
            local.get 2
            i32.store offset=16
            local.get 2
            local.get 5
            i32.store offset=24
          end
          local.get 1
          i32.const 20
          i32.add
          i32.load
          local.tee 2
          i32.eqz
          br_if 0 (;@3;)
          local.get 5
          i32.const 20
          i32.add
          local.get 2
          i32.store
          local.get 2
          local.get 5
          i32.store offset=24
        end
        block  ;; label = @3
          block  ;; label = @4
            local.get 3
            i32.load offset=4
            local.tee 2
            i32.const 2
            i32.and
            i32.eqz
            br_if 0 (;@4;)
            local.get 3
            local.get 2
            i32.const -2
            i32.and
            i32.store offset=4
            local.get 1
            local.get 0
            i32.const 1
            i32.or
            i32.store offset=4
            local.get 1
            local.get 0
            i32.add
            local.get 0
            i32.store
            br 1 (;@3;)
          end
          block  ;; label = @4
            block  ;; label = @5
              block  ;; label = @6
                block  ;; label = @7
                  block  ;; label = @8
                    block  ;; label = @9
                      block  ;; label = @10
                        local.get 3
                        i32.const 0
                        i32.load offset=1049172
                        i32.eq
                        br_if 0 (;@10;)
                        local.get 3
                        i32.const 0
                        i32.load offset=1049168
                        i32.ne
                        br_if 1 (;@9;)
                        i32.const 0
                        local.get 1
                        i32.store offset=1049168
                        i32.const 0
                        i32.const 0
                        i32.load offset=1049160
                        local.get 0
                        i32.add
                        local.tee 0
                        i32.store offset=1049160
                        local.get 1
                        local.get 0
                        i32.const 1
                        i32.or
                        i32.store offset=4
                        local.get 1
                        local.get 0
                        i32.add
                        local.get 0
                        i32.store
                        return
                      end
                      i32.const 0
                      local.get 1
                      i32.store offset=1049172
                      i32.const 0
                      i32.const 0
                      i32.load offset=1049164
                      local.get 0
                      i32.add
                      local.tee 0
                      i32.store offset=1049164
                      local.get 1
                      local.get 0
                      i32.const 1
                      i32.or
                      i32.store offset=4
                      local.get 1
                      i32.const 0
                      i32.load offset=1049168
                      i32.eq
                      br_if 1 (;@8;)
                      br 5 (;@4;)
                    end
                    local.get 2
                    i32.const -8
                    i32.and
                    local.tee 5
                    local.get 0
                    i32.add
                    local.set 0
                    local.get 5
                    i32.const 256
                    i32.lt_u
                    br_if 1 (;@7;)
                    local.get 3
                    i32.load offset=24
                    local.set 4
                    block  ;; label = @9
                      block  ;; label = @10
                        local.get 3
                        i32.load offset=12
                        local.tee 5
                        local.get 3
                        i32.ne
                        br_if 0 (;@10;)
                        local.get 3
                        i32.const 20
                        i32.const 16
                        local.get 3
                        i32.const 20
                        i32.add
                        local.tee 5
                        i32.load
                        local.tee 6
                        select
                        i32.add
                        i32.load
                        local.tee 2
                        br_if 1 (;@9;)
                        i32.const 0
                        local.set 5
                        br 4 (;@6;)
                      end
                      local.get 3
                      i32.load offset=8
                      local.tee 2
                      local.get 5
                      i32.store offset=12
                      local.get 5
                      local.get 2
                      i32.store offset=8
                      br 3 (;@6;)
                    end
                    local.get 5
                    local.get 3
                    i32.const 16
                    i32.add
                    local.get 6
                    select
                    local.set 6
                    loop  ;; label = @9
                      local.get 6
                      local.set 7
                      block  ;; label = @10
                        local.get 2
                        local.tee 5
                        i32.const 20
                        i32.add
                        local.tee 6
                        i32.load
                        local.tee 2
                        br_if 0 (;@10;)
                        local.get 5
                        i32.const 16
                        i32.add
                        local.set 6
                        local.get 5
                        i32.load offset=16
                        local.set 2
                      end
                      local.get 2
                      br_if 0 (;@9;)
                    end
                    local.get 7
                    i32.const 0
                    i32.store
                    br 2 (;@6;)
                  end
                  i32.const 0
                  i32.const 0
                  i32.store offset=1049160
                  i32.const 0
                  i32.const 0
                  i32.store offset=1049168
                  br 3 (;@4;)
                end
                block  ;; label = @7
                  local.get 3
                  i32.const 12
                  i32.add
                  i32.load
                  local.tee 5
                  local.get 3
                  i32.const 8
                  i32.add
                  i32.load
                  local.tee 3
                  i32.eq
                  br_if 0 (;@7;)
                  local.get 3
                  local.get 5
                  i32.store offset=12
                  local.get 5
                  local.get 3
                  i32.store offset=8
                  br 2 (;@5;)
                end
                i32.const 0
                i32.const 0
                i32.load offset=1048760
                i32.const -2
                local.get 2
                i32.const 3
                i32.shr_u
                i32.rotl
                i32.and
                i32.store offset=1048760
                br 1 (;@5;)
              end
              local.get 4
              i32.eqz
              br_if 0 (;@5;)
              block  ;; label = @6
                block  ;; label = @7
                  local.get 3
                  i32.load offset=28
                  i32.const 2
                  i32.shl
                  i32.const 1049032
                  i32.add
                  local.tee 2
                  i32.load
                  local.get 3
                  i32.eq
                  br_if 0 (;@7;)
                  local.get 4
                  i32.const 16
                  i32.const 20
                  local.get 4
                  i32.load offset=16
                  local.get 3
                  i32.eq
                  select
                  i32.add
                  local.get 5
                  i32.store
                  local.get 5
                  i32.eqz
                  br_if 2 (;@5;)
                  br 1 (;@6;)
                end
                local.get 2
                local.get 5
                i32.store
                local.get 5
                br_if 0 (;@6;)
                i32.const 0
                i32.const 0
                i32.load offset=1048764
                i32.const -2
                local.get 3
                i32.load offset=28
                i32.rotl
                i32.and
                i32.store offset=1048764
                br 1 (;@5;)
              end
              local.get 5
              local.get 4
              i32.store offset=24
              block  ;; label = @6
                local.get 3
                i32.load offset=16
                local.tee 2
                i32.eqz
                br_if 0 (;@6;)
                local.get 5
                local.get 2
                i32.store offset=16
                local.get 2
                local.get 5
                i32.store offset=24
              end
              local.get 3
              i32.const 20
              i32.add
              i32.load
              local.tee 3
              i32.eqz
              br_if 0 (;@5;)
              local.get 5
              i32.const 20
              i32.add
              local.get 3
              i32.store
              local.get 3
              local.get 5
              i32.store offset=24
            end
            local.get 1
            local.get 0
            i32.const 1
            i32.or
            i32.store offset=4
            local.get 1
            local.get 0
            i32.add
            local.get 0
            i32.store
            local.get 1
            i32.const 0
            i32.load offset=1049168
            i32.ne
            br_if 1 (;@3;)
            i32.const 0
            local.get 0
            i32.store offset=1049160
            br 2 (;@2;)
          end
          i32.const 0
          i32.load offset=1049200
          local.tee 5
          local.get 0
          i32.ge_u
          br_if 1 (;@2;)
          i32.const 0
          i32.load offset=1049172
          local.tee 3
          i32.eqz
          br_if 1 (;@2;)
          i32.const 0
          local.set 1
          block  ;; label = @4
            i32.const 0
            i32.load offset=1049164
            local.tee 6
            i32.const 41
            i32.lt_u
            br_if 0 (;@4;)
            i32.const 1049184
            local.set 0
            loop  ;; label = @5
              block  ;; label = @6
                local.get 0
                i32.load
                local.tee 2
                local.get 3
                i32.gt_u
                br_if 0 (;@6;)
                local.get 2
                local.get 0
                i32.load offset=4
                i32.add
                local.get 3
                i32.gt_u
                br_if 2 (;@4;)
              end
              local.get 0
              i32.load offset=8
              local.tee 0
              br_if 0 (;@5;)
            end
          end
          block  ;; label = @4
            i32.const 0
            i32.load offset=1049192
            local.tee 0
            i32.eqz
            br_if 0 (;@4;)
            i32.const 0
            local.set 1
            loop  ;; label = @5
              local.get 1
              i32.const 1
              i32.add
              local.set 1
              local.get 0
              i32.load offset=8
              local.tee 0
              br_if 0 (;@5;)
            end
          end
          i32.const 0
          local.get 1
          i32.const 4095
          local.get 1
          i32.const 4095
          i32.gt_u
          select
          i32.store offset=1049208
          local.get 6
          local.get 5
          i32.le_u
          br_if 1 (;@2;)
          i32.const 0
          i32.const -1
          i32.store offset=1049200
          return
        end
        local.get 0
        i32.const 256
        i32.lt_u
        br_if 1 (;@1;)
        local.get 1
        local.get 0
        call $_ZN8dlmalloc8dlmalloc17Dlmalloc$LT$A$GT$18insert_large_chunk17hb12990f92538fbbfE
        i32.const 0
        local.set 1
        i32.const 0
        i32.const 0
        i32.load offset=1049208
        i32.const -1
        i32.add
        local.tee 0
        i32.store offset=1049208
        local.get 0
        br_if 0 (;@2;)
        block  ;; label = @3
          i32.const 0
          i32.load offset=1049192
          local.tee 0
          i32.eqz
          br_if 0 (;@3;)
          i32.const 0
          local.set 1
          loop  ;; label = @4
            local.get 1
            i32.const 1
            i32.add
            local.set 1
            local.get 0
            i32.load offset=8
            local.tee 0
            br_if 0 (;@4;)
          end
        end
        i32.const 0
        local.get 1
        i32.const 4095
        local.get 1
        i32.const 4095
        i32.gt_u
        select
        i32.store offset=1049208
        return
      end
      return
    end
    local.get 0
    i32.const -8
    i32.and
    i32.const 1048768
    i32.add
    local.set 3
    block  ;; label = @1
      block  ;; label = @2
        i32.const 0
        i32.load offset=1048760
        local.tee 2
        i32.const 1
        local.get 0
        i32.const 3
        i32.shr_u
        i32.shl
        local.tee 0
        i32.and
        i32.eqz
        br_if 0 (;@2;)
        local.get 3
        i32.load offset=8
        local.set 0
        br 1 (;@1;)
      end
      i32.const 0
      local.get 2
      local.get 0
      i32.or
      i32.store offset=1048760
      local.get 3
      local.set 0
    end
    local.get 3
    local.get 1
    i32.store offset=8
    local.get 0
    local.get 1
    i32.store offset=12
    local.get 1
    local.get 3
    i32.store offset=12
    local.get 1
    local.get 0
    i32.store offset=8)
  (func $_ZN3std10sys_common9backtrace26__rust_end_short_backtrace17h53cabafab5b09adaE (type 2) (param i32)
    (local i32)
    global.get $__stack_pointer
    i32.const 16
    i32.sub
    local.tee 1
    global.set $__stack_pointer
    local.get 1
    i32.const 8
    i32.add
    local.get 0
    i32.const 8
    i32.add
    i32.load
    i32.store
    local.get 1
    local.get 0
    i64.load align=4
    i64.store
    local.get 1
    call $_ZN3std9panicking19begin_panic_handler28_$u7b$$u7b$closure$u7d$$u7d$17hdcfc819ce836829eE
    unreachable)
  (func $_ZN3std9panicking19begin_panic_handler28_$u7b$$u7b$closure$u7d$$u7d$17hdcfc819ce836829eE (type 2) (param i32)
    (local i32 i32)
    local.get 0
    i32.load
    local.tee 1
    i32.const 20
    i32.add
    i32.load
    local.set 2
    block  ;; label = @1
      block  ;; label = @2
        local.get 1
        i32.load offset=4
        br_table 0 (;@2;) 0 (;@2;) 1 (;@1;)
      end
      local.get 2
      br_if 0 (;@1;)
      local.get 0
      i32.load offset=4
      i32.load8_u offset=16
      call $_ZN3std9panicking20rust_panic_with_hook17h70a0e195f4db2a29E
      unreachable
    end
    local.get 0
    i32.load offset=4
    i32.load8_u offset=16
    call $_ZN3std9panicking20rust_panic_with_hook17h70a0e195f4db2a29E
    unreachable)
  (func $_ZN3std9panicking20rust_panic_with_hook17h70a0e195f4db2a29E (type 2) (param i32)
    (local i32 i32)
    i32.const 0
    i32.const 0
    i32.load offset=1048756
    local.tee 1
    i32.const 1
    i32.add
    i32.store offset=1048756
    i32.const 0
    i32.const 0
    i32.load offset=1049212
    i32.const 1
    i32.add
    local.tee 2
    i32.store offset=1049212
    block  ;; label = @1
      local.get 1
      i32.const 0
      i32.lt_s
      br_if 0 (;@1;)
      local.get 2
      i32.const 2
      i32.gt_u
      br_if 0 (;@1;)
      i32.const 0
      i32.load offset=1048752
      i32.const -1
      i32.le_s
      br_if 0 (;@1;)
      local.get 2
      i32.const 1
      i32.gt_u
      br_if 0 (;@1;)
      local.get 0
      i32.eqz
      br_if 0 (;@1;)
      call $rust_panic
      unreachable
    end
    unreachable
    unreachable)
  (func $alloc (type 4) (param i32) (result i32)
    block  ;; label = @1
      local.get 0
      br_if 0 (;@1;)
      i32.const 1
      return
    end
    block  ;; label = @1
      block  ;; label = @2
        local.get 0
        i32.const -1
        i32.le_s
        br_if 0 (;@2;)
        local.get 0
        call $_ZN8dlmalloc8dlmalloc17Dlmalloc$LT$A$GT$6malloc17ha96fcefbb44d6da5E
        local.tee 0
        i32.eqz
        br_if 1 (;@1;)
        local.get 0
        return
      end
      call $_ZN5alloc7raw_vec17capacity_overflow17h4b275cb3c10b0a78E
      unreachable
    end
    unreachable
    unreachable)
  (func $array_sum (type 5) (param i32 i32) (result i32)
    (local i32 i32 i32 i32 i32)
    block  ;; label = @1
      block  ;; label = @2
        local.get 1
        br_if 0 (;@2;)
        i32.const 0
        local.set 2
        br 1 (;@1;)
      end
      local.get 1
      i32.const 7
      i32.and
      local.set 3
      block  ;; label = @2
        block  ;; label = @3
          local.get 0
          i32.const -1
          i32.xor
          local.get 0
          local.get 1
          i32.add
          i32.add
          i32.const 7
          i32.ge_u
          br_if 0 (;@3;)
          i32.const 0
          local.set 2
          local.get 0
          local.set 4
          br 1 (;@2;)
        end
        local.get 1
        i32.const -8
        i32.and
        local.set 5
        i32.const 0
        local.set 2
        local.get 0
        local.set 6
        loop  ;; label = @3
          local.get 6
          i32.load8_u offset=7
          local.get 6
          i32.load8_u offset=6
          local.get 6
          i32.load8_u offset=5
          local.get 6
          i32.load8_u offset=4
          local.get 6
          i32.load8_u offset=3
          local.get 6
          i32.load8_u offset=2
          local.get 6
          i32.load8_u offset=1
          local.get 6
          i32.load8_u
          local.get 2
          i32.add
          i32.add
          i32.add
          i32.add
          i32.add
          i32.add
          i32.add
          i32.add
          local.set 2
          local.get 6
          i32.const 8
          i32.add
          local.tee 4
          local.set 6
          local.get 5
          i32.const -8
          i32.add
          local.tee 5
          br_if 0 (;@3;)
        end
      end
      block  ;; label = @2
        local.get 3
        i32.eqz
        br_if 0 (;@2;)
        loop  ;; label = @3
          local.get 4
          i32.load8_u
          local.get 2
          i32.add
          local.set 2
          local.get 4
          i32.const 1
          i32.add
          local.set 4
          local.get 3
          i32.const -1
          i32.add
          local.tee 3
          br_if 0 (;@3;)
        end
      end
      local.get 1
      i32.eqz
      br_if 0 (;@1;)
      local.get 0
      call $_ZN8dlmalloc8dlmalloc17Dlmalloc$LT$A$GT$4free17ha4737b7f84970addE
    end
    local.get 2
    i32.const 255
    i32.and)
  (table (;0;) 3 3 funcref)
  (memory (;0;) 17)
  (global $__stack_pointer (mut i32) (i32.const 1048576))
  (global (;1;) i32 (i32.const 1049216))
  (global (;2;) i32 (i32.const 1049216))
  (export "memory" (memory 0))
  (export "alloc" (func $alloc))
  (export "array_sum" (func $array_sum))
  (export "__data_end" (global 1))
  (export "__heap_base" (global 2))
  ;; (func (export "call_python") (result i32)
  ;;   call $test_call)
  ;; (func (export "call_setJsonStore")
  ;;   call $setJsonStore)
  ;; (func (export "call_getJsonStore")
  ;;   call $getJsonStore)
  (elem (;0;) (i32.const 1) func $_ZN4core3ptr102drop_in_place$LT$$RF$core..iter..adapters..copied..Copied$LT$core..slice..iter..Iter$LT$u8$GT$$GT$$GT$17h05fa0f971b46b0e7E $_ZN36_$LT$T$u20$as$u20$core..any..Any$GT$7type_id17h13c78596688f67b2E)
  (data $.rodata (i32.const 1048576) "library/alloc/src/raw_vec.rscapacity overflow\00\00\00\1c\00\10\00\11\00\00\00\00\00\10\00\1c\00\00\00\06\02\00\00\05\00\00\00\01\00\00\00\00\00\00\00\01\00\00\00\02\00\00\00called `Option::unwrap()` on a `None` valuelibrary/std/src/panicking.rs\00\83\00\10\00\1c\00\00\00G\02\00\00\0f\00\00\00"))
