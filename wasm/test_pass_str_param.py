import os

import pytest
from wasmtime import Store, Module, Engine, Linker, WasiConfig


def test_pass_str_param():
    wasi = WasiConfig()
    wasi.preopen_dir('.', '.')
    wasi.inherit_stdout()

    engine = Engine()
    linking1_module = Module.from_file(engine, "wasm/pass_str_param.wasm")
    linker = Linker(engine)
    linker.define_wasi()

    # Create a `Store` to hold instances, and configure wasi state
    store = Store(engine)
    store.set_wasi(wasi)

    linking1 = linker.instantiate(store, linking1_module)
    for foo in linking1_module.exports:
        print(foo.name)

    allocate = linking1.exports(store)["allocate"]
    deallocate = linking1.exports(store)["deallocate"]
    mem = linking1.exports(store)["memory"]
    greet = linking1.exports(store)["greet"]

    # allocate memory and input string
    input_str = 'vgraph'
    ptr = allocate(store, len(input_str))
    print(f'allocate memory: {ptr}')
    write_str(mem, store, input_str, ptr)
    print(f'Input String: {input_str}')

    # use ptr as input parameter and return output ptr
    out_ptr = greet(store, ptr)
    print(f'outpur ptr {out_ptr}')
    # loop to read output string
    output_str = ''
    i = 0
    while True:
        # C-string terminates by NULL.
        if mem.data_ptr(store)[out_ptr + i] == 0:
            break
        char = chr(mem.data_ptr(store)[out_ptr + i])
        output_str += char
        i += 1
    print(f'Output String: {output_str}')


def write_str(mem, store, input_str, ptr):
    # write into memory
    mem.write(store, bytearray(input_str.encode()), ptr)
    # write 0 as NULL to terminate C-string
    mem.data_ptr(store)[ptr + len(input_str)] = 0
