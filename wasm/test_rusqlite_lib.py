import os

import pytest

if os.path.basename(os.getcwd()) == "wasm":
    os.chdir("..")

from wasmtime import Store, Module, Instance, Func, FuncType, Engine, Linker, WasiConfig, ValType, Config

def test_rusqlite_lib():
    wasi = WasiConfig()
    wasi.preopen_dir('.', '.')
    wasi.inherit_stdout()

    engine = Engine()
    module = Module.from_file(engine, "wasm/rusqlite_lib.wasm")
    linker = Linker(engine)
    linker.define_wasi()

    # Create a `Store` to hold instances, and configure wasi state
    store = Store(engine)
    store.set_wasi(wasi)

    instance = linker.instantiate(store, module)
    for foo in module.exports:
        print(foo.name)

    create_db_connection = instance.exports(store)["create_db_connection"]
    conn = create_db_connection(store)
    print(f'Get result from create_db_connection: {conn}')

    add_person_data(instance, store, conn, "ALicee", 23)
    add_person_data(instance, store, conn, "Tom", 28)

    # get the nth person data in the database (no return value)
    get_person_data = instance.exports(store)["get_person_data"]
    get_person_data(store, 1, conn, 1)
    get_person_data(store, 1, conn, 2)

    get_person_age = instance.exports(store)["get_person_age"]
    age = get_person_age(store, conn, 2)
    print(f'Get result from get_person_age: {age}')
def add_person_data(linking, store, conn, name, age):
    # use write_str to write name into memory
    name_ptr = write_str(linking, store, name)
    print(f'Get result from write_str: {name_ptr}')
    add_person = linking.exports(store)["add_person"]
    add_person(store, conn, name_ptr, age)

def write_str(linking, store, input_str):
    allocate = linking.exports(store)["allocate"]
    mem = linking.exports(store)["memory"]
    ptr = allocate(store, len(input_str))
    print(f'allocate ptr {ptr}')
    print(f'input_str {input_str}')
    # write into memory
    mem.write(store, bytearray(input_str.encode()), ptr)
    # write 0 as NULL to terminate C-string
    mem.data_ptr(store)[ptr+len(input_str)] = 0
    return ptr

