use std::collections::HashMap;

use anyhow::Result;
use serde::{Deserialize, Serialize};
use wasm_py_bridge_macro::wasm_bind;

// Print a simple test message
#[wasm_bind]
fn print_hello_world() {
    println!("Hello, world!");
}

// Return a fixed string
#[wasm_bind]
fn get_string() -> String { "get string".to_string() }

// Test f64 compute
#[wasm_bind]
fn compute_f64(a: f64, b: f64) -> f64 { a + b }

// Return different strings based on conditions
#[wasm_bind]
fn conditional_response(age: i32, is_student: bool) -> String {
    if is_student { format!("Student, Age: {}", age) } else { "Non-student".to_string() }
}

// Structure for user information, demonstrating complex data handling
#[derive(Serialize, Deserialize)]
struct UserInfo {
    username: String,
    scores: Vec<i32>,
    metadata: HashMap<String, String>,
}

// Modify and return user information
#[wasm_bind]
fn update_user_info(user_info: UserInfo) -> UserInfo {
    UserInfo {
        username: user_info.username.to_uppercase(),
        scores: user_info.scores.iter().map(|x| x + 10).collect(),
        metadata: user_info.metadata.into_iter().map(|(k, v)| (k, v + "!")).collect(),
    }
}

// Structure for a user's educational record
#[derive(Serialize, Deserialize)]
struct EducationRecord {
    personal_info: PersonalInfo,
    education_history: Vec<EducationDetail>,
}

// Structure for personal information
#[derive(Serialize, Deserialize)]
struct PersonalInfo {
    id: i32,
    name: String,
}

// Structure for details of education
#[derive(Serialize, Deserialize)]
struct EducationDetail {
    year: i32,
    institution: String,
}

// Format and return the most recent educational experience
#[wasm_bind]
fn format_latest_education(record: EducationRecord) -> String {
    let latest_education = record.education_history.last().unwrap();
    format!("{} attended {} in {}", record.personal_info.name, latest_education.institution, latest_education.year)
}

#[derive(Debug, Serialize, Deserialize)]
struct StructA {
    field_a: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct StructB {
    field_b: i32,
}

#[wasm_bind]
fn process_structs(a: StructA, b: StructB) -> String {
    format!("StructA: {:?}, StructB: {:?}", a, b)
}

#[derive(Serialize, Deserialize)]
struct CustomType {
    value: i32,
}

#[wasm_bind]
fn process_map(data: HashMap<String, CustomType>) -> HashMap<String, CustomType> {
    data.into_iter()
        .map(|(k, v)| (k, CustomType { value: v.value + 1 }))
        .collect()
}

#[wasm_bind]
fn process_list(data: Vec<CustomType>) -> Vec<CustomType> {
    data.into_iter()
        .map(|x| CustomType { value: x.value + 1 })
        .collect()
}

#[wasm_bind]
fn return_result_ok_full() -> anyhow::Result<String> {
    Ok("return result".to_string())
}

#[wasm_bind]
fn return_result_ok_short() -> Result<String> {
    Ok("return result".to_string())
}

#[wasm_bind]
fn return_result_err_full() -> anyhow::Result<String> {
    Err(anyhow::anyhow!("return error"))
}

#[wasm_bind]
fn return_result_err_short() -> Result<String> {
    Err(anyhow::anyhow!("return error"))
}

#[wasm_bind]
fn return_empty_ok() -> Result<()> {
    Ok(())
}

#[wasm_bind]
fn return_empty_err() -> Result<()> {
    Err(anyhow::anyhow!("return error"))
}


// Define a custom type for books
#[derive(Serialize, Deserialize, Debug, Clone)]
struct Book {
    title: String,
    publication_year: i32,
    author: String,
}

// Define a custom type for reviews
#[derive(Serialize, Deserialize, Debug, Clone)]
struct Review {
    reviewer: String,
    score: u8,
    // assuming a score out of 10
    comment: String,
}

// Define a struct with complex nesting for a library system
#[derive(Serialize, Deserialize, Debug, Clone)]
struct LibraryCatalog {
    // A list of books
    books: Vec<Book>,
    // A dictionary mapping book titles to their reviews
    book_reviews: HashMap<String, Vec<Review>>,
    // A dictionary mapping authors to a list of their books
    author_books: HashMap<String, Vec<Book>>,
}

#[wasm_bind]
fn return_result_ok_complicate_nested() -> Result<LibraryCatalog> {
    // Create some book instances
    let book1 = Book {
        title: "Rust Programming".to_string(),
        publication_year: 2021,
        author: "John Doe".to_string(),
    };
    let book2 = Book {
        title: "Advanced Rust".to_string(),
        publication_year: 2022,
        author: "Jane Smith".to_string(),
    };

    // Create some review instances
    let review1 = Review {
        reviewer: "Alice Johnson".to_string(),
        score: 8,
        comment: "Great introduction to Rust.".to_string(),
    };
    let review2 = Review {
        reviewer: "Bob Brown".to_string(),
        score: 9,
        comment: "Excellent coverage of advanced topics.".to_string(),
    };

    // Create the complex nested structure
    let library_catalog = LibraryCatalog {
        books: vec![book1.clone(), book2.clone()],
        book_reviews: HashMap::from([
            (book1.title.clone(), vec![review1]),
            (book2.title.clone(), vec![review2]),
        ]),
        author_books: HashMap::from([
            (book1.author.clone(), vec![book1]),
            (book2.author.clone(), vec![book2]),
        ]),
    };

    Ok(library_catalog)
}