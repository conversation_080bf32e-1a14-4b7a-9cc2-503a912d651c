[package]
name = "test-wasm-py-bridge"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
wasm-py-bridge-macro = { path = "../../contract/base/source/wasm-py-bridge-macro" }
wasm-utils = { path = "../../contract/base/source/wasm-utils" }

serde = { version = "=1.0.193", features = ["derive"] }
serde_json = "=1.0.108"
anyhow = "=1.0.75"

[lib]
crate-type = ["cdylib", "rlib"]
