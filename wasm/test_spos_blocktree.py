import copy
from typing import Optional, List

import pytest
from serde.json import to_json

from chains.vgraph_spos_chain.blocktree_model import Block, Transaction, TransactionReceipt, ReorganizationPath
from common.utils import keccak256
from vm import ContractTester, resetTesterState

sposBlocktreeContractClient = ContractTester(
    address="76d68962226148b0b353697f771e61fd", packageName="spos_blocktree", wasmName="spos_blocktree"
)
sposContractClient = ContractTester(address="0468e53f8b984abcab8a0eecd451091d", packageName="spos", wasmName="spos")

numberOfSlots = 15


@pytest.fixture(autouse=True)
def clean_db():
    resetTesterState()


@pytest.fixture(autouse=True)
def register_contract():
    sposContractClient.constructor("Slots", numberOfSlots)
    sposBlocktreeContractClient.constructor("SPOSBlockTree")
    sposBlocktreeContractClient.constructor("TransactionReceipts")


def test_get_block_template():
    firstBlock, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
    assert err is None
    assert firstBlock.parent_id == "00000000-0000-0000-0000-000000000000"
    assert firstBlock.height == 1
    assert firstBlock.transactions == "[]"
    assert firstBlock.merkle == keccak256(firstBlock.transactions)
    assert firstBlock.local_timestamp == 0
    assert firstBlock.protocol_timestamp == 0
    assert firstBlock.slot_id == 0
    assert firstBlock.validator_address == ""

    # connect this block, then get the block template again
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "save_block_to_disk", None, firstBlock)
    assert err is None
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "connect_block", None, firstBlock, True)
    assert err is None

    secondBlock, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
    assert err is None
    assert secondBlock.parent_id == firstBlock.id
    assert secondBlock.height == 2
    assert secondBlock.transactions == "[]"
    assert secondBlock.merkle == keccak256(firstBlock.transactions)
    assert secondBlock.local_timestamp == 0
    assert secondBlock.protocol_timestamp == 0
    assert secondBlock.slot_id == 0
    assert secondBlock.validator_address == ""


def test_get_current_best_block():
    # if there is no best block, it should return None
    bestBlock, err = sposBlocktreeContractClient.executeReadOnly(
        "SPOSBlockTree", "get_current_best_block", Optional[Block]
    )
    assert err is None
    assert bestBlock is None

    # insert a block and set it as best block
    block, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
    assert err is None

    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "save_block_to_disk", None, block)
    assert err is None

    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "connect_block", None, block, True)
    assert err is None

    bestBlock, err = sposBlocktreeContractClient.executeReadOnly(
        "SPOSBlockTree", "get_current_best_block", Optional[Block]
    )
    assert err is None
    assert bestBlock == block


def test_get_block():
    # insert a block
    block, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
    assert err is None

    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "save_block_to_disk", None, block)
    assert err is None

    # get the block
    gotBlock, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block", Optional[Block], block.id)
    assert err is None
    assert gotBlock == block


def test_get_blocks():
    # connect 5 blocks
    #     1
    #     |
    #     2    <- locator
    #     |
    #     3
    #     |
    #     4
    #     |
    #     5
    blocks = []
    for _ in range(5):
        block, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
        assert err is None

        _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "save_block_to_disk", None, block)
        assert err is None

        _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "connect_block", None, block, True)
        assert err is None

        blocks.append(block)

    # get blocks
    # path should be [2, 3, 4, 5]
    locator = [blocks[0].id, blocks[1].id]
    path, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_blocks", List[Block], locator)
    assert err is None
    assert path == blocks[1:]


def test_validate_block():
    # Alice is a supernode at slot 1
    alice = "Alice"
    bob = "Bob"
    slot1 = 1
    _, err = sposContractClient.execute("Slots", "content_slot", None, slot1, alice)
    assert err is None

    mintingTransaction = Transaction(
        transaction_hash=None,
        from_address=alice,
        contract_address="",
        package_name="spos",
        contract_name="spos",
        struct_name="Slots",
        function_name="mint_token",
        parameters=[],
        publickeys=[],
        signatures=[],
        timestamp=0,
    )
    mintingTransaction.transaction_hash = mintingTransaction.toTransactionHash()

    validBlock = Block(
        id="",
        parent_id="00000000-0000-0000-0000-000000000000",
        height=1,
        transactions=to_json([mintingTransaction]),
        merkle=keccak256(to_json([mintingTransaction])),
        local_timestamp=0,
        protocol_timestamp=0,
        slot_id=slot1,
        proposer_address=alice,
        public_keys=[],
        signatures=[],
    )
    # set block id with block hash
    validBlock.id = validBlock.toBlockHeaderHash()
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "validate_block", None, validBlock)
    assert err is None

    # invalid block: invalid id, invalid height, invalid slot id
    invalidBlock1 = copy.deepcopy(validBlock)
    invalidBlock1.id = ""
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "validate_block", None, invalidBlock1)
    assert err is not None

    invalidBlock1 = copy.deepcopy(validBlock)
    invalidBlock1.id = "abcd"
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "validate_block", None, invalidBlock1)
    assert err is not None

    invalidBlock2 = copy.deepcopy(validBlock)
    invalidBlock2.height = 0
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "validate_block", None, invalidBlock2)
    assert err is not None

    invalidBlock3 = copy.deepcopy(validBlock)
    invalidBlock3.slot_id = 0
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "validate_block", None, invalidBlock3)
    assert err is not None

    # invalid block: minting transaction is invalid
    InvalidMintingTransaction = copy.deepcopy(mintingTransaction)
    InvalidMintingTransaction.from_address = bob
    InvalidMintingTransaction.transaction_hash = InvalidMintingTransaction.toTransactionHash()
    invalidBlock4 = copy.deepcopy(validBlock)
    invalidBlock4.transactions = to_json([InvalidMintingTransaction])
    invalidBlock4.merkle = keccak256(to_json([InvalidMintingTransaction]))
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "validate_block", None, invalidBlock4)
    assert err is not None

    # invalid block: minting transaction is not the first transaction
    InvalidBlock5 = copy.deepcopy(validBlock)
    InvalidBlock5.transactions = to_json([mintingTransaction, mintingTransaction])
    InvalidBlock5.merkle = keccak256(to_json([mintingTransaction, mintingTransaction]))
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "validate_block", None, InvalidBlock5)
    assert err is not None

    # invalid block: proposer address is invalid
    InvalidBlock6 = copy.deepcopy(validBlock)
    InvalidBlock6.proposer_address = bob
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "validate_block", None, InvalidBlock6)
    assert err is not None


def test_generate_locator():
    # if there is no best block, it should return empty locator
    locator, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "generate_locator", List[str])
    assert err is None
    assert locator == []

    # create a long chain of blocks, and generate locator
    blocks = []
    for _ in range(10):
        block, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
        assert err is None

        _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "save_block_to_disk", None, block)
        assert err is None

        _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "connect_block", None, block, True)
        assert err is None

        blocks.append(block)

    locator, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "generate_locator", List[str])
    assert err is None

    # verify the locator
    targetBlockIndex = len(blocks) - 1
    shift = 1
    factor = 1
    for i in range(min(len(locator), 32)):
        assert locator[i] == blocks[targetBlockIndex + 1 - shift].id
        if i >= 10:
            factor *= 2
        shift += factor


def test_get_reorganization_path():
    # Block Tree Structure:
    # block1 is the root block, created from ROOT_PARENT_ID
    # block2 and block3 are children of block1
    # block4 is a child of block3
    # block5 is a child of block4
    #
    #      block1
    #      /     \
    #   block2   block3
    #              |
    #            block4
    #             |
    #            block5
    block1, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
    assert err is None
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "save_block_to_disk", None, block1)
    assert err is None
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "connect_block", None, block1, True)
    assert err is None

    block2, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
    assert err is None
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "save_block_to_disk", None, block2)
    assert err is None

    block3, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
    assert err is None
    # manually change block3 id to to avoid block id collision with block2
    block3.id = hash(block3) + "_test"
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "save_block_to_disk", None, block3)
    assert err is None
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "connect_block", None, block3, True)
    assert err is None

    block4, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
    assert err is None
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "save_block_to_disk", None, block4)
    assert err is None
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "connect_block", None, block4, True)
    assert err is None

    block5, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
    assert err is None
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "save_block_to_disk", None, block5)
    assert err is None
    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "connect_block", None, block5, True)

    # reorg from block2 to block5
    path, err = sposBlocktreeContractClient.executeReadOnly(
        "SPOSBlockTree", "get_reorganization_path", ReorganizationPath, block2.id, block5.id
    )
    assert err is None
    assert path.roll_back_path == [block2.id]
    assert path.common_ancestor == block1.id
    assert path.roll_forward_path == [block3.id, block4.id, block5.id]


def test_save_block_to_disk():
    block, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
    assert err is None

    _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "save_block_to_disk", None, block)
    assert err is None

    # check if the block is saved
    gotBlock, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block", Optional[Block], block.id)
    assert err is None
    assert gotBlock == block


def test_connect_block():
    # connect 3 blocks
    blocks = []
    for _ in range(3):
        block, err = sposBlocktreeContractClient.executeReadOnly("SPOSBlockTree", "get_block_template", Block)
        assert err is None

        _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "save_block_to_disk", None, block)
        assert err is None

        _, err = sposBlocktreeContractClient.execute("SPOSBlockTree", "connect_block", None, block, True)
        assert err is None

        blocks.append(block)

    # check if the blocks are connected
    for i in range(1, len(blocks)):
        gotBlock, err = sposBlocktreeContractClient.executeReadOnly(
            "SPOSBlockTree", "get_block", Optional[Block], blocks[i].id
        )
        assert err is None
        assert gotBlock.parent_id == blocks[i - 1].id


def test_get_and_create_transaction_receipt():
    receipt = TransactionReceipt(
        transaction_hash="",
        transaction_index=0,
        block_id="",
        from_address="",
        contract_address="",
        package_name="",
        contract_name="",
        struct_name="",
        function_name="",
        parameters=[],
        publickeys=[],
        signatures=[],
        status=False,
        result=None,
    )

    _, err = sposBlocktreeContractClient.execute("TransactionReceipts", "create_transaction_receipt", None, receipt)
    assert err is None

    gotReceipt, err = sposBlocktreeContractClient.executeReadOnly(
        "TransactionReceipts", "get_transaction_receipt", Optional[TransactionReceipt], receipt.transaction_hash
    )
    assert err is None
    assert receipt == gotReceipt


def test_create_transaction_receipts():
    # create 3 transaction receipts
    receipts = []
    for i in range(3):
        receipt = TransactionReceipt(
            transaction_hash=f"hash{i}",
            transaction_index=i,
            block_id=f"block{i}",
            from_address=f"from{i}",
            contract_address=f"contract{i}",
            package_name=f"package{i}",
            contract_name=f"contract{i}",
            struct_name=f"struct{i}",
            function_name=f"function{i}",
            parameters=[],
            publickeys=[],
            signatures=[],
            status=False,
            result=None,
        )
        receipts.append(receipt)

    _, err = sposBlocktreeContractClient.execute("TransactionReceipts", "create_transaction_receipts", None, receipts)
    assert err is None

    # get the receipts
    for i in range(3):
        gotReceipt, err = sposBlocktreeContractClient.executeReadOnly(
            "TransactionReceipts", "get_transaction_receipt", Optional[TransactionReceipt], receipts[i].transaction_hash
        )
        assert err is None
        assert receipts[i] == gotReceipt
