// #![allow(unused_variables, unused_mut, dead_code, unused_imports)]
use std::ffi::{c_char, c_void, CStr};
use std::mem;

use num_bigint::{BigUint, RandBigInt};
use num_traits::{One};
use rand::thread_rng;

#[no_mangle]
pub extern fn is_prime(number_ptr: *mut c_char, accuracy: usize) -> bool {
    let number = unsafe { CStr::from_ptr(number_ptr).to_bytes().to_vec() };

    let n: &BigUint = &BigUint::parse_bytes(&number, 10).unwrap();

    if n <= &BigUint::one() || *n == BigUint::from(4u32) { return false; }
    if n <= &BigUint::from(3u32) { return true; }

    let mut rng = thread_rng();

    for _ in 0..accuracy {
        // Generate random number in [2, n-2]
        let a = rng.gen_biguint_range(&BigUint::from(2u32), &(n - BigUint::one()));
        // If a^(n-1) mod n != 1, then n is not prime.
        if BigUint::modpow(&a, &(n - BigUint::one()), n) != BigUint::one() {
            println!("{} is not a prime number.", n);
            return false;
        }
    }

    println!("{} could be a prime number.", n);
    true
}



#[no_mangle]
pub extern fn allocate(size: usize) -> *mut c_void {
    let mut buffer = Vec::with_capacity(size);
    let pointer = buffer.as_mut_ptr();
    mem::forget(buffer);

    pointer as *mut c_void
}

#[no_mangle]
pub extern fn deallocate(pointer: *mut c_void, capacity: usize) {
    unsafe {
        let _ = Vec::from_raw_parts(pointer, 0, capacity);
    }
}
