{
  description = "Minimal rust wasm example";

  inputs = {
    flake-utils.url = "github:numtide/flake-utils";
    rust-overlay = {
      url = "github:oxalica/rust-overlay";
      inputs.nixpkgs.follows = "nixpkgs";
    };
    nixpkgs.url = "nixpkgs/nixos-unstable";
  };

  outputs = { self, nixpkgs, flake-utils, rust-overlay }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        overlays = [ rust-overlay.overlays.default ];
        pkgs = import nixpkgs { inherit system overlays; };
        rust = pkgs.rust-bin.fromRustupToolchainFile ./rust-toolchain.toml;
      in
      {
        packages = {
          default = pkgs.rustPlatform.buildRustPackage {
            pname = "wasm32-wasi-build";
            version = "1.0.0";
            src = ./.;
            cargoLock = {
              lockFile = ./Cargo.lock;
            };
            nativeBuildInputs = [ rust ];
            buildPhase = ''
              cargo build --release --target=wasm32-wasi
              echo 'Creating out dir...'
              mkdir -p $out/;
              cp target/wasm32-wasi/release/*.wasm $out/;
            '';
            installPhase = "echo 'skip installPhase'";
          };
        };

        devShells = {
          default = pkgs.mkShell { packages = [ rust ]; };
        };
      }
    );
}
