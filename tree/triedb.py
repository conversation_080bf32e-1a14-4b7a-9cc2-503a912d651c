from __future__ import annotations

import dataclasses
from typing import Dict, Optional

import vgraphdb


class TrieDB:
    def __init__(self, db: vgraphdb.KeyValueStore):
        self.db: vgraphdb.KeyValueStore = db
        self.dirties: TrieChanges = TrieChanges()

    def commit(self) -> TrieChanges:
        res = self.dirties
        self.dirties = TrieChanges()
        return res

    def reset(self):
        self.dirties.clear()

    def __contains__(self, item):
        if item in self.dirties:
            if self.dirties[item].deleted:
                return False
            return True
        return item in self.db

    def __getitem__(self, item):
        if item in self.dirties:
            if self.dirties[item].deleted:
                return None
            return self.dirties[item].value
        return self.db.get(item)

    def __setitem__(self, key, value):
        self.dirties[key] = Value(value, False)

    def __delitem__(self, key):
        self.dirties[key] = Value(None, True)


@dataclasses.dataclass
class Value:
    value: Optional[bytes]
    deleted: bool


class TrieChanges(Dict[bytes, Value]):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def merge(self, other: Optional[TrieChanges]):
        if other is not None:
            self.update(other)
