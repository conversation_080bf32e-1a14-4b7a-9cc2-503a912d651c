from trie import HexaryTrie

from common import TrieHasher


class MemoryTrie(TrieHasher):
    """
    A trie that stores its data in memory using py-trie
    """

    def __init__(self):
        self.trie = HexaryTrie({})

    def update(self, index: bytes, value: bytes) -> None:
        self.trie[index] = value

    def hash(self) -> bytes:
        return self.trie.root_hash

    def reset(self) -> None:
        self.trie = HexaryTrie({})
