from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Optional, Tuple

import common

from . import triedb


class TrieABC(ABC):
    """
    Trie is a Vgraph Merkle Patricia trie.
    """

    @abstractmethod
    def getKey(self, key: bytes) -> bytes:
        """
        <PERSON><PERSON><PERSON> returns the sha3 preimage of a hashed key that was previously used to store a value.
        """
        raise NotImplementedError

    @abstractmethod
    def getAccount(self, address: bytes) -> Optional[common.StateAccount]:
        """
        GetAccount abstracts an account read from the trie. It retrieves the
        account blob from the trie with provided account address and decodes it
        with associated decoding algorithm. If the specified account is not in
        the trie, nil will be returned. If the trie is corrupted(e.g. some nodes
        are missing or the account blob is incorrect for decoding), an error will
        be raised.
        """
        raise NotImplementedError

    @abstractmethod
    def getStorage(self, address: bytes, key: bytes) -> bytes:
        """
        GetStorage returns the value for key stored in the trie. The value bytes
        must not be modified by the caller. If a node was not found in the database,
        a MissingNodeError is raised.
        """
        raise NotImplementedError

    @abstractmethod
    def updateAccount(self, address: bytes, account: common.StateAccount) -> None:
        """
            UpdateAccount abstracts an account write to the trie. It encodes the
            provided account object with associated algorithm and then updates it
        in the trie with provided address.
        """
        raise NotImplementedError

    @abstractmethod
    def updateStorage(self, address: bytes, key: bytes, value: bytes) -> None:
        """
        UpdateStorage associates key with value in the trie. If value has length zero,
        any existing value is deleted from the trie. The value bytes must not be modified
        by the caller while they are stored in the trie. If a node was not found in the
        database, a trie.MissingNodeError is returned.
        """
        raise NotImplementedError

    @abstractmethod
    def deleteAccount(self, address: bytes) -> None:
        """
        DeleteAccount abstracts an account deletion from the trie.
        """
        raise NotImplementedError

    @abstractmethod
    def deleteStorage(self, address: bytes, key: bytes) -> None:
        """
        DeleteStorage removes any existing value for key from the trie. If a node
        was not found in the database, a MissingNodeError is raised.
        """
        raise NotImplementedError

    # TODO: not allow upgrade contract now
    # def updateContractCode(self, address: bytes, code: bytes) -> None:
    #     """
    #     UpdateContractCode abstracts code write to the trie. It is expected
    #     to be moved to the stateWriter interface when the latter is ready.
    #     """
    #     raise NotImplementedError

    @abstractmethod
    def hash(self) -> bytes:
        """
        Hash returns the root hash of the trie.
        """
        raise NotImplementedError

    @abstractmethod
    def commit(self) -> Tuple[bytes, triedb.TrieChanges]:
        """
        Commit writes any changes to the trie to the underlying database and returns the root hash.
        """
        raise NotImplementedError

    @abstractmethod
    def isCommitted(self) -> bool:
        """
        IsCommitted returns whether the trie is committed.
        """
        raise NotImplementedError

    # TODO node iterator

    # TODO prove
