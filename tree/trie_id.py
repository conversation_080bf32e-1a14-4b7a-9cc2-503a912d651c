from __future__ import annotations

import dataclasses

from trie import HexaryTrie


@dataclasses.dataclass
class ID:
    stateRoot: bytes
    owner: bytes
    root: bytes


def stateTrieId(root: bytes) -> ID:
    """
    StateTrieID constructs an identifier for state trie with the provided state root.
    """
    return ID(root, b"", root)


def storageTrieId(stateRoot: bytes, owner: bytes, root: bytes) -> ID:
    """
    StorageTrieID constructs an identifier for storage trie which belongs to a certain
    state and contract specified by the stateRoot and owner.
    """
    return ID(stateRoot, owner, root)


def emptyTrieId() -> ID:
    """
    EmptyTrieID constructs an identifier for an empty trie.
    """
    return stateTrieId(HexaryTrie.BLANK_NODE_HASH)
