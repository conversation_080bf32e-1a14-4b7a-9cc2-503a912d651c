from __future__ import annotations

from typing import Optional, Tuple

import eth_utils
import rlp
from trie import <PERSON><PERSON>ry<PERSON>rie
from trie.exceptions import MissingTrieNode

import common
from common import encodeRLP

from . import trie_abc as trieabc
from . import trie_id as trieid
from . import triedb


class TrieAlreadyCommitted(Exception):
    """
    TrieAlreadyCommitted is returned when a trie is attempted to be modified
    after it has been committed.
    """

    def __init__(self):
        super().__init__("trie is already committed")


class StateTrie(HexaryTrie, trieabc.TrieABC):
    def __init__(self, id: trieid.ID, db: triedb.TrieDB, commited: bool = False):
        self.diskdb: triedb.TrieDB = db
        super().__init__(db, id.root)
        self.committed: bool = commited

    def get(self, key: bytes) -> Optional[bytes]:
        if self.committed:
            raise TrieAlreadyCommitted
        try:
            return super().get(key)
        except rlp.exceptions.DecodingError as e:
            if e.args[0] == "Can only decode RLP bytes, got type NoneType":
                raise MissingTrieNode(b"", self.root_hash, key) from e

    def set(self, key: bytes, value: bytes):
        if self.committed:
            raise TrieAlreadyCommitted
        super().set(key, value)

    def commit(self) -> Tuple[bytes, triedb.TrieChanges]:
        if self.committed:
            raise TrieAlreadyCommitted
        self.committed = True
        return self.hash(), self.diskdb.commit()

    def isCommitted(self) -> bool:
        """
        IsCommitted returns whether the trie is committed.
        """
        return self.committed

    def reset(self):
        self.diskdb.reset()
        self.committed = False

    def getKey(self, key: bytes) -> bytes:
        return self.get(key)

    def getAccount(self, address: bytes) -> Optional[common.StateAccount]:
        hashKey = _hashKey(address)
        data = self.get(hashKey)
        if data == b"" or data is None:
            return None
        return rlp.decode(data, common.StateAccountRLP).toModel()

    def getStorage(self, _address: bytes, key: bytes) -> Optional[bytes]:
        hashKey = _hashKey(key)
        try:
            encodedValue = self.get(hashKey)
        except MissingTrieNode:
            # the storage key does not exist
            return None
        if encodedValue == b"":
            return None
        return rlp.decode(encodedValue)

    def updateAccount(self, address: bytes, account: common.StateAccount) -> None:
        hashKey = _hashKey(address)
        encodedAccount = encodeRLP(account)
        self.set(hashKey, encodedAccount)

    def updateStorage(self, _address: bytes, key: bytes, value: bytes) -> None:
        hashKey = _hashKey(key)
        encodedValue = rlp.encode(value)
        self.set(hashKey, encodedValue)

    def deleteAccount(self, address: bytes) -> None:
        hash_key = _hashKey(address)
        self.delete(hash_key)

    def deleteStorage(self, _address: bytes, key: bytes) -> None:
        hash_key = _hashKey(key)
        self.delete(hash_key)

    def hash(self) -> bytes:
        """
        Hash returns the root hash of the trie. It does not write to the
        database and can be used even if the trie doesn't have one.
        """
        return self.root_hash

    def copy(self) -> trieabc.TrieABC:
        """
        copy returns a new trie with the same root hash and database as the
        current trie.
        """
        return StateTrie(trieid.stateTrieId(self.root_hash), self.diskdb, self.committed)

    def __repr__(self):
        return f"StateTrie({self.root_hash})"

    # TODO node iterator

    # TODO prove


def _hashKey(key: bytes) -> bytes:
    return eth_utils.keccak(key)
