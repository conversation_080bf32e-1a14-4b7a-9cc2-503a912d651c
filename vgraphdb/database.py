from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Iterator, Optional


class KeyV<PERSON>ueReader(ABC):
    """
    KeyValueReader wraps the contains and get method of a backing data store.
    """

    @abstractmethod
    def contains(self, key: bytes) -> bool:
        """
        Contains retrieves if a key is present in the key-value data store.
        """
        raise NotImplementedError

    @abstractmethod
    def get(self, key: bytes) -> Optional[bytes]:
        """
        Get retrieves the given key if it's present in the key-value data store.
        """
        raise NotImplementedError

    def __getitem__(self, key: bytes) -> Optional[bytes]:
        return self.get(key)

    def __contains__(self, key: bytes) -> bool:
        return self.contains(key)


class KeyValueWriter(ABC):
    """
    KeyValueWriter wraps the Put method of a backing data store.
    """

    @abstractmethod
    def put(self, key: bytes, value: bytes) -> None:
        """
        Put inserts the given value into the key-value data store.
        """
        raise NotImplementedError

    @abstractmethod
    def delete(self, key: bytes) -> None:
        """
        Delete removes the key from the key-value data store.
        """
        raise NotImplementedError

    def __setitem__(self, key: bytes, value: bytes) -> None:
        self.put(key, value)

    def __delitem__(self, key: bytes) -> None:
        self.delete(key)


class Batcher(ABC):
    """
    Batcher wraps the NewBatch method of a backing data store.
    """

    @abstractmethod
    def newBatch(self) -> Batch:
        """
        NewBatch creates a write-only database that buffers changes to its host db
        until a final write is called.
        """
        raise NotImplementedError


class Batch(KeyValueWriter):
    """
    Batch is a write-only database that commits changes to its host database
    when Write is called. A batch cannot be used concurrently.
    """

    @abstractmethod
    def valueSize(self) -> int:
        """
        ValueSize retrieves the amount of data queued up for writing.
        """
        raise NotImplementedError

    @abstractmethod
    def write(self) -> None:
        """
        Write flushes any accumulated data to disk.
        """
        raise NotImplementedError

    @abstractmethod
    def reset(self) -> None:
        """
        Reset resets the batch for reuse.
        """
        raise NotImplementedError

    @abstractmethod
    def replay(self, writer: KeyValueWriter) -> None:
        """
        Replay replays the batch contents.
        """
        raise NotImplementedError


class DBIterator(ABC):
    """
    Iterator iterates over a database's key/value pairs in ascending key order.

    When it encounters an error any seek will return false and will yield no key/
    value pairs. The error can be queried by calling the Error method. Calling
    Release is still necessary.

    An iterator must be released after use, but it is not necessary to read an
    iterator until exhaustion. An iterator is not safe for concurrent use, but it
    is safe to use multiple iterators concurrently.
    """

    @abstractmethod
    def next(self) -> bool:
        """
        Next moves the iterator to the next key/value pair. It returns whether the
        iterator is exhausted.
        """
        raise NotImplementedError

    @abstractmethod
    def key(self) -> Optional[bytes]:
        """
        Key returns the key of the current key/value pair, or None if done. The caller
        should not modify the contents of the returned slice, and its contents may
        change on the next call to Next.
        """
        raise NotImplementedError

    @abstractmethod
    def value(self) -> Optional[bytes]:
        """
        Value returns the value of the current key/value pair, or None if done. The
        caller should not modify the contents of the returned slice, and its contents
        may change on the next call to Next.
        """
        raise NotImplementedError

    @abstractmethod
    def release(self):
        """
        Release releases associated resources. Release should always succeed and can
        be called multiple times without causing an error.
        """
        raise NotImplementedError

    def __iter__(self):
        """
        Returns an iterator object. This is part of the Python iterator protocol.
        """
        return self

    def __next__(self):
        """
        Moves the iterator to the next key/value pair and returns the key. If the
        iterator is exhausted, it raises StopIteration. This is part of the Python
        iterator protocol.
        """
        if not self.next():
            raise StopIteration
        return self

    def __del__(self):
        self.release()


class Iteratee(ABC):
    """
    Iteratee wraps the NewIterator methods of a backing data store.
    """

    @abstractmethod
    def newIterator(self, prefix: bytes = b"", start: bytes = b"", end: bytes = b"") -> Iterator:
        """
        NewIterator creates a binary-alphabetical iterator over a subset
        of database content with a particular key prefix, starting at a particular
        initial key (or after, if it does not exist) and ending at a particular key
        (inclusive).

        Args:
            prefix: The prefix to filter keys
            start: The key to start iteration from (inclusive)
            end: The key to end iteration at (inclusive)

        Note: This method assumes that the prefix is NOT part of the start or end, so there's
        no need for the caller to prepend the prefix to the start or end
        """
        raise NotImplementedError

    @abstractmethod
    def newReverseIterator(self, prefix: bytes = b"", start: bytes = b"", end: bytes = b"") -> Iterator:
        """
        newReverseIterator creates a binary-alphabetical reverse iterator over a subset
        of database content with a particular key prefix, starting at a particular
        initial key and moving backwards until reaching the end key.

        Args:
            prefix: The prefix to filter keys
            start: The key to start reverse iteration from (inclusive)
            end: The lower bound key to stop iteration at (inclusive)

        Note: This method assumes that the prefix is NOT part of the start or end, so there's
        no need for the caller to prepend the prefix to the start or end
        """
        raise NotImplementedError


class Closer(ABC):
    """
    The Closer class is an abstract class for closing resources.
    """

    def __del__(self):
        self.close()

    @abstractmethod
    def close(self):
        """close the resources"""
        raise NotImplementedError


class KeyValueStore(KeyValueReader, KeyValueWriter, Batcher, Iteratee, Closer):
    """
    Database contains all the methods required to allow handling different
    key-value data stores backing the high level database.
    """

    pass
