from __future__ import annotations

from typing import Dict, Optional

import lmdb

import config

from . import database

dbPath = f"{config.CONFIG.dbPath}_{config.CONFIG.chainId}"
maxDbSize = 3 * 1024 * 1024 * 1024 * 1024  # 3TB


class Lmdb(database.KeyValueStore):
    """
    The Lmdb class implements the key-value database layer based on LMDB.

    Attributes:
        db (lmdb.Environment): The LMDB environment instance.
    """

    ErrLmdbClosed: Exception = Exception("lmdb closed")

    def __init__(self, path: str = None, maxSize: int = None):
        """
        Initialize a new LMDB database.

        Args:
            path: Path to the LMDB database (default: uses config values)
            maxSize: Maximum size of the database in bytes (default: 3TB)
        """
        if path is None:
            path = dbPath
        if maxSize is None:
            maxSize = maxDbSize

        self.path = path
        self.db = lmdb.open(path, create=True, map_size=maxSize)

    # Implement Closer

    def close(self):
        """
        Close closes the database.
        """
        if self.db is not None:
            self.db.close()
            self.db = None

    # Implement KeyValueReader

    def contains(self, key: bytes) -> bool:
        """
        Contains retrieves if a key is present in the key-value data store.
        """
        with self.db.begin() as txn:
            return txn.get(key) is not None

    def get(self, key: bytes) -> Optional[bytes]:
        """
        Get retrieves the given key if it's present in the key-value data store.
        """
        with self.db.begin() as txn:
            return txn.get(key)

    # Implement KeyValueWriter

    def put(self, key: bytes, value: bytes) -> None:
        """
        Put inserts the given value into the key-value data store.
        """
        with self.db.begin(write=True) as txn:
            txn.put(key, value)

    def delete(self, key: bytes) -> None:
        """
        Delete removes the key from the key-value data store.
        """
        with self.db.begin(write=True) as txn:
            txn.delete(key)

    # Implement Batcher

    def newBatch(self) -> database.Batch:
        """
        NewBatch creates a write-only key-value store that buffers changes to its host
        database until a final write is called.
        """
        return LmdbBatch(self)

    # Implement Iteratee

    def newIterator(self, prefix: bytes = b"", start: bytes = b"", end: bytes = b"") -> database.DBIterator:
        """
        NewIterator creates a binary-alphabetical iterator over a subset
        of database content with a particular key prefix, starting at a particular
        initial key (or after, if it does not exist).

        Args:
            prefix: The prefix to filter keys
            start: The key to start iteration from (inclusive)
            end: The key to end iteration at (inclusive)
        """
        return _LmdbIterator(self.db, prefix, start, end)

    def newReverseIterator(self, prefix: bytes = b"", start: bytes = b"", end: bytes = b"") -> database.DBIterator:
        """
        newReverseIterator creates a binary-alphabetical reverse iterator over a subset
        of database content with a particular key prefix, starting at a particular
        initial key and moving backwards.

        Args:
            prefix: The prefix to filter keys
            start: The key to start reverse iteration from (inclusive)
            end: The lower bound key to stop iteration at (inclusive)

        Returns:
            LmdbReverseIterator: A reverse iterator that supports variable-length keys
        """
        return LmdbReverseIterator(self.db, prefix, start, end)


class LmdbBatch(database.Batch):
    """
    The LmdbBatch class implements the Batch interface for LMDB.
    """

    def __init__(self, db: Lmdb):
        self.db = db
        self.writes: Dict[bytes, Optional[bytes]] = {}
        self.size = 0

    #  Implement KeyValueWriter

    def put(self, key: bytes, value: bytes) -> None:
        """
        Put inserts the given value into the key-value data store.
        """
        if self.db.db is None:
            raise Lmdb.ErrLmdbClosed
        self.writes[key] = value
        self.size += len(key) + len(value)

    def delete(self, key: bytes) -> None:
        """
        Delete removes the key from the key-value data store.
        """
        if self.db.db is None:
            raise Lmdb.ErrLmdbClosed
        self.writes[key] = None
        self.size += len(key)

    # Implement Batch

    def write(self) -> None:
        """
        Write commits the batched changes to the database.
        """
        if self.db.db is None:
            raise Lmdb.ErrLmdbClosed
        with self.db.db.begin(write=True) as txn:
            for key, value in self.writes.items():
                if value is None:
                    txn.delete(key)
                else:
                    txn.put(key, value)

    def valueSize(self) -> int:
        """
        ValueSize retrieves the amount of data queued up for writing.
        """
        return self.size

    def reset(self) -> None:
        """
        Reset resets the batch for reuse.
        """
        self.writes.clear()
        self.size = 0

    def replay(self, writer: database.KeyValueWriter) -> None:
        """
        Replay replays the batch contents.
        """
        for key, value in self.writes.items():
            if value is None:
                writer.delete(key)
                continue
            writer.put(key, value)


class _LmdbIterator(database.DBIterator):
    """
    Iterator can walk over the (potentially partial) keyspace of a memory key
    value store. Internally it is a deep copy of the entire iterated state,
    sorted by keys.
    """

    def __init__(self, dbEnv: lmdb.Environment, prefix: bytes, start: bytes, end: bytes = b""):
        self.prefix = prefix
        self.start = start
        self.end = end
        self.readTxn = dbEnv.begin()
        self.cursor = None

    def next(self) -> bool:
        """
        Next moves the iterator to the next key/value pair. It returns whether the
        iterator is exhausted.

        Returns:
            bool: True if a valid next key/value pair exists, False if iteration is complete
        """
        if self.cursor is None:
            self.cursor = self.readTxn.cursor()
            # Try to position cursor at or after start key
            if not self.cursor.set_range(self.prefix + self.start):
                return False

            # Check if current key has the required prefix
            hasPrefix = self.cursor.key().startswith(self.prefix)
            if not hasPrefix:
                return False

            # If end key specified, check if current key is within bounds
            if self.end != b"":
                return self.cursor.key() <= self.prefix + self.end
            return True

        # Move to next key and verify it has the required prefix
        hasNextKey = self.cursor.next()
        if not hasNextKey:
            return False

        hasPrefix = self.cursor.key().startswith(self.prefix)
        if not hasPrefix:
            return False

        # If end key specified, check if still within bounds
        if self.end != b"":
            return self.cursor.key() <= self.prefix + self.end
        return True

    def key(self) -> Optional[bytes]:
        """
        Returns the key of the current key/value pair, or None if done.
        """
        return self.cursor.key() if self.cursor else None

    def value(self) -> Optional[bytes]:
        """
        Returns the value of the current key/value pair, or None if done.
        """
        return self.cursor.value() if self.cursor else None

    def release(self):
        """
        Releases associated resources.
        """
        if self.cursor is not None:
            self.cursor.close()
            self.readTxn.abort()
            self.cursor = None
            self.readTxn = None


class LmdbReverseIterator(database.DBIterator):
    """
    Iterator that walks over the keyspace of an LMDB database in reverse order.
    This implementation supports keys of variable length and correctly handles
    prefix, start, and end boundaries.
    """

    def __init__(self, dbEnv: lmdb.Environment, prefix: bytes, start: bytes, end: bytes = b""):
        """
        Initialize a new reverse iterator.

        Args:
            dbEnv: The LMDB environment to iterate over
            prefix: The prefix to filter keys
            start: The key to start reverse iteration from (inclusive)
            end: The lower bound key to stop iteration at (inclusive)
        """
        self.prefix = prefix
        self.start = start
        self.end = end
        self.readTxn = dbEnv.begin()
        self.cursor = None
        self.initialized = False
        self.validPosition = False

        # No need to collect all keys in advance for streaming implementation

    def next(self) -> bool:
        """
        Moves the iterator to the previous key/value pair starting from the
        key <= prefix + start. Returns whether the iterator is exhausted.

        Returns:
            bool: True if a valid previous key/value pair exists, False if iteration is complete
        """
        # If readTxn is None, the iterator has been released
        if self.readTxn is None:
            return False

        if not self.initialized:
            return self._initialize()

        # Streaming implementation doesn't use all_keys list

        # Already initialized but in invalid position
        if not self.validPosition:
            return False

        # Move to previous key
        if not self.cursor.prev():
            self.validPosition = False
            return False

        # Check if the key is still valid (has prefix and within boundaries)
        self.validPosition = self._checkBoundaries()
        return self.validPosition

    def _initialize(self) -> bool:
        """
        Initialize the iterator by positioning the cursor at the appropriate starting point.

        Returns:
            bool: True if successfully positioned at a valid key, False otherwise
        """
        self.initialized = True
        self.cursor = self.readTxn.cursor()

        # In reverse iteration, start should be >= end
        # But we'll handle this in _checkBoundaries instead of rejecting outright
        # This allows more flexible range queries

        # Position the cursor at the appropriate starting point
        if self.start:
            startKey = self.prefix + self.start

            # First, try to position at the start key
            if self.cursor.set_key(startKey):
                # Exact match found, check if it has the required prefix
                if not self.cursor.key().startswith(self.prefix):
                    return self._setInvalid()
            else:
                # Start key not found, find the largest key <= startKey
                if self.cursor.set_range(startKey):
                    # Found a key >= startKey, move back one position if needed
                    if self.cursor.key() > startKey:
                        if not self.cursor.prev():
                            return self._setInvalid()
                else:
                    # No key >= startKey, go to the last key
                    if not self.cursor.last():
                        return self._setInvalid()

                # Check if the key has the required prefix
                if not self.cursor.key().startswith(self.prefix):
                    # Use a more efficient method to find the last key with the prefix
                    if not self._findLastKeyWithPrefix():
                        return False  # _findLastKeyWithPrefix already sets validPosition to False
        else:
            # No start key, find the last key with our prefix
            if self.prefix:
                if not self._findLastKeyWithPrefix():
                    return False  # _findLastKeyWithPrefix already sets validPosition to False
            else:
                # No prefix specified, just position at the last key
                if not self.cursor.last():
                    return self._setInvalid()

        # Check if the current key is within the specified boundaries
        self.validPosition = self._checkBoundaries()
        return self.validPosition

    def _setInvalid(self) -> bool:
        """
        Helper method to set the iterator to an invalid position.

        Returns:
            bool: Always returns False
        """
        self.validPosition = False
        return False

    def _findLastKeyWithPrefix(self) -> bool:
        """
        Find the last key with the specified prefix using an efficient approach.

        Returns:
            bool: True if a key with the prefix was found, False otherwise
        """
        if not self.prefix:
            return self._setInvalid()

        # Construct a key that is just beyond the prefix range
        # This creates a key that is lexicographically greater than any key with the prefix
        next_prefix = self._getNextPrefix(self.prefix)

        # Try to position at or after the next_prefix
        if self.cursor.set_range(next_prefix):
            # Found a key >= next_prefix, move back one position to get the last key < next_prefix
            if not self.cursor.prev():
                return self._setInvalid()
        else:
            # No key >= next_prefix, go to the last key in the database
            if not self.cursor.last():
                return self._setInvalid()

        # Check if the key has our prefix
        if not self.cursor.key().startswith(self.prefix):
            return self._setInvalid()

        return True

    def _getNextPrefix(self, prefix: bytes) -> bytes:
        """
        Calculate the next prefix boundary that is lexicographically greater than any key with the given prefix.

        For example, if prefix is b"user:", this returns b"user;", which is greater than any key starting with "user:"

        Args:
            prefix: The prefix to calculate the boundary for

        Returns:
            bytes: A key that is lexicographically greater than any key with the given prefix
        """
        if not prefix:
            return b"\xff\xff"  # If no prefix, return a very high value

        # Special case for 0xFF prefix - we need to handle this differently
        if prefix == b"\xff":
            return b"\xff\xff\xff"  # Return a value higher than any key starting with 0xFF

        # Convert prefix to bytearray so we can modify it
        next_prefix = bytearray(prefix)

        # Find the last byte that can be incremented
        for i in range(len(next_prefix) - 1, -1, -1):
            if next_prefix[i] < 255:
                next_prefix[i] += 1
                next_prefix = next_prefix[: i + 1]  # Truncate the rest
                return bytes(next_prefix)

        # If we can't increment any byte (all are 0xFF), append a 0x00 byte
        return prefix + b"\x00"

    def _checkBoundaries(self) -> bool:
        """
        Check if the current key is within the specified boundaries.

        Returns:
            bool: True if the current key is valid and within bounds, False otherwise
        """
        if not self.cursor:
            return False

        currentKey = self.cursor.key()

        # Optimized prefix check
        prefixLen = len(self.prefix)
        if prefixLen > 0:
            if len(currentKey) < prefixLen or currentKey[:prefixLen] != self.prefix:
                return False

        # Optimized end key check
        if self.end:
            endKey = self.prefix + self.end
            # For reverse iteration, we need to ensure the key is >= endKey
            # This prevents keys from other prefixes being included
            if currentKey < endKey:
                return False

        # For reverse iteration, we also need to check the start boundary
        if self.start:
            startKey = self.prefix + self.start
            # Ensure the key is <= startKey
            if currentKey > startKey:
                return False

        return True

    def key(self) -> Optional[bytes]:
        """
        Returns the key of the current key/value pair, or None if done.

        Returns:
            bytes: The current key or None if the iterator is not at a valid position
        """
        if not self.validPosition or not self.cursor:
            return None

        return self.cursor.key()

    def value(self) -> Optional[bytes]:
        """
        Returns the value of the current key/value pair, or None if done.

        Returns:
            bytes: The current value or None if the iterator is not at a valid position
        """
        if not self.validPosition or not self.cursor:
            return None

        return self.cursor.value()

    def release(self):
        """
        Releases associated resources. This should always succeed and can
        be called multiple times without causing errors.
        """
        if self.cursor is not None:
            self.cursor.close()
            self.cursor = None

        if self.readTxn is not None:
            self.readTxn.abort()
            self.readTxn = None

        self._setInvalid()
        self.initialized = False
