from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any, Dict

import pytest

from .. import database


class TestDatabaseSuite(ABC):
    """
    TestDatabaseSuite runs a suite of tests against a KeyValueStore database implementation
    """

    @pytest.fixture
    def db(self) -> database.KeyValueStore:
        """
        db returns a KeyValueStore instance, and automic uses __del__ to close it after the test completes
        """
        db = self.cleanInitDb()
        return db

    @abstractmethod
    def cleanInitDb(self) -> database.KeyValueStore:
        """
        cleanAndInitDb will clean up the database and return a new instance of the database
        """
        raise NotImplementedError

    @pytest.mark.parametrize(
        "testCase",
        [
            {"content": {}, "prefix": b"", "start": b"", "order": []},
            {"content": {}, "prefix": b"non-existent-prefix", "start": b"", "order": []},
            {"content": {b"key": b"val"}, "prefix": b"", "start": b"", "order": [b"key"]},
            {"content": {b"key": b"val"}, "prefix": b"k", "start": b"", "order": [b"key"]},
            {"content": {b"key": b"val"}, "prefix": b"l", "start": b"", "order": []},
            {
                "content": {b"k1": b"v1", b"k5": b"v5", b"k2": b"v2", b"k4": b"v4", b"k3": b"v3"},
                "prefix": b"",
                "start": b"",
                "order": [b"k1", b"k2", b"k3", b"k4", b"k5"],
            },
            {
                "content": {b"k1": b"v1", b"k5": b"v5", b"k2": b"v2", b"k4": b"v4", b"k3": b"v3"},
                "prefix": b"k",
                "start": b"",
                "order": [b"k1", b"k2", b"k3", b"k4", b"k5"],
            },
            {
                "content": {b"k1": b"v1", b"k5": b"v5", b"k2": b"v2", b"k4": b"v4", b"k3": b"v3"},
                "prefix": b"l",
                "start": b"",
                "order": [],
            },
            {
                "content": {
                    b"ka1": b"va1",
                    b"ka5": b"va5",
                    b"ka2": b"va2",
                    b"ka4": b"va4",
                    b"ka3": b"va3",
                    b"kb1": b"vb1",
                    b"kb5": b"vb5",
                    b"kb2": b"vb2",
                    b"kb4": b"vb4",
                    b"kb3": b"vb3",
                },
                "prefix": b"ka",
                "start": b"",
                "order": [b"ka1", b"ka2", b"ka3", b"ka4", b"ka5"],
            },
            {
                "content": {
                    b"ka1": b"va1",
                    b"ka5": b"va5",
                    b"ka2": b"va2",
                    b"ka4": b"va4",
                    b"ka3": b"va3",
                    b"kb1": b"vb1",
                    b"kb5": b"vb5",
                    b"kb2": b"vb2",
                    b"kb4": b"vb4",
                    b"kb3": b"vb3",
                },
                "prefix": b"kc",
                "start": b"",
                "order": [],
            },
            {
                "content": {
                    b"ka1": b"va1",
                    b"ka5": b"va5",
                    b"ka2": b"va2",
                    b"ka4": b"va4",
                    b"ka3": b"va3",
                    b"kb1": b"vb1",
                    b"kb5": b"vb5",
                    b"kb2": b"vb2",
                    b"kb4": b"vb4",
                    b"kb3": b"vb3",
                },
                "prefix": b"ka",
                "start": b"3",
                "order": [b"ka3", b"ka4", b"ka5"],
            },
            {
                "content": {
                    b"ka1": b"va1",
                    b"ka5": b"va5",
                    b"ka2": b"va2",
                    b"ka4": b"va4",
                    b"ka3": b"va3",
                    b"kb1": b"vb1",
                    b"kb5": b"vb5",
                    b"kb2": b"vb2",
                    b"kb4": b"vb4",
                    b"kb3": b"vb3",
                },
                "prefix": b"ka",
                "start": b"8",
                "order": [],
            },
            {
                "content": {b"k1": b"v1", b"k5": b"v5", b"k2": b"v2", b"k4": b"v4", b"k3": b"v3"},
                "prefix": b"k",
                "start": b"2",
                "end": b"4",
                "order": [b"k2", b"k3", b"k4"],
            },
            {
                "content": {b"k1": b"v1", b"k5": b"v5", b"k2": b"v2", b"k4": b"v4", b"k3": b"v3"},
                "prefix": b"k",
                "start": b"",
                "end": b"3",
                "order": [b"k1", b"k2", b"k3"],
            },
            {
                "content": {
                    b"ka1": b"va1",
                    b"ka5": b"va5",
                    b"ka2": b"va2",
                    b"ka4": b"va4",
                    b"ka3": b"va3",
                    b"kb1": b"vb1",
                },
                "prefix": b"ka",
                "start": b"2",
                "end": b"4",
                "order": [b"ka2", b"ka3", b"ka4"],
            },
        ],
    )
    def testIterator(self, db: database.KeyValueStore, testCase: Dict[str, Any]):
        # Create the key-value data store
        for key, val in testCase["content"].items():
            db.put(key, val)

        # Iterate over the database with the given configs and verify the results
        it, idx = db.newIterator(testCase["prefix"], testCase["start"], testCase.get("end", b"")), 0

        expectingLen = len(testCase["order"])
        while it.next():
            if expectingLen <= idx:
                pytest.fail(
                    f"prefix={testCase['prefix']} more items than expected: checking idx={idx} (key {it.key()}), expecting len={expectingLen}"
                )

            assert it.key() == testCase["order"][idx], (
                f"item {idx}: key mismatch: have {it.key()}, want {testCase['order'][idx]}"
            )
            assert it.value() == testCase["content"][testCase["order"][idx]], (
                f"item {idx}: value mismatch: have {it.value()}, want {testCase['content'][testCase['order'][idx]]}"
            )
            idx += 1

        assert idx == expectingLen, f"iteration terminated prematurely: have {idx}, want {expectingLen}"

    def testIteratorWith(self, db: database.KeyValueStore):
        keys = [b"1", b"2", b"3", b"4", b"6", b"10", b"11", b"12", b"20", b"21", b"22"]
        keys.sort()

        for key in keys:
            db[key] = b""

        # Test 1: Iterate over all keys
        it = db.newIterator(b"", b"")
        result = [kv.key() for kv in it]
        assert result == keys, f"got {result}, want {keys}"

        # Test 2: Iterate with prefix "1"
        it = db.newIterator(b"1", b"")
        result = [kv.key() for kv in it]
        assert result == [b"1", b"10", b"11", b"12"], f"got {result}, want [b'1', b'10', b'11', b'12']"

        # Test 3: Iterate with prefix "5"
        it = db.newIterator(b"5", b"")
        result = [it.key() for it in it]
        assert result == [], f"got {result}, want []"

        # Test 4: Iterate with start "2"
        it = db.newIterator(b"", b"2")
        result = [it.key() for it in it]
        assert result == [
            b"2",
            b"20",
            b"21",
            b"22",
            b"3",
            b"4",
            b"6",
        ], f"got {result}, want [b'2', b'20', b'21', b'22', b'3', b'4', b'6']"

        # Test 5: Iterate with start "5"
        it = db.newIterator(b"", b"5")
        result = [it.key() for it in it]
        assert result == [b"6"], f"got {result}, want [b'6']"

    def testKeyValueOperations(self, db: database.KeyValueStore):
        key = b"foo"
        got = key in db
        assert not got, f"key {key} should not exist in the database"

        value = b"hello world"
        db[key] = value
        got = key in db
        assert got, f"key {key} should exist in the database"

        got = db[key]
        assert got == value, f"wrong value: {got}"

        del db[key]
        got = key in db
        assert not got, f"key {key} should not exist in the database"

        db[key] = value
        got = db.get(key)
        db.delete(key)
        assert got == value, f"wrong value: {got}"

    def testBatch(self, db: database.KeyValueStore):
        b = db.newBatch()

        keys = [b"1", b"2", b"3", b"4"]
        for k in keys:
            b.put(k, b"")

        contains = db.contains(b"1")
        assert not contains, "key 1 should not exist in the batch"

        b.write()
        contains = db.contains(b"1")
        assert contains, "key 1 should exist in the batch"

        for i, kv in enumerate(db.newIterator(b"", b"")):
            assert kv.key() == keys[i], f"got {kv.key()}, want {keys[i]}"

        b.reset()

        # Mix writes and deletes in batch
        b.put(b"5", b"")
        b.delete(b"1")
        b.put(b"6", b"")

        b.delete(b"3")
        b.put(b"3", b"")

        b.put(b"7", b"")
        b.delete(b"7")

        b.write()

        keys = [b"2", b"3", b"4", b"5", b"6"]

        for i, kv in enumerate(db.newIterator(b"", b"")):
            assert kv.key() == keys[i], f"got {kv.key()}, want {keys[i]}"

    def testBatchReplay(self, db: database.KeyValueStore):
        want = {b"1": b"one", b"2": b"two", b"3": b"three"}

        b = db.newBatch()
        for k, v in want.items():
            b.put(k, v)

        b2 = db.newBatch()
        b.replay(b2)

        b2.replay(db)

        for i, kv in enumerate(db.newIterator(b"", b"")):
            assert kv.key() == list(want.keys())[i], f"got {kv.key()}, want {list(want.keys())[i]}"
            assert kv.value() == list(want.values())[i], f"got {kv.value()}, want {list(want.values())[i]}"

    def testOperationsAfterClose(self):
        db = self.cleanInitDb()
        db.close()

        with pytest.raises(Exception):  # noqa
            db.put(b"key", b"value")

        with pytest.raises(Exception):  # noqa
            db.get(b"key")

        with pytest.raises(Exception):  # noqa
            db.delete(b"key")

        with pytest.raises(Exception):  # noqa
            db.contains(b"key")

        with pytest.raises(Exception):  # noqa
            db.pop(b"key")

        b = db.newBatch()
        with pytest.raises(Exception):  # noqa
            b.put(b"key", b"value")

        with pytest.raises(Exception):  # noqa
            b.delete(b"key")

        with pytest.raises(Exception):  # noqa
            b.pop(b"key")

        with pytest.raises(Exception):  # noqa
            b.write()

    def testReverseIterator(self, db: database.KeyValueStore):
        """Test the reverse iterator functionality"""
        # Test data
        data = {
            b"k1": b"v1",
            b"k2": b"v2",
            b"k3": b"v3",
            b"k4": b"v4",
            b"k5": b"v5",
            b"ka1": b"va1",
            b"ka2": b"va2",
            b"kb1": b"vb1",
        }
        for k, v in data.items():
            db.put(k, v)

        # Test cases
        test_cases = [
            {
                "prefix": b"k",
                "start": b"5",
                "end": b"2",
                "expected": [b"k5", b"k4", b"k3", b"k2"],
            },
            {
                "prefix": b"ka",
                "start": b"2",
                "end": b"1",
                "expected": [b"ka2", b"ka1"],
            },
            {
                "prefix": b"k",
                "start": b"3",
                "end": b"",
                "expected": [b"k3", b"k2", b"k1"],
            },
            {
                "prefix": b"kc",
                "start": b"",
                "end": b"",
                "expected": [],
            },
        ]

        # Sort expected keys in reverse lexicographical order for each test case
        for tc in test_cases:
            tc["expected"] = sorted(tc["expected"], reverse=True)

        for tc in test_cases:
            it = db.newReverseIterator(tc["prefix"], tc["start"], tc["end"])
            result = []
            while it.next():
                result.append(it.key())
                assert it.value() == data[it.key()], f"wrong value for key {it.key()}"

            assert result == tc["expected"], (
                f"wrong keys for prefix={tc['prefix']}, start={tc['start']}, end={tc['end']}\n"
                f"got: {result}\nwant: {tc['expected']}"
            )

    def testIteratorBoundaries(self, db: database.KeyValueStore):
        """Test iterator behavior with various boundary conditions"""
        data = {b"k1": b"v1", b"k2": b"v2", b"k3": b"v3", b"k4": b"v4", b"k5": b"v5"}
        for k, v in data.items():
            db.put(k, v)

        # Forward iterator tests
        it = db.newIterator(b"k", b"2", b"4")
        result = []
        while it.next():
            result.append(it.key())
        assert result == [b"k2", b"k3", b"k4"], "forward iterator with bounds failed"

        # Reverse iterator tests
        it = db.newReverseIterator(b"k", b"4", b"2")
        result = []
        while it.next():
            result.append(it.key())
        expected = sorted([b"k4", b"k3", b"k2"], reverse=True)
        assert result == expected, "reverse iterator with bounds failed"

        # Test empty ranges
        it = db.newIterator(b"k", b"4", b"2")
        assert not it.next(), "forward iterator with invalid range should be empty"

        it = db.newReverseIterator(b"k", b"2", b"4")
        assert not it.next(), "reverse iterator with invalid range should be empty"
