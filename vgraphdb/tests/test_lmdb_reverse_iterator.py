"""
Combined test module for the enhanced LMDB reverse iterator implementation.
This script tests both the low-level LmdbReverseIterator class and its integration
with the Lmdb class in real-world scenarios.
"""

import os
import shutil

import lmdb
import pytest

from ..lmdb import Lmdb, LmdbReverseIterator, dbPath


@pytest.fixture
def lmdbEnv():
    """Set up a temporary LMDB environment for testing the LmdbReverseIterator directly."""
    # Remove the LMDB database file if it exists
    if os.path.exists(dbPath):
        shutil.rmtree(dbPath)
    env = lmdb.open(dbPath, map_size=10485760)  # 10MB

    # Insert test data with variable-length keys
    with env.begin(write=True) as txn:
        # Keys with prefix "a:"
        txn.put(b"a:1", b"value1")
        txn.put(b"a:2", b"value2")
        txn.put(b"a:10", b"value10")
        txn.put(b"a:100", b"value100")

        # Keys with prefix "b:"
        txn.put(b"b:1", b"valueb1")
        txn.put(b"b:2", b"valueb2")
        txn.put(b"b:short", b"valuebshort")
        txn.put(b"b:longer_key", b"valueblonger")
        txn.put(b"b:very_long_key_with_more_text", b"valuebverylong")

        # Keys with prefix "c:"
        txn.put(b"c:1", b"valuec1")
        txn.put(b"c:z", b"valuecz")

    yield env

    # Cleanup
    env.close()
    if os.path.exists(dbPath):
        shutil.rmtree(dbPath)


@pytest.fixture
def lmdbDb():
    """Set up a temporary LMDB database for integration testing."""
    # Remove the LMDB database file if it exists
    if os.path.exists(dbPath):
        shutil.rmtree(dbPath)
    db = Lmdb()

    # Insert test data with variable-length keys
    # Simulate blockchain data with contract addresses and keys of different lengths

    # Contract 1 data (fixed length keys)
    contract1 = b"contract1:"
    for i in range(10):
        key = f"{i:04d}".encode()
        db.put(contract1 + key, f"value1_{i}".encode())

    # Contract 2 data (variable length keys)
    contract2 = b"contract2:"
    db.put(contract2 + b"name", b"Contract 2 Name")
    db.put(contract2 + b"symbol", b"C2")
    db.put(contract2 + b"decimals", b"18")
    db.put(contract2 + b"total_supply", b"1000000000000000000000000")
    db.put(contract2 + b"owner", b"0x1234567890123456789012345678901234567890")

    # Contract 3 data (mixed length keys with common prefixes)
    contract3 = b"contract3:"
    db.put(contract3 + b"balance:user1", b"100")
    db.put(contract3 + b"balance:user2", b"200")
    db.put(contract3 + b"balance:user10", b"1000")
    db.put(contract3 + b"balance:user100", b"10000")
    db.put(contract3 + b"allowance:user1:user2", b"50")
    db.put(contract3 + b"allowance:user1:user3", b"75")
    db.put(contract3 + b"allowance:user2:user1", b"25")

    yield db

    # Cleanup
    db.close()
    if os.path.exists(dbPath):
        shutil.rmtree(dbPath)


# ============================================================================
# Direct LmdbReverseIterator Tests
# ============================================================================


def test_empty_prefix(lmdbEnv):
    """Test iteration with an empty prefix (all keys)."""
    # This should iterate through all keys in reverse order
    iterator = LmdbReverseIterator(lmdbEnv, b"", b"")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Keys should be in reverse lexicographical order
    expectedKeys = [
        b"c:z",
        b"c:1",
        b"b:very_long_key_with_more_text",
        b"b:short",
        b"b:longer_key",
        b"b:2",
        b"b:1",
        b"a:100",
        b"a:10",
        b"a:2",
        b"a:1",
    ]
    # Sort expected keys in reverse lexicographical order
    expectedKeys = sorted(expectedKeys, reverse=True)

    assert keys == expectedKeys
    iterator.release()


def test_prefix_filter(lmdbEnv):
    """Test iteration with a specific prefix."""
    # This should iterate through all keys with prefix "b:" in reverse order
    iterator = LmdbReverseIterator(lmdbEnv, b"b:", b"")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Keys with prefix "b:" in reverse lexicographical order
    expectedKeys = [b"b:very_long_key_with_more_text", b"b:short", b"b:longer_key", b"b:2", b"b:1"]
    # Sort expected keys in reverse lexicographical order
    expectedKeys = sorted(expectedKeys, reverse=True)

    assert keys == expectedKeys
    iterator.release()


def test_start_key(lmdbEnv):
    """Test iteration with a start key."""
    # This should iterate through all keys with prefix "a:" starting from "a:2" in reverse order
    iterator = LmdbReverseIterator(lmdbEnv, b"a:", b"2")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Keys with prefix "a:" <= "a:2" in reverse order
    # When using lexicographical ordering, we get all keys <= "a:2"
    expectedKeys = [b"a:2", b"a:100", b"a:10", b"a:1"]

    assert keys == expectedKeys
    iterator.release()


def test_end_key(lmdbEnv):
    """Test iteration with an end key."""
    # This should iterate through all keys with prefix "a:" from "a:100" down to "a:10"
    iterator = LmdbReverseIterator(lmdbEnv, b"a:", b"100", b"10")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Keys with prefix "a:" between "a:100" and "a:10" inclusive, in reverse order
    expectedKeys = [b"a:100", b"a:10"]

    assert keys == expectedKeys
    iterator.release()


def test_nonexistent_start_key(lmdbEnv):
    """Test iteration with a start key that doesn't exist."""
    # This should find the largest key <= "a:50" (which is "a:10")
    iterator = LmdbReverseIterator(lmdbEnv, b"a:", b"50")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Keys with prefix "a:" <= "a:50" in reverse order
    # When using lexicographical ordering, we get all keys <= "a:50"
    expectedKeys = [b"a:2", b"a:100", b"a:10", b"a:1"]

    assert keys == expectedKeys
    iterator.release()


def test_nonexistent_prefix(lmdbEnv):
    """Test iteration with a prefix that doesn't exist."""
    # This should return no keys
    iterator = LmdbReverseIterator(lmdbEnv, b"d:", b"")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    assert keys == []
    iterator.release()


def test_variable_length_keys(lmdbEnv):
    """Test iteration with variable-length keys."""
    # This should iterate through all keys with prefix "b:" in reverse order
    iterator = LmdbReverseIterator(lmdbEnv, b"b:", b"")

    keys = []
    values = []
    while iterator.next():
        keys.append(iterator.key())
        values.append(iterator.value())

    # Keys with prefix "b:" in reverse lexicographical order
    expectedKeys = [b"b:very_long_key_with_more_text", b"b:short", b"b:longer_key", b"b:2", b"b:1"]
    # Sort expected keys in reverse lexicographical order
    expectedKeys = sorted(expectedKeys, reverse=True)

    # Create a dictionary to map keys to values
    valueMap = {
        b"b:very_long_key_with_more_text": b"valuebverylong",
        b"b:short": b"valuebshort",
        b"b:longer_key": b"valueblonger",
        b"b:2": b"valueb2",
        b"b:1": b"valueb1",
    }
    # Update expected values to match the sorted keys
    expectedValues = [valueMap[key] for key in expectedKeys]

    assert keys == expectedKeys
    assert values == expectedValues
    iterator.release()


def test_start_greater_than_all(lmdbEnv):
    """Test iteration with a start key greater than all existing keys."""
    # This should iterate through all keys with prefix "a:" in reverse order
    iterator = LmdbReverseIterator(lmdbEnv, b"a:", b"999")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # All keys with prefix "a:" in reverse order
    expectedKeys = [b"a:100", b"a:10", b"a:2", b"a:1"]
    # Sort expected keys in reverse lexicographical order
    expectedKeys = sorted(expectedKeys, reverse=True)

    assert keys == expectedKeys
    iterator.release()


def test_start_less_than_all(lmdbEnv):
    """Test iteration with a start key less than all existing keys."""
    # This should return no keys
    iterator = LmdbReverseIterator(lmdbEnv, b"a:", b"0")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    assert keys == []
    iterator.release()


def test_release_multiple_times(lmdbEnv):
    """Test calling release multiple times."""
    iterator = LmdbReverseIterator(lmdbEnv, b"a:", b"")
    iterator.next()  # Move to first key
    iterator.release()
    # Calling release again should not cause errors
    iterator.release()
    # Trying to iterate after release should not cause errors
    assert not iterator.next()


# ============================================================================
# Integration Tests with Lmdb Class
# ============================================================================


def test_reverse_iteration_fixed_length(lmdbDb):
    """Test reverse iteration with fixed-length keys."""
    # Iterate through all contract1 keys in reverse order
    iterator = lmdbDb.newReverseIterator(b"contract1:", b"")

    keys = []
    values = []
    while iterator.next():
        keys.append(iterator.key())
        values.append(iterator.value())

    # Keys should be in reverse order (0009 down to 0000)
    expectedKeys = [
        b"contract1:0009",
        b"contract1:0008",
        b"contract1:0007",
        b"contract1:0006",
        b"contract1:0005",
        b"contract1:0004",
        b"contract1:0003",
        b"contract1:0002",
        b"contract1:0001",
        b"contract1:0000",
    ]

    assert keys == expectedKeys
    iterator.release()


def test_reverse_iteration_variable_length(lmdbDb):
    """Test reverse iteration with variable-length keys."""
    # Iterate through all contract2 keys in reverse order
    iterator = lmdbDb.newReverseIterator(b"contract2:", b"")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Keys should be in reverse lexicographical order
    expectedKeys = [
        b"contract2:total_supply",
        b"contract2:symbol",
        b"contract2:owner",
        b"contract2:name",
        b"contract2:decimals",
    ]

    assert keys == expectedKeys
    iterator.release()


def test_reverse_iteration_with_prefix(lmdbDb):
    """Test reverse iteration with a specific prefix."""
    # Iterate through all contract3 balance keys in reverse order
    iterator = lmdbDb.newReverseIterator(b"contract3:balance:", b"")

    keys = []
    values = []
    while iterator.next():
        keys.append(iterator.key())
        values.append(iterator.value())

    # Keys should be in reverse lexicographical order
    expectedKeys = [
        b"contract3:balance:user100",
        b"contract3:balance:user10",
        b"contract3:balance:user2",
        b"contract3:balance:user1",
    ]
    # Sort expected keys in reverse lexicographical order
    expectedKeys = sorted(expectedKeys, reverse=True)

    # Create a dictionary to map keys to values
    valueMap = {
        b"contract3:balance:user100": b"10000",
        b"contract3:balance:user10": b"1000",
        b"contract3:balance:user2": b"200",
        b"contract3:balance:user1": b"100",
    }
    # Update expected values to match the sorted keys
    expectedValues = [valueMap[key] for key in expectedKeys]

    assert keys == expectedKeys
    assert values == expectedValues
    iterator.release()


def test_reverse_iteration_with_start_key(lmdbDb):
    """Test reverse iteration with a start key."""
    # Iterate through contract3 balance keys starting from user2 in reverse order
    iterator = lmdbDb.newReverseIterator(b"contract3:balance:", b"user2")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Keys should be in reverse lexicographical order, starting from user2
    # When using lexicographical ordering, we get all keys with prefix <= "user2"
    expectedKeys = [
        b"contract3:balance:user2",
        b"contract3:balance:user100",
        b"contract3:balance:user10",
        b"contract3:balance:user1",
    ]

    assert keys == expectedKeys
    iterator.release()


def test_reverse_iteration_with_range(lmdbDb):
    """Test reverse iteration with a range (start and end keys)."""
    # Iterate through contract3 balance keys from user2 down to user1
    iterator = lmdbDb.newReverseIterator(b"contract3:balance:", b"user2", b"user1")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Keys should include user2 and user1 in reverse order
    # When using lexicographical ordering and a range, we get all keys >= end and <= start
    expectedKeys = [
        b"contract3:balance:user2",
        b"contract3:balance:user100",
        b"contract3:balance:user10",
        b"contract3:balance:user1",
    ]

    assert keys == expectedKeys
    iterator.release()


def test_reverse_iteration_with_nonexistent_start(lmdbDb):
    """Test reverse iteration with a start key that doesn't exist."""
    # Iterate through contract3 balance keys starting from user5 (which doesn't exist)
    iterator = lmdbDb.newReverseIterator(b"contract3:balance:", b"user5")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Should return keys less than user5
    # When using lexicographical ordering, we get all keys <= "user5"
    expectedKeys = [
        b"contract3:balance:user2",
        b"contract3:balance:user100",
        b"contract3:balance:user10",
        b"contract3:balance:user1",
    ]

    assert keys == expectedKeys
    iterator.release()


def test_empty_result(lmdbDb):
    """Test reverse iteration with a prefix that doesn't exist."""
    # Iterate through keys with a non-existent prefix
    iterator = lmdbDb.newReverseIterator(b"nonexistent:", b"")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Should return no keys
    assert keys == []
    iterator.release()


def test_optimized_prefix_search(lmdbEnv):
    """Test the optimized prefix search algorithm."""
    # This test verifies that our optimized algorithm for finding the last key with a prefix works correctly
    # We'll add some special test keys to verify edge cases

    with lmdbEnv.begin(write=True) as txn:
        # Add keys with various prefixes to test boundary conditions
        txn.put(b"z:1", b"z-value1")
        txn.put(b"z:2", b"z-value2")
        txn.put(b"z:final", b"z-final")

        # Add keys that are lexicographically just before and after our test prefix
        # 'z' is ASCII 122, '{' is ASCII 123 (next character after 'z')
        txn.put(b"y:last", b"before-z")
        txn.put(b"{", b"after-z")

        # Add a key with 0xFF bytes to test edge case
        txn.put(b"\xff\xff:test", b"max-byte-value")

    # Test finding the last key with prefix "z:"
    iterator = LmdbReverseIterator(lmdbEnv, b"z:", b"")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Keys should be in reverse lexicographical order
    expectedKeys = [b"z:final", b"z:2", b"z:1"]

    assert keys == expectedKeys
    iterator.release()

    # Test finding the last key with prefix "\xff"
    iterator = LmdbReverseIterator(lmdbEnv, b"\xff", b"")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Should find our 0xFF test key
    assert keys == [b"\xff\xff:test"]
    iterator.release()


def test_nonexistent_start_key_with_different_prefix(lmdbEnv):
    """Test iteration with a start key that doesn't exist and current key doesn't have the required prefix."""
    # This test specifically targets our optimization for finding the last key with a prefix
    # when the start key doesn't exist and the current key doesn't have the required prefix

    with lmdbEnv.begin(write=True) as txn:
        # Add keys with various prefixes
        txn.put(b"p:1", b"p-value1")
        txn.put(b"p:2", b"p-value2")
        txn.put(b"q:1", b"q-value1")  # This is between p: and r:
        txn.put(b"s:1", b"s-value1")  # This is after r:

    # Test with prefix "r:" which doesn't exist, and start key "r:999"
    # The set_range will position at "s:1" which doesn't have our prefix
    # Our optimized algorithm should correctly handle this case
    iterator = LmdbReverseIterator(lmdbEnv, b"r:", b"r:999")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Should return no keys since there are no keys with prefix "r:"
    assert keys == []
    iterator.release()

    # Test with prefix "r:" and no start key
    # This tests the other optimization path
    iterator = LmdbReverseIterator(lmdbEnv, b"r:", b"")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Should return no keys
    assert keys == []
    iterator.release()


def test_single_byte_prefix(lmdbEnv):
    """Test iteration with a single byte prefix."""
    # This test verifies that our optimized prefix checking works correctly with short prefixes

    with lmdbEnv.begin(write=True) as txn:
        # Add keys with single byte prefixes - using 'x' to avoid conflict with existing keys
        txn.put(b"x", b"x-value")
        txn.put(b"xy", b"xy-value")
        txn.put(b"xyz", b"xyz-value")
        txn.put(b"y", b"y-value")
        txn.put(b"yz", b"yz-value")

    # Test with single byte prefix "x"
    iterator = LmdbReverseIterator(lmdbEnv, b"x", b"")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Should return all keys starting with "x" in reverse order
    expectedKeys = [b"xyz", b"xy", b"x"]

    assert keys == expectedKeys
    iterator.release()


def test_special_characters_in_prefix(lmdbEnv):
    """Test iteration with special characters in the prefix."""
    # This test verifies that our optimized prefix checking works correctly with special characters

    with lmdbEnv.begin(write=True) as txn:
        # Add keys with special characters in prefixes
        txn.put(b"\x00key1", b"null-value1")
        txn.put(b"\x00key2", b"null-value2")
        txn.put(b"\x01key1", b"soh-value1")  # SOH = Start of Heading
        txn.put(b"\x1fkey1", b"us-value1")  # US = Unit Separator
        txn.put(b"\x7fkey1", b"del-value1")  # DEL = Delete

    # Test with null byte prefix
    iterator = LmdbReverseIterator(lmdbEnv, b"\x00", b"")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Should return all keys starting with null byte in reverse order
    expectedKeys = [b"\x00key2", b"\x00key1"]

    assert keys == expectedKeys
    iterator.release()

    # Test with DEL character prefix
    iterator = LmdbReverseIterator(lmdbEnv, b"\x7f", b"")

    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Should return all keys starting with DEL character
    expectedKeys = [b"\x7fkey1"]

    assert keys == expectedKeys
    iterator.release()


def test_address_range_boundary(lmdbEnv):
    """Test reverse iteration with address range to verify boundary checking."""
    # This test simulates the problematic case with address ranges

    with lmdbEnv.begin(write=True) as txn:
        # Add keys with different address prefixes
        txn.put(b"0xd2400802048b94c71858b9898ffee67486e3ef0800d3c297e6-0000000000000001000", b"value1")
        txn.put(b"0xd2400802048b94c71858b9898ffee67486e3ef0800d3c297e6-0000000000000002000", b"value2")
        txn.put(b"0xd2400802048b94c71858b9898ffee67486e3ef0800d3c297e6-0000000000000003000", b"value3")

        # Add a key with a different address prefix that was causing issues
        txn.put(b"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06-1745983785426996000", b"other_value")

    # Create a reverse iterator with the specified range
    address = b"0xd2400802048b94c71858b9898ffee67486e3ef0800d3c297e6"
    iterator = LmdbReverseIterator(
        lmdbEnv,
        address + b"-",
        b"9999999999999999999",  # Start from a very high timestamp
        b"0000000000000000000",  # End at a very low timestamp
    )

    # Collect all keys returned by the iterator
    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Verify that only keys with the correct address prefix are returned
    for key in keys:
        assert key.startswith(address), f"Key {key} does not have the correct address prefix"

    # Verify that the problematic key is not included
    problematic_key = b"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06-1745983785426996000"
    assert problematic_key not in keys, f"Problematic key {problematic_key} was incorrectly included"

    # Verify that we found the expected keys
    expected_keys = [
        b"0xd2400802048b94c71858b9898ffee67486e3ef0800d3c297e6-0000000000000003000",
        b"0xd2400802048b94c71858b9898ffee67486e3ef0800d3c297e6-0000000000000002000",
        b"0xd2400802048b94c71858b9898ffee67486e3ef0800d3c297e6-0000000000000001000",
    ]
    assert keys == expected_keys, f"Expected {expected_keys}, got {keys}"

    iterator.release()


def test_different_address_boundary(lmdbEnv):
    """Test reverse iteration with a different address to verify boundary checking."""
    # This test verifies that we can correctly query a different address

    with lmdbEnv.begin(write=True) as txn:
        # Add keys with different address prefixes (same as in test_address_range_boundary)
        txn.put(b"0xd2400802048b94c71858b9898ffee67486e3ef0800d3c297e6-0000000000000001000", b"value1")
        txn.put(b"0xd2400802048b94c71858b9898ffee67486e3ef0800d3c297e6-0000000000000002000", b"value2")
        txn.put(b"0xd2400802048b94c71858b9898ffee67486e3ef0800d3c297e6-0000000000000003000", b"value3")
        txn.put(b"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06-1745983785426996000", b"other_value")

    # Create a reverse iterator for the other address
    address = b"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"
    iterator = LmdbReverseIterator(lmdbEnv, address + b"-", b"9999999999999999999", b"0000000000000000000")

    # Collect all keys
    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # Verify that only keys with the correct address prefix are returned
    expected_keys = [b"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06-1745983785426996000"]
    assert keys == expected_keys, f"Expected {expected_keys}, got {keys}"

    iterator.release()


def test_large_dataset(lmdbEnv):
    """Test iteration with a large number of keys to verify performance."""
    # This test simulates a large database to verify our optimizations work well with many keys

    # Number of keys to insert for each prefix
    num_keys = 1000

    with lmdbEnv.begin(write=True) as txn:
        # Add many keys with different prefixes
        # We'll create a scenario where the target prefix is surrounded by many other keys

        # Keys before our target prefix
        for i in range(num_keys):
            key = f"before:{i:05d}".encode()
            txn.put(key, f"before-value-{i}".encode())

        # Keys with our target prefix (in the middle)
        for i in range(num_keys):
            key = f"target:{i:05d}".encode()
            txn.put(key, f"target-value-{i}".encode())

        # Keys after our target prefix
        for i in range(num_keys):
            key = f"zafter:{i:05d}".encode()
            txn.put(key, f"after-value-{i}".encode())

    # Test finding the last key with our target prefix
    # This tests our optimization for finding the last key with a prefix
    iterator = LmdbReverseIterator(lmdbEnv, b"target:", b"")

    # Count the number of keys found
    count = 0
    last_key = None
    while iterator.next():
        count += 1
        if count == 1:
            last_key = iterator.key()  # Save the first key we find (should be the last in lexicographical order)

    # We should find exactly num_keys keys
    assert count == num_keys

    # The last key should be "target:" followed by the highest number
    expected_last_key = f"target:{num_keys - 1:05d}".encode()
    assert last_key == expected_last_key

    iterator.release()

    # Test with a start key in the middle of the target range
    middle_idx = num_keys // 2
    middle_key = f"{middle_idx:05d}".encode()
    iterator = LmdbReverseIterator(lmdbEnv, b"target:", middle_key)

    # Count keys and collect them
    keys = []
    while iterator.next():
        keys.append(iterator.key())

    # We should find keys from middle_idx down to 0
    # Plus any keys that are lexicographically between target:middle_idx and target:(middle_idx+1)
    # In this case, there are none, so we expect middle_idx + 1 keys
    assert len(keys) >= middle_idx + 1

    # The first key should be the one at middle_idx
    expected_first_key = f"target:{middle_idx:05d}".encode()
    assert keys[0] == expected_first_key

    iterator.release()
