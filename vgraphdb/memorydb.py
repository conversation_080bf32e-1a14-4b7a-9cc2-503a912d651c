from __future__ import annotations

import copy
from typing import Dict, List, Optional

from readerwriterlock.rwlock import R<PERSON><PERSON>ock<PERSON>air

from . import database


class MemoryDBAlreadyClosed(Exception):
    """
    MemoryDBClosedError is an error type that is returned when attempting to
    access a closed memory database.
    """

    def __init__(self):
        super().__init__("memory database already closed")


class MemoryDB(database.KeyValueStore):
    """
    The MemoryDB implements the key-value database layer based on memory maps.
    """

    def __init__(self):
        self.db: Dict[bytes, bytes] = {}
        self.lock: RWLockFair = RWLockFair()

    # Implement Closer

    def close(self):
        """
        Close closes the database.
        """
        with self.lock.gen_wlock():
            self.db = None

    # Implement KeyValueReader

    def contains(self, key: bytes) -> bool:
        """
        Contains retrieves if a key is present in the key-value data store.
        """
        if self.db is None:
            raise MemoryDBAlreadyClosed
        with self.lock.gen_rlock():
            return key in self.db

    def get(self, key: bytes) -> Optional[bytes]:
        """
        Get retrieves the given key if it's present in the key-value data store.
        """
        if self.db is None:
            raise MemoryDBAlreadyClosed
        with self.lock.gen_rlock():
            return self.db.get(key)

    # Implement KeyValueWriter

    def put(self, key: bytes, value: bytes) -> None:
        """
        Put inserts the given value into the key-value data store.
        """
        if self.db is None:
            raise MemoryDBAlreadyClosed
        with self.lock.gen_wlock():
            self.db[key] = value

    def delete(self, key: bytes) -> None:
        """
        Delete removes the key from the key-value data store.
        """
        if self.db is None:
            raise MemoryDBAlreadyClosed
        with self.lock.gen_wlock():
            self.db.pop(key, None)

    # Implement Batcher

    def newBatch(self) -> database.Batch:
        """
        NewBatch creates a write-only key-value store that buffers changes to its host
        database until a final write is called.
        """
        return _MemoryDBBatch(db=self)

    # Implement Iteratee

    def newIterator(self, prefix: bytes = b"", start: bytes = b"", end: bytes = b"") -> database.DBIterator:
        """
        NewIterator creates a binary-alphabetical iterator over a subset
        of database content with a particular key prefix, starting at a particular
        initial key (or after, if it does not exist).

        Args:
            prefix: The prefix to filter keys
            start: The key to start iteration from (inclusive)
            end: The key to end iteration at (inclusive)
        """
        with self.lock.gen_rlock():
            pr: bytes = prefix
            st: bytes = prefix + start
            keys: List[bytes] = []
            values: List[bytes] = []

            # Collect the keys from the memory database corresponding to the given prefix and starting key.
            for key in self.db.keys():
                if key.startswith(pr) and key >= st:
                    if end != b"" and key > prefix + end:
                        continue
                    keys.append(key)

            # Sort the items and retrieve the associated values
            keys.sort()
            for key in keys:
                values.append(self.db[key])

            return _MemoryDBIterator(index=-1, keys=keys, values=values)

    def newReverseIterator(self, prefix: bytes = b"", start: bytes = b"", end: bytes = b"") -> _MemoryDBReverseIterator:
        """
        newReverseIterator creates a binary-alphabetical reverse iterator over a subset
        of database content with a particular key prefix, starting at a particular
        initial key and moving backwards.

        Args:
            prefix: The prefix to filter keys
            start: The key to start reverse iteration from (inclusive)
            end: The lower bound key to stop iteration at (inclusive)

        Returns:
            _MemoryDBReverseIterator: A reverse iterator that moves from higher to lower keys
        """
        with self.lock.gen_rlock():
            pr: bytes = prefix
            st: bytes = prefix + start
            keys: List[bytes] = []
            values: List[bytes] = []

            # Collect the keys from the memory database corresponding to the given prefix and starting key.
            for key in self.db.keys():
                if key.startswith(pr):
                    if end and key < prefix + end:
                        continue
                    if start and key > st:
                        continue
                    keys.append(key)

            # Sort the items in reverse order and retrieve the associated values
            keys.sort(reverse=True)
            for key in keys:
                values.append(self.db[key])

            return _MemoryDBReverseIterator(index=-1, keys=keys, values=values)


class _MemoryDBBatch(database.Batch):
    """
    _MemoryDBBatch is a write-only memory batch that commits changes to its host
    database when Write is called. A batch cannot be used concurrently.
    """

    def __init__(self, db: MemoryDB):
        self.db: MemoryDB = db
        self.writes: Dict[bytes, Optional[bytes]] = {}  # key -> value, None means delete
        self.size: int = 0

    # Implement KeyValueWriter

    def put(self, key: bytes, value: bytes):
        """
        Put inserts the given value into the key-value data store.
        """
        if self.db.db is None:
            raise MemoryDBAlreadyClosed

        # Update size calculation
        if key in self.writes:
            # If key already exists, subtract its previous contribution to size
            old_value = self.writes[key]
            if old_value is not None:  # Only if it wasn't marked for deletion
                self.size -= len(old_value)

        # Store the value and update size
        self.writes[key] = copy.deepcopy(value)
        self.size += len(key) + len(value)

    def delete(self, key: bytes):
        """
        Delete removes the key from the key-value data store.
        """
        if self.db.db is None:
            raise MemoryDBAlreadyClosed

        # Update size calculation
        if key in self.writes and self.writes[key] is not None:
            # If key exists and not already marked for deletion, subtract its value size
            self.size -= len(self.writes[key])

        # Mark for deletion and update size
        self.writes[key] = None
        self.size += len(key)

    # Implement Batch

    def valueSize(self) -> int:
        """
        ValueSize retrieves the amount of data queued up for writing.
        """
        return self.size

    def write(self) -> None:
        """
        Write flushes any accumulated data to the memory database.
        """
        if self.db.db is None:
            raise MemoryDBAlreadyClosed
        with self.db.lock.gen_wlock():
            for key, value in self.writes.items():
                if value is None:
                    self.db.db.pop(key, None)
                else:
                    self.db.db[key] = value

    def reset(self) -> None:
        """
        Reset resets the batch for reuse.
        """
        self.writes.clear()
        self.size = 0

    def replay(self, writer: database.KeyValueWriter) -> None:
        """
        Replay replays the batch contents.
        """
        for key, value in self.writes.items():
            if value is None:
                writer.delete(key)
            else:
                writer.put(key, value)


class _MemoryDBIterator(database.DBIterator):
    """
    iterator can walk over the (potentially partial) keyspace of a memory key
    value store. Internally it is a deep copy of the entire iterated state,
    sorted by keys.
    """

    def __init__(self, index: int, keys: List[bytes], values: List[bytes]):
        self.index: int = index
        self.keys: List[bytes] = keys
        self.values: List[bytes] = values

    def next(self) -> bool:
        """
        Next moves the iterator to the next key/value pair. It returns whether the
        iterator is exhausted.
        """
        # Short circuit if iterator is already exhausted in the forward direction.
        if self.index >= len(self.keys):
            return False
        self.index += 1
        return self.index < len(self.keys)

    def key(self) -> Optional[bytes]:
        """
        Key returns the key of the current key/value pair, or nil if done. The caller
        should not modify the contents of the returned slice, and its contents may
        change on the next call to Next.
        """
        # Short circuit if iterator is not in a valid position
        if self.index < 0 or self.index >= len(self.keys):
            return None
        return self.keys[self.index]

    def value(self) -> Optional[bytes]:
        """
        Value returns the value of the current key/value pair, or nil if done. The
        caller should not modify the contents of the returned slice, and its contents
        may change on the next call to Next.
        """
        # Short circuit if iterator is not in a valid position
        if self.index < 0 or self.index >= len(self.values):
            return None
        return self.values[self.index]

    def release(self):
        """
        Release releases associated resources. Release should always succeed and can
        be called multiple times without causing error.
        """
        if self.keys is not None or self.index != -1 or self.values is not None:
            self.index = -1
            self.keys.clear()
            self.values.clear()
            self.keys = None
            self.values = None


class _MemoryDBReverseIterator(database.DBIterator):
    """
    Iterator can walk over the (potentially partial) keyspace of a memory key
    value store in reverse order. Internally it is a deep copy of the entire
    iterated state, sorted by keys in reverse order.
    """

    def __init__(self, index: int, keys: List[bytes], values: List[bytes]):
        self.index: int = index
        self.keys: List[bytes] = keys
        self.values: List[bytes] = values

    def next(self) -> bool:
        """
        Next moves the iterator to the previous key/value pair. It returns whether the
        iterator is exhausted.

        Returns:
            bool: True if a valid previous key/value pair exists, False if iteration is complete
        """
        # Short circuit if iterator is already exhausted in the reverse direction.
        if self.index >= len(self.keys):
            return False
        self.index += 1
        return self.index < len(self.keys)

    def key(self) -> Optional[bytes]:
        """
        Key returns the key of the current key/value pair, or None if done.
        The caller should not modify the contents of the returned slice.
        """
        # Short circuit if iterator is not in a valid position
        if self.index < 0 or self.index >= len(self.keys):
            return None
        return self.keys[self.index]

    def value(self) -> Optional[bytes]:
        """
        Value returns the value of the current key/value pair, or None if done.
        The caller should not modify the contents of the returned slice.
        """
        # Short circuit if iterator is not in a valid position
        if self.index < 0 or self.index >= len(self.values):
            return None
        return self.values[self.index]

    def release(self):
        """
        Release releases associated resources. Release should always succeed and can
        be called multiple times without causing error.
        """
        if self.keys is not None or self.index != -1 or self.values is not None:
            self.index = -1
            self.keys.clear()
            self.values.clear()
            self.keys = None
            self.values = None
