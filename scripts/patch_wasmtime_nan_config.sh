#!/bin/bash
# ==============================
# Script Name: patch_wasmtime_nan_config.sh
# Description: This script is used to modify _config.py of local wasmtime-py package
#              to add the cranelift_nan_canonicalization setter property.
#              If the cranelift_nan_canonicalization setter property already exists,
#              the script will recover the original _config.py file.
# Author: bingo-V
# Date: 2025-03-12
# Usage: ./scripts/patch_wasmtime_nan_config.sh
# ==============================

# Get the installation location of the wasmtime package
PACKAGE_LOCATION=$(pipenv run pip show wasmtime | grep 'Location:' | awk '{print $2}')

# Check if the package location was retrieved successfully
if [ -z "$PACKAGE_LOCATION" ]; then
  echo "Error: Failed to find wasmtime package location."
  exit 1
fi

echo "Found wasmtime at: $PACKAGE_LOCATION"

# Locate the _config.py file
CONFIG_PATH="$PACKAGE_LOCATION/wasmtime/_config.py"

# Check if the file exists
if [ ! -f "$CONFIG_PATH" ]; then
  echo "Error: File $CONFIG_PATH does not exist."
  exit 1
fi

if grep -rq "def cranelift_nan_canonicalization" "$CONFIG_PATH"; then
  if [ -f "$CONFIG_PATH.bak" ]; then
    # delete the modified _config.py file
    rm -f "$CONFIG_PATH"
    # recover the _config.py file from _config.py.bak
    mv "$CONFIG_PATH.bak" "$CONFIG_PATH"
    echo "$CONFIG_PATH is recovered."
    exit 0
  else
    echo "Error: Backup file $CONFIG_PATH.bak does not exist."
    echo "$CONFIG_PATH is already patched."
    exit 1
  fi
fi

# Backup the original file
cp "$CONFIG_PATH" "$CONFIG_PATH.bak"

# Use awk to insert code
awk '
/ffi.wasmtime_config_cranelift_debug_verifier_set/ {
    print $0;
    print ""
    print "    @setter_property";
    print "    def cranelift_nan_canonicalization(self, enable: bool) -> None:";
    print "        if not isinstance(enable, bool):";
    print "            raise TypeError(\"expected a bool\")";
    print "        ffi.wasmtime_config_cranelift_nan_canonicalization_set(self.ptr(), enable)";
    next;
}
{ print $0 }' "$CONFIG_PATH.bak" > "$CONFIG_PATH"

# Check if the file was modified successfully
if [ $? -eq 0 ]; then
  echo "Patched $CONFIG_PATH successfully."
else
  echo "Error: Failed to patch $CONFIG_PATH."
  exit 1
fi
