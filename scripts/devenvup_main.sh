#!/usr/bin/env bash

# Function to determine the architecture and set library paths
set_library_paths() {
    ARCH=$(uname -m)

    case "$ARCH" in
        x86_64)
            LIB_DIR="/usr/lib/x86_64-linux-gnu"
            DRI_DIR="/usr/lib/x86_64-linux-gnu/dri"
            ;;
        aarch64 | arm64)
            LIB_DIR="/usr/lib/aarch64-linux-gnu"
            DRI_DIR="/usr/lib/aarch64-linux-gnu/dri"
            ;;
        armv7l)
            LIB_DIR="/usr/lib/arm-linux-gnueabihf"
            DRI_DIR="/usr/lib/arm-linux-gnueabihf/dri"
            ;;
        *)
            echo "Unsupported architecture: $ARCH"
            exit 1
            ;;
    esac

    echo "$LIB_DIR" "$DRI_DIR"
}

# Function to check if the system is Ubuntu
is_ubuntu() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        if [[ "$ID" == "ubuntu" || "$NAME" == *"Ubuntu"* ]]; then
            return 0
        fi
    fi
    return 1
}

# Get the directory where the script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"

# Optional: Change to the project root if scripts/ is a subdirectory
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT" || exit 1

echo "Project Root: $(pwd)"
# Rest of your script...


# Initialize variables
CONFIG_OPTION=""
ENV_VARS=""

# Check if CONFIG environment variable is set
if [ -n "$CONFIG" ]; then
    CONFIG_OPTION="CONFIG=$CONFIG"
    echo "Using configuration file: $CONFIG"
fi

# Check if the system is Ubuntu
if is_ubuntu; then
    echo "System is Ubuntu. Setting library paths based on architecture."

    # Retrieve library directories based on architecture
    read LIB_DIR DRI_DIR < <(set_library_paths)

    # Optionally, echo the paths for debugging
    echo "LD_LIBRARY_PATH set to: $LIB_DIR"
    echo "LIBGL_DRIVERS_PATH set to: $DRI_DIR"

    # Prepare environment variables inline
    ENV_VARS="LD_LIBRARY_PATH=\"$LIB_DIR\" LIBGL_DRIVERS_PATH=\"$DRI_DIR\""
fi

# Execute the Python script with the environment variables (if any)
# Using eval to correctly handle the inline environment variables and CONFIG_OPTION
# On ubuntu, you will run it to error of version `GLIBC_2.3X' not found if using poetry run, use direct python instead
if [ -n "$ENV_VARS" ]; then
    eval $ENV_VARS python main.py
else
    poetry run python main.py
fi
