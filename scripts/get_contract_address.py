import os
import sys
import time

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))  # noqa

from common import bytesToHex, generateContractAddress

if __name__ == "__main__":
    filePath = "contract/base/primechain_utils.wasm"

    with open(filePath, "rb") as f:
        contractCode = f.read()

    sender = "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"
    timestamp = time.time_ns()

    print(bytesToHex(generateContractAddress(contractCode.hex(), sender, timestamp)))
