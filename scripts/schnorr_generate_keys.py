import os
import sys

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from common import generateKeys, publicKeyToAddress

if __name__ == "__main__":
    # generate keys
    (publicKey, privateKey) = generateKeys()

    print(f"Your private key is: {privateKey}")
    print(f"Your public key is: {publicKey}")
    print(f"Your address is: {publicKeyToAddress(publicKey)}")
