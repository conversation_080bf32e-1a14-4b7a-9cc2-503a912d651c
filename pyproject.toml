[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# A non-priority source for resolving Package docutils (0.21.post1) not found issue
# See issue: https://github.com/python-poetry/poetry/issues/9293
[[tool.poetry.source]]
name = "pypi-public"
url = "https://pypi.org/simple/"

[tool.poetry]
name = "vgraph"
version = "0.1.0"
description = ""
authors = []
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.11, <=3.12"
kivy = "2.2.1"
cython = "*"
wasmtime = "*"
pyserde = "*"
orjson = "*"
aiorpcx = "*"
pyyaml = "*"
lmdb = "*"
pycryptodome = "*"
ntplib = "*"
base58 = "*"
attrs = "*"
rlp = "*"
trie = "*"
readerwriterlock = "*"
cachetools = "*"
python-multipart = "*"
fastapi = "*"
uvicorn = "*"
uvloop = "*"
websockets = "*"

# Add git dependencies
rusty-rlp = { git = "https://github.com/zshimonz/rusty-rlp.git" }

# for vgraph watcher
ruamel-yaml = "^0.18.10"

[tool.poetry.dev-dependencies]
pytest = "*"
pytest-benchmark = "*"
pytest-asyncio = "*"
ruff = "*"

# pytest configuration
[tool.pytest.ini_options]
addopts = "--ignore=wasm/ --ignore=storage/vgraphdb/testsuite.py --ignore=.history/ --benchmark-skip"
asyncio_default_fixture_loop_scope = "function"
# TODO: `filterwarnings = ignore::DeprecationWarning:beartype` should be remove when all deprecation warnings are fixed
filterwarnings = ["ignore::DeprecationWarning:beartype"]

# ruff configuration
# See: https://docs.astral.sh/ruff/
# Usage:
#   - format code: `ruff format`
#   - lint code: `ruff check`
#   - lint code and fix: `ruff check --fix`
[tool.ruff]
# TODO: target version to py311
target-version = "py38"  # The minimum Python version
line-length = 120  # The maximum line length
exclude = ["wasm", "udpHolePunching"] #  In addition to the standard set of exclusions

[tool.ruff.format]
docstring-code-format = true  # Enable reformatting of code snippets in docstring.
indent-style = "space"  # Use 4 space indentation.
quote-style = "double"  # Prefer double quotes for strings.

# Lint rule selection
# See: https://docs.astral.sh/ruff/rules/
# Select standard: open-source, GitHub star >= 1k, under maintenance
[tool.ruff.lint]
select = [
    "E", # pycodestyle(PEP8, Error)
    "W", # pycodestyle(PEP8, Warning)
    "F", # Pyflakes
    "I", # isort
    "B", # flake8-bugbear
]

# Skipped rules
ignore = [
    "E501", # Line length
    "B024", # Abstract base class without abstract methods
]
