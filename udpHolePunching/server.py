#!/usr/bin/env python3
import time

from kivy.app import App
from kivy.logger import Logger
from kivy.uix.label import Label
from oscpy.server import OSCThreadServer
import random
import json

SERVER_PORT = 33478  # fixed seed server port not dynamic
SERVER_IP = "0.0.0.0"  # fixed seed server ip not dynamic


class SeedServer:
    def __init__(self, host=SERVER_IP, port=SERVER_PORT):
        self._server = OSCThreadServer(encoding="utf8")
        self._sock = self._server.listen(host, port=port, default=True)

        self._server.bind("/KEEP", self.keep)
        self._server.bind("/NNODE", self.newNode)


        # Three types of peers: server, typeA, typeB, typeC
        # Server is the seed node or the central node that is responsible to distribute the node ip and port.
        # TypeA is the node that is responsible to send the keep alive message to the other nodes.
        # Normally it is using full-cone NAT or an open network.
        # Consider adding upnp support for nodes had public ip address.
        # TypeB is the mediocre node that using restricted cone NAT.
        # It requires active connection from itself to the other nodes to establish the connection.
        # TypeC is the node that is responsible to receive the keep alive message from the other nodes.

        # Normally it is using restricted cone NAT or a closed network.
        self.peers = {
            "server": [], # fixed ip address and port
            "typeA": {},
            "typeB": {},
            "typeC": {}

        }

        # self.connections = {}  # Stores ip as key (sock,ip,port) as val
        # self.connectionMap = {}  # Stores ip as key and connect to [ip] as val

        self.firstConnectIp = ""
        self.firstConnectIpSent = False

    def newNode(self, natType, receivedIp, nodeName):
        sock, ip, port = self._server.get_sender()
        Logger.info(
            f"Received address of node {nodeName} with external ip {receivedIp}. NAT type is {natType}")
        Logger.info(f"From the server side. Received ip {ip} and port {str(port)}")
        # Check if the external ip of the client is the same as the ip of the server.
        if ip != receivedIp:
            Logger.info("The external ip of the client is not the same as the ip get from the server.")

        if natType == "typeA":
            self.peers["typeA"][nodeName] = {"ip": ip, "port": port, "lastSeen": time.time(), "type": natType}
            # Broadcast the new potential node to all the other nodes.
            self.broadcastTypeABNode(nodeName)
        elif natType == "typeB":
            self.peers["typeB"][nodeName] = {"ip": ip, "port": port, "lastSeen": time.time(), "type": natType}
            self.broadcastTypeABNode(nodeName)
        elif natType == "typeC":
            self.peers["typeC"][nodeName] = {"ip": ip, "port": port, "lastSeen": time.time(), "type": natType}
            self.broadcastTypeCNode(nodeName)


    # Ping pong to keep the connection alive
    def keep(self, nodeName, natType):
        sock, ip, port = self._server.get_sender()

        # Logger.info(f"Received keep alive from {nodeName} with ip {ip} and port {str(port)}.")
        if self.getNodeNatType(nodeName) is None:
            Logger.info(f"Received keep alive from unknown node {nodeName} with ip {ip} and port {str(port)}.")
            # Request the node network information from the server.
            self._server.send_message("/MSG", ["SeedNode", "requestNetworkStatus", ""], ip, port)
            return

        # Update node information if the node info mismatches with the stored info.
        if natType != self.getNodeNatType(nodeName):
            Logger.info(f"Node {nodeName} has changed its NAT type from {self.getNodeNatType(nodeName)} to {natType}")
            self.removeNode(nodeName)
            self.peers[natType][nodeName] = {"ip": ip, "port": port, "lastSeen": time.time(), "type": natType}
        elif self.peers[natType][nodeName]["ip"] != ip or self.peers[natType][nodeName]["port"] != port:
            Logger.info(f"Node {nodeName} has changed its ip or port.")
            Logger.info(
                f"Old ip: {self.peers['typeC'][nodeName]['ip']} and port: {self.peers['typeC'][nodeName]['port']}")
            Logger.info(f"New ip: {ip} and port: {port}")
            self.peers[natType][nodeName] = {"ip": ip, "port": port, "lastSeen": time.time(), "type": natType}

        # Update the last seen time of the node.
        self.peers[natType][nodeName]["lastSeen"] = time.time()

        self._server.send_message("/MSG", ["SeedNode", "keep", ""], ip, port)

    def broadcastTypeABNode(self, nodeName):
        self.broadcastTypeANode(nodeName)
        self.broadcastTypeBNode(nodeName)


    def broadcastTypeANode(self, nodeName):
        Logger.info(f"Broadcasting new typeA node {nodeName} to all the other nodes.")
        # Broadcast the new node to all the other nodes.
        for name,info in self.peers["typeA"].items():
            if name == nodeName:
                continue
            # Exchange the ip and port of the new node with the other nodes.
            self._server.send_message("/NODE", [self.peers["typeA"][nodeName]["ip"], self.peers["typeA"][nodeName]["port"], nodeName, "typeA"], info["ip"], info["port"])
            self._server.send_message("/NODE", [info["ip"], info["port"], name, info["type"]], self.peers["typeA"][nodeName]["ip"], self.peers["typeA"][nodeName]["port"])

    def broadcastTypeBNode(self, nodeName):
        Logger.info(f"Broadcasting new {self.getNodeNatType(nodeName)} node {nodeName} to all the other nodes.")
        # Broadcast the new node to all the other nodes.
        for name, info in self.peers["typeB"].items():
            if name == nodeName:
                continue
            # Exchange the ip and port of the new node with the other nodes.
            self._server.send_message("/NODE",
                                      [self.peers["typeB"][nodeName]["ip"], self.peers["typeB"][nodeName]["port"],
                                       nodeName, "typeB"], info["ip"], info["port"])
            self._server.send_message("/NODE", [info["ip"], info["port"], name, info["type"]],
                                      self.peers["typeB"][nodeName]["ip"], self.peers["typeB"][nodeName]["port"])

    def broadcastTypeCNode(self, nodeName):
        Logger.info(f"Broadcasting new typeC node {nodeName} to all the other nodes.")
        # Broadcast the new node to all the other nodes.
        for name,info in self.peers["typeA"].items():
            # Since typeA nodes are now full cone or open, we can directly connect to them. No need to notify the typeA nodes. And typeC nodes can't be connected by other nodes anyway.
            self._server.send_message("/NODE", [info["ip"], info["port"], name, info["type"]], self.peers["typeC"][nodeName]["ip"], self.peers["typeC"][nodeName]["port"])

    def getNodeNatType(self, nodeName):
        if nodeName in self.peers["typeA"]:
            return "typeA"
        elif nodeName in self.peers["typeB"]:
            return "typeB"
        elif nodeName in self.peers["typeC"]:
            return "typeC"
        else:
            return None

    def removeNode(self, nodeName):
        if nodeName in self.peers["typeA"]:
            self.peers["typeA"].pop(nodeName)
        elif nodeName in self.peers["typeB"]:
            self.peers["typeB"].pop(nodeName)
        elif nodeName in self.peers["typeC"]:
            self.peers["typeC"].pop(nodeName)
        else:
            Logger.info(f"Node {nodeName} is not in the list of peers.")





class ServerApp(App):  # refer to https://github.com/kivy/kivy/issues/5689
    label = None

    def build(self):
        self.label = Label(text="SEED Server")
        self.server = SeedServer()
        return self.label


if __name__ == '__main__':
    ServerApp().run()
