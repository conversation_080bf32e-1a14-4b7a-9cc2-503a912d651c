#!/usr/bin/python3

import datetime
import time
from functools import partial

from kivy.app import App
from kivy.clock import Clock
from kivy.clock import mainthread
from kivy.logger import Logger
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from oscpy.server import OSCThreadServer
from kivy.storage.jsonstore import JsonStore

import uuid

SERVER_PORT = 33478  # Default seed server port not dynamic
SERVERS_ADDRESS = ["localhost", "gabija.vos.systems"]  # Default seed servers address
CLIENT_PORT = 9951
PROTOCOL_VERSION = "0.1"


class VGraphClient:

    def __init__(self, app, host="0.0.0.0", port=CLIENT_PORT, ssHost=None, sPort=SERVER_PORT):
        if ssHost is None:
            ssHost = SERVERS_ADDRESS
        self.serverPort = sPort
        self.app = app
        
        # Prepare oscpy server for receiving the keep alive message from the other nodes.
        self._server = OSCThreadServer(encoding="utf8")
        self._sock = self._server.listen(host, port=port, default=True)

        self._server.bind("/NODE", self.receiveNode)
        self._server.bind("/MSG", self.nodeMessageReceived)


        # Three types of peers: server, typeA, typeC
        # Server is the seed node or the central node that is responsible to distribute the node ip and port.
        # TypeA is the node that is responsible to send the keep alive message to the other nodes.
        # Normally it is using full-cone NAT or an open network.
        # Consider adding upnp support for nodes had public ip address.
        # TypeB is the mediocre node that using restricted cone NAT.
        # It requires active connection from itself to the other nodes to establish the connection.
        # TypeC is the node that is responsible to receive the keep alive message from the other nodes.
        # Normally it is using restricted cone NAT or a closed network.
        self.peers = {
            "server": ssHost, # server(s) will be predefined as the seed node
            "typeA": {},
            "typeB": {},
            "typeC": {}
        }

        # Node type is determined by the NAT type. Default is none. It will be set when executing the getNetworkStatus function.
        self.nodeNatType = None
        
        # Current external ip address, default is None. It will be set when executing the getNetworkStatus function.
        self.externalIp = None

        # Load previous setting from the json file
        self.settingStore = JsonStore(App.get_running_app().user_data_dir + "settings.json")
        if self.settingStore.exists("nodeName"):
            self.nodeName = self.settingStore.get("nodeName")["nodeName"]
        else:
            # Node name is the unique identifier of the node. It is generated randomly using uuid4.
            # Why do we need a unique identifier? Because the ip address is not unique, it will be easy to track the different nodes with the same ip address.
            self.nodeName = str(uuid.uuid4())
            self.settingStore.put("nodeName", nodeName=self.nodeName)

        # Load protocol version from the json file
        if self.settingStore.exists("protocolVersion"):
            previousProtocolVersion = self.settingStore.get("protocolVersion")["protocolVersion"]
            Logger.info(f"Previous setting protocol version is {previousProtocolVersion}")
            if previousProtocolVersion != PROTOCOL_VERSION:
                # Do upgrade the protocol version
                # Delete all previous peers
                # TBD
                pass
        else:
            self.settingStore.put("protocolVersion", protocolVersion=PROTOCOL_VERSION)

        Logger.info(f"Initializing the client with node name {self.nodeName}")
        # self.nodeStatus = {}  # key as ip:port time value for last seen connection

        # Get current network status mainly the NAT type
        self.getNetworkStatus()
        
        # List of nodes that is keeping alive
        # Key is node id and value is the clock that used to schedule the keep alive message
        self.currentKeepAlive = {}

        # The number of nodes that is keeping alive, default to 10
        self.keepAliveCount = 10

        # Start the keep alive for nodes
        self.serverConnect = Clock.schedule_interval(self.connect, 3)  # Getting the node information from the seed node
        self.keepAliveClock = None
        self.checkRNodesClock = Clock.schedule_interval(self.checkRNodes, 60)  # previously disconnect nodes status check


    # Handles get network status and send the network status to the seed nodes
    def getNetworkStatus(self, sendToServerIp=None):
        # First get the current network status mainly the NAT type
        import udpHolePunching.stun as stun
        natType, externalIp, _ = stun.get_ip_info()
        Logger.info(f"NAT type is {natType}")
        if natType == stun.FullCone or natType == stun.OpenInternet:
            natType = "typeA"
        elif natType == stun.RestricNAT:
            natType = "typeB"
        else:
            natType = "typeC"
        self.nodeNatType = natType
        
        if externalIp is not None:
            self.externalIp = externalIp
        else:
            externalIp = "None"
            self.externalIp = "None"
        Logger.info(f"The NAT type is {natType} and the external ip is {externalIp}")
        Logger.info("Sending the external ip, port and NAT type to the seed node.")

        # If sendToServerIp is not set, broadcast the network status to the seed node
        if sendToServerIp is None:
            # Update the seed nodes with the external ip, port and NAT type
            for seedNode in self.peers["server"]:
                self._server.send_message("/NNODE", [natType, externalIp, self.nodeName], seedNode, self.serverPort)
        else:
            self._server.send_message("/NNODE", [natType, externalIp, self.nodeName], sendToServerIp, self.serverPort)


    # Receive the node from the seed node or other nodes
    # Add or update the node information in the list of peers
    # If there is connection quota(current keep alive nodes are less than the keep alive count), send the keep alive message to the node
    def receiveNode(self, nodeIp, nodePort, nodeName, natType):
        # Cancel keep grabbing new nodes from the seed node
        if self.serverConnect is not None:
            self.serverConnect.cancel()
            self.serverConnect = None
        Logger.info(f"Received the node {nodeName} from the seed node.")

        # Check if node is self node
        if nodeName == self.nodeName:
            return

        # Check if the node is already in the list of peers
        if self.getNodeNatType(nodeName) != natType and self.getNodeNatType(nodeName) is not None:
            # If the node is in the list of peers, but the NAT type is different, remove the node from the list of peers
            self.removeNode(nodeName)

        # Store node information in the dictionary and try to connect to the node
        self.peers[natType][nodeName] = {"nodeIp": nodeIp,
                                         "nodePort": nodePort,
                                         "lastSeen": 0, # Default last seen time is 0 for client when receiving the node information
                                         "type": natType}

        # If the current keep alive nodes is less than the keep alive count, schedule the keep alive message to the new node
        if len(self.currentKeepAlive) < self.keepAliveCount:
            from functools import partial
            # The default router udp expiration time is 30 seconds, schedule the keep alive message with 5 seconds interval(to save the battery)
            self.currentKeepAlive[nodeName] = Clock.schedule_interval(partial(self.nodeKeepAlive, nodeName), 5)
        
    
    # Function to send keep alive message to other clients(nodes)
    # Just for sending, receiving will actually confirm the node is reachable and update the last seen time
    def nodeKeepAlive(self, targetNodeId, dt=0):
        nodeType = self.getNodeNatType(targetNodeId)
        if nodeType is None:
            Logger.info(f"Node {targetNodeId} is not in the list of peers, cancel the keep alive message.")
            self.currentKeepAlive[targetNodeId].cancel()
            del self.currentKeepAlive[targetNodeId]
            return
        else:
            self._server.send_message("/MSG", [self.nodeName, "keep", ""], self.peers[nodeType][targetNodeId]["nodeIp"],
                                      self.peers[nodeType][targetNodeId]["nodePort"])


    # Function to receive all messages from the other nodes
    # Update the last seen time for the node
    def nodeMessageReceived(self, nodeId, message, data):
        sock, ip, port = self._server.get_sender()
        # Update the last seen time
        self.updateLastSeen(nodeId)
        if message == "keep":
            Logger.info(f"Keep alive message: received from {nodeId}")
            Logger.info(f"ip: {ip}, port: {port}")
        elif message == "requestNetworkStatus":
            Logger.info(f"Request network status message: received from {nodeId}")
            Logger.info(f"ip: {ip}, port: {port}")
            self.getNetworkStatus(ip)
        elif message == "newBlock":
            Logger.info(f"New block message: received from {nodeId}, ip: {ip}, port: {port}")
            self.app.handleNewBlockReceived(ip, port, data)
        elif message == "locator":
            Logger.info(f"Locator message: received from {nodeId}, ip: {ip}, port: {port}")
            self.app.handleLocatorReceived(ip, port, data)
        elif message == "syncBlocks":
            Logger.info(f"Sync blocks message: received from {nodeId}, ip: {ip}, port: {port}")
            self.app.handleSyncBlocksReceived(ip, port, data)

        self.refreshList()

    def boardcastNewBlock(self, blockData):
        # boardcast to nodes that are keeping alive
        # need to boardcast to all nodes?
        for nodeId in self.currentKeepAlive.keys():
            nodeType = self.getNodeNatType(nodeId)
            self._server.send_message("/MSG", [self.nodeName, "newBlock", blockData], self.peers[nodeType][nodeId]["nodeIp"],
                                      self.peers[nodeType][nodeId]["nodePort"])
    
    # send locator data to the target node
    def sendLocator(self, targetIp, targetPort, locatorData):
        self._server.send_message("/MSG", [self.nodeName, "locator", locatorData], targetIp, targetPort)

    def sendBlocks(self, targetIp, targetPort, blocksData):
        self._server.send_message("/MSG", [self.nodeName, "syncBlocks", blocksData], targetIp, targetPort)

    # Function to check the last seen time for the current nodes, if the last seen time is greater than 30 seconds, then disconnect the node
    def checkRNodes(self, dt=0):
        nodeNeedToDisconnect = []

        for nodeId in self.currentKeepAlive.keys():
            nodeType = self.getNodeNatType(nodeId)
            currentTime = time.time()
            if currentTime - self.peers[nodeType][nodeId]["lastSeen"] > 30:
                Logger.info(f"The node {nodeId} is not reachable. Stop sending keep alive message to that node.")
                nodeNeedToDisconnect.append(nodeId)

        for nodeId in nodeNeedToDisconnect:
            self.currentKeepAlive[nodeId].cancel()
            self.currentKeepAlive.pop(nodeId)

            # Check if the current keep alive nodes is less than the keep alive count, then schedule the keep alive message to the new node
            if len(self.currentKeepAlive) < self.keepAliveCount and len(self.currentKeepAlive) < self.getTotalNodeCount():
                self.fillNodes()

        # Try to reconnect to the seed node to avoid random connection loss
        if len(nodeNeedToDisconnect) == 0 and len(self.currentKeepAlive) < self.keepAliveCount and len(self.currentKeepAlive) < self.getTotalNodeCount():
           self.fillNodes()


    def fillNodes(self):
        # Different NAT type has different priority
        # TypeA and TypeB can connect to TypeA and TypeB
        # TypeC can only connect to TypeA
        
        # Prepare the list of nodes to connect, only need the nodeId
        if self.nodeNatType == "typeA" or self.nodeNatType == "typeB":
            nodeList = self.peers["typeA"].copy()
            nodeList.update(self.peers["typeB"])
            nodeList = list(nodeList.keys())
        else: # TypeC can only connect to TypeA
            nodeList = list(self.peers["typeA"].copy().keys())

        for i in range(self.keepAliveCount - len(self.currentKeepAlive)):
            import random
            # select a random node from the list of nodes that is not in the current keep alive list
            retry = 0 # If already tried 10 times, then stop trying

            nodeId = random.choice(nodeList) # Select a random node from the list

            while nodeId in self.currentKeepAlive.keys() and retry < 10:
                nodeId = random.choice(nodeList)
                retry += 1
            if retry >= 10:
                Logger.info(
                    "No node is available to send the keep alive message. Maybe all nodes are in the current keep alive list or the list is empty.")
                break
            Logger.info(f"Scheduling the keep alive message to an random node {nodeId}")
            # Schedule the keep alive message with 5 seconds interval(to save the battery)
            from functools import partial
            self.currentKeepAlive[nodeId] = Clock.schedule_interval(partial(self.nodeKeepAlive, nodeId), 5)


    # Function to exchange node peer information with other nodes
    # TBD
    def exchangePeers(self, dt=0):
        # Check self peers is less than certain number, then send the peer request to other nodes
        # Consider network discovery?
        pass

    # Util function to update the last seen time for the node
    def updateLastSeen(self, nodeId):
        if nodeId == "SeedNode":
            pass
        else:
            nodeType = self.getNodeNatType(nodeId)
            if nodeType is not None:
                self.peers[nodeType][nodeId]["lastSeen"] = time.time()
            else:
                Logger.info(f"Received the keep alive message from the node {nodeId} but the node is not in the list.")

    # Util function to get node NAT type
    def getNodeNatType(self, nodeId):
        if nodeId in self.peers["typeA"].keys():
            return "typeA"
        elif nodeId in self.peers["typeB"].keys():
            return "typeB"
        elif nodeId in self.peers["typeC"].keys():
            return "typeC"
        else:
            return None

    # Util function to remove the node from the list
    def removeNode(self, nodeId):
        nodeType = self.getNodeNatType(nodeId)
        if nodeType is not None:
            self.peers[nodeType].pop(nodeId)
        else:
            Logger.info(f"Removing node stopped because the node {nodeId} is not in peers.")


    # Util function to get total number of nodes in peers
    def getTotalNodeCount(self):
        return len(self.peers["typeA"]) + len(self.peers["typeC"])


    # Util function to get node info from node id no matter the NAT type
    def getNodeInfo(self, nodeId):
        if nodeId in self.peers["typeA"].keys():
            return self.peers["typeA"][nodeId]
        elif nodeId in self.peers["typeB"].keys():
            return self.peers["typeB"][nodeId]
        elif nodeId in self.peers["typeC"].keys():
            return self.peers["typeC"][nodeId]
        else:
            return None

    # Update the GUI with the current node status
    # use @mainthread decorator to update the GUI, refer to https://github.com/kivy/kivy/wiki/Working-with-Python-threads-inside-a-Kivy-application
    @mainthread
    def refreshList(self):
        app = App.get_running_app()
        app.root.scrollStack.clear_widgets()
        app.root.nodeNum = self.getTotalNodeCount()
        label = NodeLabel()
        label.text = f"Total known node count is {app.root.nodeNum}\n Current connected node count is {len(self.currentKeepAlive)}\n" \
                     f"Below is the list of connected nodes:"
        app.root.scrollStack.add_widget(label)
        for nodeId in self.currentKeepAlive.keys():
            label = NodeLabel()
            NodeInfo = self.getNodeInfo(nodeId)
            label.text = f"Node id: {nodeId}\nNode ip: {NodeInfo['nodeIp']}\nNode port: {NodeInfo['nodePort']}\nNode type: {NodeInfo['type']}\n" \
                            f"Last seen: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(NodeInfo['lastSeen']))}"
            app.root.scrollStack.add_widget(label)


    def connect(self, dt=0):
        # Send external ip, prt and nat type to the seed node
        for seedNode in self.peers["server"]:
            # Logger.info(f"Sending an keep message to the seed node {seedNode}")
            self._server.send_message("/KEEP", [self.nodeName, self.nodeNatType], seedNode, self.serverPort)



class NodeLabel(Label):
    pass


class ClientGUI(BoxLayout):
    pass


class ClientApp(App):  # refer to https://github.com/kivy/kivy/issues/5689
    label = None

    def build(self):
        self.label = Label(text="TURN Server")
        self.client = VGraphClient(self)
        return ClientGUI()


if __name__ == '__main__':
    ClientApp().run()
