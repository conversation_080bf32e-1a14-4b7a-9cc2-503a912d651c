# Copyright © 2021 Primecoin Project

# How to build server image:
#   python -m compileall server.py
#   docker build -t seed-server .

FROM ubuntu:focal as base

# Setup env
ENV LANG C.UTF-8
ENV LC_ALL C.UTF-8
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONFAULTHANDLER 1

FROM base AS python-deps

# Install python3
RUN apt update
RUN apt install python3 python3-pip -y
RUN python3 --version

# Install pipenv
RUN pip3 install --upgrade pipenv

# Install python dependencies in /.venv
COPY ./Pipfile .
COPY ./Pipfile.lock .
RUN PIPENV_VENV_IN_PROJECT=1 pipenv install --deploy

FROM base AS runtime

# Install dependencies
RUN apt update --fix-missing
RUN apt install xvfb python3 -y
RUN apt install libmtdev1 -y

# Copy virtual env from python-deps stage
COPY --from=python-deps /.venv /.venv
ENV PATH="/.venv/bin:$PATH"

# Create and switch to a new user
RUN useradd --create-home turn
WORKDIR /home/<USER>
# USER turn

# Install application into container
COPY ./ .


RUN chmod a+x headless.sh
RUN ls -la .
RUN ls -la __pycache__

# Run specification
ENV DISPLAY :99
EXPOSE 33478/udp
ENTRYPOINT ["/home/<USER>/headless.sh"]
