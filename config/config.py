import os
from typing import List, Optional

import yaml
from kivy.logger import Logger


class Config:
    """
    Config class, responsible for loading config from config file.
    """

    maxSessions: int
    maxSendRecv: int
    sessionTimeout: int

    # Server settings(misc)
    logSessionInterval: int
    peerServers: List[str]
    localAddress: List[str]
    internetAddress: List[str]
    queryApiKey: Optional[str]

    # Chain settings
    dbPath: str
    chainId: str
    privateKey: Optional[str]

    # Swagger related
    swaggerServer: str
    systemContractAddressPath: str
    contractSourcePath: str
    tokenAddress: str

    _LOGGER_TITLE = "Config:"

    def __init__(self):
        self.loadConfig()
        self.validateConfig()

    def loadConfig(self):
        configFile = os.environ.get("CONFIG", "config.yaml")
        if not os.path.exists(f"./config/{configFile}"):
            raise Exception(f"Config file {configFile} not found")

        with open(f"./config/{configFile}", "r") as f:
            config = yaml.safe_load(f)
            self.maxSessions = config.get("max_sessions", 1000)
            self.maxSendRecv = config.get("max_send_recv", 5_000_000)  # default 5MB
            self.sessionTimeout = config.get("session_timeout", 60)
            self.logSessionInterval = config.get("log_session_interval", 60)
            self.peerServers = config.get("peer_servers", [])
            self.localAddress = config.get("local_address", [])
            self.internetAddress = config.get("internet_address", [])
            self.dbPath = config.get("db_path", "vgraph_lmdb")
            self.chainId = config.get("chain_id", "vgraphspos")
            self.tokenAddress = config.get(
                "token_address", "0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3"
            )
            self.privateKey = config.get("private_key", None)
            self.queryApiKey = config.get("query_api_key", None)

            # Swagger related
            self.swaggerServer = config.get("swagger", None)
            self.systemContractAddressPath = config.get("swagger_system_contracts_address_file", None)
            self.contractSourcePath = config.get("swagger_contract_source_code_dir", None)

        Logger.info(f"{self._LOGGER_TITLE} Config loaded successfully")

    # ----- validation -----
    def validateConfig(self):
        self.validateChainId()

    def validateChainId(self):
        """
        validate chain id

        rules:
            - chain id must not be None
            - chain id must be either 'vgraphspos' or 'vgraphpowprime'
        """
        if self.chainId is None:
            raise Exception("Chain ID is not set in config file")

        if self.chainId not in ["vgraphspos", "vgraphpowprime"]:
            raise Exception("Invalid chain ID")


# init global config
CONFIG = Config()
