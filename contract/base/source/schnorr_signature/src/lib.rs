#[glue::contract]
mod schnorr_signature {

    use rand::{rngs::OsRng, rngs::StdRng, SeedableRng};
    use schnorrkel::{signing_context, verify_batch, Keypair};
    use sha2::{Digest, Sha256};

    #[glue::storage]
    pub struct SchnorrUtils {}

    impl SchnorrUtils {
        #[glue::constructor]
        pub fn new() -> Self {
            SchnorrUtils {}
        }

        /// Generates a cryptographic key pair using a hashed version of the provided seed.
        #[glue::readonly]
        pub fn generate_keys_with_seed(&self, seed: String) -> anyhow::Result<(String, String)> {
            // Hash the seed to create a 32-byte array
            let mut hasher = Sha256::new();
            hasher.update(seed);
            let seed_hash = hasher.finalize();
            let seed_array: [u8; 32] = seed_hash
                .try_into()
                .expect("Hash algorithm changed output size");

            // Generate the key pair
            let mut rng = StdRng::from_seed(seed_array);
            let keypair = Keypair::generate_with(&mut rng);
            let public_key = bytes_to_hex(keypair.public.to_bytes());
            let private_key = bytes_to_hex(keypair.secret.to_bytes());
            Ok((public_key, private_key))
        }

        /// Generates a cryptographic key pair using the operating system's random number generator.
        #[glue::readonly]
        pub fn generate_keys(&self) -> anyhow::Result<(String, String)> {
            let keypair = Keypair::generate_with(OsRng);
            let public_key = bytes_to_hex(keypair.public.to_bytes());
            let private_key = bytes_to_hex(keypair.secret.to_bytes());
            Ok((public_key, private_key))
        }

        /// Converts a Schnorr private key to a public key.
        /// Both the private key and the public key are expected to be and are encoded using Base58 encoding.
        #[glue::readonly]
        pub fn private_key_to_public_key(&self, private_key: String) -> anyhow::Result<String> {
            let private_key_bytes = hex_to_bytes(&private_key)?;
            let private_key = schnorrkel::SecretKey::from_bytes(&private_key_bytes)
                .map_err(|_| anyhow::anyhow!("Invalid Schnorr private key"))?;
            let public_key = private_key.to_public();
            Ok(bytes_to_hex(public_key.to_bytes()))
        }

        /// Signs a message using a Schnorr private key and a given context.
        /// The message is signed using the Schnorrkel algorithm.
        /// The private key and the generated signature are both expected to be and are encoded using Base58 encoding.
        #[glue::readonly]
        pub fn sign_message(
            &self,
            message: String,
            private_key: String,
            context: String,
        ) -> anyhow::Result<String> {
            let private_key_bytes = hex_to_bytes(&private_key)?;
            let private_key = schnorrkel::SecretKey::from_bytes(&private_key_bytes)
                .map_err(|_| anyhow::anyhow!("Invalid Schnorr private key"))?;
            let keypair = Keypair::from(private_key);
            let sign_context = schnorrkel::signing_context(context.as_bytes());

            let signature = keypair.sign(sign_context.bytes(message.as_bytes()));
            Ok(bytes_to_hex(signature.to_bytes()))
        }

        /// Verifies the signature of a message given the public key and context.
        /// The function returns `true` if the signature is valid, and `false` otherwise.
        /// The public key and signature are expected to be encoded using Base58 encoding.
        #[glue::readonly]
        pub fn verify_signature(
            &self,
            message: String,
            signature: String,
            public_key: String,
            context: String,
        ) -> anyhow::Result<bool> {
            let signature_bytes = hex_to_bytes(&signature)?;
            let signature = schnorrkel::Signature::from_bytes(&signature_bytes)
                .map_err(|_| anyhow::anyhow!("Invalid Schnorr signature"))?;
            let public_key_bytes = hex_to_bytes(&public_key)?;
            let public_key = schnorrkel::PublicKey::from_bytes(&public_key_bytes)
                .map_err(|_| anyhow::anyhow!("Invalid Schnorr public key"))?;

            let sign_context = schnorrkel::signing_context(context.as_bytes());

            Ok(public_key
                .verify(sign_context.bytes(&message.as_bytes()), &signature)
                .is_ok())
        }

        /// Verifies the signatures of multiple messages given the public keys and context.
        /// The function returns `true` if all signatures are valid, and `false` otherwise.
        /// The public keys and signatures are expected to be encoded using Base58 encoding.
        /// Suppose all signatures are signed by the same signing context.
        #[glue::readonly]
        pub fn verify_batch_signatures(
            &self,
            messages: Vec<String>,
            signatures: Vec<String>,
            public_keys: Vec<String>,
            context: String,
        ) -> anyhow::Result<bool> {
            // check if the number of messages, signatures, and public keys match
            if messages.len() != signatures.len() || signatures.len() != public_keys.len() {
                return Err(anyhow::anyhow!(
                    "Messages, signatures, and public keys count must match"
                ));
            }

            // decode the signatures and public keys
            let signatures = signatures
                .iter()
                .map(|signature| {
                    let signature_bytes = hex_to_bytes(&signature)?;
                    schnorrkel::Signature::from_bytes(&signature_bytes)
                        .map_err(|_| anyhow::anyhow!("Invalid Schnorr signature"))
                })
                .collect::<Result<Vec<_>, _>>()?;

            let public_keys = public_keys
                .iter()
                .map(|public_key| {
                    let public_key_bytes = hex_to_bytes(&public_key)?;
                    schnorrkel::PublicKey::from_bytes(&public_key_bytes)
                        .map_err(|_| anyhow::anyhow!("Invalid Schnorr public key"))
                })
                .collect::<Result<Vec<_>, _>>()?;

            // create the signing context and transcripts
            let signing_context = signing_context(&context.as_bytes());
            let transcripts = messages
                .iter()
                .map(|message| signing_context.bytes(&message.as_bytes()))
                .collect::<Vec<_>>()
                .into_iter();

            // use the schnorrkel verify_batch function to verify the signatures
            Ok(verify_batch(transcripts, &signatures, &public_keys, false).is_ok())
        }
    }

    pub fn hex_to_bytes(hex: &str) -> Result<Vec<u8>, hex::FromHexError> {
        let trimmed_hex = if hex.starts_with("0x") {
            &hex[2..]
        } else {
            &hex
        };

        hex::decode(trimmed_hex)
    }

    pub fn bytes_to_hex<T: AsRef<[u8]>>(bytes: T) -> String {
        format!("0x{}", hex::encode(bytes.as_ref()))
    }
}

#[cfg(test)]
mod test_schnorr_utils {
    use crate::schnorr_signature;
    use crate::schnorr_signature::hex_to_bytes;

    #[glue::test]
    fn test_generate_keys_with_seed() {
        schnorr_signature::set_instance(schnorr_signature::SchnorrUtils::new());
        let schnorr_utils = schnorr_signature::get_instance();
        let seed = "some random seed";
        let (public_key, private_key) = schnorr_utils
            .generate_keys_with_seed(seed.to_string())
            .unwrap();
        println!("Public key: {}", public_key);
        println!("Private key: {}", private_key);
        assert_eq!(
            public_key,
            "0xec5f8415bed17e43573a6ce9d443fa845795a03d9689dd974afec4c674657739".to_string()
        );
        assert_eq!(private_key, "0x64234ec5cd8b2dca27df3cd6b946ff2b2b8c09977bc833501b8dcb3e53166c08d20f87d0b25716ecf5c1bd9b1e033d4fd8a8ccdc84b35d3c663231328bfeffb6".to_string())
    }

    #[glue::test]
    fn test_generate_keys() {
        schnorr_signature::set_instance(schnorr_signature::SchnorrUtils::new());
        let schnorr_utils = schnorr_signature::get_instance();
        let (public_key, private_key) = schnorr_utils.generate_keys().unwrap();
        println!("Public key: {}", public_key);
        println!("Private key: {}", private_key);
        // length of public key bytes should be 32
        let public_key_bytes = hex_to_bytes(&public_key).unwrap();
        assert_eq!(public_key_bytes.len(), 32);
        // length of private key bytes should be 64
        let private_key_bytes = hex_to_bytes(&private_key).unwrap();
        assert_eq!(private_key_bytes.len(), 64);
    }

    #[glue::test]
    fn test_private_key_to_public_key() {
        schnorr_signature::set_instance(schnorr_signature::SchnorrUtils::new());
        let schnorr_utils = schnorr_signature::get_instance();
        let private_key = "0x64234ec5cd8b2dca27df3cd6b946ff2b2b8c09977bc833501b8dcb3e53166c08d20f87d0b25716ecf5c1bd9b1e033d4fd8a8ccdc84b35d3c663231328bfeffb6".to_string();
        let public_key = schnorr_utils
            .private_key_to_public_key(private_key)
            .unwrap();
        println!("Public key: {}", public_key);
        assert_eq!(
            public_key,
            "0xec5f8415bed17e43573a6ce9d443fa845795a03d9689dd974afec4c674657739".to_string()
        );
    }

    #[glue::test]
    fn test_sign_message() {
        schnorr_signature::set_instance(schnorr_signature::SchnorrUtils::new());
        let schnorr_utils = schnorr_signature::get_instance();
        let message = "This is a test message".to_string();
        let private_key = "0x64234ec5cd8b2dca27df3cd6b946ff2b2b8c09977bc833501b8dcb3e53166c08d20f87d0b25716ecf5c1bd9b1e033d4fd8a8ccdc84b35d3c663231328bfeffb6".to_string();
        let context = "test context".to_string();
        let signature = schnorr_utils
            .sign_message(message, private_key, context)
            .unwrap();
        println!("Signature: {}", signature);
        // length of signature bytes should be 64
        let signature_bytes = hex_to_bytes(&signature).unwrap();
        assert_eq!(signature_bytes.len(), 64);
    }

    #[glue::test]
    fn test_verify_signature() {
        schnorr_signature::set_instance(schnorr_signature::SchnorrUtils::new());
        let schnorr_utils = schnorr_signature::get_instance();
        let message = "This is a test message";
        let public_key = "0xec5f8415bed17e43573a6ce9d443fa845795a03d9689dd974afec4c674657739";
        let context = "test context";
        let signature = "0x90b8165274692548a42d191deba124ac59d78d3cf4e84285fb3a84a9337d276ee5e9e663d6ce6910c83a4e35819148612fcb8c1e7e78d13d54579b8564f47b8b".to_string();
        let is_valid = schnorr_utils
            .verify_signature(
                message.to_string(),
                signature,
                public_key.to_string(),
                context.to_string(),
            )
            .unwrap();
        assert_eq!(is_valid, true);

        let invalid_signature = "0x10b8165274692548a42d191deba124ac19d78d3cf4e84285fb3a84a9337d276ee5e9e663d6ce6910c83a4e35819148612fcb8c1e7e78d13d54579b8564f47b8b".to_string();
        let is_invalid = schnorr_utils
            .verify_signature(
                message.to_string(),
                invalid_signature,
                public_key.to_string(),
                context.to_string(),
            )
            .unwrap();
        assert_eq!(is_invalid, false);
    }

    #[glue::test]
    fn test_verify_batch_signatures() {
        schnorr_signature::set_instance(schnorr_signature::SchnorrUtils::new());
        let schnorr_utils = schnorr_signature::get_instance();
        let messages = vec![
            "This is a test message 1".to_string(),
            "This is a test message 2".to_string(),
            "This is a test message 3".to_string(),
        ];
        let context = "test context".to_string();
        let mut public_keys = Vec::new();
        let mut signatures = Vec::new();
        for i in 0..messages.len() {
            let (public_key, private_key) = schnorr_utils.generate_keys().unwrap();
            let signature = schnorr_utils
                .sign_message(messages[i].clone(), private_key, context.clone())
                .unwrap();
            public_keys.push(public_key);
            signatures.push(signature);
        }

        let is_valid = schnorr_utils
            .verify_batch_signatures(messages, signatures, public_keys, context.clone())
            .unwrap();
        assert_eq!(is_valid, true);
    }
}
