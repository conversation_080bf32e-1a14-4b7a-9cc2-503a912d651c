use hex;
use rand::rngs::OsRng;
use schnorrkel::{signing_context, Keypair, Signature};

fn example() {
    // Generate a new keypair using OsRng
    let keypair: Keypair = Keypair::generate_with(OsRng);

    // print the public key and private key
    println!(
        "Public key: {}",
        hex::encode(keypair.public.to_bytes()).into_string()
    );
    println!("Public key len: {}", keypair.public.to_bytes().len());
    println!(
        "Private key: {}",
        hex::encode(keypair.secret.to_bytes()).into_string()
    );
    println!("Private key len: {}", keypair.secret.to_bytes().len());

    // Define the signing context
    let context = signing_context(b"this signature does this thing");

    // Define the message to be signed
    let message: &[u8] = "This is a test of the tsunami alert system.".as_bytes();

    // Sign the message using the keypair and signing context
    let signature: Signature = keypair.sign(context.bytes(message));
    println!(
        "Signature: {}",
        hex::encode(signature.to_bytes()).into_string()
    );

    // Verify the signature(should be correct)
    match keypair.verify(context.bytes(message), &signature) {
        Ok(()) => println!("Signature verified"),
        Err(e) => println!("Error verifying signature: {:?}", e),
    }

    // Verify a fake signature(should be incorrect)
    let fake_message: &[u8] = "This is a test of the tsunami alert system?".as_bytes();
    let fake_signature: Signature = keypair.sign(context.bytes(fake_message));

    match keypair.verify(context.bytes(message), &fake_signature) {
        Ok(()) => println!("Fake signature verified"),
        Err(e) => println!("Error verifying fake signature: {:?}", e),
    }
}
