[package]
name = "schnorr_signature"
version = "0.1.0"
edition = "2021"
authors = ["VGraph"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
glue = { path = "../glue" }

sha2 = "0.10.8"
hex = "0.4.3"
rand = "0.8.5"
anyhow = "1.0.75"
schnorrkel = "0.11.4"
serde = { version = "=1.0.193", features = ["derive"] }

[dev-dependencies]
serial_test = "3.1.1"

[lib]
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[profile.release]
codegen-units = 1
opt-level = "z"
lto = true
