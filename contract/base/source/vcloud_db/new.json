[{"_id": "67fe04430a8dc2ee4243280d", "duration": 3, "amount": 0.0075, "publicKey": "GvaJxsN4wzfxhM3v8iAA9b1u6sCi4ahtjTRaZ4SbiJ9T", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUEcxSD6hM2ngNRV5FwB9sHfkSk9wvTprhR", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "67fe0a14c8686a72a20a9f86", "duration": 4, "amount": 0.01, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "67fe2bfc1f425d3c937bd45b", "duration": 1, "amount": 0.0025, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "67fe04430a8dc2ee4243280d", "duration": 3, "amount": 0.0075, "publicKey": "GvaJxsN4wzfxhM3v8iAA9b1u6sCi4ahtjTRaZ4SbiJ9T", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUEcxSD6hM2ngNRV5FwB9sHfkSk9wvTprhR", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "67fe2bfc1f425d3c937bd45b", "duration": 1, "amount": 0.0025, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "67fe511b4abfbf2f57dc95c5", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "region": "Europe", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "67fe511b4abfbf2f57dc95c5", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "region": "Europe", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "67ff09ff893fc3aaa78650db", "duration": 1, "amount": 0.0025, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "67ff0a84893fc3aaa78650dd", "duration": 2, "amount": 0.005, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "persistStorage": "No", "region": "Europe", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "67ff0b10893fc3aaa78650df", "duration": 8, "amount": 0.08, "publicKey": "CbXq5biQdELi5oG4ZHy4YEfPYqxJZ2kzUAdep9L4yqPB", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATsGRfX2EpUt2Uxqj9TwAWXZipH3azmPfLc", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceAbort", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "4-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": **********, "serviceDoneTS": 0, "serviceRefundTS": **********, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "67ff0bc8893fc3aaa78650e1", "duration": 358, "amount": 3.579999999999999, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "persistStorage": "Yes", "portSpecification": "System Random Map Port", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": "6gHELn5DaNGQX3XKoE7rwaFeVeVfAfquiCp71PBYjqp4"}, {"_id": "67ff15a6695e6c7c1aa96b59", "duration": 1, "amount": 0.0025, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 3600, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "67ff15a6695e6c7c1aa96b59", "duration": 1, "amount": 0.0025, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 3600, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "67ff1782695e6c7c1aa96b5b", "duration": 1, "amount": 0.005, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "2-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "Yes"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "67ff09ff893fc3aaa78650db", "duration": 1, "amount": 0.0025, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "67ff2002dc4de8c16d2fdda1", "duration": 2, "amount": 0.01, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "2-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "Yes", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "67ff0b10893fc3aaa78650df", "duration": 8, "amount": 0.08, "publicKey": "CbXq5biQdELi5oG4ZHy4YEfPYqxJZ2kzUAdep9L4yqPB", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATsGRfX2EpUt2Uxqj9TwAWXZipH3azmPfLc", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceAbort", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "4-Unit-Resource", "persistStorage": "No", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": **********, "serviceDoneTS": 0, "serviceRefundTS": **********, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "67ff0a84893fc3aaa78650dd", "duration": 2, "amount": 0.005, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "67ff1782695e6c7c1aa96b5b", "duration": 1, "amount": 0.005, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "2-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "Yes"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "67fe0a14c8686a72a20a9f86", "duration": 4, "amount": 0.01, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "67ff2002dc4de8c16d2fdda1", "duration": 2, "amount": 0.01, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "Yes", "portSpecification": "User Specified Service Port", "resourceUnit": "2-Unit-Resource", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "67ff4728f8ea697185ee08b1", "duration": 2, "amount": 0.01, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "2-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "Yes"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "67ff4ce2b57b6aaf34d209d9", "duration": 2, "amount": 0.005, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "67ff4ce2b57b6aaf34d209d9", "duration": 2, "amount": 0.005, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "67ff4d0bb57b6aaf34d209db", "duration": 2, "amount": 0.005, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "Yes", "region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "67ff5b3d52a8fd3e8e33a4b0", "duration": 4, "amount": 0.04, "publicKey": "7cPhubxUw3e6MoxLxu4kWk1kb3k29kM4iTkuQmuCs7P1", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "4-Unit-Resource", "persistStorage": "No", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "labelHash": ""}, {"_id": "67ff5fbef396698024d282ce", "duration": 4, "amount": 0.01, "publicKey": "7cPhubxUw3e6MoxLxu4kWk1kb3k29kM4iTkuQmuCs7P1", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "labelHash": ""}, {"_id": "67ff64368ea7ab2738172605", "duration": 2, "amount": 0.005, "publicKey": "53HX6ApdmDobYqS2WtE5Dh8ynbc7Cb1aah7wqjiDDeR6", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "labelHash": ""}, {"_id": "67ff6978436134718232c6ff", "duration": 1, "amount": 0.0025, "publicKey": "7cPhubxUw3e6MoxLxu4kWk1kb3k29kM4iTkuQmuCs7P1", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "labelHash": ""}, {"_id": "67ff69e0436134718232c701", "duration": 168, "amount": 0.42, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "67ff6b6d436134718232c703", "duration": 1, "amount": 0.0025, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "67ff4d0bb57b6aaf34d209db", "duration": 2, "amount": 0.005, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "Yes", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "67ff6f23436134718232c705", "duration": 1, "amount": 0.0025, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "67ff6978436134718232c6ff", "duration": 1, "amount": 0.0025, "publicKey": "7cPhubxUw3e6MoxLxu4kWk1kb3k29kM4iTkuQmuCs7P1", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "labelHash": ""}, {"_id": "67ff6b6d436134718232c703", "duration": 1, "amount": 0.0025, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "67ff6f23436134718232c705", "duration": 1, "amount": 0.0025, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "67ff64368ea7ab2738172605", "duration": 2, "amount": 0.005, "publicKey": "53HX6ApdmDobYqS2WtE5Dh8ynbc7Cb1aah7wqjiDDeR6", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "labelHash": ""}, {"_id": "67ff4728f8ea697185ee08b1", "duration": 2, "amount": 0.01, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "2-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "Yes"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "67ff5b3d52a8fd3e8e33a4b0", "duration": 4, "amount": 0.04, "publicKey": "7cPhubxUw3e6MoxLxu4kWk1kb3k29kM4iTkuQmuCs7P1", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "region": "North America", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "labelHash": ""}, {"_id": "67ff5fbef396698024d282ce", "duration": 4, "amount": 0.01, "publicKey": "7cPhubxUw3e6MoxLxu4kWk1kb3k29kM4iTkuQmuCs7P1", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "labelHash": ""}, {"_id": "68005db6de6d2bb564767797", "duration": 8, "amount": 0.08, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "", "resourceUnit": "1-Unit-Resource", "region": "North America", "persistStorage": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68006dafe1dec366ef342400", "duration": 2, "amount": 0.005, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "68006dafe1dec366ef342400", "duration": 2, "amount": 0.005, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "68005db6de6d2bb564767797", "duration": 8, "amount": 0.08, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "", "resourceUnit": "1-Unit-Resource", "region": "North America", "persistStorage": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6801c007adb153f5f751b602", "duration": 9, "amount": 0.0225, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "6805e54325c1b0cac778f716", "duration": 8760, "amount": 87.6, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"persistStorage": "", "resourceUnit": "1-Unit-Resource", "region": "North America", "portSpecification": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "6805e54325c1b0cac778f716", "duration": 8760, "amount": 87.6, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"portSpecification": "", "resourceUnit": "1-Unit-Resource", "persistStorage": "", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "6801c007adb153f5f751b602", "duration": 9, "amount": 0.0225, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "68076c64b0589cb2c2b5a67b", "duration": 720, "amount": 3.6, "publicKey": "53HX6ApdmDobYqS2WtE5Dh8ynbc7Cb1aah7wqjiDDeR6", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "North America", "resourceUnit": "2-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 2592000, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "labelHash": ""}, {"_id": "68076c8cb0589cb2c2b5a67d", "duration": 8760, "amount": 21.9, "publicKey": "53HX6ApdmDobYqS2WtE5Dh8ynbc7Cb1aah7wqjiDDeR6", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceAbort", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "region": "North America", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 31536000, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": **********, "serviceDoneTS": 0, "serviceRefundTS": **********, "service": "Container Service", "createdAddr": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "labelHash": ""}, {"_id": "680843d10d02fa0ddfecefe7", "duration": 8760, "amount": 87.6, "publicKey": "53HX6ApdmDobYqS2WtE5Dh8ynbc7Cb1aah7wqjiDDeR6", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceAbort", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": **********, "serviceDoneTS": 0, "serviceRefundTS": **********, "service": "Container Service", "createdAddr": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "labelHash": ""}, {"_id": "68076c8cb0589cb2c2b5a67d", "duration": 8760, "amount": 21.9, "publicKey": "53HX6ApdmDobYqS2WtE5Dh8ynbc7Cb1aah7wqjiDDeR6", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceAbort", "serviceOptions": {"region": "North America", "resourceUnit": "1-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 31536000, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": **********, "serviceDoneTS": 0, "serviceRefundTS": **********, "service": "Container Service", "createdAddr": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "labelHash": ""}, {"_id": "68084686e7e3cf8e520a01fd", "duration": 12, "amount": 0.03, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "68084e7163672e048c2eb057", "duration": 8760, "amount": 21.9, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "persistStorage": "No", "region": "Europe", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "labelHash": "5nzjm3wpNh9txkB8uxexL6oo27VpybhHomyeLuA6be9R"}, {"_id": "680861253c6e611fc5e98029", "duration": 1, "amount": 0.01, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "", "resourceUnit": "1-Unit-Resource", "persistStorage": "", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680861253c6e611fc5e98029", "duration": 1, "amount": 0.01, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "North America", "persistStorage": "", "portSpecification": "", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "68084e7163672e048c2eb057", "duration": 8760, "amount": 21.9, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "labelHash": "5nzjm3wpNh9txkB8uxexL6oo27VpybhHomyeLuA6be9R"}, {"_id": "68084e7163672e048c2eb058", "duration": 8760, "amount": 21.9, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "North America", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "labelHash": "A63NeHnARWTRAjQEqSFedfr1n7UvWjFV32tyVeNQNYAq"}, {"_id": "67ff69e0436134718232c701", "duration": 168, "amount": 0.42, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "6808b2ed5c2b7fa6a0c4c53f", "duration": 192, "amount": 1.92, "publicKey": "CbXq5biQdELi5oG4ZHy4YEfPYqxJZ2kzUAdep9L4yqPB", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATsGRfX2EpUt2Uxqj9TwAWXZipH3azmPfLc", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 691200, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATsGRfX2EpUt2Uxqj9TwAWXZipH3azmPfLc", "labelHash": ""}, {"_id": "68084686e7e3cf8e520a01fd", "duration": 12, "amount": 0.03, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "680a07c8998d3cf9940559c9", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "680defe0e0269b7f169d97aa", "duration": 24, "amount": 0.24, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "", "persistStorage": "", "resourceUnit": "1-Unit-Resource", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680df12ae0269b7f169d97ac", "duration": 24, "amount": 0.24, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "North America", "portSpecification": "", "persistStorage": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680dfc553f4a3b156213930f", "duration": 192, "amount": 3.84, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "2-Unit-Resource", "region": "North America", "portSpecification": "", "persistStorage": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680eeb19031907342f831ec6", "duration": 48, "amount": 0.12, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "persistStorage": "No", "region": "North America", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680843d10d02fa0ddfecefe7", "duration": 8760, "amount": 87.6, "publicKey": "53HX6ApdmDobYqS2WtE5Dh8ynbc7Cb1aah7wqjiDDeR6", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceAbort", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "persistStorage": "No", "region": "North America", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": **********, "serviceDoneTS": 0, "serviceRefundTS": **********, "service": "Container Service", "createdAddr": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "labelHash": ""}, {"_id": "680efaf767b4b9948beeea8a", "duration": 8760, "amount": 87.6, "publicKey": "53HX6ApdmDobYqS2WtE5Dh8ynbc7Cb1aah7wqjiDDeR6", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "region": "North America", "portSpecification": "User Specified Service Port", "persistStorage": "Yes"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "labelHash": ""}, {"_id": "680f22672bafed1c02f4ae5d", "duration": 168, "amount": 0.42, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"persistStorage": "No", "region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680f22672bafed1c02f4ae5d", "duration": 168, "amount": 0.42, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680f22982bafed1c02f4ae5f", "duration": 168, "amount": 0.42, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "Yes", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680defe0e0269b7f169d97aa", "duration": 24, "amount": 0.24, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "", "portSpecification": "", "region": "North America", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680df12ae0269b7f169d97ac", "duration": 24, "amount": 0.24, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "portSpecification": "", "region": "North America", "persistStorage": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "6810344767b0cf46a707b1c7", "duration": 3, "amount": 0.0075, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "North America", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6810344767b0cf46a707b1c7", "duration": 3, "amount": 0.0075, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6811781f779d2c01fccd4ca9", "duration": 3, "amount": 0.0075, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "680eeb19031907342f831ec6", "duration": 48, "amount": 0.12, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "68119699793e73e872d96c71", "duration": 8, "amount": 0.04, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "2-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "6811d5b0bd3cdc46a0808d19", "duration": 24, "amount": 0.12, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No", "resourceUnit": "2-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "6811d8ec455aad1a1bfea403", "duration": 3, "amount": 0.0075, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"persistStorage": "No", "resourceUnit": "1-Unit-Resource", "portSpecification": "System Random Map Port", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 10800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6811d8ec455aad1a1bfea403", "duration": 3, "amount": 0.0075, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "persistStorage": "No", "portSpecification": "System Random Map Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 10800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6811dde15a59eeb1a56b410d", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6811ed0399723125cd8cb67e", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "4-Unit-Resource", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "680a07c8998d3cf9940559c9", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6811ed0399723125cd8cb67e", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "67ff0bc8893fc3aaa78650e1", "duration": 358, "amount": 3.579999999999999, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "persistStorage": "Yes", "portSpecification": "System Random Map Port", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": "6gHELn5DaNGQX3XKoE7rwaFeVeVfAfquiCp71PBYjqp4"}, {"_id": "680f22982bafed1c02f4ae5f", "duration": 168, "amount": 0.42, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "Yes", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680dfc553f4a3b156213930f", "duration": 192, "amount": 3.84, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "", "region": "North America", "resourceUnit": "2-Unit-Resource", "portSpecification": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "681980a00418786d74d69bec", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "4-Unit-Resource", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681980a00418786d74d69bec", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6819abe9eb4b7712cbcfbd30", "duration": 4, "amount": 0.04, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6819bee53cf482ff0aa7143a", "duration": 1, "amount": 0.01, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 3600, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "6819bee53cf482ff0aa7143a", "duration": 1, "amount": 0.01, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 3600, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "6819bf76b4c22c174712ae97", "duration": 12, "amount": 0.03, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "persistStorage": "No", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "6819c1c13cf482ff0aa7143c", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "persistStorage": "No", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6819c4573cf482ff0aa7143e", "duration": 1, "amount": 0.01, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "6819c4573cf482ff0aa7143e", "duration": 1, "amount": 0.01, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "4-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "6819d7d97e92efd5ea5bc22c", "duration": 888, "amount": 2.22, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "6819c1c13cf482ff0aa7143c", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "4-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6819abe9eb4b7712cbcfbd30", "duration": 4, "amount": 0.04, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6819bf76b4c22c174712ae97", "duration": 12, "amount": 0.03, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "681abf8d2ec190d784cbee43", "duration": 168, "amount": 0.42, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "6811781f779d2c01fccd4ca9", "duration": 3, "amount": 0.0075, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "1-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681af91c584fd49d4fbc1c4f", "duration": 2, "amount": 0.01, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No", "resourceUnit": "2-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681af91c584fd49d4fbc1c4f", "duration": 2, "amount": 0.01, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "2-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681c11dbd5fe66fa9b86b7f9", "duration": 168, "amount": 1.68, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"region": "North America", "persistStorage": "", "portSpecification": "", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "681c1207d5fe66fa9b86b7fc", "duration": 336, "amount": 0.84, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceAbort", "serviceOptions": {"persistStorage": "No", "region": "North America", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": **********, "serviceDoneTS": 0, "serviceRefundTS": **********, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "681c51c39eb6b1e974f9d0c1", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681c51c39eb6b1e974f9d0c1", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681c51e29eb6b1e974f9d0c3", "duration": 4, "amount": 0.04, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "resourceUnit": "4-Unit-Resource", "region": "Europe", "portSpecification": "System Random Map Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681c62b3e9527721a1fef170", "duration": 168, "amount": 0.42, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": "BEBGKRKH1w3WwvTYmjSXbkCMDXVmPdoMdVumuBLegbuT"}, {"_id": "681c71523135c1774efba19b", "duration": 3, "amount": 0.03, "publicKey": "53HX6ApdmDobYqS2WtE5Dh8ynbc7Cb1aah7wqjiDDeR6", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 10800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "labelHash": ""}, {"_id": "681c71523135c1774efba19b", "duration": 3, "amount": 0.03, "publicKey": "53HX6ApdmDobYqS2WtE5Dh8ynbc7Cb1aah7wqjiDDeR6", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "4-Unit-Resource", "persistStorage": "No", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 10800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "labelHash": ""}, {"_id": "681c51e29eb6b1e974f9d0c3", "duration": 4, "amount": 0.04, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "System Random Map Port", "region": "Europe", "resourceUnit": "4-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681d5f7c4d78b9fd74c6da4c", "duration": 5, "amount": 0.05, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681d5f7c4d78b9fd74c6da4c", "duration": 5, "amount": 0.05, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681daccb707a0cbb7cc27246", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "persistStorage": "No", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681dbbc73702feb404b39956", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681dbbf03702feb404b3995f", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "persistStorage": "", "portSpecification": "", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681dbf7b3b7059c82fe9660a", "duration": 5, "amount": 0.025, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "2-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "681dcb4e8720974bdfd53bc3", "duration": 168, "amount": 1.68, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "North America", "persistStorage": "No", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "681dcff54cae4c39592ed537", "duration": 4, "amount": 0.04, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 14400, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "681dcff54cae4c39592ed537", "duration": 4, "amount": 0.04, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "persistStorage": "No", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 14400, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "681daccb707a0cbb7cc27246", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "persistStorage": "No", "region": "Europe", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681dbf7b3b7059c82fe9660a", "duration": 5, "amount": 0.025, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "2-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "681dbbf03702feb404b3995f", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"portSpecification": "", "persistStorage": "", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68214d4078b88ab81d1ea5ea", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"region": "Europe", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68214d5278b88ab81d1ea5ec", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "4-Unit-Resource", "persistStorage": "No", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68214e6f78b88ab81d1ea5ee", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6821508f78b88ab81d1ea5f0", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682150d478b88ab81d1ea5f2", "duration": 3, "amount": 0.0075, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6821526a78b88ab81d1ea5f4", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682153a478b88ab81d1ea5f6", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "North America", "persistStorage": "No", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 10800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682153a478b88ab81d1ea5f6", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "North America", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 10800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68215736ebfa71ddec0d10af", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "resourceUnit": "4-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682157d2ebfa71ddec0d10b1", "duration": 2, "amount": 0.01, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "2-Unit-Resource", "region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682158abeea069cce064c783", "duration": 2, "amount": 0.01, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "2-Unit-Resource", "persistStorage": "No", "region": "North America", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68215b67eea069cce064c785", "duration": 3, "amount": 0.0075, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "68214e6f78b88ab81d1ea5ee", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6821508f78b88ab81d1ea5f0", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682157d2ebfa71ddec0d10b1", "duration": 2, "amount": 0.01, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "2-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682158abeea069cce064c783", "duration": 2, "amount": 0.01, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "North America", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "2-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68214d5278b88ab81d1ea5ec", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "persistStorage": "No", "region": "Europe", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682150d478b88ab81d1ea5f2", "duration": 3, "amount": 0.0075, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6821526a78b88ab81d1ea5f4", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68215736ebfa71ddec0d10af", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6819d7d97e92efd5ea5bc22c", "duration": 888, "amount": 2.22, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "6822fe0939586c9a7b05a5c6", "duration": 2, "amount": 0.005, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "6822fe0939586c9a7b05a5c6", "duration": 2, "amount": 0.005, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "6823ed400300ba5d6e9abab7", "duration": 1, "amount": 0.005, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "2-Unit-Resource", "region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "6823f231380a7a5c5b80d79c", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "persistStorage": "No", "portSpecification": "System Random Map Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6823f231380a7a5c5b80d79c", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "resourceUnit": "1-Unit-Resource", "persistStorage": "No", "portSpecification": "System Random Map Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6823f6d4804f7a04a6123b65", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681abf8d2ec190d784cbee43", "duration": 168, "amount": 0.42, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "6823f6d4804f7a04a6123b65", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682547e04adc165db31acae1", "duration": 168, "amount": 1.68, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "resourceUnit": "1-Unit-Resource", "portSpecification": "", "persistStorage": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "682547e04adc165db31acae1", "duration": 168, "amount": 1.68, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "resourceUnit": "1-Unit-Resource", "persistStorage": "", "portSpecification": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "682548304adc165db31acae3", "duration": 168, "amount": 1.68, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"portSpecification": "", "region": "Europe", "resourceUnit": "1-Unit-Resource", "persistStorage": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "682548304adc165db31acae3", "duration": 168, "amount": 1.68, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "portSpecification": "", "persistStorage": "", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "682548d94adc165db31acae5", "duration": 168, "amount": 0.42, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "persistStorage": "No", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "68258a9a659f970821cbf1d0", "duration": 168, "amount": 0.42, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "68258b0b659f970821cbf1d2", "duration": 168, "amount": 0.42, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "68259b098adc1b650ac29e61", "duration": 168, "amount": 1.68, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "4-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "681c62b3e9527721a1fef170", "duration": 168, "amount": 0.42, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": "BEBGKRKH1w3WwvTYmjSXbkCMDXVmPdoMdVumuBLegbuT"}, {"_id": "680efaf767b4b9948beeea8a", "duration": 8760, "amount": 87.6, "publicKey": "53HX6ApdmDobYqS2WtE5Dh8ynbc7Cb1aah7wqjiDDeR6", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"persistStorage": "Yes", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU6HropZPz5jz2cRJMiudCke9pfJaaLk8mF", "labelHash": ""}, {"_id": "6825c0de52bc239d975bce67", "duration": 2160, "amount": 5.4, "publicKey": "7cPhubxUw3e6MoxLxu4kWk1kb3k29kM4iTkuQmuCs7P1", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "labelHash": ""}, {"_id": "6825c0de52bc239d975bce67", "duration": 2160, "amount": 5.4, "publicKey": "7cPhubxUw3e6MoxLxu4kWk1kb3k29kM4iTkuQmuCs7P1", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "labelHash": ""}, {"_id": "6826954c6395013dc65832c7", "duration": 168, "amount": 0.42, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "68269b77e00c99a77e62c623", "duration": 720, "amount": 1.8, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 2592000, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68269bf6e00c99a77e62c625", "duration": 168, "amount": 0.42, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"region": "Europe", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68269c3aa56d0b902c05b7f3", "duration": 168, "amount": 0.42, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68269d68a56d0b902c05b7f5", "duration": 3, "amount": 0.0075, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "68269eebe00c99a77e62c627", "duration": 168, "amount": 0.42, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "6826a54921674ef4d8e0a804", "duration": 168, "amount": 0.42, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6826a58921674ef4d8e0a806", "duration": 168, "amount": 0.42, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6826a944546831c715736382", "duration": 168, "amount": 0.42, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6826a954546831c715736384", "duration": 168, "amount": 1.68, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"region": "Europe", "resourceUnit": "1-Unit-Resource", "portSpecification": "", "persistStorage": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6826a974546831c715736386", "duration": 720, "amount": 1.8, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 2592000, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68269d68a56d0b902c05b7f5", "duration": 3, "amount": 0.0075, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "681dcb4e8720974bdfd53bc3", "duration": 168, "amount": 1.68, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "North America", "persistStorage": "No", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "68281f94109a40b609b7863a", "duration": 8760, "amount": 21.9, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"persistStorage": "Yes", "portSpecification": "User Specified Service Port", "region": "North America", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "labelHash": "DXVqc4bmGhJUsBj5T4jYKKuqzTMBpbFHq33jt6SviQM7"}, {"_id": "68281f94109a40b609b7863a", "duration": 8760, "amount": 21.9, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "persistStorage": "Yes", "portSpecification": "User Specified Service Port", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "labelHash": "DXVqc4bmGhJUsBj5T4jYKKuqzTMBpbFHq33jt6SviQM7"}, {"_id": "682ae612828846fd42ee50e1", "duration": 720, "amount": 14.4, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"region": "North America", "portSpecification": "", "resourceUnit": "2-Unit-Resource", "persistStorage": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "labelHash": ""}, {"_id": "682ae612828846fd42ee50e1", "duration": 720, "amount": 14.4, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"region": "North America", "resourceUnit": "2-Unit-Resource", "persistStorage": "", "portSpecification": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "labelHash": ""}, {"_id": "682afc8eecab37cdeffac5f7", "duration": 2160, "amount": 21.6, "publicKey": "7cPhubxUw3e6MoxLxu4kWk1kb3k29kM4iTkuQmuCs7P1", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "labelHash": ""}, {"_id": "682afcb7ecab37cdeffac5f9", "duration": 2160, "amount": 5.4, "publicKey": "7cPhubxUw3e6MoxLxu4kWk1kb3k29kM4iTkuQmuCs7P1", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "persistStorage": "No", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7776000, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "labelHash": ""}, {"_id": "682afc8eecab37cdeffac5f7", "duration": 2160, "amount": 21.6, "publicKey": "7cPhubxUw3e6MoxLxu4kWk1kb3k29kM4iTkuQmuCs7P1", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpjrq7uxmRyGRJmdPxLei9n3faXiU7vhY", "labelHash": ""}, {"_id": "682bdc5047013f5c79617912", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "4-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682bdc5047013f5c79617912", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682bf3bcb150a5550e849114", "duration": 1, "amount": 0.0025, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 3600, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682bfa0778cc789687fb6d61", "duration": 168, "amount": 0.84, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceAbort", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "2-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": **********, "serviceDoneTS": 0, "serviceRefundTS": **********, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "682bfa0778cc789687fb6d61", "duration": 168, "amount": 0.84, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceAbort", "serviceOptions": {"region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "2-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": **********, "serviceDoneTS": 0, "serviceRefundTS": **********, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "682c331b79eb401b0b3ef98a", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "persistStorage": "No", "region": "Europe", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6823ed400300ba5d6e9abab7", "duration": 1, "amount": 0.005, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "resourceUnit": "2-Unit-Resource", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "682d34e824c09000332b16c4", "duration": 24, "amount": 0.06, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "North America", "resourceUnit": "1-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "681c1207d5fe66fa9b86b7fc", "duration": 336, "amount": 0.84, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceAbort", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": **********, "serviceDoneTS": 0, "serviceRefundTS": **********, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "682d4fbd41d47587092a7de5", "duration": 8760, "amount": 87.6, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "682d4fbd41d47587092a7de5", "duration": 8760, "amount": 87.6, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "4-Unit-Resource", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "682d903c6f82bdbda90550ff", "duration": 1, "amount": 0.0025, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "682548d94adc165db31acae5", "duration": 168, "amount": 0.42, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "681c1207d5fe66fa9b86b7fb", "duration": 336, "amount": 3.36, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "North America", "portSpecification": "", "persistStorage": "", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "68258b0b659f970821cbf1d2", "duration": 168, "amount": 0.42, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "682ed31bc0b67e9ec8c6951e", "duration": 168, "amount": 0.42, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "682ed878d1ed1844aade80e2", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "", "portSpecification": "", "region": "North America", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68259b098adc1b650ac29e61", "duration": 168, "amount": 1.68, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "4-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "682ed878d1ed1844aade80e2", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"portSpecification": "", "region": "North America", "resourceUnit": "1-Unit-Resource", "persistStorage": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6826954c6395013dc65832c7", "duration": 168, "amount": 0.42, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "68269eebe00c99a77e62c627", "duration": 168, "amount": 0.42, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "persistStorage": "No", "region": "Europe", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "683021d1fa387af8b65e7d5c", "duration": 1, "amount": 0.0025, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 3600, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "682d903c6f82bdbda90550ff", "duration": 1, "amount": 0.0025, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "6830496c2422e35551ee4e01", "duration": 168, "amount": 0.42, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "North America", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "Yes"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "labelHash": ""}, {"_id": "6830496c2422e35551ee4e01", "duration": 168, "amount": 0.42, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"persistStorage": "Yes", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "labelHash": ""}, {"_id": "682ed31bc0b67e9ec8c6951e", "duration": 168, "amount": 0.42, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "68382f4f1f84d645d975acff", "duration": 2, "amount": 0.005, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "6838319a1f84d645d975ad01", "duration": 1, "amount": 0.0025, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 3600, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "labelHash": ""}, {"_id": "68215b67eea069cce064c785", "duration": 3, "amount": 0.0075, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "6811d5b0bd3cdc46a0808d19", "duration": 24, "amount": 0.12, "publicKey": "BAJ8dBnNCKC2VbkPPAbP3q36eQMtd1gxnH96pADqwCgP", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "Europe", "resourceUnit": "2-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATtAbpVr5kJA4v5FpeyNcnSVGU9koX3atGD", "labelHash": ""}, {"_id": "683e93e3acf533a979ce8bad", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683e93e3acf533a979ce8bad", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682c331b79eb401b0b3ef98a", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"portSpecification": "User Specified Service Port", "region": "Europe", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683eb7eae6adb1876d007a93", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683eb7eae6adb1876d007a93", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683eb91ce6adb1876d007a95", "duration": 24, "amount": 0.06, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 86400, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683eb91ce6adb1876d007a95", "duration": 24, "amount": 0.06, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 86400, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683f9af46e8a8f579a2e960b", "duration": 720, "amount": 1.8, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 2592000, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683f9c656e8a8f579a2e960d", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683f9c656e8a8f579a2e960d", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683f9cc90e331b0e018fa6f2", "duration": 720, "amount": 1.8, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"region": "Europe", "resourceUnit": "1-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 2592000, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683f9d7b6e8a8f579a2e960f", "duration": 1, "amount": 0.0025, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"persistStorage": "No", "resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 3600, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683f9d7b6e8a8f579a2e960f", "duration": 1, "amount": 0.0025, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 3600, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683fbc0eff9a6330bb9aa0b1", "duration": 168, "amount": 0.42, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "683febd89a7af9abfdfbafd0", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683febf59a7af9abfdfbafd2", "duration": 720, "amount": 1.8, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 2592000, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683fec21f7498f3f73ef0572", "duration": 720, "amount": 1.8, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 2592000, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "683021d1fa387af8b65e7d5c", "duration": 1, "amount": 0.0025, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 3600, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "684015636c2a6f666e45d3ab", "duration": 2, "amount": 0.005, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "684015636c2a6f666e45d3ab", "duration": 2, "amount": 0.005, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "persistStorage": "No", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "labelHash": ""}, {"_id": "684100695381c50d560fdd62", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "region": "Europe", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "684104b75381c50d560fdd64", "duration": 2, "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"persistStorage": "No", "region": "Europe", "portSpecification": "User Specified Service Port", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68410844a4e5f6b2d4c2dad3", "duration": 3, "amount": 0.0075, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 10800, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "684108c2a4e5f6b2d4c2dad5", "duration": 24, "amount": 0.06, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "persistStorage": "No", "portSpecification": "User Specified Service Port", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 86400, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Container Service", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "68005db6de6d2bb564767797", "duration": 8, "amount": 0.08, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "", "region": "North America", "resourceUnit": "1-Unit-Resource", "portSpecification": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6805e54325c1b0cac778f716", "duration": 8760, "amount": 87.6, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"persistStorage": "", "portSpecification": "", "resourceUnit": "1-Unit-Resource", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680861253c6e611fc5e98029", "duration": 1, "amount": 0.01, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "persistStorage": "", "region": "North America", "portSpecification": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680defe0e0269b7f169d97aa", "duration": 24, "amount": 0.24, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "", "portSpecification": "", "region": "North America", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680df12ae0269b7f169d97ac", "duration": 24, "amount": 0.24, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "", "region": "North America", "resourceUnit": "1-Unit-Resource", "portSpecification": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "680dfc553f4a3b156213930f", "duration": 192, "amount": 3.84, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"region": "North America", "persistStorage": "", "resourceUnit": "2-Unit-Resource", "portSpecification": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "681c11dbd5fe66fa9b86b7f9", "duration": 168, "amount": 1.68, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "persistStorage": "", "portSpecification": "", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "681c1207d5fe66fa9b86b7fb", "duration": 336, "amount": 3.36, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "North America", "persistStorage": "", "portSpecification": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "681dbbc73702feb404b3995b", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "portSpecification": "", "persistStorage": "", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "681dbbf03702feb404b3995f", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"persistStorage": "", "region": "Europe", "portSpecification": "", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682547e04adc165db31acae1", "duration": 168, "amount": 1.68, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"persistStorage": "", "region": "Europe", "portSpecification": "", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "682548304adc165db31acae3", "duration": 168, "amount": 1.68, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServicePending", "serviceOptions": {"region": "Europe", "persistStorage": "", "portSpecification": "", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "labelHash": ""}, {"_id": "6826a954546831c715736384", "duration": 168, "amount": 1.68, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "", "resourceUnit": "1-Unit-Resource", "persistStorage": "", "region": "Europe"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 604800, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "6826a974546831c715736388", "duration": 2, "amount": 0.02, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceInactivated", "serviceOptions": {"portSpecification": "", "persistStorage": "", "resourceUnit": "1-Unit-Resource", "region": "North America"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": 7200, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}, {"_id": "682ae612828846fd42ee50e1", "duration": 720, "amount": 14.4, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": true, "status": "ServiceRunning", "serviceOptions": {"resourceUnit": "2-Unit-Resource", "persistStorage": "", "region": "North America", "portSpecification": ""}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "labelHash": ""}, {"_id": "682ed878d1ed1844aade80e2", "duration": 3, "amount": 0.03, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "serviceID": "67c17e030f86c60edbc5f547", "serviceActivated": false, "status": "ServiceDone", "serviceOptions": {"persistStorage": "", "region": "North America", "portSpecification": "", "resourceUnit": "1-Unit-Resource"}, "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "endAt": **********, "serviceActivateTS": **********, "serviceRunningTS": **********, "serviceAbortTS": 0, "serviceDoneTS": **********, "serviceRefundTS": 0, "service": "Log Host", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "labelHash": ""}]