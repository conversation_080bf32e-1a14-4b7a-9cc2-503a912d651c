# Order Service Count 函数实现文档

## 概述

本文档描述了为 Order Service 添加的 count 函数功能，包括查询优化、分页支持和完整的测试覆盖。

## 实现的功能

### 1. 新增的数据结构字段

在 `OrderQueryParam` 结构体中添加了分页和排序支持：

```rust
pub struct OrderQueryParam {
    // ... 现有字段 ...
    #[serde(rename = "Limit")]
    pub limit: Option<u64>,
    #[serde(rename = "Offset")]
    pub offset: Option<u64>,
    #[serde(rename = "SortDesc")]
    pub sort_desc: Option<bool>,
}
```

### 2. 新增的公共函数

#### 2.1 count_orders_advanced
```rust
#[glue::readonly]
pub fn count_orders_advanced(&self, params_json: String) -> anyhow::Result<String>
```
- **功能**: 使用完整 JSON 参数进行高级订单计数
- **参数**: JSON 格式的 OrderQueryParam
- **返回**: 订单总数的字符串表示
- **索引优化**: 根据查询条件自动选择最优索引

#### 2.2 count_orders
```rust
#[glue::readonly]
pub fn count_orders(
    &self,
    ids: Option<Vec<String>>,
    address: Option<String>,
    recipient: Option<String>,
    service: Option<String>,
    order_type: Option<String>,
    statuses: Option<Vec<String>>,
) -> anyhow::Result<String>
```
- **功能**: 使用简化参数进行订单计数
- **参数**: 常用的过滤条件参数
- **返回**: 订单总数的字符串表示
- **实现**: 内部调用 count_orders_advanced

### 3. 增强的查询函数

#### 3.1 query_orders (增强版)
- **新增功能**: 分页支持 (limit, offset)
- **新增功能**: 排序支持 (sort_desc)
- **优化**: 使用索引进行高效查询
- **默认限制**: 100 条记录 (可配置)

### 4. 辅助函数

#### 4.1 count_with_composite_index
- **功能**: 使用复合索引进行高效计数
- **参数**: 索引名称、键前缀、查询参数
- **优化**: 避免全表扫描

#### 4.2 count_with_created_at_index
- **功能**: 使用 created_at 索引进行计数
- **用途**: 当没有特定索引可用时的回退方案

#### 4.3 query_orders_with_index
- **功能**: 使用索引进行分页查询
- **支持**: 多种索引策略和排序选项

#### 4.4 query_with_order_composite_index
- **功能**: 使用复合索引进行排序查询
- **支持**: 升序/降序排序和分页

#### 4.5 query_with_order_created_at_index
- **功能**: 使用 created_at 索引进行排序查询
- **用途**: 默认的时间排序查询

## 索引使用策略

### 优先级顺序 (从高到低)

1. **批量 ID 查询**: 直接通过 ID 获取
2. **复合索引**: address + status
3. **单字段索引**: 
   - order_address
   - order_recipient  
   - order_provider
   - order_type
   - order_status
4. **时间索引**: order_created_at (回退方案)

### 索引映射

| 查询条件 | 使用的索引 | 索引格式 |
|---------|-----------|----------|
| address + statuses | order_address_status | `{address}-{status}-{created_at}` |
| address | order_address | `{address}-{created_at}` |
| recipient | order_recipient | `{recipient}-{created_at}` |
| service (provider) | order_provider | `{provider}-{created_at}` |
| order_type | order_type | `{order_type}-{created_at}` |
| statuses | order_status | `{status}-{created_at}` |
| 无特定条件 | order_created_at | `{created_at}` |

## 分页和排序

### 分页参数
- **limit**: 每页记录数 (默认: 100)
- **offset**: 跳过的记录数 (默认: 0)

### 排序参数
- **sort_desc**: 是否降序排列 (默认: false)
- **排序字段**: created_at (按创建时间排序)

### 排序实现
- **升序**: 从旧到新 (created_at 小到大)
- **降序**: 从新到旧 (created_at 大到小)
- **索引优化**: 利用索引的自然排序避免额外排序开销

## 测试覆盖

### 单元测试

#### test_count_orders
- 测试所有简化参数的计数功能
- 覆盖各种过滤条件组合
- 验证计数准确性

#### test_count_orders_advanced  
- 测试高级 JSON 参数的计数功能
- 覆盖复杂查询条件
- 验证时间范围过滤

#### test_order_pagination_and_sorting
- 测试分页功能 (limit, offset)
- 测试排序功能 (升序/降序)
- 验证页面间无重叠
- 验证排序正确性

### 测试场景

1. **基础计数**: 无过滤条件的总数统计
2. **单条件过滤**: 按地址、接收者、服务商等单一条件
3. **多条件过滤**: 地址+状态等复合条件
4. **时间范围**: TSStart 和 TSEnd 时间过滤
5. **ID 批量**: 指定 ID 列表的精确查询
6. **分页测试**: 不同页面大小和偏移量
7. **排序测试**: 升序和降序排列验证

## 性能优化

### 索引选择算法
1. 优先使用最具选择性的索引
2. 复合索引优于单字段索引
3. 避免全表扫描
4. 利用索引内置排序

### 查询优化
- **早期过滤**: 在索引层面进行条件过滤
- **分页优化**: 使用 offset 和 limit 减少内存使用
- **排序优化**: 利用索引自然顺序避免额外排序

### 内存管理
- **流式处理**: 逐条处理而非批量加载
- **限制结果集**: 默认限制防止内存溢出
- **及时释放**: 不保留不必要的中间结果

## 兼容性

### 向后兼容
- 现有 query_orders 函数保持兼容
- 新增参数为可选参数
- 默认行为保持不变

### API 一致性
- 参数命名与 user service 保持一致
- 返回格式与现有函数保持一致
- 错误处理机制统一

## 使用示例

### 简单计数
```rust
// 计数所有订单
let count = count_orders(None, None, None, None, None, None)?;

// 按地址计数
let count = count_orders(None, Some("0xaddr".to_string()), None, None, None, None)?;

// 按状态计数
let count = count_orders(None, None, None, None, None, Some(vec!["pending".to_string()]))?;
```

### 高级计数
```json
{
  "Address": "0xtest_addr",
  "Statuses": ["pending", "paid"],
  "TSStart": 1640995200000000000,
  "TSEnd": 1641081600000000000
}
```

### 分页查询
```json
{
  "Address": "0xtest_addr",
  "Limit": 20,
  "Offset": 40,
  "SortDesc": true
}
```

## 总结

本实现为 Order Service 提供了完整的计数功能，包括：

✅ **功能完整**: 简化和高级两种 API  
✅ **性能优化**: 智能索引选择和查询优化  
✅ **分页支持**: 完整的分页和排序功能  
✅ **测试覆盖**: 全面的单元测试和边界测试  
✅ **向后兼容**: 保持现有 API 的兼容性  
✅ **代码质量**: 遵循项目的编码规范和架构模式  

该实现与现有的 user service count 函数保持一致的设计模式，确保了代码的可维护性和一致性。
