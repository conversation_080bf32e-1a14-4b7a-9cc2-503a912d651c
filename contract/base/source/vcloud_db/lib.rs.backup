#[glue::contract]
mod vcloud_db {
    use serde::{Deserialize, Serialize};
    use std::collections::HashMap;

    // User Service data structures
    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct UserService {
        #[serde(rename = "_id")]
        pub _id: String,
        #[serde(default)]
        pub duration: i64,
        #[serde(default)]
        pub amount: f64,
        #[serde(rename = "publicKey", default)]
        pub public_key: String,
        #[serde(default)]
        pub provider: String,
        #[serde(rename = "providerAddress", default)]
        pub provider_address: String,
        #[serde(default)]
        pub address: String,
        #[serde(rename = "serviceID", default)]
        pub service_id: String,
        #[serde(rename = "serviceActivated", default)]
        pub service_activated: bool,
        #[serde(default)]
        pub status: String,  // Changed from ServiceStatus enum to String
        #[serde(rename = "serviceOptions", default)]
        pub service_options: HashMap<String, String>,
        #[serde(rename = "createdAt", default)]
        pub created_at: i64,
        #[serde(rename = "updatedAt", default)]
        pub updated_at: i64,
        #[serde(rename = "deletedAt", default)]
        pub deleted_at: i64,
        // New fields added
        #[serde(rename = "endAt", default)]
        pub end_at: i64,
        #[serde(rename = "serviceActivateTS", default)]
        pub service_activate_ts: i64,
        #[serde(rename = "serviceRunningTS", default)]
        pub service_running_ts: i64,
        #[serde(rename = "serviceAbortTS", default)]
        pub service_abort_ts: i64,
        #[serde(rename = "serviceDoneTS", default)]
        pub service_done_ts: i64,
        #[serde(rename = "serviceRefundTS", default)]
        pub service_refund_ts: i64,
        #[serde(default)]
        pub service: String,
        #[serde(rename = "createdAddr", default)]
        pub created_addr: String,
        #[serde(rename = "labelHash", default)]
        pub label_hash: String,
    }

    // Order data structures
    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct SingleServiceBriefInfo {
        #[serde(rename = "UserServiceID", default)]
        pub user_service_id: String,
        #[serde(rename = "Duration", default)]
        pub duration: i64,
        #[serde(rename = "Amount", default)]
        pub amount: f64,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct Order {
        #[serde(rename = "ID")]
        pub id: String,
        #[serde(rename = "CreatedAt", default)]
        pub created_at: i64,
        #[serde(rename = "UpdatedAt", default)]
        pub updated_at: i64,
        #[serde(rename = "DeletedAt", default)]
        pub deleted_at: i64,
        #[serde(rename = "Type", default)]
        pub order_type: String,
        #[serde(rename = "Amount", default)]
        pub amount: f64,
        #[serde(rename = "AmountPaid", default)]
        pub amount_paid: f64,
        #[serde(rename = "Provider", default)]
        pub provider: String,
        #[serde(rename = "Address", default)]
        pub address: String,
        #[serde(rename = "Recipient", default)]
        pub recipient: String,
        #[serde(rename = "Status", default)]
        pub status: String,
        #[serde(rename = "LastPaymentTS", default)]
        pub last_payment_ts: i64,
        #[serde(rename = "PaidTS", default)]
        pub paid_ts: i64,
        #[serde(rename = "FiledTS", default)]
        pub filed_ts: i64,
        #[serde(rename = "PublicKey", default)]
        pub public_key: String,
        #[serde(rename = "UserServiceIDs", default)]
        pub user_service_ids: Vec<String>,
        #[serde(rename = "Items", default)]
        pub items: Vec<SingleServiceBriefInfo>,
    }

    /// Query parameters for filtering user services
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct QueryParams {
        #[serde(rename = "serviceID")]
        pub service_id: Option<String>,
        pub address: Option<String>,
        pub provider: Option<String>,
        #[serde(rename = "providerAddress")]
        pub provider_address: Option<String>,  // New field for provider_address filtering
        pub status: Option<String>,  // Changed from ServiceStatus to String
        #[serde(rename = "serviceActivated")]
        pub service_activated: Option<bool>,  // New field for service_activated filtering
        pub ids: Option<Vec<String>>,  // New field for batch ID queries
        #[serde(rename = "createdAtStart")]
        pub created_at_start: Option<i64>,
        #[serde(rename = "createdAtEnd")]
        pub created_at_end: Option<i64>,
        #[serde(rename = "updatedAtStart")]
        pub updated_at_start: Option<i64>,
        #[serde(rename = "updatedAtEnd")]
        pub updated_at_end: Option<i64>,
        pub offset: Option<u64>,
        pub limit: Option<u64>,
        #[serde(rename = "sortBy")]
        pub sort_by: Option<String>,
        #[serde(rename = "sortDesc")]
        pub sort_desc: Option<bool>,
    }

    /// Query parameters for filtering orders
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct OrderQueryParam {
        #[serde(rename = "IDs", default)]
        pub ids: Vec<String>,
        #[serde(rename = "ServiceID", default)]
        pub service_id: String,
        #[serde(rename = "Service", default)]
        pub service: String,
        #[serde(rename = "Address", default)]
        pub address: String,
        #[serde(rename = "Recipient", default)]
        pub recipient: String,
        #[serde(rename = "Type", default)]
        pub order_type: String,
        #[serde(rename = "Statuses", default)]
        pub statuses: Vec<String>,
        #[serde(rename = "TSStart")]
        pub ts_start: Option<i64>,
        #[serde(rename = "TSEnd")]
        pub ts_end: Option<i64>,
    }

    /// Distinct parameters for order queries
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct OrderDistinctParam {
        #[serde(rename = "DistinctField", default)]
        pub distinct_field: String,
        #[serde(rename = "Address", default)]
        pub address: String,
        #[serde(rename = "Statuses", default)]
        pub statuses: Vec<String>,
    }

    /// Batch operation result
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct BatchResult {
        pub created: u64,
        pub updated: u64,
        pub deleted: u64,
        pub errors: Vec<String>,
    }

    #[glue::storage]
    pub struct VCloudDB {
        pub user_services: glue::collections::Map<String, UserService>,
        pub orders: glue::collections::Map<String, Order>,
    }

    impl VCloudDB {
        #[glue::bind_index]
        pub fn bind_index(&mut self) {
            // User Service indexes
            // Index by provider for efficient provider-based queries
            self.user_services.bind_index(
                "user_services_provider",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.provider, v.created_at)]
                }),
            );

            // Index by address for efficient address-based queries
            self.user_services.bind_index(
                "user_services_address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.address, v.created_at)]
                }),
            );

            // Index by provider_address for efficient provider_address-based queries
            self.user_services.bind_index(
                "user_services_provider_address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.provider_address, v.created_at)]
                }),
            );

            // Index by status for efficient status-based queries
            self.user_services.bind_index(
                "user_services_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.status, v.created_at)]
                }),
            );

            // Index by service_id for efficient service_id-based queries
            self.user_services.bind_index(
                "user_services_service_id",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.service_id, v.created_at)]
                }),
            );

            // Index by service_activated for efficient service_activated-based queries
            self.user_services.bind_index(
                "user_services_service_activated",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.service_activated, v.created_at)]
                }),
            );

            // Composite index: address + status for efficient combined queries
            self.user_services.bind_index(
                "user_services_address_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.address, v.status, v.created_at)]
                }),
            );

            // Composite index: provider + status for efficient combined queries
            self.user_services.bind_index(
                "user_services_provider_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.provider, v.status, v.created_at)]
                }),
            );

            // Composite index: address + service_activated for IDs + address + service_activated queries
            self.user_services.bind_index(
                "user_services_address_service_activated",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.address, v.service_activated, v.created_at)]
                }),
            );

            // Index by created_at for time-based queries
            self.user_services.bind_index(
                "user_services_created_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.created_at)]
                }),
            );

            // Index by updated_at for time-based queries
            self.user_services.bind_index(
                "user_services_updated_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.updated_at)]
                }),
            );

            // Order indexes
            // Index by address for efficient address-based queries
            self.orders.bind_index(
                "order_address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.address, v.created_at)]
                }),
            );

            // Index by recipient for efficient recipient-based queries
            self.orders.bind_index(
                "order_recipient",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.recipient, v.created_at)]
                }),
            );

            // Index by provider for efficient provider-based queries
            self.orders.bind_index(
                "order_provider",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.provider, v.created_at)]
                }),
            );

            // Index by status for efficient status-based queries
            self.orders.bind_index(
                "order_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.status, v.created_at)]
                }),
            );

            // Index by order_type for efficient type-based queries
            self.orders.bind_index(
                "order_type",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.order_type, v.created_at)]
                }),
            );

            // Index by paid_ts for time-based queries
            self.orders.bind_index(
                "order_paid_ts",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.paid_ts)]
                }),
            );

            // Index by filed_ts for time-based queries
            self.orders.bind_index(
                "order_filed_ts",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.filed_ts)]
                }),
            );

            // Index by created_at for time-based queries
            self.orders.bind_index(
                "order_created_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.created_at)]
                }),
            );

            // Composite index: address + status for efficient combined queries
            self.orders.bind_index(
                "order_address_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.address, v.status, v.created_at)]
                }),
            );
        }

        #[glue::constructor]
        pub fn new() -> Self {
            let mut ret = Self {
                user_services: glue::collections::Map::new(),
                orders: glue::collections::Map::new(),
            };
            ret.bind_index();
            ret
        }

        /// Get current timestamp - returns actual Unix timestamp
        fn get_current_timestamp(&self) -> i64 {
            // Get current Unix timestamp in seconds
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as i64
        }

        /// Apply timestamp handling logic based on input values
        fn apply_timestamp_handling(&self, service: &mut UserService) {
            let current_time = self.get_current_timestamp();

            // For created_at: if input is 0, set to current timestamp
            if service.created_at == 0 {
                service.created_at = current_time;
            }

            // For updated_at: if input is 0, set to current timestamp
            if service.updated_at == 0 {
                service.updated_at = current_time;
            }

            // For deleted_at: if input is 0, keep it as 0 (not deleted)
            // No action needed as 0 means not deleted
        }

        /// Apply timestamp handling logic for orders
        fn apply_order_timestamp_handling(&self, order: &mut Order) {
            let current_time = self.get_current_timestamp();

            // For created_at: if input is 0, set to current timestamp
            if order.created_at == 0 {
                order.created_at = current_time;
            }

            // For updated_at: if input is 0, set to current timestamp
            if order.updated_at == 0 {
                order.updated_at = current_time;
            }

            // For deleted_at: if input is 0, keep it as 0 (not deleted)
            // No action needed as 0 means not deleted
        }

        /// Create a new user service from JSON string
        #[glue::atomic]
        pub fn create_user_service(&mut self, service_json: String) -> anyhow::Result<String> {
            
            let mut service: UserService = serde_json::from_str(&service_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse service JSON: {}", e))?;
            // Validate required fields
            if service._id.is_empty() {
                return Err(anyhow::anyhow!("Service ID cannot be empty"));
            }

            if self.user_services.contains(&service._id) {
                return Err(anyhow::anyhow!("User service with this ID already exists"));
            }

            // Apply timestamp handling logic
            self.apply_timestamp_handling(&mut service);

            // Ensure deleted_at is 0 for new services
            service.deleted_at = 0;

            self.user_services.insert(&service._id, &service);
            Ok(service._id)
        }

        /// Batch create user services from JSON string
        #[glue::atomic]
        pub fn batch_create_user_services(&mut self, services_json: String) -> anyhow::Result<String> {
            self.create_many_internal(services_json)
        }

        /// Create multiple user services in a single transaction
        ///
        /// This function enables efficient batch creation of UserService records with:
        /// - Reduced gas costs through single transaction processing
        /// - Graceful handling of partial failures with detailed error reporting
        /// - Validation of each service object before processing
        /// - Duplicate ID detection both within batch and against existing services
        /// - Atomic transaction behavior for critical operations
        ///
        /// # Parameters
        /// * `services_json` - JSON string containing array of UserService objects
        ///   Example: `[{"id": "service1", "amount": 100.0, ...}, {"id": "service2", "amount": 200.0, ...}]`
        ///
        /// # Returns
        /// JSON string containing BatchResult with:
        /// - `created`: Number of successfully created services
        /// - `updated`: Number of updated services (always 0 for create operations)
        /// - `deleted`: Number of deleted services (always 0 for create operations)
        /// - `errors`: Array of error messages for failed service creations
        ///
        /// # Error Handling
        /// - JSON parsing errors return descriptive error messages
        /// - Individual service validation failures are collected and reported
        /// - Duplicate ID conflicts are handled gracefully without stopping the batch
        /// - Critical errors may cause entire batch rollback due to atomic transaction
        #[glue::atomic]
        pub fn create_many(&mut self, services_json: String) -> anyhow::Result<String> {
            self.create_many_internal(services_json)
        }

        /// Internal implementation for batch service creation
        /// Shared by both batch_create_user_services and create_many functions
        fn create_many_internal(&mut self, services_json: String) -> anyhow::Result<String> {
            // Parse JSON input with detailed error reporting
            let services: Vec<UserService> = serde_json::from_str(&services_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse services JSON: {}. Expected format: [{{\"id\": \"service1\", \"amount\": 100.0, ...}}, ...]", e))?;

            // Validate input is not empty
            if services.is_empty() {
                return Err(anyhow::anyhow!("Services array cannot be empty"));
            }

            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            // Track IDs within this batch to detect duplicates
            let mut batch_ids = std::collections::HashSet::new();

            for mut service in services {
                // Validate required fields
                if service._id.is_empty() {
                    result.errors.push("Service ID cannot be empty".to_string());
                    continue;
                }

                // Check for duplicate IDs within the batch
                if !batch_ids.insert(service._id.clone()) {
                    result.errors.push(format!("Duplicate service ID '{}' found within batch", service._id));
                    continue;
                }

                // Check for existing service with same ID
                if self.user_services.contains(&service._id) {
                    result.errors.push(format!("Service with ID '{}' already exists in database", service._id));
                    continue;
                }

                // Validate business logic constraints
                if service.amount < 0.0 {
                    result.errors.push(format!("Service '{}' has invalid negative amount: {}", service._id, service.amount));
                    continue;
                }

                if service.duration < 0 {
                    result.errors.push(format!("Service '{}' has invalid negative duration: {}", service._id, service.duration));
                    continue;
                }

                // Apply timestamp handling logic
                self.apply_timestamp_handling(&mut service);

                // Ensure deleted_at is 0 for new services
                service.deleted_at = 0;

                // Insert the service
                self.user_services.insert(&service._id, &service);
                result.created += 1;
            }

            Ok(serde_json::to_string(&result)?)
        }

        /// Get a single user service by ID
        #[glue::readonly]
        pub fn get_user_service(&self, id: String) -> anyhow::Result<String> {
            let service = self.user_services.get(&id);
            match service {
                Some(service) => Ok(serde_json::to_string(&service)?),
                None => Err(anyhow::anyhow!("User service not found")),
            }
        }

        /// Update an existing user service from JSON string
        #[glue::atomic]
        pub fn update_user_service(&mut self, service_json: String) -> anyhow::Result<()> {
            let mut service: UserService = serde_json::from_str(&service_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse service JSON: {}", e))?;

            // Validate required fields
            if service._id.is_empty() {
                return Err(anyhow::anyhow!("Service ID cannot be empty"));
            }

            if !self.user_services.contains(&service._id) {
                return Err(anyhow::anyhow!("User service not found"));
            }

            // Apply timestamp handling logic for updates
            if service.updated_at == 0 {
                service.updated_at = self.get_current_timestamp();
            }

            self.user_services.insert(&service._id, &service);
            Ok(())
        }

        /// Batch update user services from JSON string (specifically for runtime duration updates)
        #[glue::atomic]
        pub fn batch_update_user_services(&mut self, updates_json: String) -> anyhow::Result<String> {
            let updates: Vec<(String, i64)> = serde_json::from_str(&updates_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse updates JSON: {}", e))?;

            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            let current_time = self.get_current_timestamp();

            for (service_id, new_duration) in updates {
                match self.user_services.get(&service_id) {
                    Some(mut service) => {
                        service.duration = new_duration;
                        service.updated_at = current_time;
                        self.user_services.insert(&service_id, &service);
                        result.updated += 1;
                    }
                    None => {
                        result.errors.push(format!("Service with ID {} not found", service_id));
                    }
                }
            }

            Ok(serde_json::to_string(&result)?)
        }

        /// Delete a user service (soft delete by setting deleted_at)
        #[glue::atomic]
        pub fn delete_user_service(&mut self, id: String) -> anyhow::Result<()> {
            match self.user_services.get(&id) {
                Some(mut service) => {
                    service.deleted_at = self.get_current_timestamp();
                    service.updated_at = service.deleted_at;
                    self.user_services.insert(&id, &service);
                    Ok(())
                }
                None => Err(anyhow::anyhow!("User service not found")),
            }
        }

        /// Batch upsert user services from JSON string (insert, update, or delete based on record state)
        #[glue::atomic]
        pub fn batch_upsert_user_services(&mut self, services_json: String) -> anyhow::Result<String> {
            let services: Vec<UserService> = serde_json::from_str(&services_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse services JSON: {}", e))?;

            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            let current_time = self.get_current_timestamp();

            for mut service in services {
                // If deleted_at is set and > 0, this is a delete operation
                if service.deleted_at > 0 {
                    match self.user_services.get(&service._id) {
                        Some(mut existing_service) => {
                            existing_service.deleted_at = current_time;
                            existing_service.updated_at = current_time;
                            self.user_services.insert(&service._id, &existing_service);
                            result.deleted += 1;
                        }
                        None => {
                            result.errors.push(format!("Service with ID {} not found for deletion", service._id));
                        }
                    }
                } else if self.user_services.contains(&service._id) {
                    // Update existing service - apply timestamp handling
                    if service.updated_at == 0 {
                        service.updated_at = current_time;
                    }
                    self.user_services.insert(&service._id, &service);
                    result.updated += 1;
                } else {
                    // Create new service - apply timestamp handling
                    self.apply_timestamp_handling(&mut service);
                    service.deleted_at = 0;
                    self.user_services.insert(&service._id, &service);
                    result.created += 1;
                }
            }

            Ok(serde_json::to_string(&result)?)
        }

        /// Enhanced query user services with comprehensive filtering, pagination, and sorting
        #[glue::readonly]
        pub fn query_user_services_advanced(&self, params_json: String) -> anyhow::Result<String> {
            let params: QueryParams = serde_json::from_str(&params_json)?;

            let mut services = Vec::new();
            let mut count = 0u64;
            let limit = params.limit.unwrap_or(10);
            let offset = params.offset.unwrap_or(0);

            // Determine the most efficient index to use based on provided filters
            // Priority: Handle batch ID queries first, then use composite indexes when multiple filters are present
            if let Some(ref ids) = params.ids {
                // Batch ID query - fetch services by IDs directly
                for id in ids {
                    if let Some(service) = self.user_services.get(id) {
                        if self.matches_filters(&service, &params) {
                            if count < offset {
                                count += 1;
                                continue;
                            }
                            if services.len() >= limit as usize {
                                break;
                            }
                            services.push(service);
                            count += 1;
                        }
                    }
                }
            } else if let (Some(ref address), Some(ref status)) = (&params.address, &params.status) {
                // Use composite address_status index with efficient sorting
                let key_prefix = format!("{}-{}", address, status);
                self.query_with_composite_index_sorting("user_services_address_status", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let (Some(ref address), Some(service_activated)) = (&params.address, &params.service_activated) {
                // Use composite address_service_activated index with efficient sorting
                let key_prefix = format!("{}-{}", address, service_activated);
                self.query_with_composite_index_sorting("user_services_address_service_activated", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let (Some(ref provider), Some(ref status)) = (&params.provider, &params.status) {
                // Use composite provider_status index with efficient sorting
                let key_prefix = format!("{}-{}", provider, status);
                self.query_with_composite_index_sorting("user_services_provider_status", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref address) = params.address {
                // Use address index with efficient sorting
                self.query_with_composite_index_sorting("user_services_address", address, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref provider) = params.provider {
                // Use provider index with efficient sorting
                self.query_with_composite_index_sorting("user_services_provider", provider, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref provider_address) = params.provider_address {
                // Use provider_address index with efficient sorting
                self.query_with_composite_index_sorting("user_services_provider_address", provider_address, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref status) = params.status {
                // Use status index with efficient sorting
                self.query_with_composite_index_sorting("user_services_status", status, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(service_activated) = params.service_activated {
                // Use service_activated index with efficient sorting
                let key_prefix = service_activated.to_string();
                self.query_with_composite_index_sorting("user_services_service_activated", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref service_id) = params.service_id {
                // Use service_id index with efficient sorting
                self.query_with_composite_index_sorting("user_services_service_id", service_id, &params, &mut services, &mut count, limit, offset)?;
            } else {
                // No specific index, use created_at index with efficient sorting
                self.query_with_created_at_sorting(&params, &mut services, &mut count, limit, offset)?;
            }
            // Note: Sorting is now handled efficiently during iteration, no post-processing needed
            Ok(serde_json::to_string(&services)?)
        }

        /// Count user services with advanced filtering
        #[glue::readonly]
        pub fn count_user_services_advanced(&self, params_json: String) -> anyhow::Result<String> {
            
            // let params: QueryParams = serde_json::from_str(&params_json)?;
            // let mut count = 0u64;

            // // Use the same filtering logic as query but only count matches
            // if let Some(ref ids) = params.ids {
            //     // Batch ID query - count services by IDs directly
            //     for id in ids {
            //         if let Some(service) = self.user_services.get(id) {
            //             if self.matches_filters(&service, &params) {
            //                 count += 1;
            //             }
            //         }
            //     }
            // } else {
            //     // Use index-based counting for other queries
            //     let mut iter = self.user_services.index("created_at").iter(
            //         false,
            //         &format!("{:0>19}", 0),
            //         &format!("{:9>19}", i64::MAX)
            //     );
            //     while iter.next() {
            //         let service = iter.value()?;
            //         if self.matches_filters(&service, &params) {
            //             count += 1;
            //         }
            //     }
            // }

            let params: QueryParams = serde_json::from_str(&params_json)?;

            let mut services = Vec::new();
            let mut count = 0u64;
            let limit = params.limit.unwrap_or(10);
            let offset = params.offset.unwrap_or(0);

            // Determine the most efficient index to use based on provided filters
            // Priority: Handle batch ID queries first, then use composite indexes when multiple filters are present
            if let Some(ref ids) = params.ids {
                // Batch ID query - fetch services by IDs directly
                for id in ids {
                    if let Some(service) = self.user_services.get(id) {
                        if self.matches_filters(&service, &params) {
                            if count < offset {
                                count += 1;
                                continue;
                            }
                            if services.len() >= limit as usize {
                                break;
                            }
                            services.push(service);
                            count += 1;
                        }
                    }
                }
            } else if let (Some(ref address), Some(ref status)) = (&params.address, &params.status) {
                // Use composite address_status index with efficient sorting
                let key_prefix = format!("{}-{}", address, status);
                self.query_with_composite_index_sorting("user_services_address_status", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let (Some(ref address), Some(service_activated)) = (&params.address, &params.service_activated) {
                // Use composite address_service_activated index with efficient sorting
                let key_prefix = format!("{}-{}", address, service_activated);
                self.query_with_composite_index_sorting("user_services_address_service_activated", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let (Some(ref provider), Some(ref status)) = (&params.provider, &params.status) {
                // Use composite provider_status index with efficient sorting
                let key_prefix = format!("{}-{}", provider, status);
                self.query_with_composite_index_sorting("user_services_provider_status", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref address) = params.address {
                // Use address index with efficient sorting
                self.query_with_composite_index_sorting("user_services_address", address, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref provider) = params.provider {
                // Use provider index with efficient sorting
                self.query_with_composite_index_sorting("user_services_provider", provider, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref provider_address) = params.provider_address {
                // Use provider_address index with efficient sorting
                self.query_with_composite_index_sorting("user_services_provider_address", provider_address, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref status) = params.status {
                // Use status index with efficient sorting
                self.query_with_composite_index_sorting("user_services_status", status, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(service_activated) = params.service_activated {
                // Use service_activated index with efficient sorting
                let key_prefix = service_activated.to_string();
                self.query_with_composite_index_sorting("user_services_service_activated", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref service_id) = params.service_id {
                // Use service_id index with efficient sorting
                self.query_with_composite_index_sorting("user_services_service_id", service_id, &params, &mut services, &mut count, limit, offset)?;
            } else {
                // No specific index, use created_at index with efficient sorting
                self.query_with_created_at_sorting(&params, &mut services, &mut count, limit, offset)?;
            }
            // Note: Sorting is now handled efficiently during iteration, no post-processing needed
            Ok(count.to_string())
        }

        /// Count user services with simplified parameters
        #[glue::readonly]
        pub fn count_user_services(
            &self,
            ids: Option<Vec<String>>,
            address: Option<String>,
            service_activated: Option<bool>,
            status: Option<String>,
            provider_address: Option<String>,
        ) -> anyhow::Result<String> {
            let params = QueryParams {
                service_id: None,
                address,
                provider: None,
                provider_address,
                status,
                service_activated,
                ids,
                created_at_start: None,
                created_at_end: None,
                updated_at_start: None,
                updated_at_end: None,
                offset: None,
                limit: None,
                sort_by: None,
                sort_desc: None,
            };

            let params_json = serde_json::to_string(&params)?;
            self.count_user_services_advanced(params_json)
        }

        /// Query user services with simplified parameters
        #[glue::readonly]
        pub fn query_user_services(
            &self,
            ids: Option<Vec<String>>,
            address: Option<String>,
            service_activated: Option<bool>,
            status: Option<String>,
            provider_address: Option<String>,
            limit: u64,
            offset: u64,
            sort_desc: Option<bool>
        ) -> anyhow::Result<String> {
            let params = QueryParams {
                service_id: None,
                address,
                provider: None,
                provider_address,
                status,
                service_activated,
                ids,
                created_at_start: None,
                created_at_end: None,
                updated_at_start: None,
                updated_at_end: None,
                offset: Some(offset),
                limit: Some(limit),
                sort_by: Some("created_at".to_string()),
                sort_desc,
            };

            let params_json = serde_json::to_string(&params)?;
            self.query_user_services_advanced(params_json)
        }

        /// Helper function to check if a service matches the given filters
        fn matches_filters(&self, service: &UserService, params: &QueryParams) -> bool {
            // Skip deleted services unless specifically querying for them
            if service.deleted_at > 0 {
                return false;
            }

            // Check IDs filter (batch ID query)
            if let Some(ref ids) = params.ids {
                if !ids.contains(&service._id) {
                    return false;
                }
            }

            // Check service_id filter
            if let Some(ref service_id) = params.service_id {
                if service.service_id != *service_id {
                    return false;
                }
            }

            // Check address filter
            if let Some(ref address) = params.address {
                if service.address != *address {
                    return false;
                }
            }

            // Check provider filter
            if let Some(ref provider) = params.provider {
                if service.provider != *provider {
                    return false;
                }
            }

            // Check provider_address filter
            if let Some(ref provider_address) = params.provider_address {
                if service.provider_address != *provider_address {
                    return false;
                }
            }

            // Check status filter
            if let Some(ref status) = params.status {
                if service.status != *status {
                    return false;
                }
            }

            // Check service_activated filter
            if let Some(service_activated) = params.service_activated {
                if service.service_activated != service_activated {
                    return false;
                }
            }

            // Check created_at time range
            if let Some(start) = params.created_at_start {
                if service.created_at < start {
                    return false;
                }
            }
            if let Some(end) = params.created_at_end {
                if service.created_at > end {
                    return false;
                }
            }

            // Check updated_at time range
            if let Some(start) = params.updated_at_start {
                if service.updated_at < start {
                    return false;
                }
            }
            if let Some(end) = params.updated_at_end {
                if service.updated_at > end {
                    return false;
                }
            }

            true
        }

        /// Efficient query using created_at index with built-in sorting
        fn query_with_created_at_sorting(
            &self,
            params: &QueryParams,
            services: &mut Vec<UserService>,
            count: &mut u64,
            limit: u64,
            offset: u64,
        ) -> anyhow::Result<()> {
            // Determine sort order from params
            let sort_desc = params.sort_desc.unwrap_or(false);

            // Set up iteration range based on sort order
            let (start_key, end_key, reverse) = if sort_desc {
                // Descending: newest first (reverse iteration from max to min)
                (format!("{:9>19}", i64::MAX), format!("{:0>19}", 0), true)
            } else {
                // Ascending: oldest first (forward iteration from min to max)
                (format!("{:0>19}", 0), format!("{:9>19}", i64::MAX), false)
            };

            let mut iter = self.user_services.index("user_services_created_at").iter(reverse, &start_key, &end_key);
            while iter.next() {
                println!("iter.key:{:?}",iter.key());
                let service = iter.value()?;
                if self.matches_filters(&service, &params) {
                    if *count < offset {
                        *count += 1;
                        continue;
                    }
                    if services.len() >= limit as usize {
                        break;
                    }
                    services.push(service);
                    *count += 1;
                }
            }

            Ok(())
        }

        /// Enhanced query with efficient index-based sorting for composite indexes
        fn query_with_composite_index_sorting(
            &self,
            index_name: &str,
            key_prefix: &str,
            params: &QueryParams,
            services: &mut Vec<UserService>,
            count: &mut u64,
            limit: u64,
            offset: u64,
        ) -> anyhow::Result<()> {
            // Determine sort order from params
            let sort_desc = params.sort_desc.unwrap_or(false);

            // Composite indexes already include created_at in the key, so we can use reverse iteration
            let (start_key, end_key, reverse) = if sort_desc {
                // Descending: newest first
                (format!("{}-{:9>19}", key_prefix, i64::MAX), format!("{}-{:0>19}", key_prefix, 0), true)
            } else {
                // Ascending: oldest first
                (format!("{}-{:0>19}", key_prefix, 0), format!("{}-{:9>19}", key_prefix, i64::MAX), false)
            };
            println!("index_name:{:?}",index_name);
            println!("start_key:{:?}",start_key);
            println!("end_key:{:?}",end_key);
            let mut iter = self.user_services.index(index_name).iter(reverse, &start_key, &end_key);
            while iter.next() {
                let service = iter.value()?;
                println!("iter.key:{:?}",iter.key());
                if self.matches_filters(&service, &params) {
                    if *count < offset {
                        *count += 1;
                        continue;
                    }
                    if services.len() >= limit as usize {
                        break;
                    }
                    services.push(service);
                    *count += 1;
                }
            }
            

            Ok(())
        }

        // Order functions
        /// Create a new order from JSON string
        #[glue::atomic]
        pub fn create_order(&mut self, order_json: String) -> anyhow::Result<String> {
            let mut order: Order = serde_json::from_str(&order_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse order JSON: {}", e))?;

            // Validate required fields
            if order.id.is_empty() {
                return Err(anyhow::anyhow!("Order ID cannot be empty"));
            }

            if self.orders.contains(&order.id) {
                return Err(anyhow::anyhow!("Order with this ID already exists"));
            }

            // Apply timestamp handling logic
            self.apply_order_timestamp_handling(&mut order);

            // Ensure deleted_at is 0 for new orders
            order.deleted_at = 0;

            self.orders.insert(&order.id, &order);
            Ok(order.id)
        }

        /// Get a single order by ID
        #[glue::readonly]
        pub fn get_order(&self, id: String) -> anyhow::Result<String> {
            let order = self.orders.get(&id);
            match order {
                Some(order) => Ok(serde_json::to_string(&order)?),
                None => Err(anyhow::anyhow!("Order not found")),
            }
        }

        /// Update an existing order from JSON string
        #[glue::atomic]
        pub fn update_order(&mut self, order_json: String) -> anyhow::Result<()> {
            let mut order: Order = serde_json::from_str(&order_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse order JSON: {}", e))?;

            // Validate required fields
            if order.id.is_empty() {
                return Err(anyhow::anyhow!("Order ID cannot be empty"));
            }

            if !self.orders.contains(&order.id) {
                return Err(anyhow::anyhow!("Order not found"));
            }

            // Apply timestamp handling logic for updates
            if order.updated_at == 0 {
                order.updated_at = self.get_current_timestamp();
            }

            self.orders.insert(&order.id, &order);
            Ok(())
        }

        /// Helper function to check if an order matches the given filters
        fn matches_order_filters(&self, order: &Order, params: &OrderQueryParam) -> bool {
            // Skip deleted orders unless specifically querying for them
            if order.deleted_at > 0 {
                return false;
            }

            // Check IDs filter (batch ID query)
            if !params.ids.is_empty() {
                if !params.ids.contains(&order.id) {
                    return false;
                }
            }

            // Check address filter
            if !params.address.is_empty() {
                if order.address != params.address {
                    return false;
                }
            }

            // Check recipient filter
            if !params.recipient.is_empty() {
                if order.recipient != params.recipient {
                    return false;
                }
            }

            // Check provider filter (using service field for provider matching)
            if !params.service.is_empty() {
                if order.provider != params.service {
                    return false;
                }
            }

            // Check order_type filter
            if !params.order_type.is_empty() {
                if order.order_type != params.order_type {
                    return false;
                }
            }

            // Check statuses filter
            if !params.statuses.is_empty() {
                if !params.statuses.contains(&order.status) {
                    return false;
                }
            }

            // Check time range filter (TSStart and TSEnd)
            if let (Some(ts_start), Some(ts_end)) = (params.ts_start, params.ts_end) {
                // Filter records where (paidTS OR filedTS) is greater than TSStart AND (paidTS OR filedTS) is less than TSEnd
                let relevant_ts = if order.paid_ts > 0 { order.paid_ts } else { order.filed_ts };
                if relevant_ts <= ts_start || relevant_ts >= ts_end {
                    return false;
                }
            }

            true
        }

        /// Query orders with filtering
        #[glue::readonly]
        pub fn query_orders(&self, params_json: String) -> anyhow::Result<String> {
            let params: OrderQueryParam = serde_json::from_str(&params_json)?;

            let mut orders = Vec::new();

            // Use direct ID lookup if IDs are provided
            if !params.ids.is_empty() {
                for id in &params.ids {
                    if let Some(order) = self.orders.get(id) {
                        if self.matches_order_filters(&order, &params) {
                            orders.push(order);
                        }
                    }
                }
            } else {
                // Use index-based iteration for other queries
                let mut iter = self.orders.index("order_created_at").iter(
                    false,
                    &format!("{:0>19}", 0),
                    &format!("{:9>19}", i64::MAX)
                );
                while iter.next() {
                    let order = iter.value()?;
                    if self.matches_order_filters(&order, &params) {
                        orders.push(order);
                    }
                }
            }

            Ok(serde_json::to_string(&orders)?)
        }

        /// Get distinct values for a specific field in orders
        #[glue::readonly]
        pub fn distinct_orders(&self, params_json: String) -> anyhow::Result<String> {
            let params: OrderDistinctParam = serde_json::from_str(&params_json)?;

            let mut distinct_values = std::collections::HashSet::new();

            // Iterate through all orders
            let mut iter = self.orders.index("order_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let order = iter.value()?;

                // Skip deleted orders
                if order.deleted_at > 0 {
                    continue;
                }

                // Apply address filter
                if !params.address.is_empty() && order.address != params.address {
                    continue;
                }

                // Apply statuses filter
                if !params.statuses.is_empty() && !params.statuses.contains(&order.status) {
                    continue;
                }

                // Extract the distinct field value
                let field_value = match params.distinct_field.as_str() {
                    "Status" => order.status.clone(),
                    "Type" => order.order_type.clone(),
                    "Provider" => order.provider.clone(),
                    "Address" => order.address.clone(),
                    "Recipient" => order.recipient.clone(),
                    _ => continue, // Skip unknown fields
                };

                distinct_values.insert(field_value);
            }

            let result: Vec<String> = distinct_values.into_iter().collect();
            Ok(serde_json::to_string(&result)?)
        }

        /// Update many orders based on filter criteria
        #[glue::atomic]
        pub fn update_many_orders(&mut self, filter_json: String, update_json: String) -> anyhow::Result<String> {
            let filter_params: OrderQueryParam = serde_json::from_str(&filter_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse filter JSON: {}", e))?;
            let update_order: Order = serde_json::from_str(&update_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse update JSON: {}", e))?;

            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            let current_time = self.get_current_timestamp();

            // Find orders matching the filter criteria
            let mut orders_to_update = Vec::new();

            if !filter_params.ids.is_empty() {
                for id in &filter_params.ids {
                    if let Some(order) = self.orders.get(id) {
                        if self.matches_order_filters(&order, &filter_params) {
                            orders_to_update.push(order);
                        }
                    }
                }
            } else {
                let mut iter = self.orders.index("order_created_at").iter(
                    false,
                    &format!("{:0>19}", 0),
                    &format!("{:9>19}", i64::MAX)
                );
                while iter.next() {
                    let order = iter.value()?;
                    if self.matches_order_filters(&order, &filter_params) {
                        orders_to_update.push(order);
                    }
                }
            }

            // Update each matching order
            for mut order in orders_to_update {
                // Update only non-empty/valid fields from update_order
                if !update_order.order_type.is_empty() {
                    order.order_type = update_order.order_type.clone();
                }
                if update_order.amount > 0.0 {
                    order.amount = update_order.amount;
                }
                if update_order.amount_paid > 0.0 {
                    order.amount_paid = update_order.amount_paid;
                }
                if !update_order.provider.is_empty() {
                    order.provider = update_order.provider.clone();
                }
                if !update_order.address.is_empty() {
                    order.address = update_order.address.clone();
                }
                if !update_order.recipient.is_empty() {
                    order.recipient = update_order.recipient.clone();
                }
                if !update_order.status.is_empty() {
                    order.status = update_order.status.clone();
                }
                if update_order.last_payment_ts > 0 {
                    order.last_payment_ts = update_order.last_payment_ts;
                }
                if update_order.paid_ts > 0 {
                    order.paid_ts = update_order.paid_ts;
                }
                if update_order.filed_ts > 0 {
                    order.filed_ts = update_order.filed_ts;
                }
                if !update_order.public_key.is_empty() {
                    order.public_key = update_order.public_key.clone();
                }
                if !update_order.user_service_ids.is_empty() {
                    order.user_service_ids = update_order.user_service_ids.clone();
                }
                if !update_order.items.is_empty() {
                    order.items = update_order.items.clone();
                }

                // Always update the updated_at timestamp
                order.updated_at = current_time;

                self.orders.insert(&order.id, &order);
                result.updated += 1;
            }

            Ok(serde_json::to_string(&result)?)
        }
    }
}