[package]
name = "name_service"
version = "0.1.0"
edition = "2021"
authors = ["VGraph"]

[dependencies]
glue = { path = "../glue" }

serde = { version = "=1.0.193", features = ["derive"] }
serde_json = "1.0.108"
anyhow = "1.0.75"
uuid = { version = "1.10.0", features = ["v4"] }

[dev-dependencies]
serial_test = "3.1.1"

[lib]
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[profile.release]
codegen-units = 1
opt-level = "z"
lto = true

