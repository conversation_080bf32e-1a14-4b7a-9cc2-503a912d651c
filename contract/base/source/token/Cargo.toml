[package]
name = "token"
version = "0.1.0"
edition = "2021"
authors = ["VGraph"]
homepage = "https://example.com"
repository = "https://example.com"
description = "A token contract"

[dependencies]
glue = { path = "../glue" }
serde = { version = "1.0.193", features = ["derive"] }
serde_json = "1.0.108"
anyhow = "1.0.83"

[dev-dependencies]
serial_test = "3.1.1"

[lib]
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[profile.release]
codegen-units = 1
opt-level = "z"
lto = true
