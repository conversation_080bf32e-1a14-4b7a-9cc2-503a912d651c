BUILD_TARGET := wasm32-wasip1
BUILD_OUT_DIR := ./target/$(BUILD_TARGET)/release

WASM_FILE_NAME := $(shell basename `pwd` | sed 's/-/_/g')
MOVE_DIR := ../..

all: build move

build:
	-@rustup target add $(BUILD_TARGET) > /dev/null 2>&1
	@cargo build --release --target $(BUILD_TARGET) -q
	@echo "build $(WASM_FILE_NAME) success"

move:
	-@rm -f $(MOVE_DIR)/$(WASM_FILE_NAME).wasm
	@mv -f $(BUILD_OUT_DIR)/$(WASM_FILE_NAME).wasm $(MOVE_DIR)
	@echo "move $(WASM_FILE_NAME) success"

clean:
	@rm -f $(MOVE_DIR)/$(WASM_FILE_NAME).wasm
	@cargo clean
	@echo "clean $(WASM_FILE_NAME) success"

cargo-clean:
	@cargo clean
	@echo "cargo clean $(WASM_FILE_NAME) success"

test:
# filter the test which name contains "test"
	@printf "cargo test $(WASM_FILE_NAME)"
	@cargo test test -q

.PHONY: all build move clean cargo-clean test
