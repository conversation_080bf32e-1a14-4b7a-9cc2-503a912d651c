#[glue::contract]
mod token_swap {
    use glue::call::build_call;

    #[glue::storage]
    pub struct TokenSwap {}

    impl TokenSwap {
        #[glue::constructor]
        pub fn new() -> Self {
            TokenSwap {}
        }

        #[glue::atomic]
        pub fn swap(
            &self,
            token_a: String,
            provider_a: String,
            amount_a: i64,
            token_b: String,
            provider_b: String,
            amount_b: i64,
        ) -> anyhow::Result<()> {
            build_call()
                .contract_address(token_a.clone().as_str())
                .function_name("transfer")
                .push_arg(provider_a.clone().as_str())
                .push_arg(provider_b.clone().as_str())
                .push_arg(amount_a)
                .call_no_return()?;

            build_call()
                .contract_address(token_b.clone().as_str())
                .function_name("transfer")
                .push_arg(provider_b.clone().as_str())
                .push_arg(provider_a.clone().as_str())
                .push_arg(amount_b)
                .call_no_return()?;

            Ok(())
        }
    }
}

// cannot write unit test for this contract, may need end-to-end test for this contract