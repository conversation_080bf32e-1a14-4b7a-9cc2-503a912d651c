#[glue::contract]
mod spos {
    use std::collections::HashSet;

    use glue::call::build_call;

    const TOKEN_CONTRACT_ADDRESS: &str = "0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3";

    // Storage
    #[glue::storage]
    pub struct Storage {
        // Stakes
        pub stake_accounts_map: glue::collections::Map<String, StakeAccount>, // address -> stake account
        pub stake_graph: glue::collections::Map<(String, String), i64>, // (staker, stakee) -> amount
        // stake_graph_index is used to store the stakee list of a staker, speed up the cancel_all_stake_out operation
        pub stake_graph_index: glue::collections::Map<String, HashSet<String>>, // staker -> stakee list

        // Slots
        pub number_of_slots: glue::StorageField<u32>, // number of slots
        pub slot_vec: glue::collections::Vec<String>, // supernode address list. indexes are slot ids
        pub address_map: glue::collections::Map<String, u32>, // supernode address -> index in slot_vec
    }

    impl Storage {
        #[glue::constructor]
        pub fn new(number_of_slots: u32) -> Self {
            // push `numbers_of_slots + 1` empty string to slot_vec
            // the first slot is not used
            let mut slot_vec = glue::collections::Vec::default();
            for _ in 0..(number_of_slots + 1) {
                slot_vec.push(&"".to_string());
            }

            Self {
                stake_accounts_map: glue::collections::Map::default(),
                stake_graph: glue::collections::Map::default(),
                stake_graph_index: glue::collections::Map::default(),
                number_of_slots: glue::StorageField::new(&number_of_slots),
                slot_vec: slot_vec,
                address_map: glue::collections::Map::default(),
            }
        }

        // ********** Stakes **********

        // ------ wasm ------
        // ---------- readonly ----------
        #[glue::readonly]
        pub fn get_available_balance(&self, address: String) -> anyhow::Result<i64> {
            // available available = balance - total_stake_out
            let account_balance = build_call()
                .contract_address(TOKEN_CONTRACT_ADDRESS)
                .function_name("balance")
                .push_arg(address.clone())
                .call::<i64>()?;
            match self.stake_accounts_map.get(&address) {
                Some(stake_account) => Ok(account_balance - stake_account.total_stake_out),
                None => Ok(account_balance),
            }
        }

        #[glue::readonly]
        pub fn get_minting_power(&self, address: String) -> anyhow::Result<i64> {
            // minting power = available + total_stake_in = balance + total_stake_in - total_stake_out
            let account_balance = build_call()
                .contract_address(TOKEN_CONTRACT_ADDRESS)
                .function_name("balance")
                .push_arg(address.clone())
                .call::<i64>()?;

            match self.stake_accounts_map.get(&address) {
                Some(stake_account) => {
                    Ok(account_balance + stake_account.total_stake_in
                        - stake_account.total_stake_out)
                }
                None => Ok(account_balance),
            }
        }

        #[glue::readonly]
        pub fn get_stake_balance(&self, address: String) -> anyhow::Result<(i64, i64)> {
            match self.stake_accounts_map.get(&address) {
                Some(stake_account) => {
                    Ok((stake_account.total_stake_in, stake_account.total_stake_out))
                }
                None => Ok((0, 0)),
            }
        }

        #[glue::readonly]
        pub fn get_stake_amount(&self, staker: String, stakee: String) -> anyhow::Result<i64> {
            match self.stake_graph.get(&(staker.clone(), stakee.clone())) {
                Some(amount) => Ok(amount),
                None => Ok(0),
            }
        }

        #[glue::readonly]
        pub fn get_stakee_list(&self, staker: String) -> anyhow::Result<HashSet<String>> {
            Ok(self
                .stake_graph_index
                .get(&staker.clone())
                .unwrap_or(HashSet::new()))
        }

        // ---------- atomic ----------
        #[glue::atomic]
        pub fn stake_out(
            &mut self,
            staker: String,
            stakee: String,
            amount: i64,
        ) -> anyhow::Result<()> {
            // verify the available balance
            let available_balance = self.get_available_balance(staker.clone())?;
            if available_balance < amount {
                return Err(anyhow::anyhow!(
                    "stake out failed, available balance is not enough"
                ));
            }

            // staker's stake out should be increased
            let mut staker_stake_account = self
                .stake_accounts_map
                .get(&staker.clone())
                .unwrap_or(StakeAccount::new(&staker));
            staker_stake_account.total_stake_out += amount;
            self.update_state_account(staker_stake_account)?;

            // stakee's stake in should be increased
            let mut stakee_stake_account = self
                .stake_accounts_map
                .get(&stakee.clone())
                .unwrap_or(StakeAccount::new(&stakee));
            stakee_stake_account.total_stake_in += amount;
            self.update_state_account(stakee_stake_account)?;

            // update the stake graph
            match self.stake_graph.get(&(staker.clone(), stakee.clone())) {
                Some(old_amount) => {
                    self.stake_graph
                        .insert(&(staker.clone(), stakee.clone()), &(old_amount + amount));
                }
                None => {
                    self.stake_graph
                        .insert(&(staker.clone(), stakee.clone()), &amount);

                    // update the stake graph index
                    let mut stakee_list = self
                        .stake_graph_index
                        .get(&staker.clone())
                        .unwrap_or(HashSet::new());
                    stakee_list.insert(stakee.clone());
                    self.stake_graph_index.insert(&staker.clone(), &stakee_list);
                }
            }
            Ok(())
        }

        #[glue::atomic]
        pub fn cancel_one_stake_out(
            &mut self,
            staker: String,
            stakee: String,
        ) -> anyhow::Result<()> {
            match self.stake_graph.get(&(staker.clone(), stakee.clone())) {
                Some(amount) => {
                    // staker's stake out should be decreased
                    let mut staker_stake_account = self
                        .stake_accounts_map
                        .get(&staker.clone())
                        .unwrap_or(StakeAccount::new(&staker));
                    staker_stake_account.total_stake_out -= amount;
                    self.update_state_account(staker_stake_account)?;

                    // stakee's stake in should be decreased
                    let mut stakee_stake_account = self
                        .stake_accounts_map
                        .get(&stakee.clone())
                        .unwrap_or(StakeAccount::new(&stakee));
                    stakee_stake_account.total_stake_in -= amount;
                    self.update_state_account(stakee_stake_account)?;

                    // remove the stake graph
                    self.stake_graph.remove(&(staker.clone(), stakee.clone()));

                    // remove the stake graph index
                    let mut stakee_list = self
                        .stake_graph_index
                        .get(&staker.clone())
                        .unwrap_or(HashSet::new());
                    stakee_list.remove(&stakee.clone());
                    // TODO: if stakee_list is empty, remove the key from the index
                    self.stake_graph_index.insert(&staker.clone(), &stakee_list);
                    Ok(())
                }
                None => Ok(()),
            }
        }

        #[glue::atomic]
        pub fn cancel_all_stake_out(&mut self, staker: String) -> anyhow::Result<()> {
            let stakee_list = self
                .stake_graph_index
                .get(&staker.clone())
                .unwrap_or(HashSet::new());
            for stakee in stakee_list {
                self.cancel_one_stake_out(staker.clone(), stakee.clone())?;
            }
            Ok(())
        }

        // ------ rust ------
        #[glue::atomic]
        pub fn update_state_account(&mut self, state_account: StakeAccount) -> anyhow::Result<()> {
            self
                .stake_accounts_map
                .insert(&state_account.address, &state_account);
            Ok(())
        }

        // ********** Slots **********
        // ----------read only----------
        #[glue::readonly]
        pub fn check_owners_supernode_slot(&mut self, address: String) -> anyhow::Result<u32> {
            match self.address_map.get(&address) {
                Some(slot_id) => Ok(slot_id),
                None => Ok(0),
            }
        }

        // TODO: add test for this function
        #[glue::readonly]
        pub fn get_slot_owner(&mut self, slot_id: u32) -> anyhow::Result<String> {
            match self.slot_vec.get(slot_id) {
                Some(owner) => Ok(owner.clone()),
                None => Err(anyhow::anyhow!("The slot doesn't exist")),
            }
        }

        #[glue::readonly]
        pub fn get_all_supernodes(&mut self) -> anyhow::Result<Vec<String>> {
            let mut supernodes = Vec::new();
            for i in 1..=self.number_of_slots.get() {
                if let Some(owner) = self.slot_vec.get(i) {
                    // skip empty slot
                    if owner == "" {
                        continue;
                    }
                    supernodes.push(owner.clone());
                }
            }
            Ok(supernodes)
        }

        // ----------atomic----------
        #[glue::atomic]
        pub fn content_slot(
            &mut self,
            slot: u32,
            competitor_address: String,
        ) -> anyhow::Result<()> {
            // check if the slot is valid first
            if slot < 1 || slot > self.number_of_slots.get() {
                return Err(anyhow::anyhow!("Invalid slot id"));
            }

            // check if the competitor already owns a slot
            if self.address_map.contains(&competitor_address) {
                return Err(anyhow::anyhow!("The competitor already owns a slot"));
            }

            // get current owner of the slot
            let current_owner = self.slot_vec.get(slot).unwrap();
            if current_owner == "" {
                // if the slot is empty, competitor can take it
                let _ = self.slot_vec.set(slot, &competitor_address);
                self.address_map.insert(&competitor_address, &slot);
            } else {
                // if the slot is occupied, check minting power of the competitor and the current owner
                // if the competitor has higher minting power, update the slot with the competitor address
                // otherwise, return error

                let current_owner_minting_power =
                    self.get_minting_power(current_owner.clone())?;
                let competitor_minting_power =
                    self.get_minting_power(competitor_address.clone())?;

                if competitor_minting_power > current_owner_minting_power {
                    // if competitor has higher minting power, it can take the slot
                    let _ = self.slot_vec.set(slot, &competitor_address);
                    self.address_map.insert(&competitor_address, &slot);
                    // remove the slot from the current owner
                    self.address_map.remove(&current_owner);
                } else {
                    return Err(anyhow::anyhow!(
                        "The competitor has lower minting power than the current owner"
                    ));
                }
            }

            Ok(())
        }

        #[glue::atomic]
        pub fn release_slot(&mut self, owner_address: String) -> anyhow::Result<()> {
            // get the slot id of the owner
            let slot_id = match self.address_map.get(&owner_address) {
                Some(slot_id) => slot_id,
                None => return Err(anyhow::anyhow!("The owner doesn't own any slot")),
            };

            // check if the owner is the last super node
            // if it is the last super node, return error
            if self.address_map.size() == 1 {
                return Err(anyhow::anyhow!("The last super node can't be released"));
            }

            // release the slot
            let _ = self.slot_vec.set(slot_id, &"".to_string());
            self.address_map.remove(&owner_address);
            Ok(())
        }

        #[glue::atomic]
        pub fn mint_token(&mut self, recipient_address: String, amount: i64) -> anyhow::Result<()> {
            // check if the transaction is the first transaction in the block
            let env = env();

            let transaction_index = env.transaction_index;
            if transaction_index != 0 {
                return Err(anyhow::anyhow!(
                    "Minting token is only allowed in the first transaction"
                ));
            }

            // TODO: check if the transaction signer is the block signer
            // We don't have block signer and transaction signer currently

            // TODO: only supernode can mint token
            // ?: Is "sender" the transaction signer?
            // Suppose it is.
            let transaction_initiator = env.sender;
            if !self.address_map.contains(&transaction_initiator) {
                return Err(anyhow::anyhow!("Transaction initiator is not a supernode"));
            }

            build_call()
                .contract_address(TOKEN_CONTRACT_ADDRESS)
                .function_name("issue")
                .push_arg(recipient_address)
                .push_arg(amount)
                .call_no_return()
        }
    }

    #[glue::storage_item]
    pub struct StakeAccount {
        pub address: String,
        pub total_stake_in: i64,  // stake in amount
        pub total_stake_out: i64, // stake out amount
    }

    impl StakeAccount {
        pub fn new(address: &str) -> Self {
            Self {
                address: address.to_string(),
                total_stake_in: 0,
                total_stake_out: 0,
            }
        }
    }
}
