#[glue::contract]
mod kvstore {
    use anyhow::Ok;

    #[glue::storage]
    pub struct KvStorage {
        pub kv_data: glue::collections::Map<(String, String), String>,
    }

    impl KvStorage {
        #[glue::constructor]
        pub fn new() -> Self {
            Self {
                kv_data: glue::collections::Map::new(),
            }
        }

        #[glue::readonly]
        pub fn get_value(&self, key: String, address: String) -> anyhow::Result<String> {
            let tuple_key = (key.clone(), address.clone());
            if !self.kv_data.contains(&tuple_key) {
                return Ok(String::new());
            }

            let content = self.kv_data.get(&tuple_key).expect("not found");
            Ok(content.clone())
        }

        #[glue::atomic]
        pub fn set_value(&mut self, key: String, item: String) -> anyhow::Result<()> {
            let env = env();
            let sender_address = env.sender.clone();
            let tuple_key = (key.clone(), sender_address.clone());

            self.kv_data.insert(&tuple_key, &item);
            Ok(())
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[glue::test]
    fn test_kv() {
        let test_environment = glue::env::TestEnvironmentBuilder::new()
            .sender("0x1306878a34a2987b91dd566dbbff76b1a3b1d545318a50d01d946bc311556700")
            .build();
        glue::env::update_test_env(&test_environment);

        kvstore::set_instance(kvstore::KvStorage::new());
        let kv = kvstore::get_instance();
        kv.set_value("key1".to_string(), "item1".to_string()).unwrap();
        let value = kv.get_value("key1".to_string(), "0x1306878a34a2987b91dd566dbbff76b1a3b1d545318a50d01d946bc311556700".to_string()).unwrap();
        assert_eq!(value, "item1");
    }
}