[package]
name = "kvstore"
version = "0.1.0"
edition = "2021"
authors = ["VGraph"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
glue = { path = "../glue" }

serde = { version = "=1.0.193", features = ["derive"] }
serde_json = "1.0.108"
anyhow = "1.0.75"
#sha3 = "0.10.8"
#uuid = { version = "1.6.1", features = ["serde", "v4", "fast-rng"] }
# bs58 = "0.5.0"
# schnorrkel = "0.11.4"

[dev-dependencies]
serial_test = "3.1.1"

[lib]
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[profile.release]
codegen-units = 1
opt-level = "z"
lto = true
