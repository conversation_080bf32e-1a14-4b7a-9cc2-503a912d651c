[package]
name = "primechain_utils"
version = "0.1.0"
edition = "2021"
authors = ["VGraph"]

[dependencies]
glue = { path = "../glue" }

anyhow = "=1.0.75"
serde = { version = "=1.0.193", features = ["derive"] }
serde_json = "=1.0.108"
num-bigint = "0.4.4"
num-traits = "0.2.18"
hex = "0.4.3"

[dev-dependencies]
serial_test = "3.1.1"
reqwest = { version = "0.11", features = ["json"] } # reqwest with JSON parsing support
futures = "0.3" # for our async / await blocks
tokio = { version = "1.12.0", features = ["full"] } # for our async runtime

[lib]
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[profile.release]
codegen-units = 1
opt-level = "z"
lto = true
