use num_bigint::BigInt;
use num_traits::{One, ToPrimitive, Zero};

pub const DIFFICULTY_UNITY: u64 = 1 << 24;

pub fn fermat_probable_primality_test(n: &BigInt) -> anyhow::Result<u64> {
    // Base 2
    let r = BigInt::from(2).modpow(&(n - BigInt::one()), n);

    if r == BigInt::one() || *n == BigInt::zero() {
        match DIFFICULTY_UNITY.to_u64() {
            Some(difficulty_unity) => Ok(difficulty_unity),
            None => Err(anyhow::anyhow!("Failed to convert to u64")),
        }
    } else {
        // Calculate fractional difficulty
        let result = ((n - &r) * &DIFFICULTY_UNITY) / n;
        match result.to_u64() {
            Some(difficulty) => Ok(difficulty),
            None => Err(anyhow::anyhow!("Failed to convert to u64")),
        }
    }
}

pub fn twin_prime_chain_scale(origin: &BigInt) -> anyhow::Result<u64> {
    let mut delta = BigInt::from(-1i32);
    let mut scale = 0u64;
    let mut current_origin = origin.clone();

    loop {
        let p = &current_origin + &delta;
        let difficulty = fermat_probable_primality_test(&p)?;
        // println!("Difficulty: {}", difficulty);
        scale += difficulty;

        if difficulty < DIFFICULTY_UNITY {
            break; // End of twin prime chain
        }
        if delta > Zero::zero() {
            current_origin <<= 1;
        }
        delta = delta * -1;
    }

    Ok(scale)
}

pub fn verify_submitted_work(origin: &BigInt, difficulty: u64) -> anyhow::Result<bool> {
    // Calculate the scale of the twin prime chain
    let scale = twin_prime_chain_scale(&origin)?;

    // Compare the scale with the difficulty
    // divide by 2^24
    // let real_difficulty = scale as f64 / (1 << 24) as f64;
    // println!("Submitted origin difficulty: {}", real_difficulty);

    Ok(scale >= difficulty)
}
