pub mod difficulty;
pub mod test_prime_origin;

#[glue::contract]
mod primechain_utils {

    use num_bigint::BigInt;
    use num_traits::Num;

    use super::*;

    #[glue::storage]
    pub struct PrimechainUtils {}

    impl PrimechainUtils {
        #[glue::constructor]
        pub fn new() -> Self {
            Self {}
        }

        // difficulty
        #[glue::readonly]
        pub fn get_initial_difficulty(&self) -> anyhow::Result<String> {
            Ok(difficulty::target_get_initial().to_string())
        }

        #[glue::readonly]
        pub fn get_next_difficulty(&mut self, block_difficulty_score: i32, block_timestamp: i64, parent_block_timestamp: i64) -> anyhow::Result<String> {

            let n_difficulty: i32;
            if block_difficulty_score > 1 {
                n_difficulty = block_difficulty_score;
            } else {
                // 7 * pow(2,24)
                n_difficulty = difficulty::target_get_initial();
            }

            let n_difficulty_next = difficulty::target_get_next(
                n_difficulty,
                block_timestamp / 1000000000,
                parent_block_timestamp / 1000000000,
            );

            Ok(n_difficulty_next.to_string())
        }

        // test prime origin
        #[glue::readonly]
        pub fn get_difficulty_unity(&self) -> anyhow::Result<u64> {
            Ok(test_prime_origin::DIFFICULTY_UNITY)
        }

        #[glue::readonly]
        pub fn verify_work(&mut self, origin: String, difficulty: u64) -> anyhow::Result<bool> {
            let origin = BigInt::from_str_radix(&origin, 10)?;
            Ok(test_prime_origin::verify_submitted_work(&origin, difficulty)?)
        }
    }
}

#[cfg(test)]
mod test_difficulty {
    use super::*;

    #[glue::test]
    fn test_verify_work() {
        primechain_utils::set_instance(primechain_utils::PrimechainUtils::new());
        let utils = primechain_utils::get_instance();
        let difficulty_unity = utils.get_difficulty_unity().unwrap();

        let origin = "16104852924139568456006626349957181947614826079179167293804058866767923317510414698411827609927680".to_string();
        let difficulty = 11.69245762 * difficulty_unity as f64;
        let result = utils.verify_work(origin, difficulty as u64).unwrap();
        assert_eq!(result, true);

        let origin = "53116128645141147208311952277331334682541568096207660913084590785789019086113551886126310".to_string();
        let difficulty = 6.99609375 * difficulty_unity as f64;
        let result = utils.verify_work(origin, difficulty as u64).unwrap();
        assert_eq!(result, true);

        let origin = "31374298392901195153898941717506973017356439773600594248208334547756268722849666432245972690".to_string();
        let difficulty = 7.649105191230774 * difficulty_unity as f64;
        let result = utils.verify_work(origin, difficulty as u64).unwrap();
        assert_eq!(result, true);
    }
}
