const N_FRACTIONAL_BITS: i32 = 24;
const TARGET_FRACTIONAL_MASK: i32 = (1 << N_FRACTIONAL_BITS) - 1;
const TARGET_LENGTH_MASK: i32 = !TARGET_FRACTIONAL_MASK;
const N_FRACTIONAL_DIFFICULTY_LINEAR_MAX: i64 = 1 << (N_FRACTIONAL_BITS + 32);
const N_FRACTIONAL_DIFFICULTY_LINEAR_MIN: i64 = 1 << 32;
const N_FRACTIONAL_DIFFICULTY_THRESHOLD: i64 = 1 << (8 + 32);

const N_TARGET_TIMESPAN: i64 = 7 * 24 * 60 * 60; // one week
const N_TARGET_SPACING: i64 = 60;
const N_INTERVAL: i64 = N_TARGET_TIMESPAN / N_TARGET_SPACING;

const N_TARGET_INITIAL_LENGTH : i32 = 2; // initial chain length target
const N_TARGET_MIN_LENGTH : i32 = 2;

fn target_get_fractional(n_difficulty: i32) -> i32 {
    return n_difficulty & TARGET_FRACTIONAL_MASK;
}

fn target_get_fractional_difficulty(n_difficulty: &i32) -> i64 {
    return N_FRACTIONAL_DIFFICULTY_LINEAR_MAX
        / ((1 << N_FRACTIONAL_BITS) - target_get_fractional(*n_difficulty)) as i64;
}

fn target_get_length(n_difficulty: & i32) -> i32
{
    return (*n_difficulty & TARGET_LENGTH_MASK) >> N_FRACTIONAL_BITS;
}

fn target_increment_length(n_difficulty: &mut i32) {
    *n_difficulty += 1 << N_FRACTIONAL_BITS;
}

fn target_decrement_length(n_difficulty: &mut i32){
    if target_get_length(n_difficulty) > N_TARGET_MIN_LENGTH{
        *n_difficulty -= 1 << N_FRACTIONAL_BITS;
    }
}

fn target_set_fractional_difficulty(n_fractional_difficulty_linear : i64, n_difficulty : &mut i32) -> bool{
    if n_fractional_difficulty_linear < N_FRACTIONAL_DIFFICULTY_LINEAR_MIN{
        println!("nFractionalDifficulty < N_FRACTIONAL_DIFFICULTY_MIN");
    }
    let mut n_fractional_difficulty = (N_FRACTIONAL_DIFFICULTY_LINEAR_MAX / n_fractional_difficulty_linear) as i32;
    if n_fractional_difficulty > 1<<N_FRACTIONAL_BITS{
        println!("nFractional > 1<<N_FRACTIONAL_BITS overflow");
    }

    n_fractional_difficulty = (1<<N_FRACTIONAL_BITS) - n_fractional_difficulty;
    *n_difficulty &= TARGET_LENGTH_MASK;
    *n_difficulty |= n_fractional_difficulty;
    return true;
}

pub fn target_get_initial() -> i32{
    return N_TARGET_INITIAL_LENGTH << N_FRACTIONAL_BITS;
}

pub fn target_get_next(n_difficulty: i32, current_timestamp : i64, parent_timestamp:i64) -> i32 {
    let n_actual_spacing = current_timestamp - parent_timestamp;

    let mut n_difficulty_next = n_difficulty;

    let n_fractional_difficulty_linear = target_get_fractional_difficulty(&n_difficulty);

    let mut bn_fractional_difficulty_linear = n_fractional_difficulty_linear;
    bn_fractional_difficulty_linear *= (N_INTERVAL + 1) * N_TARGET_SPACING;
    bn_fractional_difficulty_linear /=
        (N_INTERVAL - 1) * N_TARGET_SPACING + n_actual_spacing + n_actual_spacing;

    if bn_fractional_difficulty_linear > N_FRACTIONAL_DIFFICULTY_LINEAR_MAX {
        bn_fractional_difficulty_linear = N_FRACTIONAL_DIFFICULTY_LINEAR_MAX;
    }
    if bn_fractional_difficulty_linear < N_FRACTIONAL_DIFFICULTY_LINEAR_MIN {
        bn_fractional_difficulty_linear = N_FRACTIONAL_DIFFICULTY_LINEAR_MIN;
    }

    let mut n_fractional_difficulty_linear_new = bn_fractional_difficulty_linear;

    if n_fractional_difficulty_linear_new > N_FRACTIONAL_DIFFICULTY_THRESHOLD {
        n_fractional_difficulty_linear_new = N_FRACTIONAL_DIFFICULTY_LINEAR_MIN;
        target_increment_length(&mut n_difficulty_next);
    } else if n_fractional_difficulty_linear_new == N_FRACTIONAL_DIFFICULTY_LINEAR_MIN
        && target_get_length(&n_difficulty_next) > N_TARGET_MIN_LENGTH
    {
        n_fractional_difficulty_linear_new = N_FRACTIONAL_DIFFICULTY_THRESHOLD;
        target_decrement_length(&mut n_difficulty_next);
    }
    let _err = target_set_fractional_difficulty(n_fractional_difficulty_linear_new, &mut n_difficulty_next);

    return n_difficulty_next;
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde::Deserialize;
    use reqwest;
    use tokio;

    #[derive(Deserialize, Debug)]
    struct BlockFromPrimecoin {
        result: BlockFromPrimecoinResult,
    }

    #[derive(Deserialize, Debug)]
    struct BlockFromPrimecoinResult {
        bits: String,
        time: i64,
    }

    async fn check_block(height: i64) {
        let client = reqwest::Client::new();
        let current_block = client
            .get(format!(
                "https://explorer.primecoin.net/api/getblock/{}",
                height
            ))
            .send()
            .await
            .unwrap()
            .json::<BlockFromPrimecoin>()
            .await
            .unwrap();
        println!("current_block: {:?}", current_block);

        let parent_block = client
            .get(format!(
                "https://explorer.primecoin.net/api/getblock/{}",
                height - 1
            ))
            .send()
            .await
            .unwrap()
            .json::<BlockFromPrimecoin>()
            .await
            .unwrap();
        println!("parent_block: {:?}", parent_block);

        let next_block = client
            .get(format!(
                "https://explorer.primecoin.net/api/getblock/{}",
                height + 1
            ))
            .send()
            .await
            .unwrap()
            .json::<BlockFromPrimecoin>()
            .await
            .unwrap();
        println!("next_block: {:?}", next_block);
        let a = i32::from_str_radix(current_block.result.bits.as_str(), 16).unwrap();
        println!("a: {}", a);
        let next_difficulty =
            target_get_next(a, current_block.result.time, parent_block.result.time);
        println!("next_difficulty: {}", next_difficulty);

        let actual_difficulty = i32::from_str_radix(next_block.result.bits.as_str(), 16).unwrap();
        println!("actual diffuclt: {}", actual_difficulty);
        assert_eq!(next_difficulty.to_string(), actual_difficulty.to_string());
    }

    #[tokio::test]
    #[ignore = "This test required external network"]
    async fn test_target_get_next_up() {
        // over THRESHOLD
        // 7 -> 8
        check_block(37323).await;
        check_block(37324).await;
        check_block(37325).await;
    }

    #[tokio::test]
    #[ignore = "This test required external network"]
    async fn test_target_get_next_down() {
        // 11 -> 10
        check_block(3252271).await;
        check_block(3252272).await;
        check_block(3252273).await;
        check_block(3252274).await;
        check_block(3252275).await;
    }
}