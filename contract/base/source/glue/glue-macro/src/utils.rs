use regex::Regex;
use quote::ToTokens;
use syn::Attribute;
use syn::Item;

pub trait DocExtractor {
    fn is_doc_attribute(&self) -> bool;
    fn extract_docs(&self) -> Option<String>;
}

impl DocExtractor for Attribute {
    fn is_doc_attribute(&self) -> bool {
        self.path().is_ident("doc")
    }

    fn extract_docs(&self) -> Option<String> {
        if !self.is_doc_attribute() {
            return None;
        }
        match &self.meta {
            syn::Meta::NameValue(nv) => {
                if let syn::Expr::Lit(l) = &nv.value {
                    if let syn::Lit::Str(s) = &l.lit {
                        return Some(s.value().trim().to_string());
                    }
                }
            }
            _ => return None,
        }
        None
    }
}

/// Extracts the inner type from a string that represents an `anyhow::Result<T>` or any `Result<T>`.
///
/// # Arguments
/// * `type_str` - A string slice that may represent a type in the form of `anyhow::Result<T>`.
///
/// # Returns
/// A `String` containing the extracted inner type `T` if the input matches the `Result<T>` pattern.
/// If it does not match, it returns the original string with whitespace removed.
pub fn extract_anyhow_result_t(type_str: &str) -> String {
    // Compile a regular expression that looks for a "Result<...>" pattern,
    // capturing the text inside the angle brackets.
    // The pattern: ^.*Result\s*<\s*(.+)\s*>\s*$
    //  1. ^ and $ ensure we match the entire input string (after any trimming).
    //  2. .*Result matches any leading text (including "anyhow::"), followed by "Result".
    //  3. \s*<\s* allows for any whitespace around the angle bracket '<'.
    //  4. (.+) captures the content inside the angle brackets.
    //  5. \s*>\s* allows for any whitespace around '>'.
    let re = Regex::new(r"^.*Result\s*<\s*(.+)\s*>\s*$").expect("Failed to build regex");

    // Use the regex to capture the inner type T if it exists.
    // If the pattern matches, caps[1] (the capture group) is used.
    // Otherwise, return the input string as-is (minus whitespace).
    re.captures(&type_str)
        .map(|caps| caps[1].to_string())
        .or_else(|| Some(type_str.to_string()))
        .unwrap_or_default()
        .replace(" ", "")
}

#[derive(Clone, Eq, PartialEq)]
pub enum FuncType {
    Constructor,
    Readonly,
    Atomic,
}

pub fn extract_func_type(attr: &syn::Attribute) -> Option<FuncType> {
    let segments = &attr.path().segments;
    let func_type_str = if segments.len() == 2 && segments[0].ident == "glue" {
        Some(segments[1].ident.to_string())
    } else {
        None
    };
    match func_type_str {
        Some(s) if s == "constructor" => Some(FuncType::Constructor),
        Some(s) if s == "readonly" => Some(FuncType::Readonly),
        Some(s) if s == "atomic" => Some(FuncType::Atomic),
        _ => None,
    }
}

pub fn find_bind_index(items: &mut Vec<Item>) -> bool {
    for item in items.iter_mut() {
        match item {
            // Impl block
            Item::Impl(impl_block) => {
                // Traverse all items in the impl block
                for mut item in &mut impl_block.items {
                    match item {
                        // If the item is a function
                        syn::ImplItem::Fn(ref mut method) => {
                            // Match method macro
                            for attr in &method.attrs {
                                if find_macro(attr, "bind_index") {
                                    // glue::bind_index
                                    return true;
                                }
                            }
                        }
                        _ => {}
                    }
                }
            }
            _ => {}
        }
    }
    false
}

pub fn find_macro(attr: &syn::Attribute, name: &str) -> bool {
    if let Some(last) = attr.path().segments.last() {
        return last.ident == name
            && attr
                .path()
                .segments
                .iter()
                .map(|seg| seg.ident.to_string())
                .collect::<Vec<_>>()
                == vec!["glue", name];
    }
    false
}

pub fn extract_map_types(map_str: &str) -> Option<(String, String)> {
    let re = Regex::new(r".*Map\s*<\s*(.+)\s*>\s*").unwrap();
    if let Some(captures) = re.captures(map_str) {
        let types_str = captures.get(1)?.as_str();
        let (key_type, value_type) = split_key_value(types_str)?;
        return Some((key_type.trim().to_string(), value_type.trim().to_string()));
    }
    None
}

fn split_key_value(types_str: &str) -> Option<(&str, &str)> {
    let mut brackets = 0;
    for (i, ch) in types_str.chars().enumerate() {
        match ch {
            '<' | '(' | '[' => brackets += 1,
            '>' | ')' | ']' => brackets -= 1,
            ',' if brackets == 0 => {
                return Some((types_str[..i].trim(), types_str[i + 1..].trim()))
            }
            _ => {}
        }
    }
    None
}

pub fn extract_vec_type(vec_str: &str) -> Option<String> {
    let re = Regex::new(r".*Vec\s*<\s*(.+)\s*>\s*").unwrap();
    if let Some(captures) = re.captures(vec_str) {
        let type_str = captures.get(1)?.as_str();
        return Some(type_str.trim().to_string());
    }
    None
}

pub fn camel_to_snake_case(input: &str) -> String {
    let mut snake_case = String::new();
    let mut prev_char_was_upper = false;

    for (i, c) in input.chars().enumerate() {
        if c.is_uppercase() {
            if i > 0 && !prev_char_was_upper {
                snake_case.push('_');
            }
            snake_case.push(c.to_lowercase().next().unwrap());
            prev_char_was_upper = true;
        } else {
            snake_case.push(c);
            prev_char_was_upper = false;
        }
    }

    snake_case
}

pub fn extract_between_angle_brackets(input: &str) -> Option<String> {
    let mut start = None;
    let mut count = 0;

    for (i, c) in input.char_indices() {
        if c == '<' {
            if start.is_none() {
                start = Some(i);
            }
            count += 1;
        } else if c == '>' {
            count -= 1;
            if count == 0 {
                return start.map(|s| input[s + 1..i].to_string());
            }
        }
    }

    None
}

#[cfg(test)]
mod test {
    use super::*;

    #[test]
    fn test_extract_map_types() {
        let inputs_and_expected = vec![
            (
                "glue :: collections :: Map < (String, String), i32 >",
                Some(("(String, String)", "i32")),
            ),
            (
                "glue::collections::Map<(String, String), (i32, i32)>",
                Some(("(String, String)", "(i32, i32)")),
            ),
            ("Map<String, i32>", Some(("String", "i32"))),
            (
                "Map<String, HA<i32, i32>>",
                Some(("String", "HA<i32, i32>")),
            ),
            (
                "Map<String,（HA<i32, i32>,HA<i32, i32>)>",
                Some(("String", "（HA<i32, i32>,HA<i32, i32>)")),
            ),
        ];

        for (input, expected) in inputs_and_expected {
            let result = extract_map_types(input);
            assert_eq!(
                result.map(|(k, v)| (k.to_string(), v.to_string())),
                expected.map(|(k, v)| (k.to_string(), v.to_string())),
                "Failed on input: {}",
                input
            );
        }
    }

    #[test]
    fn test_extract_vec_type() {
        let inputs_and_expected = vec![
            ("Vec<String>", Some("String")),
            ("Vec< (String, String) >", Some("(String, String)")),
            ("glue::collections::Vec<i32>", Some("i32")),
            ("Vec<HA<i32, i32>>", Some("HA<i32, i32>")),
            (
                "Vec<(HA<i32, i32>, HA<i32, i32>)>",
                Some("(HA<i32, i32>, HA<i32, i32>)"),
            ),
        ];

        for (input, expected) in inputs_and_expected {
            let result = extract_vec_type(input);
            assert_eq!(
                result.map(|v| v.to_string()),
                expected.map(|v| v.to_string()),
                "Failed on input: {}",
                input
            );
        }
    }

    #[test]
    fn test_camel_to_snake_case() {
        let camel_case = "camelCaseString";
        let snake_case = camel_to_snake_case(camel_case);
        assert_eq!(snake_case, "camel_case_string");

        let camel_case_with_capital = "CamelCaseString";
        let snake_case_capital = camel_to_snake_case(camel_case_with_capital);
        assert_eq!(snake_case_capital, "camel_case_string");
    }

    #[test]
    fn test_extract_between_angle_brackets() {
        let input = "Vec<(String, i32)>";
        let result = extract_between_angle_brackets(input);
        assert_eq!(result, Some("(String, i32)".to_string()));

        let input = "Vec<String>";
        let result = extract_between_angle_brackets(input);
        assert_eq!(result, Some("String".to_string()));

        let input = "Vec<i32>";
        let result = extract_between_angle_brackets(input);
        assert_eq!(result, Some("i32".to_string()));

        let input = "Vec<HA<i32, i32>>";
        let result = extract_between_angle_brackets(input);
        assert_eq!(result, Some("HA<i32, i32>".to_string()));

        let input = "Vec<(HA<i32, i32>, HA<i32, i32>)>";
        let result = extract_between_angle_brackets(input);
        assert_eq!(result, Some("(HA<i32, i32>, HA<i32, i32>)".to_string()));
    }

    #[test]
    fn test_extract_anyhow_result_t() {
        // Here we have a variety of test cases, including one
        // with an extra '>' so that we confirm greedy capture works.
        let cases = vec![
            ("anyhow::Result<i32>", "i32".to_string()),
            ("anyhow :: Result < i64 >", "i64".to_string()),
            (
                "   anyhow::Result<Vec<Option<String>>>   ",
                "Vec<Option<String>>".to_string(),
            ),
            ("Result<i32>", "i32".to_string()), // Missing "anyhow::", should not match
            ("FooBar", "FooBar".to_string()),   // Not a Result type, should not match
        ];

        for (input, expected) in cases {
            let got = extract_anyhow_result_t(input);
            assert_eq!(got, expected, "Test failed for input: {}", input);
        }
    }
}
