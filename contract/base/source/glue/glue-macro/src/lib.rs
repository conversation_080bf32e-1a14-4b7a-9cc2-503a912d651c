#![allow(unused_variables, unused_mut, dead_code, unused_imports)]

use crate::utils::find_bind_index;
use crate::utils::DocExtractor;
use proc_macro::TokenStream;
use quote::{format_ident, quote, ToTokens};
use regex::Regex;
use syn::parse::{Parse, Parser};
use syn::punctuated::Punctuated;
use syn::token::Comma;
use syn::{parse_macro_input, DeriveInput, ImplItemFn, ItemMod, ItemStruct};
use syn::{Block, FnArg, Ident, ItemFn, PatType, ReturnType};

mod impl_handle;
mod struct_handle;
mod utils;

#[proc_macro_attribute]
pub fn contract(args: TokenStream, item: TokenStream) -> TokenStream {
    let mut module = parse_macro_input!(item as ItemMod);
    let mut module_code = quote! {};

    // collect the module docs to vec string
    let module_docs = module
        .attrs
        .iter()
        .filter_map(|attr| attr.extract_docs())
        .collect();

    // Insert env code
    module_code.extend(quote! {
        #[allow(dead_code)]
        #[inline]
        pub fn env() -> glue::env::Environment {
            glue::env::get_env().clone()
        }
    });

    // Insert emit event code
    module_code.extend(quote! {
        #[allow(dead_code)]
        #[inline]
        pub fn emit<E: glue::events::Event>(event: E) {
            glue::events::emit_event(event);
        }
    });

    // Traverse all items in the module
    if let Some((_, items)) = &mut module.content {
        // Process the struct block
        let is_bind_index = find_bind_index(items);
        struct_handle::process_struct(&mut module_code, items, is_bind_index);

        // Process the impl block
        impl_handle::process_impl(module_docs, &mut module_code, items);
    }

    if let Some((_, items)) = module.content.as_mut() {
        items.push(syn::Item::Verbatim(module_code));
    }

    let expanded = quote! {
        #[cfg_attr(not(target_arch = "wasm32"), allow(dead_code))]
        #module

        #[no_mangle]
        static __ONE_RUST_PROJECT_ONLY_CAN_HAVE_ONE_CONTRACT: () = ();
    };

    // print code
    // println!("{}", TokenStream::from(expanded.clone()).to_string());

    TokenStream::from(expanded)
}

#[proc_macro_attribute]
pub fn bind_index(args: TokenStream, item: TokenStream) -> TokenStream {
    let input_fn = parse_macro_input!(item as ImplItemFn);

    // Error if method name is not bind_index
    if input_fn.sig.ident != "bind_index" {
        return TokenStream::from(quote! {
            compile_error!("The bind index function must be named `bind_index`");
        });
    }

    let expanded = quote! {
        #input_fn
    };

    // print code
    // println!("{}", TokenStream::from(expanded.clone()).to_string());

    TokenStream::from(expanded)
}

#[proc_macro_attribute]
pub fn storage(_attr: TokenStream, item: TokenStream) -> TokenStream {
    let mut item_struct = parse_macro_input!(item as ItemStruct);

    // Create the derive attribute
    let serialize_attr = syn::parse_quote!(#[derive(Default)]);
    item_struct.attrs.push(serialize_attr);

    // Generate the output tokens from the modified struct
    TokenStream::from(quote!(#item_struct))
}

#[proc_macro_attribute]
pub fn storage_item(_attr: TokenStream, item: TokenStream) -> TokenStream {
    let mut item_struct = parse_macro_input!(item as ItemStruct);

    // Create the derive attribute
    let serialize_attr = syn::parse_quote!(#[derive(serde::Serialize, serde::Deserialize, Eq, PartialEq, Clone, Debug, Default)]);
    item_struct.attrs.push(serialize_attr);

    // Generate the output tokens from the modified struct
    TokenStream::from(quote!(#item_struct))
}
#[proc_macro_attribute]
pub fn event(_attr: TokenStream, item: TokenStream) -> TokenStream {
    let mut item_struct = parse_macro_input!(item as ItemStruct);

    // Create the derive attribute
    let new_attr = syn::parse_quote!(#[derive(serde::Serialize, serde::Deserialize, Eq, PartialEq, Clone, Debug, Default, glue::Event)]);
    item_struct.attrs.push(new_attr);

    // Generate the output tokens from the modified struct
    TokenStream::from(quote!(#item_struct))
}

#[proc_macro_derive(Event, attributes(topic))]
pub fn derive_event(input: TokenStream) -> TokenStream {
    let input_struct = parse_macro_input!(input as DeriveInput);
    let struct_name = &input_struct.ident;

    // check if the input is a struct
    let fields = if let syn::Data::Struct(data) = &input_struct.data {
        match &data.fields {
            syn::Fields::Named(fields) => &fields.named,
            _ => {
                panic!("Event can only be derived for structs with named fields");
            }
        }
    } else {
        panic!("Event can only be derived for structs");
    };

    // collect topic and data fields, as well as field types in the struct
    let mut topic_fields = Vec::new();
    let mut data_fields = Vec::new();
    let mut field_types = Vec::new();

    for field in fields {
        let field_name = field.ident.clone().unwrap();
        let field_type = &field.ty;
        field_types.push(format!("{}", quote!(#field_type)));

        // distinguish between topic and data fields
        if field.attrs.iter().any(|attr| attr.path().is_ident("topic")) {
            topic_fields.push(field_name);
        } else {
            data_fields.push(field_name);
        }
    }

    // limit the number of topics to 4
    const TOPIC_LIMIT: usize = 4;
    if topic_fields.len() > TOPIC_LIMIT {
        panic!(
            "The number of topic fields must be less than or equal to {}",
            TOPIC_LIMIT
        );
    }

    let event_signature_input = format!("{}({})", struct_name, field_types.join(","));

    let expanded = quote! {
        impl glue::events::Event for #struct_name {
            fn signature(&self) -> [u8; 32] {
                let hash = glue::keccak256(#event_signature_input.as_bytes());
                let mut result = [0u8; 32];
                result.copy_from_slice(&hash);
                result
            }

            fn topics(&self) -> Vec<[u8; 32]> {
                let mut topics = Vec::new();
                #(
                    let topic_value = serde_json::to_string(&self.#topic_fields).unwrap();
                    // try hex decode, if failed, use directly
                    // TODO: temporary solution until we can handle bytes in serde
                    let topic_value = match glue::hex_to_bytes(&topic_value.trim_matches('"')) {
                        Some(bytes) => bytes,
                        None => topic_value.as_bytes().to_vec(),
                    };


                    let mut topic = [0u8; 32];
                    if topic_value.len() > 32 {
                        // if topic value is too long, use hash value
                        let hash = glue::keccak256(&topic_value);
                        topic.copy_from_slice(&hash);
                    } else {
                        // if topic value is short enough, use directly
                        // add padding to the left
                        let padding = 32 - topic_value.len();
                        for i in 0..padding {
                            topic[i] = 0;
                        }
                        topic[padding..].copy_from_slice(&topic_value);
                    }

                    topics.push(topic);
                )*
                topics
            }

            fn data(&self) -> Vec<Vec<u8>> {
                let mut data = Vec::new();
                #(
                    let data_value = serde_json::to_string(&self.#data_fields)
                        .unwrap()
                        .as_bytes()
                        .to_vec();
                    data.push(data_value);
                )*
                data
            }
        }
    };

    expanded.into()
}

#[proc_macro_attribute]
pub fn test(attr: TokenStream, item: TokenStream) -> TokenStream {
    let input = parse_macro_input!(item as ItemFn);

    let syn::ItemFn {
        attrs,
        vis,
        sig,
        block,
    } = input;

    let new_block = quote! {
        {
            glue::env::set_test_env();
            #block
        }
    };

    let output = quote! {
        #(#attrs)*
        #[test]
        #[serial_test::serial]
        #vis #sig
        #new_block
    };

    output.into()
}

#[proc_macro_attribute]
pub fn constructor(args: TokenStream, item: TokenStream) -> TokenStream {
    let input_fn = parse_macro_input!(item as ImplItemFn);

    // Error if method name is not new
    if input_fn.sig.ident != "new" {
        return TokenStream::from(quote! {
            compile_error!("The constructor function must be named `new`");
        });
    }

    let expanded = quote! {
        #input_fn
    };

    // print code
    // println!("{}", TokenStream::from(expanded.clone()).to_string());

    TokenStream::from(expanded)
}

#[proc_macro_attribute]
pub fn atomic(_attr: TokenStream, item: TokenStream) -> TokenStream {
    item
}

#[proc_macro_attribute]
pub fn readonly(_attr: TokenStream, item: TokenStream) -> TokenStream {
    item
}

// Define a procedural macro for binding Rust functions WebAssembly to the universal interface
#[proc_macro_attribute]
pub fn wasm_bind(_attr: TokenStream, item: TokenStream) -> TokenStream {
    // Parse the input function
    let input_fn: ItemFn = parse_macro_input!(item as ItemFn);

    // Extract relevant parts of the function
    let fn_name: &Ident = &input_fn.sig.ident;
    let closure_fn_name: Ident = format_ident!("{}", fn_name);
    let fn_inputs: &Punctuated<FnArg, Comma> = &input_fn.sig.inputs;
    let fn_output: &ReturnType = &input_fn.sig.output;
    let fn_block: &Box<Block> = &input_fn.block;

    // Process function arguments to create struct fields
    let mut struct_fields = Vec::new();
    let mut field_names: Vec<Ident> = Vec::new();
    let mut args: Vec<&FnArg> = Vec::new();
    // processing arguments
    for (i, arg) in fn_inputs.iter().enumerate() {
        args.push(arg);

        if let FnArg::Typed(PatType { ty, .. }) = arg {
            // Generate struct fields and field names
            // gen field name, like "p0", "p1", "p2"...
            let field_name = format_ident!("p{}", i.to_string());

            struct_fields.push(quote! { pub #field_name: #ty });
            field_names.push(field_name);
        }
    }

    // Convert function name to camel case for the params struct
    let fn_name_camel = fn_name
        .to_string()
        .split('_')
        .map(|word| {
            word.chars()
                .enumerate()
                .map(|(j, c)| {
                    if j == 0 {
                        // Capitalize the first letter of each word
                        c.to_uppercase().to_string()
                    } else {
                        // Other characters remain unchanged
                        c.to_string()
                    }
                })
                .collect::<String>()
        })
        .collect::<String>();

    // Construct a struct for function parameters
    let params_struct_name: Ident = format_ident!("_{}Params", fn_name_camel);

    // construct params struct
    let mut params_struct_code = quote! {};
    // optional params code
    let mut params_env_code = quote! {};
    params_struct_code.extend(quote! {
        #[derive(serde::Serialize, serde::Deserialize)]
        struct #params_struct_name {
            #(#struct_fields),*
        }
    });
    // Code for handling parameters
    params_env_code.extend(quote! {
        let func_args: glue::FuncArgs<#params_struct_name> = glue::json_ptr_to_object(input_ptr).unwrap();
        glue::env::set_env(func_args.envs);
        let params = func_args.args;
    });

    // Generate code for function return
    let mut return_code = quote! {};
    match fn_output {
        // for handling different return types
        ReturnType::Default => {
            // no return value
            return_code.extend(quote! {
                let #closure_fn_name = |#fn_inputs| #fn_output {
                    #fn_block
                };

                #closure_fn_name(#(params.#field_names),*);
                let serializable_result = glue::SerializableResult::default();
                glue::object_to_json_ptr(&serializable_result).unwrap()
            });
        }
        ReturnType::Type(_, type_box) => {
            // has return value

            // Call the hidden function with the struct as input
            return_code.extend(quote! {
                let #closure_fn_name = |#fn_inputs| #fn_output {
                    #fn_block
                };

                let result_values = #closure_fn_name(#(params.#field_names),*);
            });

            // Extract the return type name
            let mut return_type_name = quote! { #type_box }.to_string();
            let regex = Regex::new(r"<.*?>").unwrap();
            return_type_name = regex.replace_all(&return_type_name, "").to_string();

            // Check if the return type is a Result
            if return_type_name.contains("Result") {
                return_code.extend(quote! {
                    let serializable_result = glue::SerializableResult::from(result_values);
                });
            } else {
                // If the return type is not a Result, wrap it in a Result
                return_code.extend(quote! {
                    let serializable_result = glue::SerializableResult::from(Ok(result_values));
                });
            }

            // Convert the return value to a JSON string
            return_code.extend(quote! {
                glue::object_to_json_ptr(&serializable_result).unwrap()
            });
        }
    }

    // Generate the final code
    // Expose the function to WebAssembly with the original name
    // Convert the function parameters to a struct
    // Call the hidden function with the struct as input
    let gen = quote! {
        #[cfg(target_arch = "wasm32")]
        #params_struct_code

        #[cfg(target_arch = "wasm32")]
        #[no_mangle]
        pub extern "C" fn #fn_name(input_ptr: *mut std::ffi::c_char) -> *mut std::ffi::c_char {
            #params_env_code
            #return_code
        }
    };

    gen.into()
}
