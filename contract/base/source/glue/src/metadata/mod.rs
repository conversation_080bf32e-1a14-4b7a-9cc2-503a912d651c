use serde::{Deserialize, Serialize};

mod specs;

pub use specs::{
    ContractSpec, ContractSpecBuilder, FunctionParamSpec, FunctionReturnTypeSpec, FunctionSpec,
};

#[cfg(test)]
mod tests;

/// The serialized metadata format (which this represents) is different from the
/// version of this crate or the contract for Rust semantic versioning purposes.
const METADATA_VERSION: u64 = 1;

#[derive(Debug, Serialize, Deserialize)]
pub struct GlueProject {
    version: u64,
    // TODO: storage layout
    spec: ContractSpec,
}

impl GlueProject {
    pub fn new(spec: ContractSpec) -> Self {
        Self {
            version: METADATA_VERSION,
            spec,
        }
    }
}
