use crate::metadata::specs::*;

#[test]
fn test_contract_spec_to_json() {
    // 1. Define the contract's constructor
    let constructor = FunctionSpec::from_label("init".to_string())
        .readonly(false)
        .args(vec![FunctionParamSpec::new(
            "owner".to_string(),
            "address".to_string(),
        )])
        .return_type(FunctionReturnTypeSpec::new("void".to_string()))
        .build();

    // 2. Define a readonly function
    let get_value_fn = FunctionSpec::from_label("get_value".to_string())
        .readonly(true)
        .args(vec![])
        .return_type(FunctionReturnTypeSpec::new("i32".to_string()))
        .build();

    // 3. Define an atomic function
    let set_value_fn = FunctionSpec::from_label("set_value".to_string())
        .readonly(false)
        .args(vec![FunctionParamSpec::new(
            "val".to_string(),
            "i32".to_string(),
        )])
        .return_type(FunctionReturnTypeSpec::new("void".to_string()))
        .build();

    // 4. Generate the final contract specification using the Builder Pattern
    let contract_spec = ContractSpec::new()
        .constructor(constructor)
        .readonly_functions(vec![get_value_fn])
        .atomic_functions(vec![set_value_fn])
        .build();

    // 5. Serialize the contract specification to JSON format
    let pretty_json = serde_json::to_string_pretty(&contract_spec).unwrap();
    print!("{}", pretty_json);
}
