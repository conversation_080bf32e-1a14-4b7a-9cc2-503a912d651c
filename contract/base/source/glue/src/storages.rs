#![allow(unused_imports, dead_code, unused_variables)]

use std::collections::HashMap;
#[cfg(target_arch = "wasm32")]
use std::ffi::c_char;
use std::marker::PhantomData;
use std::sync::{LazyLock, Mutex};

#[cfg(target_arch = "wasm32")]
use crate::{deallocate_c_char, get_string, string_to_c_char};

#[cfg(not(target_arch = "wasm32"))]
/// Change list for storage, this is only used in test environment
pub(crate) static CHANGE_LIST: LazyLock<Mutex<HashMap<String, Option<String>>>> =
    LazyLock::new(|| Mutex::new(HashMap::new()));

#[cfg(not(target_arch = "wasm32"))]
/// Clear the change list, this is only used in test environment
pub(crate) fn clear_change_list() {
    let mut storage = CHANGE_LIST.lock().unwrap();
    storage.clear();
}

#[cfg(target_arch = "wasm32")]
extern "C" {
    fn get_slot(key: *mut c_char) -> *mut c_char;
    fn put_slot(key: *mut c_char, value: *mut c_char);
    fn delete_slot(key: *mut c_char);
    fn readonly_panic();
}

#[inline]
pub(crate) fn storage_put(key: &str, value: &str) {
    #[cfg(not(target_arch = "wasm32"))]
    {
        let mut storage = CHANGE_LIST.lock().unwrap();
        // fix insert success but return None value for test env
        // HashMap.insert returns None if the key is not present in the map
        let _is_insert = storage
            .insert(String::from(key), Option::from(String::from(value)))
            .is_none();
    }

    #[cfg(target_arch = "wasm32")]
    unsafe {
        let env = crate::env::get_env();
        if env.readonly {
            readonly_panic();
            panic!("storage is readonly");
        }

        let key_ptr = string_to_c_char(key);
        let value_ptr = string_to_c_char(value);

        put_slot(key_ptr, value_ptr);
    }
}

#[inline]
pub(crate) fn storage_get(key: &str) -> Option<String> {
    #[cfg(not(target_arch = "wasm32"))]
    {
        let storage = CHANGE_LIST.lock().unwrap();
        return storage.get(key).cloned().unwrap_or(None);
    }

    #[cfg(target_arch = "wasm32")]
    unsafe {
        let key_ptr = string_to_c_char(key);
        let env = crate::env::get_env();

        let value_ptr = get_slot(key_ptr);
        let value = get_string(value_ptr);
        deallocate_c_char(value_ptr);
        value
    }
}

#[inline]
pub(crate) fn storage_delete(key: &str) {
    #[cfg(not(target_arch = "wasm32"))]
    {
        let mut storage = CHANGE_LIST.lock().unwrap();
        storage.insert(key.to_string(), None);
    }

    #[cfg(target_arch = "wasm32")]
    unsafe {
        let env = crate::env::get_env();
        if env.readonly {
            readonly_panic();
            panic!("storage is readonly");
        }

        let key_ptr = string_to_c_char(key);
        delete_slot(key_ptr);
    }
}

#[inline]
pub(crate) fn storage_pop(key: &str) -> Option<String> {
    let value = storage_get(key);
    storage_delete(key);
    value
}

/// Define a storage field used in glue::storage struct field
pub struct StorageField<
    V: serde::Serialize + for<'a> serde::Deserialize<'a>,
    KeyPrefix: StorageKey = (),
> {
    _marker: PhantomData<fn() -> (V, KeyPrefix)>,
    key: String,
}

impl<V: serde::Serialize + for<'a> serde::Deserialize<'a>, KeyPrefix: StorageKey> Default
    for StorageField<V, KeyPrefix>
{
    #[inline]
    fn default() -> Self {
        Self {
            _marker: PhantomData,
            key: KeyPrefix::key(),
        }
    }
}

impl<V: serde::Serialize + for<'a> serde::Deserialize<'a>, KeyPrefix: StorageKey>
    StorageField<V, KeyPrefix>
{
    #[inline]
    pub fn new(key: &V) -> Self {
        let field = Self {
            _marker: PhantomData,
            key: KeyPrefix::key(),
        };
        field.set(key);
        field
    }

    #[inline]
    pub fn set(&self, value: &V) {
        storage_put(&self.key, &serde_json::to_string(value).unwrap());
    }

    #[inline]
    pub fn get(&self) -> V {
        let value = storage_get(&self.key).unwrap();
        serde_json::from_str(&value).unwrap()
    }
}

pub trait StorageKey {
    /// Returns the storage key.
    fn key() -> String;
}

impl StorageKey for () {
    #[inline]
    fn key() -> String {
        "".to_string()
    }
}
