use crate::collections::Map;
use crate::StorageKey;

pub struct Vec<V: serde::Serialize + for<'a> serde::Deserialize<'a>, KeyType: StorageKey = ()> {
    elements: Map<u32, V, KeyType>,
}

impl<V, KeyType> Default for Vec<V, KeyType>
where
    V: serde::Serialize + for<'de> serde::Deserialize<'de>,
    KeyType: StorageKey,
{
    fn default() -> Self {
        Self::new()
    }
}

impl<V, KeyType> Vec<V, KeyType>
where
    V: serde::Serialize + for<'de> serde::Deserialize<'de>,
    KeyType: StorageKey,
{
    pub fn new() -> Self {
        Self {
            elements: Map::default(),
        }
    }

    #[inline]
    pub fn len(&self) -> u32 {
        self.elements.size()
    }

    #[inline]
    pub fn is_empty(&self) -> bool {
        self.len() == 0
    }

    #[inline]
    pub fn push(&mut self, value: &V) {
        let slot = self.len();

        self.elements.insert(&slot, value)
    }

    #[inline]
    pub fn pop(&mut self) -> Option<V> {
        if self.is_empty() {
            return None;
        }
        self.elements.take(&(self.len() - 1))
    }

    #[inline]
    pub fn get(&self, index: u32) -> Option<V> {
        self.elements.get(&index)
    }

    #[inline]
    pub fn set(&mut self, index: u32, value: &V) -> anyhow::Result<()> {
        if index >= self.len() {
            return Err(anyhow::anyhow!("index out of bounds"));
        }
        self.elements.insert(&index, value);
        Ok(())
    }

    // peek the last element
    #[inline]
    pub fn peek(&self) -> Option<V> {
        if self.len() == 0 {
            return None;
        }
        self.get(self.len() - 1)
    }

    /// Clears the value of the element at `index`. It doesn't change the length of the
    /// vector.
    #[inline]
    pub fn clear_at(&mut self, index: u32) {
        assert!(index < self.len());

        self.elements.remove_not_change_size(&index);
    }

    #[inline]
    pub fn clear(&mut self) {
        for i in 0..self.len() {
            self.elements.remove_not_change_size(&i);
        }
        self.elements.set_size(0);
    }

    // This consumes a lot
    #[inline]
    pub fn iter(&self) -> impl Iterator<Item = Option<V>> + '_ {
        (0..self.len()).map(move |i| self.get(i))
    }

    // get the first element
    #[inline]
    pub fn first(&self) -> Option<V> {
        if self.len() == 0 {
            return None;
        }
        self.get(0)
    }

    // get the last element
    #[inline]
    pub fn last(&self) -> Option<V> {
        if self.len() == 0 {
            return None;
        }
        self.get(self.len() - 1)
    }
}
