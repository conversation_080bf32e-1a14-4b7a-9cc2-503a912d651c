use crate::index::{delete_index, get_index_iter, put_index, IndexIterator};
use crate::string_to_c_char;
use anyhow;
use std::ffi::c_char;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Index<V: serde::Serialize + for<'a> serde::Deserialize<'a>> {
    index_key_fn: fn(&V) -> Vec<String>,
}

impl<V> Default for Index<V>
where
    V: serde::Serialize + for<'a> serde::Deserialize<'a>,
{
    fn default() -> Self {
        fn default_key_fn<V>(_value: &V) -> Vec<String> {
            Vec::new()
        }
        Self {
            index_key_fn: default_key_fn,
        }
    }
}

impl<V> Index<V>
where
    V: serde::Serialize + for<'a> serde::Deserialize<'a>,
{
    pub fn new(index_key_fn: fn(&V) -> Vec<String>) -> Self {
        Self { index_key_fn }
    }

    /// Create a namespaced key - let Python manage the full key structure
    fn create_namespaced_key(index_name: &str, original_key: &str) -> String {
        // Let Python completely manage the key structure
        // Only add minimal index name prefix to avoid conflicts between different indexes
        format!("{}#{}", index_name, original_key)
    }

    pub fn insert(&mut self, value: &V, actual_key: &str, index_name: &str) {
        let index_key_strs = (self.index_key_fn)(value);
        for index_key_str in index_key_strs {
            // Use fixed-length namespace prefix to preserve dictionary order
            let namespaced_key = Self::create_namespaced_key(index_name, &index_key_str);
            let index_key_ptr = string_to_c_char(&namespaced_key);
            let index_value_ptr = string_to_c_char(actual_key);
            unsafe {
                put_index(index_key_ptr, index_value_ptr);
            }
        }
    }

    pub fn remove(&mut self, value: &V, index_name: &str) {
        let index_key_strs = (self.index_key_fn)(value);
        for index_key_str in index_key_strs {
            // Use fixed-length namespace prefix to preserve dictionary order
            let namespaced_key = Self::create_namespaced_key(index_name, &index_key_str);
            let index_key_ptr = string_to_c_char(&namespaced_key);
            unsafe {
                delete_index(index_key_ptr);
            }
        }
    }

    pub fn iter(
        &self,
        reverse: bool,
        start: &str,
        end: &str,
        index_name: &str,
    ) -> NamespacedIndexIterator<V> {
        // Apply simple namespace prefix to avoid conflicts between different indexes
        let namespaced_start = Self::create_namespaced_key(index_name, start);
        let namespaced_end = Self::create_namespaced_key(index_name, end);

        let iter_id: *mut c_char = unsafe {
            get_index_iter(
                reverse,
                string_to_c_char(&namespaced_start),
                string_to_c_char(&namespaced_end),
            )
        };
        let base_iterator = IndexIterator::new(iter_id);

        // Wrap it with namespace prefix removal
        NamespacedIndexIterator::new(base_iterator, index_name.to_string(), self.index_key_fn)
    }
}

/// Iterator that removes namespace prefix from keys, ensuring clean interface
pub struct NamespacedIndexIterator<V: serde::Serialize + for<'a> serde::Deserialize<'a>> {
    base_iterator: IndexIterator<V>,
}

impl<V> NamespacedIndexIterator<V>
where
    V: serde::Serialize + for<'a> serde::Deserialize<'a>,
{
    pub fn new(
        base_iterator: IndexIterator<V>,
        _index_name: String,
        _index_key_fn: fn(&V) -> Vec<String>,
    ) -> Self {
        Self { base_iterator }
    }

    pub fn next(&mut self) -> bool {
        // Simply delegate to the base iterator
        self.base_iterator.next()
    }

    pub fn key(&self) -> anyhow::Result<String> {
        let full_key = self.base_iterator.key()?;

        // Remove namespace prefix (format: "index_name#actual_key")
        if let Some(hash_pos) = full_key.find('#') {
            Ok(full_key[hash_pos + 1..].to_string())
        } else {
            // Fallback for keys without namespace (shouldn't happen with new implementation)
            Ok(full_key)
        }
    }

    pub fn value(&self) -> anyhow::Result<V> {
        self.base_iterator.value()
    }
}
