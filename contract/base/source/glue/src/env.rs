use std::collections::HashMap;
use std::sync::{LazyLock, RwLock};

#[derive(<PERSON><PERSON>, Debug, Eq, <PERSON>ialEq, Default)]
pub struct Environment {
    // block env
    pub block_height: u64,
    // transaction env
    pub sender: String,
    pub transaction_hash: String,
    pub transaction_index: u32,
    pub transaction_timestamp: u64,
    // contract env
    pub callers: Vec<String>,
    pub contract_address: String,
    // contract configs
    pub readonly: bool,
    pub register: bool,
    // TODO: add public key, private key, etc.
}

static ENV: LazyLock<RwLock<Environment>> = LazyLock::new(|| RwLock::new(Environment::default()));

#[cfg(not(target_arch = "wasm32"))]
pub fn set_test_env() {
    // use default env
    set_env(HashMap::new());
    // clear change list
    crate::storages::clear_change_list();
    // clear index container
    crate::index::clear_index_container();
}

pub fn set_env(envs: HashMap<String, String>) {
    let default_hex = || "0x1306878a34a2987b91dd566dbbff76b1a3b1d545318a50d01d946bc311556700";
    let get_or_default =
        |key: &str, default: &str| envs.get(key).unwrap_or(&default.to_string()).to_string();
    let parse_or_default = |key: &str, default: &str| {
        envs.get(key)
            .unwrap_or(&default.to_string())
            .parse()
            .unwrap()
    };
    let parse_bool_or_default = |key: &str, default: bool| {
        envs.get(key)
            .map_or(default, |v| v.parse().unwrap_or(default))
    };

    // example callers input: address1,address2,address3
    let parse_callers = |key: &str, default: &str| {
        let callers = get_or_default(key, default);
        callers.split(',').map(|s| s.trim().to_string()).collect()
    };

    *ENV.write().unwrap() = Environment {
        block_height: parse_or_default("block_height", "0"),
        sender: get_or_default("sender", &default_hex()),
        transaction_hash: get_or_default("transaction_hash", &default_hex()),
        transaction_index: parse_or_default("transaction_index", "1") as u32,
        transaction_timestamp: parse_or_default("transaction_timestamp", "0"),
        callers: parse_callers("callers", &default_hex()),
        contract_address: get_or_default("contract_address", &default_hex()),
        readonly: parse_bool_or_default("readonly", false),
        register: parse_bool_or_default("register", false),
    }
}

#[inline]
pub fn get_env<'a>() -> std::sync::RwLockReadGuard<'a, Environment> {
    ENV.read().unwrap()
}

impl Environment {
    #[cfg(not(target_arch = "wasm32"))]
    pub fn equal_test_env(&self, test_env: &TestEnvironment) -> bool {
        self.block_height == test_env.block_height
            && self.sender == test_env.sender
            && self.callers == test_env.callers
            && self.transaction_hash == test_env.transaction_hash
            && self.transaction_index == test_env.transaction_index
            && self.transaction_timestamp == test_env.transaction_timestamp
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[derive(Clone, Debug, Eq, PartialEq, Default)]
pub struct TestEnvironment {
    // block env
    pub block_height: u64,
    // transaction env
    pub sender: String,
    pub callers: Vec<String>,
    pub transaction_hash: String,
    pub transaction_index: u32,
    pub transaction_timestamp: u64,
}

#[cfg(not(target_arch = "wasm32"))]
pub struct TestEnvironmentBuilder {
    sender: Option<String>,
    callers: Option<Vec<String>>,
    block_height: Option<u64>,
    transaction_hash: Option<String>,
    transaction_index: Option<u32>,
    transaction_timestamp: Option<u64>,
}

#[cfg(not(target_arch = "wasm32"))]
impl TestEnvironmentBuilder {
    pub fn new() -> Self {
        Self {
            sender: None,
            callers: None,
            block_height: None,
            transaction_hash: None,
            transaction_index: None,
            transaction_timestamp: None,
        }
    }

    pub fn block_height(mut self, block_height: u64) -> Self {
        self.block_height = Some(block_height);
        self
    }

    pub fn sender(mut self, sender: &str) -> Self {
        self.sender = Some(sender.to_string());
        self
    }

    pub fn callers(mut self, callers: &str) -> Self {
        self.callers = Some(callers.split(',').map(|s| s.trim().to_string()).collect());
        self
    }

    pub fn transaction_hash(mut self, transaction_hash: &str) -> Self {
        self.transaction_hash = Some(transaction_hash.to_string());
        self
    }

    pub fn transaction_index(mut self, transaction_index: u32) -> Self {
        self.transaction_index = Some(transaction_index);
        self
    }

    pub fn transaction_timestamp(mut self, transaction_timestamp: u64) -> Self {
        self.transaction_timestamp = Some(transaction_timestamp);
        self
    }

    pub fn build(self) -> TestEnvironment {
        TestEnvironment {
            block_height: self.block_height.unwrap_or(0),
            sender: self.sender.unwrap_or_default(),
            callers: self.callers.unwrap_or_default(),
            transaction_hash: self.transaction_hash.unwrap_or_default(),
            transaction_index: self.transaction_index.unwrap_or(0),
            transaction_timestamp: self.transaction_timestamp.unwrap_or(0),
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
pub fn update_test_env(new_env: &TestEnvironment) {
    let mut env = ENV.write().unwrap();
    env.sender = new_env.sender.clone();
    env.callers = new_env.callers.clone();
    env.block_height = new_env.block_height;
    env.transaction_hash = new_env.transaction_hash.clone();
    env.transaction_index = new_env.transaction_index;
    env.transaction_timestamp = new_env.transaction_timestamp;
}

#[cfg(not(target_arch = "wasm32"))]
pub fn set_from_test_env(test_env: &TestEnvironment) {
    let mut env = ENV.write().unwrap();
    env.block_height = test_env.block_height;
    env.sender = test_env.sender.clone();
    env.callers = test_env.callers.clone();
    env.transaction_hash = test_env.transaction_hash.clone();
    env.transaction_index = test_env.transaction_index;
    env.transaction_timestamp = test_env.transaction_timestamp;
}
