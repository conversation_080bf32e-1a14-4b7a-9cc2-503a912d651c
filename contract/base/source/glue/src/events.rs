#![allow(unused_imports, dead_code, unused_variables)]

use serde::{Deserialize, Serialize};
use std::sync::{LazyLock, Mutex};

#[cfg(target_arch = "wasm32")]
use {crate::string_to_c_char, std::ffi::c_char};

pub trait Event: Serialize + Deserialize<'static> {
    /// Returns the unique signature of the event as a fixed-size array `[u8; 32]`.
    /// It can be automatically calculated by the event's fields:
    /// `signature = keccak256("EventName(field1Type,field2Type,...)")`
    fn signature(&self) -> [u8; 32];

    /// Returns the indexed fields (topics) of the event as a vector of `[u8; 32]` values.
    fn topics(&self) -> Vec<[u8; 32]>;

    /// Returns the data (non-topic fields) of the event as a vector of bytes.
    fn data(&self) -> Vec<Vec<u8>>;
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SerializedEvent {
    signature: String,
    topics: Vec<String>,
    data: Vec<String>,
}

// Event list to storage the serialized event
pub(crate) static EVENT_LIST: LazyLock<Mutex<Vec<SerializedEvent>>> =
    LazyLock::new(|| Mutex::new(Vec::new()));

/// Emit an event to the event list.
pub fn emit_event<E: Event>(event: E) {
    let signature = crate::utils::bytes_to_hex(event.signature());
    let topics = event
        .topics()
        .iter()
        .map(crate::utils::bytes_to_hex)
        .collect();
    let data = event
        .data()
        .iter()
        .map(crate::utils::bytes_to_hex)
        .collect();

    let serialized_event = SerializedEvent {
        signature,
        topics,
        data,
    };

    #[cfg(not(target_arch = "wasm32"))]
    {
        EVENT_LIST.lock().unwrap().push(serialized_event);
    }

    #[cfg(target_arch = "wasm32")]
    {
        unsafe {
            let serialized_event_str = serde_json::to_string(&serialized_event).unwrap();
            let serialized_event_ptr = string_to_c_char(&serialized_event_str);

            emit(serialized_event_ptr);
        }
    }
}

#[cfg(target_arch = "wasm32")]
extern "C" {
    fn emit(serialized_event: *mut c_char);
}
