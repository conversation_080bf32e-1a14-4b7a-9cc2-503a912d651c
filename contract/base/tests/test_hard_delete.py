import json
import time
import pytest
from vm import ContractTester

vcloudClient = ContractTester(
    wasmName="vcloud_db",
)


@pytest.fixture(autouse=True)
def register_contract():
    vcloudClient.constructor()


def create_test_user_service(service_id="test_service_1", address="0xtest_address_1", provider="test_provider_1", status="active"):
    """Create a test UserService with realistic data"""
    unique_suffix = str(int(time.time() * 1000000))[-6:]
    return {
        "_id": f"{service_id}_{unique_suffix}",
        "duration": 3600,
        "amount": 100.0,
        "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
        "provider": provider,
        "providerAddress": "0xprovider_address_123",
        "address": address,
        "serviceID": "service_type_compute",
        "serviceActivated": True,
        "status": status,
        "serviceOptions": {
            "cpu": "4",
            "memory": "8GB",
            "storage": "100GB"
        },
        "createdAt": 0,
        "updatedAt": 0,
        "deletedAt": 0,
        "endAt": 0,
        "serviceActivateTS": 0,
        "serviceRunningTS": 0,
        "serviceAbortTS": 0,
        "serviceDoneTS": 0,
        "serviceRefundTS": 0,
        "service": "compute",
        "createdAddr": address,
        "labelHash": "0x123456789abcdef"
    }


def create_test_order(order_id="test_order_1", address="0xtest_address_1", provider="test_provider_1", status="pending"):
    """Create a test Order with realistic data"""
    unique_suffix = str(int(time.time() * 1000000))[-6:]
    return {
        "_id": f"{order_id}_{unique_suffix}",
        "createdAt": 0,
        "updatedAt": 0,
        "deletedAt": 0,
        "type": "compute_order",
        "amount": 500.0,
        "amountPaid": 0.0,
        "provider": provider,
        "address": address,
        "recipient": "0xrecipient_address_123",
        "status": status,
        "lastPaymentTS": 0,
        "paidTS": 0,
        "filedTS": 0,
        "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
        "userServiceIDs": ["service_1", "service_2"],
        "items": [
            {
                "userServiceID": "service_1",
                "duration": 3600,
                "amount": 250.0
            },
            {
                "userServiceID": "service_2", 
                "duration": 7200,
                "amount": 250.0
            }
        ]
    }


def test_hard_delete_single_user_service():
    """Test hard delete of a single user service"""
    # Create test service
    service = create_test_user_service("hard_delete_test", "0xhard_delete_addr", "hard_delete_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    service_id = result
    
    # Verify service exists
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service_id)
    assert err is None
    
    # Hard delete the service
    filter_params = {
        "ids": [service_id]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete", str, "user_service", filter_json)
    assert err is None
    
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1
    
    # Verify service is completely gone (not just soft deleted)
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service_id)
    assert err is not None
    assert "not found" in str(err)
    
    # Verify it doesn't appear in any queries
    query_params = {
        "address": "0xhard_delete_addr",
        "limit": 100,
        "offset": 0
    }
    query_json = json.dumps(query_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", query_json)
    assert err is None
    
    found_services = json.loads(result)
    assert not any(s["_id"] == service_id for s in found_services)


def test_hard_delete_single_order():
    """Test hard delete of a single order"""
    # Create test order
    order = create_test_order("hard_delete_order_test", "0xhard_delete_order_addr", "hard_delete_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None
    order_id = result
    
    # Verify order exists
    result, err = vcloudClient.executeReadOnly("get_order", str, order_id)
    assert err is None
    
    # Hard delete the order
    filter_params = {
        "order_ids": [order_id]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete", str, "order", filter_json)
    assert err is None
    
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1
    
    # Verify order is completely gone (not just soft deleted)
    result, err = vcloudClient.executeReadOnly("get_order", str, order_id)
    assert err is not None
    assert "not found" in str(err)
    
    # Verify it doesn't appear in any queries
    query_params = {
        "order_address": "0xhard_delete_order_addr",
        "limit": 100,
        "offset": 0
    }
    query_json = json.dumps(query_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", query_json)
    assert err is None
    
    found_orders = json.loads(result)
    assert not any(o["_id"] == order_id for o in found_orders)


def test_hard_delete_many_user_services():
    """Test hard delete of multiple user services"""
    # Create test services
    services = [
        create_test_user_service("hard_delete_many_1", "0xhard_delete_many_addr", "hard_delete_many_provider", "active"),
        create_test_user_service("hard_delete_many_2", "0xhard_delete_many_addr", "hard_delete_many_provider", "active"),
        create_test_user_service("hard_delete_many_3", "0xhard_delete_many_addr", "hard_delete_many_provider", "inactive"),
    ]
    
    service_ids = []
    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None
        service_ids.append(result)
    
    # Verify all services exist
    for service_id in service_ids:
        result, err = vcloudClient.executeReadOnly("get_user_service", str, service_id)
        assert err is None
    
    # Hard delete services with status "active"
    filter_params = {
        "address": "0xhard_delete_many_addr",
        "status": "active"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete_many", str, "user_service", filter_json)
    assert err is None
    
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 2  # Should delete 2 active services
    
    # Verify active services are completely gone
    for i, service_id in enumerate(service_ids[:2]):  # First 2 were active
        result, err = vcloudClient.executeReadOnly("get_user_service", str, service_id)
        assert err is not None
        assert "not found" in str(err)
    
    # Verify inactive service still exists
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service_ids[2])
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["status"] == "inactive"


def test_hard_delete_many_orders():
    """Test hard delete of multiple orders"""
    # Create test orders
    orders = [
        create_test_order("hard_delete_many_order_1", "0xhard_delete_many_order_addr", "hard_delete_many_order_provider", "pending"),
        create_test_order("hard_delete_many_order_2", "0xhard_delete_many_order_addr", "hard_delete_many_order_provider", "pending"),
        create_test_order("hard_delete_many_order_3", "0xhard_delete_many_order_addr", "hard_delete_many_order_provider", "paid"),
    ]
    
    order_ids = []
    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None
        order_ids.append(result)
    
    # Verify all orders exist
    for order_id in order_ids:
        result, err = vcloudClient.executeReadOnly("get_order", str, order_id)
        assert err is None
    
    # Hard delete orders with status "pending"
    filter_params = {
        "order_address": "0xhard_delete_many_order_addr",
        "order_statuses": ["pending"]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete_many", str, "order", filter_json)
    assert err is None
    
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 2  # Should delete 2 pending orders
    
    # Verify pending orders are completely gone
    for i, order_id in enumerate(order_ids[:2]):  # First 2 were pending
        result, err = vcloudClient.executeReadOnly("get_order", str, order_id)
        assert err is not None
        assert "not found" in str(err)
    
    # Verify paid order still exists
    result, err = vcloudClient.executeReadOnly("get_order", str, order_ids[2])
    assert err is None
    retrieved_order = json.loads(result)
    assert retrieved_order["status"] == "paid"


def test_hard_delete_nonexistent_records():
    """Test hard delete of non-existent records"""
    # Try to delete non-existent user service
    filter_params = {
        "ids": ["non_existent_service_id"]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete", str, "user_service", filter_json)
    assert err is None
    
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 0
    
    # Try to delete non-existent order
    filter_params = {
        "order_ids": ["non_existent_order_id"]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete", str, "order", filter_json)
    assert err is None
    
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 0
