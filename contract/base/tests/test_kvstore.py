# import base58
import pytest

from vm import ContractTester, resetTesterState

kvstoreContractClient = ContractTester(
    address="0x6548ce9a84ff9f46ceac227ef490f18d3d908d8f5ece98898a7043276c6d8407",
    wasmName="kvstore",
)


@pytest.fixture(autouse=True)
def clean_db():
    resetTesterState()


@pytest.fixture(autouse=True)
def register_contract():
    kvstoreContractClient.constructor()


# get sender address from env to validate
def test_kvstore():
    # publicKey = "0x9a81270552d30238f0a8fc7ce7b5c114685d25dcf795b3072e63c541d565bc42"
    # privateKey = "0x01c16a3ddf49312573e04b954ad48fc6db8efdc5fa49af216f731d4aa366b000187f4358665a0807044e1aeed941abc3f5d15d6f295b4257db567076aaa3b5e3"
    # address = "0xbd059c3365b6a51553144a57cf01686f9c9cf50200a7a3be1f"

    # set sender address to env for testing
    envs = {
        "sender": "0xbd059c3365b6a51553144a57cf01686f9c9cf50200a7a3be1f",
    }
    result, err = kvstoreContractClient.executeWithEnv(envs, "set_value", None, "key1", "item1")
    print(f"result: {result}")
    print(f"err: {err}")
    assert err is None

    result, err = kvstoreContractClient.executeReadOnlyWithEnv(None, "get_value", str, "key1", envs["sender"])
    assert result == "item1"
    assert err is None

    # cannot get value with wrong sender
    envs_1 = {
        "sender": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
    }
    result, err = kvstoreContractClient.executeReadOnlyWithEnv(None, "get_value", str, "key1", envs_1["sender"])
    assert err is None
    assert result == ""
