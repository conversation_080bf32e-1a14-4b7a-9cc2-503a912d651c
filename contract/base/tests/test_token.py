import pytest

from common.encode import hexToBytes
from vm import ContractTester

contractClient = ContractTester("token")


@pytest.fixture(autouse=True)
def register_contract():
    print("Register contract")
    contractClient.constructor("vgraph_token")


def test_register_contract_and_run_success():
    ALICE = "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"
    BOB = "0xcb50efe8f3aba5fdddbac0618884fc5c010c7d9500b7d4f1c1"

    print("Issue 100 to ALICE")
    result, err = contractClient.execute("issue", None, ALICE, 100)
    assert err is None

    aliceBalance, err = contractClient.executeReadOnly("balance", int, ALICE)
    assert err is None
    assert aliceBalance == 100
    print(f"ALICE balance before transfer: {aliceBalance}")
    bobBalance, err = contractClient.executeReadOnly("balance", int, BOB)
    assert err is None
    assert bobBalance == 0
    print(f"BOB balance before transfer: {bobBalance}")

    print("Transfer 10 from ALICE to BOB")
    envs = {
        "sender": ALICE,
    }
    result, err = contractClient.executeWithEnv(envs, "transfer", None, ALICE, BOB, 10)
    assert err is None

    aliceBalance, err = contractClient.executeReadOnly("balance", int, ALICE)
    assert err is None
    assert aliceBalance == 90
    print(f"ALICE balance after transfer: {aliceBalance}")
    bobBalance, err = contractClient.executeReadOnly("balance", int, BOB)
    assert err is None
    assert bobBalance == 10
    print(f"BOB balance after transfer: {bobBalance}")

    # test transfer without env
    result, err = contractClient.execute("transfer", None, ALICE, BOB, 10)
    assert err is not None

    # check event
    logs = ContractTester.state.getAllLogs()
    assert len(logs) == 2  # 1 issue, 1 transfer
    log = logs[0]
    assert log.contract_address == hexToBytes(contractClient.addressHex)
    assert len(log.topics) == 2
    assert log.topics[1].lstrip("0x").lstrip("0") == ALICE.lstrip("0x").lstrip("0")

    log = logs[1]
    assert log.contract_address == hexToBytes(contractClient.addressHex)
    assert len(log.topics) == 3
    assert log.topics[1].lstrip("0x").lstrip("0") == ALICE.lstrip("0x").lstrip("0")
    assert log.topics[2].lstrip("0x").lstrip("0") == BOB.lstrip("0x").lstrip("0")
    assert hexToBytes(log.data[0]) == b"10"


def test_register_contract_and_run_fail():
    print("issue -1 to acc")
    _, err = contractClient.execute("issue", None, "acc", -1)
    print(f"issue -1 error: {err}")
    assert err is not None

    print("Transfer -1 from acc to acc2")
    envs = {
        "sender": "acc",
    }
    _, err = contractClient.executeWithEnv(envs, "transfer", None, "acc", "acc2", -1)
    print(f"transfer -1 error: {err}")
    assert err is not None

    print("Transfer 100 from acc to acc2")
    envs = {
        "sender": "acc",
    }
    _, err = contractClient.executeWithEnv(envs, "transfer", None, "acc", "acc2", 100)
    print(f"transfer 100 error: {err}")
    assert err is not None
