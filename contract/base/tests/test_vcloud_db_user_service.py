import json
import time
import pytest
from vm import ContractTester

vcloudClient = ContractTester(
    wasmName="vcloud_db",
)


@pytest.fixture(autouse=True)
def register_contract():
    vcloudClient.constructor()

def create_test_user_service(service_id="test_service_1", address="0xtest_address_1", provider="test_provider_1", status="active"):
    """Create a test UserService with realistic data including all new fields using camelCase field names"""
    # Add timestamp to make IDs unique across test runs
    unique_suffix = str(int(time.time() * 1000000))[-6:]
    return {
        "_id": f"{service_id}_{unique_suffix}",
        "duration": 3600,
        "amount": 100.0,
        "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
        "provider": provider,
        "providerAddress": "0xprovider_address_123",
        "address": address,
        "serviceID": "service_type_compute",
        "serviceActivated": True,
        "status": status,
        "serviceOptions": {
            "cpu": "4",
            "memory": "8GB",
            "storage": "100GB"
        },
        "createdAt": 0,  # Will be set by contract if 0
        "updatedAt": 0,  # Will be set by contract if 0
        "deletedAt": 0,
        # New fields with default values
        "endAt": 0,
        "serviceActivateTS": 0,
        "serviceRunningTS": 0,
        "serviceAbortTS": 0,
        "serviceDoneTS": 0,
        "serviceRefundTS": 0,
        "service": "compute_service",
        "createdAddr": address,
        "labelHash": "0xabcdef1234567890"
    }


def test_create_user_service():
    """Test creating a new user service"""
    service = create_test_user_service()
    service_json = json.dumps(service)
    print(service_json)

    # Test successful creation
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None
    assert result == service["_id"]

    # Test duplicate ID error
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is not None
    assert "already exists" in str(err)


def test_get_user_service():
    """Test retrieving a user service by ID"""
    service = create_test_user_service("get_test_1")
    service_json = json.dumps(service)

    # Create the service first
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None

    # Test successful retrieval
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["_id"])
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["_id"] == service["_id"]
    assert retrieved_service["amount"] == service["amount"]
    assert retrieved_service["provider"] == service["provider"]

    # Test non-existent service
    result, err = vcloudClient.executeReadOnly("get_user_service", str, "non_existent_id")
    assert err is not None
    assert "not found" in str(err)


def test_update_user_service():
    """Test updating an existing user service"""
    service = create_test_user_service("update_test_1")
    service_json = json.dumps(service)

    # Create the service first
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None

    # Update the service
    service["amount"] = 200.0
    service["status"] = "suspended"
    updated_service_json = json.dumps(service)
    result, err = vcloudClient.execute("update_user_service", None, updated_service_json)
    assert err is None

    # Verify the update
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["_id"])
    assert err is None
    updated_service = json.loads(result)
    assert updated_service["amount"] == 200.0
    assert updated_service["status"] == "suspended"

    # Test updating non-existent service
    non_existent_service = create_test_user_service("non_existent")
    non_existent_json = json.dumps(non_existent_service)
    result, err = vcloudClient.execute("update_user_service", None, non_existent_json)
    assert err is not None
    assert "not found" in str(err)


def test_delete_user_service():
    """Test soft deleting a user service"""
    service = create_test_user_service("delete_test_1")
    service_json = json.dumps(service)

    # Create the service first
    result, err = vcloudClient.execute("create_user_service", str, service_json)
    assert err is None

    # Delete the service
    result, err = vcloudClient.execute("delete_user_service", None, service["_id"])
    assert err is None

    # Verify the service still exists but is marked as deleted
    result, err = vcloudClient.executeReadOnly("get_user_service", str, service["_id"])
    assert err is None
    deleted_service = json.loads(result)
    assert deleted_service["deletedAt"] > 0

    # Test deleting non-existent service
    result, err = vcloudClient.execute("delete_user_service", None, "non_existent_id")
    assert err is not None
    assert "not found" in str(err)


def test_batch_create_user_services():
    """Test batch creating multiple user services"""
    services = [
        create_test_user_service("batch_create_1", "0xaddr1", "provider1"),
        create_test_user_service("batch_create_2", "0xaddr2", "provider2"),
        create_test_user_service("batch_create_3", "0xaddr3", "provider3"),
    ]
    services_json = json.dumps(services)

    # Test successful batch creation
    result, err = vcloudClient.execute("batch_create_user_services", str, services_json)
    assert err is None
    batch_result = json.loads(result)
    assert batch_result["created"] == 3
    assert batch_result["updated"] == 0
    assert batch_result["deleted"] == 0
    assert len(batch_result["errors"]) == 0

    # Verify services were created
    for service in services:
        result, err = vcloudClient.executeReadOnly("get_user_service", str, service["_id"])
        assert err is None
        retrieved_service = json.loads(result)
        assert retrieved_service["_id"] == service["_id"]

    # Test batch creation with duplicate IDs
    duplicate_services = [
        services[0],  # Use the same service object (duplicate ID)
        create_test_user_service("batch_create_new"),  # New
    ]
    duplicate_services_json = json.dumps(duplicate_services)
    result, err = vcloudClient.execute("batch_create_user_services", str, duplicate_services_json)
    assert err is None
    batch_result = json.loads(result)
    assert batch_result["created"] == 1  # Only the new one
    assert len(batch_result["errors"]) == 1  # One error for duplicate


def test_count_user_services():
    """Test counting user services with various filters"""
    # Create test services with different attributes
    services = [
        create_test_user_service("count_test_1", "0xaddr1", "provider1", "active"),
        create_test_user_service("count_test_2", "0xaddr1", "provider2", "suspended"),
        create_test_user_service("count_test_3", "0xaddr2", "provider1", "active"),
        create_test_user_service("count_test_4", "0xaddr2", "provider2", "suspended"),
    ]

    # Create all services
    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("create_user_service", str, service_json)
        assert err is None

    # Test count by address
    result, err = vcloudClient.executeReadOnly("count_user_services", str, 
        None,           # ids
        "0xaddr1",      # address
        None,           # service_activated
        None,           # status
        None            # provider_address
    )
    assert err is None
    assert result == "2"

    # Test count by status
    result, err = vcloudClient.executeReadOnly("count_user_services", str, 
        None,           # ids
        None,           # address
        None,           # service_activated
        "active",       # status
        None            # provider_address
    )
    assert err is None
    assert result == "2"

    # Test count by address and status
    result, err = vcloudClient.executeReadOnly("count_user_services", str, 
        None,           # ids
        "0xaddr1",      # address
        None,           # service_activated
        "active",       # status
        None            # provider_address
    )
    assert err is None
    assert result == "1"

    # Test count with service_activated filter
    result, err = vcloudClient.executeReadOnly("count_user_services", str, 
        None,           # ids
        None,           # address
        True,           # service_activated
        None,           # status
        None            # provider_address
    )
    assert err is None
    assert result == "4"  # All services are activated by default

    # Test count with advanced query params
    query_params = {
        "address": "0xaddr1",
        "status": "active",
        "serviceActivated": True
    }
    result, err = vcloudClient.executeReadOnly("count_user_services_advanced", str, json.dumps(query_params))
    assert err is None
    assert result == "1"
