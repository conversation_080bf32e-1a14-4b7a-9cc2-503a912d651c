import pytest

from vm import ContractTester

from . import ADDRESS1, ADDRESS2

tokenaClient = ContractTester("token")
tokenbClient = ContractTester("token")
swapClient = ContractTester("token_swap")


@pytest.fixture(autouse=True)
def register_contract():
    tokenaClient.constructor("token_a")
    tokenbClient.constructor("token_b")
    swapClient.constructor()


def test_swap_success():
    print()
    # issue token a to address 1
    print("Issue token 1000 a to " + ADDRESS1)
    _, err = tokenaClient.execute("issue", None, ADDRESS1, 1000)
    assert err is None
    # issue token b to address 2
    print("Issue token 3000 b to " + ADDRESS2)
    _, err = tokenbClient.execute("issue", None, ADDRESS2, 3000)
    assert err is None

    # check balance
    # tokena:
    #  - address1: 1000
    #  - address2: 0
    # tokenb:
    #  - address1: 0
    #  - address2: 3000
    print("----- before swap -----")
    address1tokenaBalance, err = tokenaClient.executeReadOnly("balance", int, ADDRESS1)
    assert err is None
    assert address1tokenaBalance == 1000
    print("address1 tokena balance: " + str(address1tokenaBalance))
    address2tokenaBalance, err = tokenaClient.executeReadOnly("balance", int, ADDRESS2)
    assert err is None
    assert address2tokenaBalance == 0
    print("address2 tokena balance: " + str(address2tokenaBalance))

    address1tokenbBalance, err = tokenbClient.executeReadOnly("balance", int, ADDRESS1)
    assert err is None
    assert address1tokenbBalance == 0
    print("address1 tokenb balance: " + str(address1tokenbBalance))
    address2tokenbBalance, err = tokenbClient.executeReadOnly("balance", int, ADDRESS2)
    assert err is None
    assert address2tokenbBalance == 3000
    print("address2 tokenb balance: " + str(address2tokenbBalance))
    # swap token a to token b, 300 token a for 100 token b
    print("\nSwap token 300 a for token 100 b\n")
    envs = {"callers": ADDRESS1 + "," + ADDRESS2}
    _, err = swapClient.executeWithEnv(
        envs, "swap", None, tokenaClient.addressHex, ADDRESS1, 300, tokenbClient.addressHex, ADDRESS2, 100
    )
    assert err is None

    # check balance
    # tokena:
    #  - address1: 700
    #  - address2: 100
    # tokenb:
    #  - address1: 300
    #  - address2: 2900
    print("----- after swap -----")
    address1tokenaBalance, err = tokenaClient.executeReadOnly("balance", int, ADDRESS1)
    assert err is None
    assert address1tokenaBalance == 700
    print("address1 tokena balance: " + str(address1tokenaBalance))
    address2tokenaBalance, err = tokenaClient.executeReadOnly("balance", int, ADDRESS2)
    assert err is None
    assert address2tokenaBalance == 300
    print("address2 tokena balance: " + str(address2tokenaBalance))

    address1tokenbBalance, err = tokenbClient.executeReadOnly("balance", int, ADDRESS1)
    assert err is None
    assert address1tokenbBalance == 100
    print("address1 tokenb balance: " + str(address1tokenbBalance))
    address2tokenbBalance, err = tokenbClient.executeReadOnly("balance", int, ADDRESS2)
    assert err is None
    assert address2tokenbBalance == 2900
    print("address2 tokenb balance: " + str(address2tokenbBalance))


def test_swap_fail():
    # issue token a to address 1
    _, err = tokenaClient.execute("issue", None, ADDRESS1, 1000)
    assert err is None
    # issue token b to address 2
    _, err = tokenbClient.execute("issue", None, ADDRESS2, 3000)
    assert err is None

    # test case: swap without envs
    envs = {}
    _, err = swapClient.executeWithEnv(
        envs, "swap", None, tokenaClient.addressHex, ADDRESS1, 300, tokenbClient.addressHex, ADDRESS2, 100
    )
    assert err is not None

    # test case: swap with not enough token a
    envs = {"callers": ADDRESS1 + "," + ADDRESS2}
    _, err = swapClient.executeWithEnv(
        envs, "swap", None, tokenaClient.addressHex, ADDRESS1, 2000, tokenbClient.addressHex, ADDRESS2, 100
    )
    assert err is not None

    # test case: swap with not enough token b
    envs = {"callers": ADDRESS1 + "," + ADDRESS2}
    _, err = swapClient.executeWithEnv(
        envs, "swap", None, tokenaClient.addressHex, ADDRESS1, 300, tokenbClient.addressHex, ADDRESS2, 4000
    )
    assert err is not None

    # check balance
    # tokena:
    #  - address1: 1000
    #  - address2: 0
    # tokenb:
    #  - address1: 0
    #  - address2: 3000
    address1tokenaBalance, err = tokenaClient.executeReadOnly("balance", int, ADDRESS1)
    assert err is None
    assert address1tokenaBalance == 1000
    address2tokenaBalance, err = tokenaClient.executeReadOnly("balance", int, ADDRESS2)
    assert err is None
    assert address2tokenaBalance == 0

    address1tokenbBalance, err = tokenbClient.executeReadOnly("balance", int, ADDRESS1)
    assert err is None
    assert address1tokenbBalance == 0
    address2tokenbBalance, err = tokenbClient.executeReadOnly("balance", int, ADDRESS2)
    assert err is None
    assert address2tokenbBalance == 3000
