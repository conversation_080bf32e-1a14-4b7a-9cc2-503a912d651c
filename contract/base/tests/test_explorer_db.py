import json

import pytest

from vm import ContractTester

explorerClient = ContractTester(
    wasmName="explorer_db",
)


@pytest.fixture(autouse=True)
def register_contract():
    explorerClient.constructor()


def test_explorer():
    # Process all blocks to add op_data to transactions
    blocksJson = [
        """{"hash":"0x2833aebab93e00668196aa2820677d93a5f40d54e700990dbae61ffba1e14155","height":0,"local_timestamp":0,"parent_hash":"0x00000000000000000000000000000000","proposer_address":"0x00000000000000000000000000000000","protocol_timestamp":0,"public_keys":[],"receipts_root":"0x1c0bcd032dffef784dae54d7599689869e574409d6ca0982cbf855c8696cfe9a","signatures":[],"slot_id":1,"state_root":"0x39176da0d7a9b5333ec438d5e3c138232d375cd6fd09a140fb782ac4527ddcf3","transactions":[{"block_hash":"0x2833aebab93e00668196aa2820677d93a5f40d54e700990dbae61ffba1e14155","dependent_transaction_hash":"","fuel":1000000,"hash":"0x3ae8a78c3da846a4864d7df717ed36e9a58f358836eca31468407dd93a782263","logs":[],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"content_slot","op_type":1,"parameters":[1,"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"]},"op_result":{"op_type":1,"return_data":null},"public_keys":[],"sender":"0x00000000000000000000000000000000","signatures":[],"status":true,"timestamp":0,"transaction_index":1}],"transactions_root":"0xf180a58f7e403d14b07767696251330cfad61097bc429d13bc962792b11a6b52"}""",
        """{"hash":"0x88db11658865b6d36d131bb86d84b0fe0d6cc4ce16078e8010e3db2a42bc434d","height":1,"local_timestamp":1747104795829138000,"parent_hash":"0x2833aebab93e00668196aa2820677d93a5f40d54e700990dbae61ffba1e14155","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104795862242134,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0x7e64c86e705e4be1bc42a39d413f2d4f42be7e1685075201021abc197692e588","signatures":["0xea671f26bd875c940bb356e527479404fbebf3a7949525d55ec99f1d1ab1753084039052bf4ba8ca60de475ad949f318e99bc07d67bdc8122b37cf66da05ca86"],"slot_id":1,"state_root":"0xad4bf243a7f46080f7d500981472ee7e21271ff235d700f8c056d72867d986d0","transactions":[{"block_hash":"0x88db11658865b6d36d131bb86d84b0fe0d6cc4ce16078e8010e3db2a42bc434d","dependent_transaction_hash":"","fuel":1000000,"hash":"0x0250a5f0b792ad48dc43fff0f7b15a4c19f23681673038ff4c71d27283867ecf","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":0,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0x0250a5f0b792ad48dc43fff0f7b15a4c19f23681673038ff4c71d27283867ecf","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0x9c9d61216ba4ae0178111100a15d6a05282056735b0654d48d4092ff620f16163dbedc6888a7c5881261a5d70387f9d8a77e806d9035e1698acce22c94ddc783"],"status":true,"timestamp":1747104795829138000,"transaction_index":0}],"transactions_root":"0x07785d504f1029cba6437b2fc423dec732c2e71a8a7bee6fff7f26d977ab2b6a"}""",
        """{"hash":"0x0f3d844b3dff3239755d647bba8024b7dccaabf2032a6854529c92078ee9f07c","height":2,"local_timestamp":1747104810851585000,"parent_hash":"0x88db11658865b6d36d131bb86d84b0fe0d6cc4ce16078e8010e3db2a42bc434d","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104810884687134,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xc1fb888266dbe25a0e720668fb48a4d2937dcbc84aa36a18c56899a125283183","signatures":["0x4abda4bfa0bb51adae28359fe8e01bd51ab61a1238ec3f7115058d35fd53503527ea0b37fa1ef323b877646f5fef1a7a60b62cc4ad3eb065e2b82a2207d2f786"],"slot_id":1,"state_root":"0x2eec7ed0484f36ddbf0f7720beeeb479dcf033a14a094947a7a608cc8e9e897c","transactions":[{"block_hash":"0x0f3d844b3dff3239755d647bba8024b7dccaabf2032a6854529c92078ee9f07c","dependent_transaction_hash":"","fuel":1000000,"hash":"0xbdb263720f724d2a7891bf0f348ffe086c06dd67b4dcfb6c960d75c2275205d8","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":1,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0xbdb263720f724d2a7891bf0f348ffe086c06dd67b4dcfb6c960d75c2275205d8","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0x0ae10b66b40de8c4806a37f06c38fec705faa0f09bfe8d659dac618bfee9b07ef03421653bf33f3d54285b16b57bf1c650693fbea9e26e546b8fc07296edb98b"],"status":true,"timestamp":1747104810851585000,"transaction_index":0}],"transactions_root":"0xa4a621b340f9a3c63e4c227e667fec7c62c554843ea3e93fe632758810bfe536"}""",
        """{"hash":"0x14b1d7a5b71236459dc880f6ef932d733bd1cb8b62d9031eaf9ec8a796981995","height":3,"local_timestamp":1747104825882902000,"parent_hash":"0x0f3d844b3dff3239755d647bba8024b7dccaabf2032a6854529c92078ee9f07c","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104825916007134,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0x15847ac824f289451cd02ba43a2cff5a26af3a6e3bcac8799d4bd776a9f94498","signatures":["0xec9e8e7d9a88ef08e686e94bc9a139679078da741b586d1b9f730b1bfea35601fa50d0dfe6e1bc8a783710e8e0dd0fc45e2e53fc134adc8a62e1f89c65c76c8e"],"slot_id":1,"state_root":"0xfe0009663e192e88fdb8709b14304e4cebff876a0265713e82f5549e142ee4c9","transactions":[{"block_hash":"0x14b1d7a5b71236459dc880f6ef932d733bd1cb8b62d9031eaf9ec8a796981995","dependent_transaction_hash":"","fuel":1000000,"hash":"0x0a1de3ddd66d07b4d3aee5a96f0807f1380cece67ef910e73681b4e417974ef5","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":1,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0x0a1de3ddd66d07b4d3aee5a96f0807f1380cece67ef910e73681b4e417974ef5","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0x32f80da7abdec9448a1481510092f2329927a899d0b29a2b654bea43deedd937e51a64347eed1d394621d4f66c8b249e6e2f10b48d208974a09f7b5e76cad28c"],"status":true,"timestamp":1747104825882902000,"transaction_index":0}],"transactions_root":"0xeb0beb6791392f575420967156fea8964f1d49e410fa46939710de9290a70172"}""",
        """{"hash":"0x659433eff1e3cf3e1594399acd74b0e2c905b355e284e39815ae09dbf88e1241","height":4,"local_timestamp":1747104840919049000,"parent_hash":"0x14b1d7a5b71236459dc880f6ef932d733bd1cb8b62d9031eaf9ec8a796981995","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104840961662652,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0x6c60997d054a05bd4e2721af5ed6ab81709e3b01ee02db007d8ab01e2e2cbb16","signatures":["0x50a2ab4e24b523fe72b5cb4438c6b510e14e9e724d567c7b5e9b88a61dbf1052a52dbc3cebc6f7dec0538013b53c412eb6b8adb8752117095bb6073819786286"],"slot_id":1,"state_root":"0xf70661c85bb07dfcc252776f56bc4989930cd698dc618923af831e5067b7883b","transactions":[{"block_hash":"0x659433eff1e3cf3e1594399acd74b0e2c905b355e284e39815ae09dbf88e1241","dependent_transaction_hash":"","fuel":1000000,"hash":"0x67b69a985347da7e212a12d5a5df87f85a97b9010b80598d3485d8f630583f0d","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":1,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0x67b69a985347da7e212a12d5a5df87f85a97b9010b80598d3485d8f630583f0d","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xd05583b2156296f69ebf30c9833f2d5f69a3703c7da7cd0ff4842b7d4af13e1b6b6156796fb20a48325c11f4fdd9f64f2dddc7832c19cdc90588e7697f22608e"],"status":true,"timestamp":1747104840919049000,"transaction_index":0}],"transactions_root":"0x11ea90cb038a980b40832c99ed12fbcce52ecb5c35854891c57a0dbb9db8b9df"}""",
        """{"hash":"0x214429e252591a5d751253fd4e99c2516a9e8b0ca6cfc49e6db8103792cac1c0","height":5,"local_timestamp":1747104854961786000,"parent_hash":"0x659433eff1e3cf3e1594399acd74b0e2c905b355e284e39815ae09dbf88e1241","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104855004400652,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xcaff9e8926a8f5bbce2f0eac376d6292ec91d6922b3fd8816663e14cb40b95e3","signatures":["0xa846ddbe2cde00e3d7c0dd752e31974c691c0ca03e6e2abb785e1fe666a5621584de7013b526ea736964485b39af79d2de57e5f35e8dcf96c5427a19d8e2258d"],"slot_id":1,"state_root":"0xe56186e6f73af4329b204fff0b28c874342b3096cd6679fd1f48444a5a739c96","transactions":[{"block_hash":"0x214429e252591a5d751253fd4e99c2516a9e8b0ca6cfc49e6db8103792cac1c0","dependent_transaction_hash":"","fuel":1000000,"hash":"0x4782f9c991ebc95a9de6dd2288ab53233525c0ed8f9c5631a575abbcd315b7a3","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":1,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0x4782f9c991ebc95a9de6dd2288ab53233525c0ed8f9c5631a575abbcd315b7a3","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0x725b295058d7afab116c3511fa0d39fbb0549538758c600444fd65e96a504424ab33d940273938c9086d6903ba692b9864f6b79c28ca82d82d059cc77987c882"],"status":true,"timestamp":1747104854961786000,"transaction_index":0}],"transactions_root":"0xdb2114bb4a8b7affab9d439bbb158ba89c400592a6057a3c909e2a9ed0832b8c"}""",
        """{"hash":"0x8f2e9e2a7d8e43c55f2d285d9ecfecf483eb5c1868ff17f22be89c9d079a0989","height":6,"local_timestamp":1747104869998751000,"parent_hash":"0x214429e252591a5d751253fd4e99c2516a9e8b0ca6cfc49e6db8103792cac1c0","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104870035449295,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0x57253d1f25b05eb7770e62debe37569aa6807b5feadea7a29bdefdc69b7fc1df","signatures":["0x6253ea30b561696385f5ff1806848871127f36fa37d427b7264a32db6260d4757f40398312f650f6c055267e58fb6e2dca6a6148c953afd595b67bf498d4058b"],"slot_id":1,"state_root":"0x7953822ae6f6f2bf95908c7c69f58aef36c1b4f863cd309883f715178bbe0db3","transactions":[{"block_hash":"0x8f2e9e2a7d8e43c55f2d285d9ecfecf483eb5c1868ff17f22be89c9d079a0989","dependent_transaction_hash":"","fuel":1000000,"hash":"0x0c2cefa2bdc3cc7853c26a22b009f71208c4fcd6a75d4755a242a88c39d65a87","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":1,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0x0c2cefa2bdc3cc7853c26a22b009f71208c4fcd6a75d4755a242a88c39d65a87","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0x8c7b0ea80c6b595005841dd4417e42f26ee9971c1c7598b795ba499bf07a4c2bcc38bd20cb27e529864db542c4532e59d67954159f62238743554a9f52fb5b86"],"status":true,"timestamp":1747104869998751000,"transaction_index":0}],"transactions_root":"0x26c36ae6431b742a0878b14cbec4ee514290903bde109c26d7da66a185325f56"}""",
        """{"hash":"0xbe23cd4a3493741347eeaf2098d8a325684b8d58e0ee42688f92869899fa7d5b","height":7,"local_timestamp":1747104885028491000,"parent_hash":"0x8f2e9e2a7d8e43c55f2d285d9ecfecf483eb5c1868ff17f22be89c9d079a0989","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104885065190295,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xb1ed33d9a93a51ff9061c9f8304a48f8075a3289bf45980ad66509c23aeab77f","signatures":["0xb61591f6bf7908d0035da132a7a141a373bafbdec6fc584520f1948dd8ac4d68dc9b10268e555a8f55228f44203e625c3173059eabccfff9eef88461e1afef8b"],"slot_id":1,"state_root":"0xa8de0c85e07f1239f78aa57ce58ea6d16c3ebf805b89ecc341386517323f0c5c","transactions":[{"block_hash":"0xbe23cd4a3493741347eeaf2098d8a325684b8d58e0ee42688f92869899fa7d5b","dependent_transaction_hash":"","fuel":1000000,"hash":"0xe383ebafbe6bb5a823bd8fa87d02cfc962ae05b39cb5c81ba063847c1bd3d8c1","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":1,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0xe383ebafbe6bb5a823bd8fa87d02cfc962ae05b39cb5c81ba063847c1bd3d8c1","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xb48d6a9c2de12bc5ab2fc28daae3ad74c2ba6316153706c26d2c54c8143d7a377ef0d7e2f45bb69bc495fce8826c525078bd6f54a92fba893b32f1d3e114ca8d"],"status":true,"timestamp":1747104885028491000,"transaction_index":0}],"transactions_root":"0x0ab25e68525e006db73b2158fa82c987d38410e6338d32d6e3fbdfcf6f2f1fce"}""",
        """{"hash":"0xe1c072707fbb8d71dc254a1cf6a289606203dd9b5c3331a18777ffb462b3e14f","height":8,"local_timestamp":1747104900047429000,"parent_hash":"0xbe23cd4a3493741347eeaf2098d8a325684b8d58e0ee42688f92869899fa7d5b","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104900081074345,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xff864ac4d4775387563a9c45f3edf9d15f6727e37d2d474374acb2c099be6671","signatures":["0x327ab9896bd3c7d134c165f21d71685086795fec9b9b3416cf49715c3102aa33ae5d67d95954e6e28b0cb25dcd37521bbd6a03c0af7184408959f36daca4a181"],"slot_id":1,"state_root":"0x47cd96c0c2fc6d9e1af0dce3786e49304a972be20e3fdb2ddd3d1ba842421d6b","transactions":[{"block_hash":"0xe1c072707fbb8d71dc254a1cf6a289606203dd9b5c3331a18777ffb462b3e14f","dependent_transaction_hash":"","fuel":1000000,"hash":"0xdbdecf39b068d945b4bfee0ce80c8c1a77497f5844c498c56c8ddbdc2544c969","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":1,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0xdbdecf39b068d945b4bfee0ce80c8c1a77497f5844c498c56c8ddbdc2544c969","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xb8ca3756232792c6dd49e8eaea9a061b65aeb140eee3099e1074fc1d048a83122598b9dd7d463a9c7159a8b7b58a9295fa8fb012fb4604ebf469b931b7b3df80"],"status":true,"timestamp":1747104900047429000,"transaction_index":0}],"transactions_root":"0x505ecb1f336d2eb8378930b7546e362481ec9e8fb601a14316cc40cd4c4e19c4"}""",
        """{"hash":"0x32f8e80062ed985226d3b32c875754fdf59a7e6c9f431aab2126b80a76a5ecdf","height":9,"local_timestamp":1747104915084741000,"parent_hash":"0xe1c072707fbb8d71dc254a1cf6a289606203dd9b5c3331a18777ffb462b3e14f","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104915118385345,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xed06114db3efa4e560305bd42998a84447ba5e465801ac7c7046ca163d016026","signatures":["0xfe0ecb6384131bb2e09e46947a036ca0b5a17aefe7498c8cfcd346e77dfb225896e1b893aecfd07adbc6d77bdde0cfef13331c4c7e48ca69e4dd7e7c6678a380"],"slot_id":1,"state_root":"0xc5829105f8b3f6d91559905ed9d6e38d9b93b1c125c7715d7c8258338cd8724e","transactions":[{"block_hash":"0x32f8e80062ed985226d3b32c875754fdf59a7e6c9f431aab2126b80a76a5ecdf","dependent_transaction_hash":"","fuel":1000000,"hash":"0x9728cea56d8a5bb2d3f7a1246be709d06811815687226577ebb9bfc2b8c02758","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":1,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0x9728cea56d8a5bb2d3f7a1246be709d06811815687226577ebb9bfc2b8c02758","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xf430c632ea0288b6c78bbe907beb8a97557b160bf42f5fc97e3026d6e8d9291bba20b86d44baa19cb38b889320ac88cabf97db8c5edcce592ed249b3fd49138b"],"status":true,"timestamp":1747104915084741000,"transaction_index":0}],"transactions_root":"0x46d29ba59935745f1c28abe8b939f2e5e1768a0d2176bd3509ac4a9a0bb75011"}""",
        """{"hash":"0x688d1f3e9cad6ed913ca2a641d827a8109d5503a18e7d3b1de58f3634e3237ee","height":10,"local_timestamp":1747104930128861000,"parent_hash":"0x32f8e80062ed985226d3b32c875754fdf59a7e6c9f431aab2126b80a76a5ecdf","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104930164299252,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0x75989b43fb5e0542b9f9bc8ae5862bec17c45ca6105825bdf0a220d4ad021f5b","signatures":["0x027993002a863dabcb6f21e63692d7786a018d6e23ba73fd195f9b4640d4e95438992926f357d99c14dadba4e9d3fe0ed18590af003f94c66633b1e6c908338e"],"slot_id":1,"state_root":"0xbae11776bb9aae757c07bcff98ecf5087222c40ff4a049e96bd7992535c2b4e4","transactions":[{"block_hash":"0x688d1f3e9cad6ed913ca2a641d827a8109d5503a18e7d3b1de58f3634e3237ee","dependent_transaction_hash":"","fuel":1000000,"hash":"0xee904233fb8c21047d325f23049dab64a5bb1621aceebda5bba5f5c251198e46","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":1,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0xee904233fb8c21047d325f23049dab64a5bb1621aceebda5bba5f5c251198e46","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0x4a6d1746c81a7eb7a7c5a9e3c7553c4f8e41a9e05567dabfb4d49d136498cc20b4f1d46ca8a91c3e4a28aea1e808f05d564e9b670e4c1ceb8dbc3cbd7aee218f"],"status":true,"timestamp":1747104930128861000,"transaction_index":0}],"transactions_root":"0xd107b7e3576a0c33d0a35ce3a018d13035fe3e2bf8c501518917ed2932324e83"}""",
        """{"hash":"0xc43c76c9457e864ce11e5bb474cda5e8c3a6b45b2d2de3784f2303e450fbc0e8","height":11,"local_timestamp":1747104945161842000,"parent_hash":"0x688d1f3e9cad6ed913ca2a641d827a8109d5503a18e7d3b1de58f3634e3237ee","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104945197279252,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xbbe903e4906de6f04c2e20c44e9405dddc9c23e7fc18fb2780e7da326df0506a","signatures":["0x6cd1998f6d649a40f4d90ed16d8e7b2cdd38cb324ad137dbfed40f1450100c11abcca05128552ceea7cb0e0c175b5b5831271aab15c8d3dad3bca6bbe2050c8d"],"slot_id":1,"state_root":"0x97d4928d5fd977500add3a73387f8b3de80dfbe370645f92fadd2f02c84ff2c1","transactions":[{"block_hash":"0xc43c76c9457e864ce11e5bb474cda5e8c3a6b45b2d2de3784f2303e450fbc0e8","dependent_transaction_hash":"","fuel":1000000,"hash":"0x0192796e896bafdbde0a6e3332a9778112d4035dcbb84d4e997520c4ec36d6ee","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":1,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0x0192796e896bafdbde0a6e3332a9778112d4035dcbb84d4e997520c4ec36d6ee","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xc05108e0951d418859c6ba5750750b0881d2d7774b2b2e87cece8f77bdeee57b32142ce07aac25ff4bd63c95faa3685511fd73624af5a045165c1ec37cff0c84"],"status":true,"timestamp":1747104945161842000,"transaction_index":0}],"transactions_root":"0xe3663079a4ec908ad8cce9928b3433a1fc73b38600d2c24b9ead32e7e6e3a65a"}""",
        """{"hash":"0x6ffe1bfdbc2cae38224eaeab5c5ec8329b38b008e5a3cbf36850bd92f2d74326","height":12,"local_timestamp":1747104960200886000,"parent_hash":"0xc43c76c9457e864ce11e5bb474cda5e8c3a6b45b2d2de3784f2303e450fbc0e8","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104960236190738,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0x71ce0903ac5e8fbedfa6c8fb43bb34438c2c9d315f01e531a8656781aea293f9","signatures":["0x2abf5d69fecdd6ba8b7f14f36ef63d5a16d6872139510a03909c6c684c99bd0e1e229a81fcd6069495ed8f534c8889e90fae0b7094a2cc6cfdd396730a72d98d"],"slot_id":1,"state_root":"0xd86e17160e8bbf5e0bc9c4433bc2b3867b15e1bb5c67037a387117d4bfb2f3c1","transactions":[{"block_hash":"0x6ffe1bfdbc2cae38224eaeab5c5ec8329b38b008e5a3cbf36850bd92f2d74326","dependent_transaction_hash":"","fuel":1000000,"hash":"0xac35a882b959bf35cc5e90d24b7bf2fc385875937a0c5f42bb666e340190f30e","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":1,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0xac35a882b959bf35cc5e90d24b7bf2fc385875937a0c5f42bb666e340190f30e","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xec88a758b540deccbc9c9a596ecb7befd763d60e00cc2f6999786f0bcc32160f6daf824dc2d867bab86c717a516fa3ca4abd5d9abb88c3437ffa56769f12ba8c"],"status":true,"timestamp":1747104960200886000,"transaction_index":0}],"transactions_root":"0x5de2d8de1b4c078129c8bba40171eb8eacc622a77791844aaf2396ece3354fa9"}""",
        """{"hash":"0xd9f7986ef79eeb41ebbb76e450f395f7b461f582714e95938a76221aa167131d","height":13,"local_timestamp":1747104975239524000,"parent_hash":"0x6ffe1bfdbc2cae38224eaeab5c5ec8329b38b008e5a3cbf36850bd92f2d74326","proposer_address":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","protocol_timestamp":1747104975274827738,"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"receipts_root":"0xdf7b0918d0dac011c114cf0cbc64c063fdea9618c4a0f9471dd080f4212132f0","signatures":["0x24ea5be1abeebc985794aea5e90cf9f43675d3c4d1f3aa5bfdb4764ea4e2fa0510c24a5b60c8f23a254e8b36b93190484f5a63a53675d4430f31165c703d568d"],"slot_id":1,"state_root":"0xf50067e175d25388d1f587ddc3c683065fad964c9e668578e1a8b6989fa908a8","transactions":[{"block_hash":"0xd9f7986ef79eeb41ebbb76e450f395f7b461f582714e95938a76221aa167131d","dependent_transaction_hash":"","fuel":1000000,"hash":"0xf902ed3728b92119585190332393c88b8a9d408b5a5e3540510bd9cb62a14d5d","logs":[{"contract_address":"0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3","data":["0x313030303030303030303030"],"log_index":1,"topics":["0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e","0x00000000000000dec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06"],"transaction_hash":"0xf902ed3728b92119585190332393c88b8a9d408b5a5e3540510bd9cb62a14d5d","transaction_index":0}],"op_data":{"contract_address":"0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9","function_name":"mint_token","op_type":1,"parameters":["0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",100000000000]},"op_result":{"op_type":1,"return_data":null},"public_keys":["0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d"],"sender":"0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06","signatures":["0xf8b85d378e09aa7b70cde788d88988ed8354022c7aed43a9cebbe369383948556cf042c30a20a116f165feb868dcffc2bc0f0fbb2febb3a83a08b575533f7d8f"],"status":true,"timestamp":1747104975239524000,"transaction_index":0}],"transactions_root":"0x0a27b75bb9d24e36315f764e54ae4c345a942125d5dd49c3102476f7d54006da"}""",
    ]
    for blockJson in blocksJson:
        _result, err = explorerClient.execute("insert_block", None, blockJson)
        assert err is None

    max_int64 = 2**63 - 1

    result, err = explorerClient.executeReadOnly(
        "get_address_transactions",
        str,
        False,
        "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        0,
        max_int64,
        100,
    )
    assert err is None
    print(result)

    result, err = explorerClient.executeReadOnly(
        "get_address_transactions", str, True, "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06", max_int64, 0, 100
    )
    assert err is None
    print(result)

    result, err = explorerClient.executeReadOnly(
        "get_address_transactions",
        str,
        False,
        "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        1739773035410233000,
        1739773140669267000,
        100,
    )
    assert err is None
    print(result)

    result, err = explorerClient.executeReadOnly(
        "get_address_transactions",
        str,
        True,
        "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
        1739773140669267000,
        1739773035410233000,
        100,
    )
    assert err is None
    print(result)

    # Test get_block_by_hash
    # Use the first block from processed_blocks instead of TEST_BLOCKS_DATA
    first_block = json.loads(blocksJson[0])
    result, err = explorerClient.executeReadOnly("get_block_by_hash", str, first_block["hash"])
    assert err is None
    assert json.loads(result)["hash"] == first_block["hash"]

    # Test get_block_by_hash with non-existent hash
    result, err = explorerClient.executeReadOnly(
        "get_block_by_hash", str, "0x0000000000000000000000000000000000000000000000000000000000000000"
    )
    assert err is not None

    # Test get_transaction_by_hash
    first_transaction = first_block["transactions"][0]
    result, err = explorerClient.executeReadOnly("get_transaction_by_hash", str, first_transaction["hash"])
    assert err is None
    assert json.loads(result)["hash"] == first_transaction["hash"]

    # Test get_transaction_by_hash with non-existent hash
    result, err = explorerClient.executeReadOnly(
        "get_transaction_by_hash", str, "0x0000000000000000000000000000000000000000000000000000000000000000"
    )
    assert err is not None

    # Test get_synced_block_height
    result, err = explorerClient.executeReadOnly("get_synced_block_height", int)
    assert err is None
    # The last block has height 13
    assert result == 13

    print("test end")


def test_explorer_db_transaction_with_receipt_data():
    # Create a new explorer client
    explorerClient = ContractTester(
        wasmName="explorer_db",
    )
    explorerClient.constructor()

    # Create a simple block with transaction that includes receipt fields
    simple_block = json.dumps(
        {
            "hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
            "height": 0,
            "local_timestamp": 0,
            "parent_hash": "0x00000000000000000000000000000000",
            "proposer_address": "0x00000000000000000000000000000000",
            "protocol_timestamp": 0,
            "public_keys": [],
            "receipts_root": "0x00000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x00000000000000000000000000000000",
            "transactions": [
                {
                    "dependent_transaction_hash": "",
                    "fuel": 1000,
                    "hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                    "op_data": {
                        "op_type": 1,
                        "contract_address": "0x1234567890123456789012345678901234567890123456789012345678901234",
                        "function_name": "transfer",
                        "parameters": [
                            "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
                            "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a01",
                            100,
                        ],
                    },
                    "public_keys": [],
                    "sender": "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
                    "signatures": [],
                    "timestamp": 0,
                    "transaction_index": 1,
                    "status": True,
                    "op_result": {"op_type": 1, "return_data": None},
                    "block_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                    "logs": [
                        {
                            "contract_address": "0x1234567890123456789012345678901234567890123456789012345678901234",
                            "topics": [
                                "Transfer",
                                "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
                                "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a01",
                            ],
                            "data": ["100"],
                            "transaction_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
                            "transaction_index": 1,
                            "log_index": 0,
                        }
                    ],
                }
            ],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )

    # Insert block with transaction that includes receipt data
    _result, err = explorerClient.execute("insert_block", None, simple_block)
    assert err is None

    # Test get_transaction_by_hash to verify receipt data is included
    result, err = explorerClient.executeReadOnly(
        "get_transaction_by_hash", str, "0x1234567890123456789012345678901234567890123456789012345678901234"
    )
    assert err is None
    transaction = json.loads(result)
    assert transaction["hash"] == "0x1234567890123456789012345678901234567890123456789012345678901234"
    assert transaction["transaction_index"] == 1
    assert transaction["status"] is True
    assert "op_result" in transaction
    assert "block_hash" in transaction
    assert "op_data" in transaction
    assert transaction["op_data"]["op_type"] == 1
    assert transaction["op_data"]["function_name"] == "transfer"

    # Verify logs field is included
    assert "logs" in transaction
    assert len(transaction["logs"]) == 1
    log = transaction["logs"][0]
    assert log["contract_address"] == "0x1234567890123456789012345678901234567890123456789012345678901234"
    assert "Transfer" in log["topics"]
    assert "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06" in log["topics"]
    assert "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a01" in log["topics"]
    assert log["data"] == ["100"]
    assert log["transaction_hash"] == "0x1234567890123456789012345678901234567890123456789012345678901234"
    assert log["transaction_index"] == 1
    assert log["log_index"] == 0

    # Test get_transaction_op_result
    result, err = explorerClient.executeReadOnly(
        "get_transaction_op_result", str, "0x1234567890123456789012345678901234567890123456789012345678901234"
    )
    assert err is None
    op_result = json.loads(result)
    assert op_result["op_type"] == 1  # CallContract
    assert "return_data" in op_result

    # Test get_transaction_op_data
    result, err = explorerClient.executeReadOnly(
        "get_transaction_op_data", str, "0x1234567890123456789012345678901234567890123456789012345678901234"
    )
    assert err is None
    op_data = json.loads(result)
    assert op_data["op_type"] == 1  # CallContract
    assert op_data["function_name"] == "transfer"


def test_explorer_db_block_errors():
    """Test error handling when inserting invalid blocks"""
    # Create a new explorer client
    explorerClient = ContractTester(
        wasmName="explorer_db",
    )
    explorerClient.constructor()

    # First insert a valid block as a base
    valid_block = json.dumps(
        {
            "hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
            "height": 0,
            "local_timestamp": 0,
            "parent_hash": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "proposer_address": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "protocol_timestamp": 0,
            "public_keys": [],
            "receipts_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "transactions": [],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )
    _result, err = explorerClient.execute("insert_block", None, valid_block)
    assert err is None

    # Test 1: Insert a block with non-continuous height (skipping height 1)
    non_continuous_block = json.dumps(
        {
            "hash": "0x2234567890123456789012345678901234567890123456789012345678901234",
            "height": 2,  # Skip height 1
            "local_timestamp": 1,
            "parent_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
            "proposer_address": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "protocol_timestamp": 1,
            "public_keys": [],
            "receipts_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "transactions": [],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )
    _result, err = explorerClient.execute("insert_block", None, non_continuous_block)
    assert err is not None
    assert "Block height is not continuous" in str(err)

    # Test 2: Insert a block with mismatched parent_hash
    wrong_parent_block = json.dumps(
        {
            "hash": "0x3234567890123456789012345678901234567890123456789012345678901234",
            "height": 1,
            "local_timestamp": 1,
            "parent_hash": "0x9999999999999999999999999999999999999999999999999999999999999999",  # Incorrect parent hash
            "proposer_address": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "protocol_timestamp": 1,
            "public_keys": [],
            "receipts_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "transactions": [],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )
    _result, err = explorerClient.execute("insert_block", None, wrong_parent_block)
    assert err is not None
    assert "Block parent hash does not match" in str(err)

    # Test 3: Insert a duplicate block (block with the same hash)
    # First insert a valid block at height 1
    valid_block_1 = json.dumps(
        {
            "hash": "0x4234567890123456789012345678901234567890123456789012345678901234",
            "height": 1,
            "local_timestamp": 1,
            "parent_hash": "0x1234567890123456789012345678901234567890123456789012345678901234",
            "proposer_address": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "protocol_timestamp": 1,
            "public_keys": [],
            "receipts_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "transactions": [],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )
    _result, err = explorerClient.execute("insert_block", None, valid_block_1)
    assert err is None

    # Try to insert another block with the same hash but different height
    duplicate_block = json.dumps(
        {
            "hash": "0x4234567890123456789012345678901234567890123456789012345678901234",  # Same hash as the block above
            "height": 2,
            "local_timestamp": 2,
            "parent_hash": "0x4234567890123456789012345678901234567890123456789012345678901234",
            "proposer_address": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "protocol_timestamp": 2,
            "public_keys": [],
            "receipts_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "transactions": [],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )
    # Since the block already exists, it should cause an error
    _result, err = explorerClient.execute("insert_block", None, duplicate_block)
    assert err is not None

    # Test 4: Insert a block with invalid format (missing required fields)
    # Create a block missing the height field
    invalid_format_block = json.dumps(
        {
            "hash": "0x5234567890123456789012345678901234567890123456789012345678901234",
            # Missing height field
            "local_timestamp": 2,
            "parent_hash": "0x4234567890123456789012345678901234567890123456789012345678901234",
            "proposer_address": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "protocol_timestamp": 2,
            "public_keys": [],
            "receipts_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "signatures": [],
            "slot_id": 1,
            "state_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
            "transactions": [],
            "transactions_root": "0x0000000000000000000000000000000000000000000000000000000000000000",
        }
    )
    _result, err = explorerClient.execute("insert_block", None, invalid_format_block)
    assert err is not None
    # Due to JSON parsing error, it should return an error
    assert "missing field" in str(err).lower() or "error" in str(err).lower()
