import pytest

from vm import ContractTester

primechainUtilsContractClient = ContractTester("primechain_utils")


@pytest.fixture(autouse=True)
def register_contract():
    primechainUtilsContractClient.constructor()


def test_verify_work():
    difficultyUnity, err = primechainUtilsContractClient.executeReadOnly("get_difficulty_unity", int)
    assert err is None
    assert difficultyUnity == 1 << 24

    origin = "16104852924139568456006626349957181947614826079179167293804058866767923317510414698411827609927680"
    difficulty = int(11.69245762 * difficultyUnity)
    result, err = primechainUtilsContractClient.executeReadOnly("verify_work", bool, origin, difficulty)
    assert err is None
    assert result is True

    origin = "53116128645141147208311952277331334682541568096207660913084590785789019086113551886126310"
    difficulty = int(6.99609375 * difficultyUnity)
    result, err = primechainUtilsContractClient.executeReadOnly("verify_work", bool, origin, difficulty)
    assert err is None
    assert result is True

    origin = "31374298392901195153898941717506973017356439773600594248208334547756268722849666432245972690"
    difficulty = int(7.649105191230774 * difficultyUnity)
    result, err = primechainUtilsContractClient.executeReadOnly("verify_work", bool, origin, difficulty)
    assert err is None
    assert result is True
