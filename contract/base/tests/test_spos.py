import pytest

from vm.glue import ContractTester

sposContractClient = ContractTester(
    address="0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9", wasmName="spos"
)
tokenContractClient = ContractTester(
    address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3", wasmName="token"
)

numberOfSlots = 15
tokenName = "vgraph_test"


@pytest.fixture(autouse=True)
def register_contract():
    tokenContractClient.constructor(tokenName)
    sposContractClient.constructor(numberOfSlots)


def test_stakeout():
    staker = "Alice"
    stakee = "Bob"

    # issue some token
    amount = 1000
    _, err = tokenContractClient.execute("issue", None, staker, amount)
    assert err is None

    # stake out
    stake = 500
    for i in range(1, amount // stake + 1):
        _, err = sposContractClient.execute("stake_out", None, staker, stakee, stake)
        assert err is None

        # check staker's stake in, stake out, available balance, minting power
        (stakerStakeIn, stakerStakeOut), err = sposContractClient.execute("get_stake_balance", tuple, staker)
        assert err is None
        assert stakerStakeIn == 0
        assert stakerStakeOut == i * stake
        stakerAvailableBalance, err = sposContractClient.executeReadOnly("get_available_balance", int, staker)
        assert err is None
        assert stakerAvailableBalance == amount - i * stake
        stakerMintingPower, err = sposContractClient.executeReadOnly("get_minting_power", int, staker)
        assert err is None
        assert stakerMintingPower == amount - i * stake

        # check stakee's stake in, stake out, available balance, minting power
        (stakeeStakeIn, stakeeStakeOut), err = sposContractClient.executeReadOnly("get_stake_balance", tuple, stakee)
        assert err is None
        assert stakeeStakeIn == i * stake
        assert stakeeStakeOut == 0
        stakeeAvailableBalance, err = sposContractClient.executeReadOnly("get_available_balance", int, stakee)
        assert err is None
        assert stakeeAvailableBalance == 0
        stakeeMintingPower, err = sposContractClient.executeReadOnly("get_minting_power", int, stakee)
        assert err is None
        assert stakeeMintingPower == i * stake

    # should error because insufficient balance
    _, err = sposContractClient.execute("stake_out", None, staker, stakee, stake)
    assert err is not None


def test_cancel_one_stakeout():
    staker = "Alice"
    stakee1 = "Bob"
    stakee2 = "David"

    # issue some token
    amount = 300
    _, err = tokenContractClient.execute("issue", None, staker, amount)
    assert err is None

    #  stake out stake two times to stakee1, one time to stakee2
    stake = 100
    _, err = sposContractClient.execute("stake_out", None, staker, stakee1, stake)
    assert err is None

    _, err = sposContractClient.execute("stake_out", None, staker, stakee1, stake)
    assert err is None

    _, err = sposContractClient.execute("stake_out", None, staker, stakee2, stake)
    assert err is None

    # cancel the stake out to stakee1
    _, err = sposContractClient.execute("cancel_one_stake_out", None, staker, stakee1)
    assert err is None

    # check staker's stake in and stake out
    (stakerStakeIn, stakerStakeOut), err = sposContractClient.executeReadOnly("get_stake_balance", tuple, staker)
    assert err is None
    assert stakerStakeIn == 0
    assert stakerStakeOut == amount - stake * 2

    # check stakee1's stake in and stake out
    (stakee1StakeIn, stakee1StakeOut), err = sposContractClient.executeReadOnly("get_stake_balance", tuple, stakee1)
    assert err is None
    assert stakee1StakeIn == 0
    assert stakee1StakeOut == 0

    # check stakee2's stake in and stake out
    (stakee2StakeIn, stakee2StakeOut), err = sposContractClient.executeReadOnly("get_stake_balance", tuple, stakee2)
    assert err is None
    assert stakee2StakeIn == stake
    assert stakee2StakeOut == 0


def test_cancel_all_stakeout():
    staker1 = "Alice1"
    staker2 = "Alice2"
    stakee1 = "Bob"
    stakee2 = "David"

    # issue some token to stakers
    amount = 600
    _, err = tokenContractClient.execute("issue", None, staker1, amount)
    assert err is None
    _, err = tokenContractClient.execute("issue", None, staker2, amount)
    assert err is None

    # stake out
    # staker 1 stakes out 100 to stakee 1, 2
    stake1 = 100
    _, err = sposContractClient.execute("stake_out", None, staker1, stakee1, stake1)
    assert err is None
    _, err = sposContractClient.execute("stake_out", None, staker1, stakee2, stake1)
    assert err is None

    # staker 2 stakes out 100 to stakee 1, 2
    stake2 = 200
    _, err = sposContractClient.execute("stake_out", None, staker2, stakee1, stake2)
    assert err is None
    _, err = sposContractClient.execute("stake_out", None, staker2, stakee2, stake2)
    assert err is None

    # cancel all stake out of staker 1
    _, err = sposContractClient.execute("cancel_all_stake_out", None, staker1)
    assert err is None

    # check staker 1's stake in and stake out
    (staker1StakeIn, staker1StakeOut), err = sposContractClient.executeReadOnly("get_stake_balance", tuple, staker1)
    assert err is None
    assert staker1StakeIn == 0
    assert staker1StakeOut == 0

    # check staker 2's stake in and stake out
    (staker2StakeIn, staker2StakeOut), err = sposContractClient.executeReadOnly("get_stake_balance", tuple, staker2)
    assert err is None
    assert staker2StakeIn == 0
    assert staker2StakeOut == stake2 * 2

    # check stakee 1's stake in and stake out
    (stakee1StakeIn, stakee1StakeOut), err = sposContractClient.executeReadOnly("get_stake_balance", tuple, stakee1)
    assert err is None
    assert stakee1StakeIn == stake2
    assert stakee1StakeOut == 0

    # check stakee 2's stake in and stake out
    (stakee2StakeIn, stakee2StakeOut), err = sposContractClient.executeReadOnly("get_stake_balance", tuple, stakee2)
    assert err is None
    assert stakee2StakeIn == stake2
    assert stakee2StakeOut == 0


def test_content_slot():
    alice = "Alice"
    bob = "Bob"
    charlie = "Charlie"

    # issue some tokens
    # after issuing tokens, the minting power of Alice, Bob, Charlie are:
    # Alice: 100
    # Bob: 200
    # Charlie: 300
    _, err = tokenContractClient.execute("issue", None, alice, 100)
    assert err is None
    _, err = tokenContractClient.execute("issue", None, bob, 200)
    assert err is None
    _, err = tokenContractClient.execute("issue", None, charlie, 300)

    slot1 = 1
    slot2 = 2

    # test: content empty slots
    # slot1 is empty, content slot1 with Alice should be ok
    _, err = sposContractClient.execute("content_slot", None, slot1, alice)
    assert err is None

    # slot 2 is empty, content slot2 with Bob should be ok
    _, err = sposContractClient.execute("content_slot", None, slot2, bob)
    assert err is None

    # after contenting slot1 and slot2, the state of slots are:
    # slot1: Alice
    # slot2: Bob

    # test content occupied slots
    # Charlie contents slot1, should be ok
    # because Charlie has higher minting power than Alice
    _, err = sposContractClient.execute("content_slot", None, slot1, charlie)
    assert err is None

    # Alice contents slot2, should be not ok
    # because Alice has lower minting power than Bob
    _, err = sposContractClient.execute("content_slot", None, slot2, alice)
    assert err is not None

    # after contenting slot1 and slot2, the state of slots are:
    # slot1: Charlie
    # slot2: Bob

    # test one competitor contents multiple slots
    # Charlie contents slot2, should be false
    # because Charlie already owns a slot, even though Charlie has higher minting power than Bob
    _, err = sposContractClient.execute("content_slot", None, slot2, charlie)
    assert err is not None

    # test check_owners_supernode_slot
    # check Alice's slot, should be 0
    slot, err = sposContractClient.executeReadOnly("check_owners_supernode_slot", int, alice)
    assert slot == 0

    # check Bob's slot, should be slot2
    slot, err = sposContractClient.executeReadOnly("check_owners_supernode_slot", int, bob)
    assert err is None
    assert slot == slot2

    # check Charlie's slot, should be slot1
    slot, err = sposContractClient.executeReadOnly("check_owners_supernode_slot", int, charlie)
    assert err is None
    assert slot == slot1


def test_release_slot():
    alice = "Alice"
    bob = "Bob"

    # issue tokens to Alice and Bob
    _, err = tokenContractClient.execute("issue", None, alice, 100)
    assert err is None
    _, err = tokenContractClient.execute("issue", None, bob, 200)
    assert err is None

    slot1 = 1
    slot2 = 2

    # content slot1 with Alice
    _, err = sposContractClient.execute("content_slot", None, slot1, alice)
    assert err is None

    # content slot2 with Bob
    _, err = sposContractClient.execute("content_slot", None, slot2, bob)
    assert err is None

    # after contenting slot1 and slot2, the state of slots are:
    # slot1: Alice
    # slot2: Bob

    # test release slot
    # release slot1 with Alice should be ok
    _, err = sposContractClient.execute("release_slot", None, alice)
    assert err is None

    # release Bob's slot(slot 2), should not be ok
    # because Bob cannot release the last slot
    _, err = sposContractClient.execute("release_slot", None, bob)
    assert err is not None


def test_mint_token():
    alice = "Alice"
    bob = "Bob"

    # issue tokens to Alice and Bob
    _, err = tokenContractClient.execute("issue", None, alice, 100)
    assert err is None
    _, err = tokenContractClient.execute("issue", None, bob, 200)
    assert err is None

    # content slots
    slot1 = 1
    _, err = sposContractClient.execute("content_slot", None, slot1, alice)
    assert err is None
    _, err = sposContractClient.execute("content_slot", None, slot1, bob)
    assert err is None

    # after contenting, Bob holds slot 1, Alice is not a supernode

    recipent = "Recipient"
    amount = 100

    # test missing environment variables, should be error
    _, err = sposContractClient.execute("mint_token", None, recipent, amount)
    assert err is not None

    # test transaction_index is not 0, should be error
    _, err = sposContractClient.executeWithEnv(
        {"transaction_index": "1", "sender": bob}, "mint_token", None, recipent, amount
    )
    assert err is not None

    # test bob mint token, should be ok
    _, err = sposContractClient.executeWithEnv(
        {"transaction_index": "0", "sender": bob}, "mint_token", None, recipent, amount
    )
    assert err is None

    # test alice mint token, should be error
    _, err = sposContractClient.executeWithEnv(
        {"transaction_index": "0", "sender": alice}, "mint_token", None, recipent, amount
    )
    assert err is not None
