import json
import time
import pytest
from vm import ContractTester

vcloudClient = ContractTester(
    wasmName="vcloud_db",
)


@pytest.fixture(autouse=True)
def register_contract():
    vcloudClient.constructor()

def create_test_order(order_id="test_order_1", address="0xtest_address_1", provider="test_provider_1", status="pending"):
    """Create a test Order with realistic data following the exact data model"""
    # Add timestamp to make IDs unique across test runs
    unique_suffix = str(int(time.time() * 1000000))[-6:]
    return {
        "_id": f"{order_id}_{unique_suffix}",
        "createdAt": 0,  # Will be set by contract if 0
        "updatedAt": 0,  # Will be set by contract if 0
        "deletedAt": 0,
        "type": "compute_order",
        "amount": 500.0,
        "amountPaid": 0.0,
        "provider": provider,
        "address": address,
        "recipient": "0xrecipient_address_123",
        "status": status,
        "lastPaymentTS": 0,
        "paidTS": 0,
        "filedTS": 0,
        "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
        "userServiceIDs": ["service_1", "service_2"],
        "items": [
            {
                "userServiceID": "service_1",
                "duration": 3600,
                "amount": 250.0
            },
            {
                "userServiceID": "service_2",
                "duration": 7200,
                "amount": 250.0
            }
        ]
    }


def test_create_order():
    """Test creating a new order"""
    order = create_test_order()
    order_json = json.dumps(order)
    print(order_json)

    # Test successful creation
    result, err = vcloudClient.execute("create_order", str, order_json)
    assert err is None
    assert result == order["_id"]

    # Test duplicate ID error
    result, err = vcloudClient.execute("create_order", str, order_json)
    assert err is not None
    assert "already exists" in str(err)


def test_get_order():
    """Test retrieving an order by ID"""
    order = create_test_order("get_test_1")
    order_json = json.dumps(order)

    # Create the order first
    result, err = vcloudClient.execute("create_order", str, order_json)
    assert err is None

    # Test successful retrieval
    result, err = vcloudClient.executeReadOnly("get_order", str, order["_id"])
    assert err is None
    retrieved_order = json.loads(result)
    assert retrieved_order["_id"] == order["_id"]
    assert retrieved_order["amount"] == order["amount"]
    assert retrieved_order["provider"] == order["provider"]
    assert retrieved_order["status"] == order["status"]

    # Test non-existent order
    result, err = vcloudClient.executeReadOnly("get_order", str, "non_existent_id")
    assert err is not None
    assert "not found" in str(err)


def test_update_order():
    """Test updating an existing order"""
    order = create_test_order("update_test_1")
    order_json = json.dumps(order)

    # Create the order first
    result, err = vcloudClient.execute("create_order", str, order_json)
    assert err is None

    # Update the order
    order["amount"] = 750.0
    order["status"] = "paid"
    order["amountPaid"] = 750.0
    updated_order_json = json.dumps(order)
    result, err = vcloudClient.execute("update_order", None, updated_order_json)
    assert err is None

    # Verify the update
    result, err = vcloudClient.executeReadOnly("get_order", str, order["_id"])
    assert err is None
    updated_order = json.loads(result)
    assert updated_order["amount"] == 750.0
    assert updated_order["status"] == "paid"
    assert updated_order["amountPaid"] == 750.0

    # Test updating non-existent order
    non_existent_order = create_test_order("non_existent")
    non_existent_json = json.dumps(non_existent_order)
    result, err = vcloudClient.execute("update_order", None, non_existent_json)
    assert err is not None
    assert "not found" in str(err)


def test_query_orders():
    """Test querying orders with various filters"""
    # Create test orders with different attributes
    orders = [
        create_test_order("query_test_1", "0xaddr1", "provider1", "pending"),
        create_test_order("query_test_2", "0xaddr1", "provider2", "paid"),
        create_test_order("query_test_3", "0xaddr2", "provider1", "pending"),
        create_test_order("query_test_4", "0xaddr2", "provider2", "cancelled"),
    ]

    # Create all orders
    order_ids = []
    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("create_order", str, order_json)
        assert err is None
        order_ids.append(order["_id"])

    # Test query by IDs
    query_params = {
        "_ids": order_ids[:2],  # Query first 2 orders
        "serviceID": "",
        "service": "",
        "address": "",
        "recipient": "",
        "type": "",
        "statuses": [],
        "tsStart": None,
        "tsEnd": None
    }
    result, err = vcloudClient.executeReadOnly("query_orders", str, json.dumps(query_params))
    assert err is None
    queried_orders = json.loads(result)
    assert len(queried_orders) == 2

    # Test query by address
    query_params = {
        "_ids": [],
        "serviceID": "",
        "service": "",
        "address": "0xaddr1",
        "recipient": "",
        "type": "",
        "statuses": [],
        "tsStart": None,
        "tsEnd": None
    }
    result, err = vcloudClient.executeReadOnly("query_orders", str, json.dumps(query_params))
    assert err is None
    queried_orders = json.loads(result)
    assert len(queried_orders) >= 2
    # Verify all returned orders have the correct address
    for order in queried_orders:
        if order["_id"] in order_ids:  # Only check our test orders
            assert order["address"] == "0xaddr1"

    # Test query by status
    query_params = {
        "_ids": [],
        "serviceID": "",
        "service": "",
        "address": "",
        "recipient": "",
        "type": "",
        "statuses": ["pending"],
        "tsStart": None,
        "tsEnd": None
    }
    result, err = vcloudClient.executeReadOnly("query_orders", str, json.dumps(query_params))
    assert err is None
    queried_orders = json.loads(result)
    assert len(queried_orders) >= 2
    # Verify all returned orders have the correct status
    for order in queried_orders:
        if order["_id"] in order_ids:  # Only check our test orders
            assert order["status"] == "pending"


def test_distinct_orders():
    """Test getting distinct values from orders"""
    # Create test orders with different attributes
    orders = [
        create_test_order("distinct_test_1", "0xaddr1", "provider1", "pending"),
        create_test_order("distinct_test_2", "0xaddr1", "provider2", "paid"),
        create_test_order("distinct_test_3", "0xaddr2", "provider1", "pending"),
        create_test_order("distinct_test_4", "0xaddr2", "provider3", "cancelled"),
    ]

    # Create all orders
    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("create_order", str, order_json)
        assert err is None

    # Test distinct statuses
    distinct_params = {
        "distinctField": "status",
        "address": "",
        "statuses": []
    }
    result, err = vcloudClient.executeReadOnly("distinct_orders", str, json.dumps(distinct_params))
    assert err is None
    distinct_values = json.loads(result)
    assert "pending" in distinct_values
    assert "paid" in distinct_values
    assert "cancelled" in distinct_values

    # Test distinct providers with address filter
    distinct_params = {
        "distinctField": "provider",
        "address": "0xaddr1",
        "statuses": []
    }
    result, err = vcloudClient.executeReadOnly("distinct_orders", str, json.dumps(distinct_params))
    assert err is None
    distinct_values = json.loads(result)
    assert "provider1" in distinct_values
    assert "provider2" in distinct_values
    # Should not include provider3 since it's not associated with 0xaddr1

    # Test distinct statuses with status filter
    distinct_params = {
        "distinctField": "status",
        "address": "",
        "statuses": ["pending", "paid"]
    }
    result, err = vcloudClient.executeReadOnly("distinct_orders", str, json.dumps(distinct_params))
    assert err is None
    distinct_values = json.loads(result)
    assert "pending" in distinct_values
    assert "paid" in distinct_values
    # Should not include "cancelled" due to status filter


def test_update_many_orders():
    """Test updating multiple orders based on filter criteria"""
    # Create test orders
    orders = [
        create_test_order("update_many_1", "0xaddr1", "provider1", "pending"),
        create_test_order("update_many_2", "0xaddr1", "provider2", "pending"),
        create_test_order("update_many_3", "0xaddr2", "provider1", "paid"),
    ]

    # Create all orders
    order_ids = []
    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("create_order", str, order_json)
        assert err is None
        order_ids.append(order["_id"])

    # Update all pending orders to processing status (use specific IDs to avoid interference from other tests)
    filter_params = {
        "_ids": order_ids,  # Use specific order IDs to avoid updating orders from other tests
        "serviceID": "",
        "service": "",
        "address": "",
        "recipient": "",
        "type": "",
        "statuses": ["pending"],
        "tsStart": None,
        "tsEnd": None
    }
    
    update_order = {
        "_id": "",  # Not used in update_many
        "createdAt": 0,
        "updatedAt": 0,
        "deletedAt": 0,
        "type": "",
        "amount": 0.0,
        "amountPaid": 0.0,
        "provider": "",
        "address": "",
        "recipient": "",
        "status": "processing",  # Update status to processing
        "lastPaymentTS": 0,
        "paidTS": 0,
        "filedTS": 0,
        "publicKey": "",
        "userServiceIDs": [],
        "items": []
    }

    result, err = vcloudClient.execute("update_many_orders", str, json.dumps(filter_params), json.dumps(update_order))
    assert err is None
    batch_result = json.loads(result)
    assert batch_result["updated"] == 2  # Should update 2 pending orders
    assert batch_result["created"] == 0
    assert batch_result["deleted"] == 0

    # Verify the updates
    for i, order_id in enumerate(order_ids[:2]):  # First 2 were pending
        result, err = vcloudClient.executeReadOnly("get_order", str, order_id)
        assert err is None
        updated_order = json.loads(result)
        assert updated_order["status"] == "processing"

    # Verify the third order (was paid) remains unchanged
    result, err = vcloudClient.executeReadOnly("get_order", str, order_ids[2])
    assert err is None
    unchanged_order = json.loads(result)
    assert unchanged_order["status"] == "paid"


def test_query_orders_with_time_range():
    """Test querying orders with TSStart and TSEnd filters"""
    # Create test orders with different timestamps
    current_time = int(time.time() * **********)  # nanoseconds

    orders = [
        create_test_order("time_test_1", "0xaddr1", "provider1", "paid"),
        create_test_order("time_test_2", "0xaddr1", "provider1", "paid"),
        create_test_order("time_test_3", "0xaddr1", "provider1", "filed"),
    ]

    # Set different timestamps
    orders[0]["paidTS"] = current_time - **********  # 2 seconds ago
    orders[1]["paidTS"] = current_time - **********  # 1 second ago
    orders[2]["filedTS"] = current_time - 500000000   # 0.5 seconds ago

    # Create all orders
    order_ids = []
    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("create_order", str, order_json)
        assert err is None
        order_ids.append(order["_id"])

    # Test time range query - should find orders within the range
    query_params = {
        "_ids": [],
        "serviceID": "",
        "service": "",
        "address": "0xaddr1",
        "recipient": "",
        "type": "",
        "statuses": [],
        "tsStart": current_time - **********,  # 1.5 seconds ago
        "tsEnd": current_time                   # now
    }

    result, err = vcloudClient.executeReadOnly("query_orders", str, json.dumps(query_params))
    assert err is None
    queried_orders = json.loads(result)

    # Should find orders with PaidTS or FiledTS in the range
    found_order_ids = [order["_id"] for order in queried_orders if order["_id"] in order_ids]
    assert len(found_order_ids) >= 2  # Should find orders 2 and 3


def test_order_data_validation():
    """Test data validation and error handling for orders"""
    # Test creating order with invalid JSON
    invalid_json = '{"_id": "test", "amount": invalid_number}'  # Invalid JSON syntax
    result, err = vcloudClient.execute("create_order", str, invalid_json)
    assert err is not None
    assert "Failed to parse order JSON" in str(err)

    # Test with empty ID
    empty_id_order = {"_id": ""}
    empty_id_order_json = json.dumps(empty_id_order)
    result, err = vcloudClient.execute("create_order", str, empty_id_order_json)
    assert err is not None
    assert "Order ID cannot be empty" in str(err)

    # Test with minimal valid order (contract should provide defaults)
    minimal_order = {
        "_id": "minimal_test_order"
    }
    minimal_order_json = json.dumps(minimal_order)
    result, err = vcloudClient.execute("create_order", str, minimal_order_json)
    assert err is None  # Should succeed with defaults

    # Verify the order was created with default values
    result, err = vcloudClient.executeReadOnly("get_order", str, "minimal_test_order")
    assert err is None
    retrieved_order = json.loads(result)

    # Verify default values were applied
    assert retrieved_order["_id"] == "minimal_test_order"
    assert retrieved_order["amount"] == 0.0  # Default value
    assert retrieved_order["amountPaid"] == 0.0  # Default value
    assert retrieved_order["provider"] == ""  # Default value
    assert retrieved_order["status"] == ""  # Default value
    assert retrieved_order["userServiceIDs"] == []  # Default value
    assert retrieved_order["items"] == []  # Default value
    assert retrieved_order["createdAt"] > 0  # Should be auto-set
    assert retrieved_order["updatedAt"] > 0  # Should be auto-set


def test_order_lifecycle():
    """Test complete order lifecycle: create -> update -> query -> process"""
    order_id_base = "lifecycle_test"

    # Step 1: Create order
    order = create_test_order(order_id_base, "0xlifecycle_addr", "lifecycle_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("create_order", str, order_json)
    assert err is None
    actual_order_id = result
    assert actual_order_id == order["_id"]

    # Step 2: Verify creation
    result, err = vcloudClient.executeReadOnly("get_order", str, actual_order_id)
    assert err is None
    retrieved_order = json.loads(result)
    assert retrieved_order["status"] == "pending"
    assert retrieved_order["amount"] == 500.0

    # Step 3: Update order (process payment)
    order["status"] = "paid"
    order["amountPaid"] = 500.0
    order["paidTS"] = int(time.time() * **********)
    updated_order_json = json.dumps(order)
    result, err = vcloudClient.execute("update_order", None, updated_order_json)
    assert err is None

    # Step 4: Verify update
    result, err = vcloudClient.executeReadOnly("get_order", str, actual_order_id)
    assert err is None
    updated_order = json.loads(result)
    assert updated_order["status"] == "paid"
    assert updated_order["amountPaid"] == 500.0
    assert updated_order["paidTS"] > 0

    # Step 5: Query to find the order
    query_params = {
        "_ids": [],
        "serviceID": "",
        "service": "",
        "address": "0xlifecycle_addr",
        "recipient": "",
        "type": "",
        "statuses": ["paid"],
        "tsStart": None,
        "tsEnd": None
    }
    result, err = vcloudClient.executeReadOnly("query_orders", str, json.dumps(query_params))
    assert err is None
    queried_orders = json.loads(result)
    assert len(queried_orders) >= 1
    found_order = None
    for ord in queried_orders:
        if ord["_id"] == actual_order_id:
            found_order = ord
            break
    assert found_order is not None


def test_order_edge_cases():
    """Test edge cases and error handling for order queries"""
    # Test with empty IDs list
    query_params = {
        "_ids": [],
        "serviceID": "",
        "service": "",
        "address": "",
        "recipient": "",
        "type": "",
        "statuses": [],
        "tsStart": None,
        "tsEnd": None
    }
    result, err = vcloudClient.executeReadOnly("query_orders", str, json.dumps(query_params))
    assert err is None
    queried_orders = json.loads(result)
    # Should return all orders (may be empty if no orders exist)

    # Test with invalid JSON for query
    result, err = vcloudClient.executeReadOnly("query_orders", str, "invalid_json")
    assert err is not None

    # Create an order and test filtering with non-existent values
    order = create_test_order("edge_test_1")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("create_order", str, order_json)
    assert err is None

    # Test query with non-existent address
    query_params = {
        "_ids": [],
        "serviceID": "",
        "service": "",
        "address": "0xnonexistent",
        "recipient": "",
        "type": "",
        "statuses": [],
        "tsStart": None,
        "tsEnd": None
    }
    result, err = vcloudClient.executeReadOnly("query_orders", str, json.dumps(query_params))
    assert err is None
    queried_orders = json.loads(result)
    assert len(queried_orders) == 0

    # Test query with non-existent status
    query_params = {
        "_ids": [],
        "serviceID": "",
        "service": "",
        "address": "",
        "recipient": "",
        "type": "",
        "statuses": ["nonexistent_status"],
        "tsStart": None,
        "tsEnd": None
    }
    result, err = vcloudClient.executeReadOnly("query_orders", str, json.dumps(query_params))
    assert err is None
    queried_orders = json.loads(result)
    assert len(queried_orders) == 0


def test_count_orders():
    """Test counting orders with various filters"""
    # Create test orders with different attributes
    orders = [
        create_test_order("count_test_1", "0xaddr1", "provider1", "pending"),
        create_test_order("count_test_2", "0xaddr1", "provider2", "paid"),
        create_test_order("count_test_3", "0xaddr2", "provider1", "pending"),
        create_test_order("count_test_4", "0xaddr2", "provider2", "paid"),
        create_test_order("count_test_5", "0xaddr1", "provider1", "cancelled"),
    ]

    # Create all orders
    order_ids = []
    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("create_order", str, order_json)
        assert err is None
        order_ids.append(order["_id"])

    # Test 1: Count all orders (no filters)
    result, err = vcloudClient.executeReadOnly("count_orders", str, None, None, None, None, None, None)
    assert err is None
    total_count = int(result)
    assert total_count >= 5  # At least our test orders

    # Test 2: Count by address
    result, err = vcloudClient.executeReadOnly("count_orders", str, None, "0xaddr1", None, None, None, None)
    assert err is None
    addr1_count = int(result)
    assert addr1_count >= 3  # 3 orders for addr1

    # Test 3: Count by recipient (using the default recipient from create_test_order)
    result, err = vcloudClient.executeReadOnly("count_orders", str, None, None, "0xrecipient_address_123", None, None, None)
    assert err is None
    recipient_count = int(result)
    assert recipient_count >= 5  # All our test orders use the same default recipient

    # Test 4: Count by service (provider)
    result, err = vcloudClient.executeReadOnly("count_orders", str, None, None, None, "provider1", None, None)
    assert err is None
    provider1_count = int(result)
    assert provider1_count >= 2  # 2 orders for provider1

    # Test 5: Count by order type
    result, err = vcloudClient.executeReadOnly("count_orders", str, None, None, None, None, "compute_order", None)
    assert err is None
    service_type_count = int(result)
    assert service_type_count >= 5  # All our test orders are "compute_order" type

    # Test 6: Count by statuses
    result, err = vcloudClient.executeReadOnly("count_orders", str, None, None, None, None, None, ["pending"])
    assert err is None
    pending_count = int(result)
    assert pending_count >= 2  # 2 pending orders

    # Test 7: Count by multiple statuses
    result, err = vcloudClient.executeReadOnly("count_orders", str, None, None, None, None, None, ["pending", "paid"])
    assert err is None
    pending_paid_count = int(result)
    assert pending_paid_count >= 4  # 2 pending + 2 paid orders

    # Test 8: Count by IDs
    result, err = vcloudClient.executeReadOnly("count_orders", str, order_ids[:3], None, None, None, None, None)
    assert err is None
    ids_count = int(result)
    assert ids_count == 3  # Exactly 3 orders by IDs


def test_count_orders_advanced():
    """Test advanced counting with complex filters and pagination parameters"""
    # Create test orders with specific attributes for advanced testing
    current_time = int(time.time() * **********)  # nanoseconds

    orders = [
        create_test_order("adv_count_1", "0xadvanced_addr", "advanced_provider", "pending"),
        create_test_order("adv_count_2", "0xadvanced_addr", "advanced_provider", "paid"),
        create_test_order("adv_count_3", "0xadvanced_addr", "other_provider", "pending"),
        create_test_order("adv_count_4", "0xother_addr", "advanced_provider", "paid"),
    ]

    # Set different timestamps for time-based filtering
    orders[0]["paidTS"] = current_time - **********  # 3 seconds ago
    orders[1]["paidTS"] = current_time - **********  # 2 seconds ago
    orders[2]["filedTS"] = current_time - **********  # 1 second ago
    orders[3]["paidTS"] = current_time - 500000000   # 0.5 seconds ago

    # Create all orders
    order_ids = []
    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("create_order", str, order_json)
        assert err is None
        order_ids.append(order["_id"])

    # Test 1: Count with address filter
    query_params = {
        "_ids": [],
        "serviceID": "",
        "service": "",
        "address": "0xadvanced_addr",
        "recipient": "",
        "type": "",
        "statuses": [],
        "tsStart": None,
        "tsEnd": None,
        "limit": None,
        "offset": None,
        "sortDesc": None
    }
    result, err = vcloudClient.executeReadOnly("count_orders_advanced", str, json.dumps(query_params))
    assert err is None
    addr_count = int(result)
    assert addr_count >= 3  # 3 orders for advanced_addr

    # Test 2: Count with address + status filter
    query_params["statuses"] = ["pending"]
    result, err = vcloudClient.executeReadOnly("count_orders_advanced", str, json.dumps(query_params))
    assert err is None
    addr_pending_count = int(result)
    assert addr_pending_count >= 2  # 2 pending orders for advanced_addr

    # Test 3: Count with service filter
    query_params = {
        "_ids": [],
        "serviceID": "",
        "service": "advanced_provider",
        "address": "",
        "recipient": "",
        "type": "",
        "statuses": [],
        "tsStart": None,
        "tsEnd": None,
        "limit": None,
        "offset": None,
        "sortDesc": None
    }
    result, err = vcloudClient.executeReadOnly("count_orders_advanced", str, json.dumps(query_params))
    assert err is None
    service_count = int(result)
    assert service_count >= 3  # 3 orders for advanced_provider

    # Test 4: Count with time range filter
    query_params = {
        "_ids": [],
        "serviceID": "",
        "service": "",
        "address": "",
        "recipient": "",
        "type": "",
        "statuses": [],
        "tsStart": current_time - **********,  # 2.5 seconds ago
        "tsEnd": current_time,                  # now
        "limit": None,
        "offset": None,
        "sortDesc": None
    }
    result, err = vcloudClient.executeReadOnly("count_orders_advanced", str, json.dumps(query_params))
    assert err is None
    time_range_count = int(result)
    assert time_range_count >= 3  # Orders within time range

    # Test 5: Count with IDs filter
    query_params = {
        "_ids": order_ids[:2],
        "serviceID": "",
        "service": "",
        "address": "",
        "recipient": "",
        "type": "",
        "statuses": [],
        "tsStart": None,
        "tsEnd": None,
        "limit": None,
        "offset": None,
        "sortDesc": None
    }
    result, err = vcloudClient.executeReadOnly("count_orders_advanced", str, json.dumps(query_params))
    assert err is None
    ids_count = int(result)
    assert ids_count == 2  # Exactly 2 orders by IDs


def test_order_pagination_and_sorting():
    """Test order queries with pagination and sorting functionality"""
    # Create test orders with different timestamps for sorting
    current_time = int(time.time() * **********)

    orders = []
    for i in range(8):  # Create 8 orders for pagination testing
        order = create_test_order(f"page_test_{i}", "0xpage_addr", "page_provider", "pending")
        # Set different created timestamps to test sorting
        order["createdAt"] = current_time - (i * **********)  # 1 second intervals
        orders.append(order)

    # Create all orders
    order_ids = []
    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("create_order", str, order_json)
        assert err is None
        order_ids.append(order["_id"])

    # Test 1: Query with pagination (limit and offset)
    query_params = {
        "_ids": [],
        "serviceID": "",
        "service": "",
        "address": "0xpage_addr",
        "recipient": "",
        "type": "",
        "statuses": [],
        "tsStart": None,
        "tsEnd": None,
        "limit": 3,
        "offset": 0,
        "sortDesc": False
    }
    result, err = vcloudClient.executeReadOnly("query_orders", str, json.dumps(query_params))
    assert err is None
    first_page = json.loads(result)
    assert len(first_page) <= 3  # Should return at most 3 orders

    # Test 2: Query second page
    query_params["offset"] = 3
    result, err = vcloudClient.executeReadOnly("query_orders", str, json.dumps(query_params))
    assert err is None
    second_page = json.loads(result)
    assert len(second_page) <= 3  # Should return at most 3 orders

    # Test 3: Verify no overlap between pages
    first_page_ids = {order["_id"] for order in first_page}
    second_page_ids = {order["_id"] for order in second_page}
    assert len(first_page_ids.intersection(second_page_ids)) == 0  # No overlap

    # Test 4: Query with descending sort
    query_params = {
        "_ids": [],
        "serviceID": "",
        "service": "",
        "address": "0xpage_addr",
        "recipient": "",
        "type": "",
        "statuses": [],
        "tsStart": None,
        "tsEnd": None,
        "limit": 5,
        "offset": 0,
        "sortDesc": True
    }
    result, err = vcloudClient.executeReadOnly("query_orders", str, json.dumps(query_params))
    assert err is None
    desc_orders = json.loads(result)

    # Verify descending order by created_at (newer orders first)
    if len(desc_orders) >= 2:
        for i in range(len(desc_orders) - 1):
            assert desc_orders[i]["createdAt"] >= desc_orders[i + 1]["createdAt"]

    # Test 5: Query with ascending sort
    query_params["sortDesc"] = False
    result, err = vcloudClient.executeReadOnly("query_orders", str, json.dumps(query_params))
    assert err is None
    asc_orders = json.loads(result)

    # Verify ascending order by created_at (older orders first)
    if len(asc_orders) >= 2:
        for i in range(len(asc_orders) - 1):
            assert asc_orders[i]["createdAt"] <= asc_orders[i + 1]["createdAt"]
