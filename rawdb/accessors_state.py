from __future__ import annotations

from vgraphdb import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KeyValueWriter

from .scheme import _decodeUsageCount, _encodeUsageCount, codeKey, codeUsageCount<PERSON>ey


def readCode(db: KeyValueReader, codeHash: bytes) -> bytes:
    """
    ReadCode retrieves the contract code of the provided code hash.
    """
    return db.get(codeKey(codeHash))


def containsCode(db: KeyValueReader, codeHash: bytes) -> bool:
    """
    ContainsCode returns whether the contract code of the provided code hash exists.
    """
    return codeKey(codeHash) in db


def writeCode(db: KeyValueWriter, codeHash: bytes, code: bytes):
    """
    WriteCode writes the provided contract code into database.
    """
    db.put(codeKey(codeHash), code)


def deleteCode(db: KeyValueWriter, codeHash: bytes):
    """
    DeleteCode removes the contract code of the provided code hash from the database.
    """
    db.delete(codeKey(codeHash))


def readCodeUsageCount(db: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, codeHash: bytes) -> int:
    """
    ReadCodeUsageCount retrieves the usage count of the provided code hash.
    """
    currentUsage = db.get(codeUsageCountKey(codeHash))
    if currentUsage is None:
        return 0
    return _decodeUsageCount(currentUsage)


def addCodeUsageCount(db: KeyValueWriter, codeHash: bytes, count: int = 1):
    """
    AddCodeUsageCount adds the provided count to the usage count of the provided code hash.
    """
    db.put(codeUsageCountKey(codeHash), _encodeUsageCount(readCodeUsageCount(db, codeHash) + count))


def subCodeUsageCount(db: KeyValueWriter, codeHash: bytes, count: int = 1):
    """
    SubCodeUsageCount subtracts the provided count from the usage count of the provided code hash.
    """
    currentCount = readCodeUsageCount(db, codeHash)

    if currentCount <= count:
        db.delete(codeUsageCountKey(codeHash))
        deleteCode(db, codeHash)
        return

    db.put(codeUsageCountKey(codeHash), _encodeUsageCount(currentCount - count))
