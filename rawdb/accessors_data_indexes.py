import vgraphdb
from rawdb.scheme import contractDataIndexKey, contractDataIndexPrefix

# Maximum allowed length for index keys
INDEX_KEY_LENGTH_LIMIT = 255


def writeContractDataIndex(db: vgraphdb.KeyValueWriter, contractAddress: bytes, index: bytes, dataKey: bytes):
    if len(index) > INDEX_KEY_LENGTH_LIMIT:
        raise ValueError(f"index key length must not exceed {INDEX_KEY_LENGTH_LIMIT} bytes")
    db.put(contractDataIndexKey(contractAddress, index), dataKey)


def readContractDataIndex(db: vgraphdb.KeyValueReader, contractAddress: bytes, index: bytes):
    if len(index) > INDEX_KEY_LENGTH_LIMIT:
        raise ValueError(f"index key length must not exceed {INDEX_KEY_LENGTH_LIMIT} bytes")
    return db.get(contractDataIndexKey(contractAddress, index))


def deleteContractDataIndex(db: vgraphdb.KeyValueWriter, contractAddress: bytes, index: bytes):
    if len(index) > INDEX_KEY_LENGTH_LIMIT:
        raise ValueError(f"index key length must not exceed {INDEX_KEY_LENGTH_LIMIT} bytes")
    db.delete(contractDataIndexKey(contractAddress, index))


def getContractDataIndexIterator(
    db: vgraphdb.Iteratee, contractAddress: bytes, reverse: bool, start: bytes = b"", end: bytes = b""
) -> vgraphdb.DBIterator:
    if reverse:
        return db.newReverseIterator(contractDataIndexPrefix(contractAddress), start, end)
    else:
        return db.newIterator(contractDataIndexPrefix(contractAddress), start, end)
