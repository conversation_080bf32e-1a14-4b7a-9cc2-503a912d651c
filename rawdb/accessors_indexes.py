from typing import Optional, <PERSON><PERSON>

import kivy

import vgraphdb
from common.consts import HeightKeyLength
from common.models import Block, Receipt, Transaction
from rawdb import readCanonicalHash
from rawdb.accessors_chain import readBody, readReceipts
from rawdb.scheme import transactionLookup<PERSON>ey


def readTransactionLookupEntry(db: vgraphdb.KeyValueReader, hash: bytes) -> Optional[int]:
    """
    ReadTransactionLookupEntry retrieves the positional metadata associated with a transaction
    hash to allow retrieving the transaction or receipt by hash.
    """
    # tx lookup just stores the block number
    heightBytes = db.get(transactionLookupKey(hash))
    return int.from_bytes(heightBytes, "big") if heightBytes is not None else None


def writeTransactionLookupEntry(db: vgraphdb.KeyValueWriter, hash: bytes, heightBytes: bytes):
    """
    WriteTransactionLookupEntry writes the positional metadata associated with a transaction
    hash to allow retrieving the transaction or receipt by hash.
    """
    db.put(transactionLookupKey(hash), heightBytes)


def writeTxLookupEntriesByBlock(db: vgraphdb.KeyValueWriter, block: Block):
    """
    WriteTxLookupEntriesByBlock stores a positional metadata for every transaction from
    a block, enabling hash based transaction and receipt lookups.
    """
    heightBytes = int.to_bytes(block.height(), HeightKeyLength, "big")
    for tx in block.transactions:
        writeTransactionLookupEntry(db, tx.hash(), heightBytes)


def deleteTransactionLookupEntry(db: vgraphdb.KeyValueWriter, hash: bytes):
    """
    DeleteTransactionLookupEntry deletes the positional metadata associated with a transaction
    hash to allow retrieving the transaction or receipt by hash.
    """
    db.delete(transactionLookupKey(hash))


def readTransaction(
    db: vgraphdb.KeyValueReader, hash: bytes
) -> Tuple[Optional[Transaction], Optional[bytes], int, int]:
    """
    ReadTransaction retrieves the raw transaction data associated with a hash.
    :returns: the transaction, the block hash, the block height, and the transaction index
    """
    blockHeight = readTransactionLookupEntry(db, hash)
    if blockHeight is None:
        return None, None, 0, 0
    blockHash = readCanonicalHash(db, blockHeight)
    if blockHash is None:
        return None, None, 0, 0
    body = readBody(db, blockHash, blockHeight)
    if body is None:
        kivy.Logger.error(f"Transaction referenced missing height {blockHeight} hash {blockHash}")
        return None, None, 0, 0
    for txIndex, tx in enumerate(body.transactions):
        if tx.hash() == hash:
            return tx, blockHash, blockHeight, txIndex
    kivy.Logger.error(f"Transaction not found height {blockHeight} hash {blockHash}, transactionHash {hash}")
    return None, None, 0, 0


def readReceipt(db: vgraphdb.KeyValueReader, hash: bytes) -> Optional[Receipt]:
    """
    ReadReceipt retrieves the raw transaction receipt data associated with a hash.
    """
    blockHeight = readTransactionLookupEntry(db, hash)
    if blockHeight is None:
        return None
    blockHash = readCanonicalHash(db, blockHeight)
    if blockHash is None:
        return None
    receipts = readReceipts(db, blockHash, blockHeight)
    for receipt in receipts:
        if receipt.transaction_hash == hash:
            return receipt
    kivy.Logger.error(f"Receipt not found height {blockHeight} hash {blockHash}, transactionHash {hash}")
    return None
