from typing import List, Optional, Tuple, Type, TypeVar, Union

import kivy

import vgraphdb
from common.consts import HashLength, HeightKeyLength
from common.encode import decodeRLP, encodeRLP
from common.models import AbstractHeader, Block, Body, Log, Receipt, Receipts

from .scheme import (
    _encodeB<PERSON><PERSON>eight,
    _head<PERSON>lock<PERSON><PERSON>,
    _headerHashSuffix,
    _headerPrefix,
    _head<PERSON><PERSON>er<PERSON><PERSON>,
    blockBody<PERSON>ey,
    blockReceipts<PERSON>ey,
    headerHash<PERSON>ey,
    headerHeightKey,
    headerKey,
    headerKeyPrefix,
)

H = TypeVar("H", bound=AbstractHeader)


def readCanonicalHash(db: vgraphdb.KeyValueReader, height: int) -> Optional[bytes]:
    """
    ReadCanonicalHash retrieves the hash assigned to a canonical block height.
    """
    return db.get(headerHashKey(height))


def writeCanonicalHash(db: vgraphdb.KeyValueWriter, hash: bytes, height: int):
    """
    WriteCanonicalHash stores the hash assigned to a canonical block height.
    """
    db.put(headerHashKey(height), hash)


def deleteCanonicalHash(db: vgraphdb.KeyValueWriter, height: int):
    """
    DeleteCanonicalHash removes the height to hash canonical mapping.
    """
    db.delete(headerHashKey(height))


def readAllHashes(db: vgraphdb.Iteratee, height: int) -> List[bytes]:
    """
    ReadAllHashes retrieves all the hashes assigned to blocks at a certain heights,
    both canonical and reorged forks included.
    """
    prefix = headerKeyPrefix(height)
    hashes = []

    it = db.newIterator(prefix)
    while it.next():
        key = it.key()
        if len(key) == len(prefix) + HashLength:
            hashes.append(key[len(key) - HashLength :])

    it.release()
    return hashes


class HeightHash:
    def __init__(self, height: int, hash: bytes):
        self.height = height
        self.hash = hash


def readAllHashesInRange(db: vgraphdb.Iteratee, first: int, last: int) -> List[HeightHash]:
    """
    ReadAllHashesInRange retrieves all the hashes assigned to blocks at certain
    heights, both canonical and reorged forks included.
    """
    start = _encodeBlockHeight(first)
    keyLength = len(_headerPrefix) + HeightKeyLength + HashLength
    hashes = []
    it = db.newIterator(_headerPrefix, start)

    while it.next():
        key = it.key()
        if len(key) != keyLength:
            continue
        height = int.from_bytes(key[len(_headerPrefix) : len(_headerPrefix) + HeightKeyLength], "big")
        if height > last:
            break
        hash = key[len(key) - HashLength :]
        hashes.append(HeightHash(height, hash))

    it.release()
    return hashes


def readAllCanonicalHashes(
    db: vgraphdb.Iteratee, fromHeight: int, toHeight: int, limit: int
) -> Tuple[Union[List[int], None], Union[List[bytes], None]]:
    """
    ReadAllCanonicalHashes retrieves all canonical height and hash mappings at the
    certain chain range. If the accumulated entries reaches the given threshold,
    abort the iteration and return the semi-finish result.
    """
    # Short circuit if the limit is 0.
    if limit == 0:
        return None, None

    heights = []
    hashes = []
    # Construct the key prefix of the start point.
    start, end = headerHashKey(fromHeight), headerHashKey(toHeight)
    it = db.newIterator(b"", start)
    while it.next():
        key = it.key()
        if key > end:
            break
        if len(key) == len(_headerPrefix) + HeightKeyLength + 1 and key[len(key) - 1 :] == _headerHashSuffix:
            heights.append(int.from_bytes(key[len(_headerPrefix) : len(_headerPrefix) + HeightKeyLength], "big"))
            hashes.append(it.value())
            # If the accumulated entries reaches the limit threshold, return.
            if len(heights) >= limit:
                break

    it.release()
    return heights, hashes


def readHeaderHeight(db: vgraphdb.KeyValueReader, hash: bytes) -> Optional[int]:
    """
    ReadHeaderHeight returns the header height assigned to a hash.
    """
    data = db.get(headerHeightKey(hash))
    if data is None or len(data) != HeightKeyLength:
        return None
    return int.from_bytes(data, "big")


def writeHeaderHeight(db: vgraphdb.KeyValueWriter, hash: bytes, height: int):
    """
    WriteHeaderHeight stores the hash->height mapping.
    """
    try:
        key = headerHeightKey(hash)
        blockHeight = _encodeBlockHeight(height)
        db.put(key, blockHeight)
    except Exception as e:
        kivy.Logger.critical(f"Failed to store hash to height mapping, err {e}")


def deleteHeaderHeight(db: vgraphdb.KeyValueWriter, hash: bytes):
    """
    DeleteHeaderHeight removes hash->height mapping.
    """
    try:
        db.delete(headerHeightKey(hash))
    except Exception as e:
        kivy.Logger.critical(f"Failed to remove hash to height mapping, err {e}")


def readHeadHeaderHash(db: vgraphdb.KeyValueReader) -> Optional[bytes]:
    """
    ReadHeadHeaderHash retrieves the hash of the current canonical head header.
    """
    data = db.get(_headHeaderKey)
    if data is None or len(data) == 0:
        return None
    return data


def writeHeadHeaderHash(db: vgraphdb.KeyValueWriter, hash: bytes):
    """
    WriteHeadHeaderHash stores the hash of the current canonical head header.
    """
    try:
        db.put(_headHeaderKey, hash)
    except Exception as e:
        kivy.Logger.critical(f"Failed to store head header hash, err {e}")


def readHeadBlockHash(db: vgraphdb.KeyValueReader) -> Optional[bytes]:
    """
    ReadHeadBlockHash retrieves the hash of the current canonical head block.
    """
    data = db.get(_headBlockKey)
    if data is None or len(data) == 0:
        return None
    return data


def writeHeadBlockHash(db: vgraphdb.KeyValueWriter, hash: bytes):
    """
    WriteHeadBlockHash stores the hash of the current canonical head block.
    """
    try:
        db.put(_headBlockKey, hash)
    except Exception as e:
        kivy.Logger.critical(f"Failed to store head block hash, err {e}")


def readHeaderRLP(db: vgraphdb.KeyValueReader, hash: bytes, height: int) -> Optional[bytes]:
    """
    ReadHeaderRLP retrieves a block header from the database in RLP format.
    """
    return db.get(headerKey(height, hash))


def hasHeader(db: vgraphdb.KeyValueReader, hash: bytes, height: int) -> bool:
    """
    HasHeader verifies the existence of a block header corresponding to the hash.
    """
    return db.contains(headerKey(height, hash))


def readHeader(db: vgraphdb.KeyValueReader, headerType: Type[H], hash: bytes, height: int) -> Optional[H]:
    """
    ReadHeader retrieves a block header from the database.
    """
    data = readHeaderRLP(db, hash, height)
    if data is None:
        return None
    try:
        return decodeRLP(headerType, data)
    except Exception as e:
        kivy.Logger.error(f"Invalid block header RLP hash {hash} err {e}")
        return None


def writeHeader(db: vgraphdb.KeyValueWriter, header: AbstractHeader):
    """
    WriteHeader stores a block header into the database and also stores the hash->height mapping.
    """
    hash = header.hash()
    height = header.height
    # Write the hash->header mapping.
    writeHeaderHeight(db, hash, height)

    # Write the encoded header.
    try:
        data = encodeRLP(header)
    except Exception as e:
        kivy.Logger.critical(f"Failed to RLP encode, err {e}")
    key = headerKey(height, hash)
    try:
        db.put(key, data)
    except Exception as e:
        kivy.Logger.critical(f"Failed to store header, err {e}")


def _deleteHeaderWithoutHeight(db: vgraphdb.KeyValueWriter, hash: bytes, height: int):
    """
    DeleteHeaderWithoutHeight removes only the block header but does not remove the hash to number mapping.
    """
    try:
        db.delete(headerKey(height, hash))
    except Exception as e:
        kivy.Logger.critical(f"Failed to remove header, err {e}")


def deleteHeader(db: vgraphdb.KeyValueWriter, hash: bytes, height: int):
    """
    DeleteHeader removes all block header data associated with a hash.
    """
    _deleteHeaderWithoutHeight(db, hash, height)
    try:
        db.delete(headerHeightKey(hash))
    except Exception as e:
        kivy.Logger.critical(f"Failed to remove hash to height mapping, err {e}")


def readBodyRLP(db: vgraphdb.KeyValueReader, hash: bytes, height: int) -> Optional[bytes]:
    """
    ReadBodyRLP retrieves a block body from the database in RLP format.
    """
    return db.get(blockBodyKey(height, hash))


def writeBodyRLP(db: vgraphdb.KeyValueWriter, hash: bytes, height: int, data: bytes):
    """
    WriteBodyRLP stores a block body into the database.
    """
    try:
        db.put(blockBodyKey(height, hash), data)
    except Exception as e:
        kivy.Logger.critical(f"Failed to store block body, err {e}")


def hasBody(db: vgraphdb.KeyValueReader, hash: bytes, height: int) -> bool:
    """
    HasBody verifies the existence of a block body corresponding to the hash.
    """
    return db.contains(blockBodyKey(height, hash))


def readBody(db: vgraphdb.KeyValueReader, hash: bytes, height: int) -> Optional[Body]:
    """
    ReadBody retrieves a block body from the database.
    """
    data = readBodyRLP(db, hash, height)
    if data is None:
        return None
    try:
        return decodeRLP(Body, data)
    except Exception as e:
        kivy.Logger.error(f"Invalid block body RLP hash {hash} err {e}")
        return None


def writeBody(db: vgraphdb.KeyValueWriter, hash: bytes, height: int, body: Body):
    """
    WriteBody stores a block body into the database.
    """
    data = encodeRLP(body)
    writeBodyRLP(db, hash, height, data)


def deleteBody(db: vgraphdb.KeyValueWriter, hash: bytes, height: int):
    """
    DeleteBody removes all block body data associated with a hash.
    """
    try:
        db.delete(blockBodyKey(height, hash))
    except Exception as e:
        kivy.Logger.critical(f"Failed to remove block body, err {e}")


def hasReceipts(db: vgraphdb.KeyValueReader, hash: bytes, height: int) -> bool:
    """
    HasReceipts verifies the existence of all the transaction receipts belonging
    """
    return db.contains(blockReceiptsKey(height, hash))


def readReceiptsRLP(db: vgraphdb.KeyValueReader, hash: bytes, height: int) -> Optional[bytes]:
    """
    ReadReceiptsRLP retrieves all the transaction receipts belonging to a block from the database in RLP format.
    """
    return db.get(blockReceiptsKey(height, hash))


def readReceipts(db: vgraphdb.KeyValueReader, hash: bytes, height: int) -> Optional[List[Receipt]]:
    """
    ReadReceipt retrieves all the transaction receipts belonging to a block from the database.
    """
    data = readReceiptsRLP(db, hash, height)
    if data is None or len(data) == 0:
        return None
    try:
        return decodeRLP(Receipts, data)
    except Exception as e:
        kivy.Logger.error(f"Invalid block receipts RLP hash {hash} err {e}")
        return None


def writeReceipts(db: vgraphdb.KeyValueWriter, hash: bytes, height: int, receipts: List[Receipt]):
    """
    WriteReceipts stores all the transaction receipts belonging to a block into the database.
    """
    try:
        data = encodeRLP(Receipts(receipts))
        db.put(blockReceiptsKey(height, hash), data)
    except Exception as e:
        kivy.Logger.critical(f"Failed to store block receipts, err {e}")


def deleteReceipts(db: vgraphdb.KeyValueWriter, hash: bytes, height: int):
    """
    DeleteReceipts removes all receipt data associated with a block hash.
    """
    try:
        db.delete(blockReceiptsKey(height, hash))
    except Exception as e:
        kivy.Logger.critical(f"Failed to remove block receipts, err {e}")


def readLogs(db: vgraphdb.KeyValueReader, hash: bytes, height: int) -> Optional[List[Log]]:
    """
    readLogs retrieves all the logs belonging to a block from the database.
    """
    receipts = readReceipts(db, hash, height)
    if receipts is None:
        return None
    logs = [log for receipt in receipts for log in receipt.logs]
    return logs


def readBlock(db: vgraphdb.KeyValueReader, headerType: Type[H], hash: bytes, height: int) -> Optional[Block]:
    """
    ReadBlock retrieves an entire block corresponding to the hash, assembling it back from the stored header and body.
    If either the header or body could not be retrieved nil is returned. Note, due to concurrent download of header and
    block body the header and thus canonical hash can be stored in the database but the body data not (yet).
    """
    header = readHeader(db, headerType, hash, height)
    if header is None:
        return None
    body = readBody(db, hash, height)
    if body is None:
        return None
    return Block(header, body.transactions)


def writeBlock(db: vgraphdb.KeyValueWriter, block: Block):
    """
    WriteBlock stores an entire block into the database, storing both the header and body.
    """
    writeHeader(db, block.header)
    writeBody(db, block.header.hash(), block.header.height, Body(block.transactions))


def deleteBlock(db: vgraphdb.KeyValueWriter, hash: bytes, height: int):
    """
    DeleteBlock removes all block data associated with a hash.
    """
    deleteReceipts(db, hash, height)
    deleteHeader(db, hash, height)
    deleteBody(db, hash, height)


def deleteBlockWithoutHeight(db: vgraphdb.KeyValueWriter, hash: bytes, height: int):
    """
    DeleteBlockWithoutHeight removes all block data associated with a hash but does not remove the hash to number mapping.
    """
    deleteReceipts(db, hash, height)
    _deleteHeaderWithoutHeight(db, hash, height)
    deleteBody(db, hash, height)


# TODO: move find common ancestor to here


def readHeadHeader(db: vgraphdb.KeyValueReader, headerType: Type[H]) -> Optional[H]:
    """
    ReadHeadHeader retrieves the head header from the database.
    """
    headHeaderHash = readHeadHeaderHash(db)
    if headHeaderHash is None:
        return None
    headHeaderHeight = readHeaderHeight(db, headHeaderHash)
    return readHeader(db, headerType, headHeaderHash, headHeaderHeight)


def readHeadBlock(db: vgraphdb.KeyValueReader, headerType: Type[H]) -> Optional[Block]:
    """
    ReadHeadBlock retrieves the head block from the database.
    """
    headBlockHash = readHeadBlockHash(db)
    if headBlockHash is None:
        return None
    headBlockHeight = readHeaderHeight(db, headBlockHash)
    return readBlock(db, headerType, headBlockHash, headBlockHeight)
