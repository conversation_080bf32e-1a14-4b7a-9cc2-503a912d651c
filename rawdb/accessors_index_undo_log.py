from typing import Optional

import vgraphdb

# Prefixes for undo log entries
IndexUndoLogPrefix = b"U"  # IndexUndoLogPrefix + block number (uint64 big endian) -> undo log entries


def encodeBlockNumber(blockNumber: int) -> bytes:
    """
    Encode a block number as a big-endian 8-byte value.
    """
    return int.to_bytes(blockNumber, 8, "big")


def decodeBlockNumber(data: bytes) -> int:
    """
    Decode a block number from a big-endian 8-byte value.
    """
    return int.from_bytes(data, "big")


def indexUndoLogKey(blockNumber: int) -> bytes:
    """
    Create a key for storing undo log entries for a specific block.

    Args:
        blockNumber: The block number

    Returns:
        The key for the undo log entries
    """
    return IndexUndoLogPrefix + encodeBlockNumber(blockNumber)


def writeIndexUndoLog(db: vgraphdb.KeyValueWriter, blockNumber: int, undoLogData: bytes) -> None:
    """
    Write undo log entries for a specific block.

    Args:
        db: The database writer
        blockNumber: The block number
        undoLogData: The serialized undo log data
    """
    db.put(indexUndoLogKey(blockNumber), undoLogData)


def readIndexUndoLog(db: vgraphdb.KeyValueReader, blockNumber: int) -> Optional[bytes]:
    """
    Read undo log entries for a specific block.

    Args:
        db: The database reader
        blockNumber: The block number

    Returns:
        The serialized undo log data, or None if not found
    """
    return db.get(indexUndoLogKey(blockNumber))


def deleteIndexUndoLog(db: vgraphdb.KeyValueWriter, blockNumber: int) -> None:
    """
    Delete undo log entries for a specific block.

    Args:
        db: The database writer
        blockNumber: The block number
    """
    db.delete(indexUndoLogKey(blockNumber))


def getIndexUndoLogIterator(
    db: vgraphdb.KeyValueStore, reverse: bool = False, startBlock: int = 0, endBlock: int = 0
) -> vgraphdb.DBIterator:
    """
    Get an iterator over index undo log entries.

    Args:
        db: The database
        reverse: Whether to iterate forward (True) or backward (False)
        startBlock: The starting block number (inclusive)
        endBlock: The ending block number (exclusive)

    Returns:
        An iterator over (blockNumber, undoLogData) pairs
    """
    startKey = encodeBlockNumber(startBlock) if startBlock > 0 else b"\xff" * 8 if reverse else b"\x00" * 8
    endKey = encodeBlockNumber(endBlock) if endBlock > 0 else b"\x00" * 8 if reverse else b"\xff" * 8

    iteratorMethod = db.newReverseIterator if reverse else db.newIterator
    return iteratorMethod(IndexUndoLogPrefix, startKey, endKey)
