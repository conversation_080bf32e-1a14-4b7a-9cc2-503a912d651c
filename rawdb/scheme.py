import common

CodePrefix = b"c"  # CodePrefix + code hash -> account code
CodeUsageCountPrefix = b"u"  # CodeUsageCountPrefix + code hash -> code usage count

_headerPrefix = b"h"  # headerPrefix + height (uint64 big endian) + hash -> header
_headerHashSuffix = b"n"  # headerPrefix + height (uint64 big endian) + headerHashSuffix -> hash
_blockBodyPrefix = b"b"  # blockBodyKey + hash -> block body
_blockReceiptsPrefix = b"r"  # blockReceiptsPrefix + height (uint64 big endian) + hash -> block receipts
_transactionLookupPrefix = b"l"  # txLookupPrefix + hash -> transaction/receipt lookup metadata

ContractDataIndexPrefix = b"I"  # ContractDataIndexPrefix + index -> key on rawdb

# headHeader<PERSON>ey tracks the latest known header's hash.
_headHeaderKey = b"LastHeader"

# headBlock<PERSON>ey tracks the latest known full block's hash.
_headBlockKey = b"LastBlock"


def codeKey(hash: bytes) -> bytes:
    """
    codeKey = CodePrefix + hash
    """
    return CodePrefix + hash


def codeUsageCountKey(hash: bytes) -> bytes:
    """
    codeUsageCountKey = CodeUsageCountPrefix + hash
    """
    return CodeUsageCountPrefix + hash


def _encodeUsageCount(usage: int) -> bytes:
    return int.to_bytes(usage, common.CodeUsageCountLength, "big")


def _decodeUsageCount(data: bytes) -> int:
    return int.from_bytes(data, "big")


def _encodeBlockHeight(height: int) -> bytes:
    return int.to_bytes(height, common.HeightKeyLength, "big")


def headerHashKey(height: int) -> bytes:
    """
    headerHashKey = headerHashKey = headerPrefix + height (uint64 big endian) + headerHashSuffix
    """
    return _headerPrefix + _encodeBlockHeight(height) + _headerHashSuffix


def headerKeyPrefix(height: int) -> bytes:
    """
    headerKeyPrefix = headerPrefix + height (uint64 big endian)
    """
    return _headerPrefix + _encodeBlockHeight(height)


def headerKey(height: int, hash: bytes) -> bytes:
    """
    headerKey = headerKey = headerPrefix + height (uint64 big endian) + hash
    """
    return _headerPrefix + _encodeBlockHeight(height) + hash


def headerHeightKey(hash: bytes) -> bytes:
    """
    headerHeightKey = headerHeightPrefix + hash
    """
    return _headerPrefix + hash


def blockBodyKey(height: int, hash: bytes) -> bytes:
    """
    blockBodyKey = blockBodyPrefix + height (uint64 big endian) + hash
    """
    return _blockBodyPrefix + _encodeBlockHeight(height) + hash


def blockReceiptsKey(height: int, hash: bytes) -> bytes:
    """
    blockReceiptsKey = blockReceiptsPrefix + height (uint64 big endian) + hash
    """
    return _blockReceiptsPrefix + _encodeBlockHeight(height) + hash


def transactionLookupKey(hash: bytes) -> bytes:
    """
    transactionLookupKey = transactionLookupPrefix + hash
    """
    return _transactionLookupPrefix + hash


def contractDataIndexKey(address: bytes, index: bytes) -> bytes:
    """
    contractDataIndexKey = ContractDataIndexPrefix + address + index
    """
    return ContractDataIndexPrefix + address + index


def contractDataIndexPrefix(address: bytes) -> bytes:
    """
    contractDataIndexPrefix = ContractDataIndexPrefix + address
    """
    return ContractDataIndexPrefix + address
