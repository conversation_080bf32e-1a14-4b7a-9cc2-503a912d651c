# Contract build instruction

## Environment Configuration

### NixOS

#### Edit NixOS configuration `sudo nano /etc/nixos/configuration.nix`

1. Enable nix experimental-features nix-command and flakes. Or you can just use `nix build --extra-experimental-features "nix-command flakes"`

   ```nix
   nix = {
       package = pkgs.nix;
       extraOptions = ''
         experimental-features = nix-command flakes
       '';
   };
   ```

2. (option) If you want to use git, vim or others package in NixOS, you can search in https://search.nixos.org/ and add it into environment.systemPackages.

   ```nix
   environment.systemPackages = with pkgs; [
     ...
     vim
     git
     gnumake
     ...
   ];
   ```

3. Use `sudo nixos-rebuild switch` to apply config change.

### Linux / MacOS

1. Download and install nix from its website https://nixos.org/download
2. Enable flake in nix config file `/etc/nix/nix.conf` by adding the following line (multi-user way installs nix)

   single-user way installs nix in `~/.config/nix/nix.conf`

   ```
   experimental-features = nix-command flakes
   ```

## Build instruction

1. Create a `flake.nix` file in the Rust Contract project. 'outputHashes' item can be empty at first, run `nix build` to get hashes.

```nix
{
  description = "Rust wasm32-wasi build with wasi-sdk";

  inputs = {
    flake-utils.url = "github:numtide/flake-utils";
    rust-overlay = {
      url = "github:oxalica/rust-overlay";
      inputs.nixpkgs.follows = "nixpkgs";
    };
    nixpkgs.url = "nixpkgs/nixos-23.11";
  };

  outputs = { self, nixpkgs, flake-utils, rust-overlay }:
    flake-utils.lib.eachSystem [ "x86_64-linux" "aarch64-darwin" "aarch64-linux" "x86_64-darwin" ]
      (system: let
        overlays = [ rust-overlay.overlays.default ];
        pkgs = import nixpkgs { inherit system overlays; };
        rust = pkgs.rust-bin.fromRustupToolchainFile ./rust-toolchain.toml;
      in {
        packages = {
          rust-wasm32-wasi = pkgs.rustPlatform.buildRustPackage {
            pname = "rust-wasm32-wasi";
            version = "1.0.0";
            src = ./.;
            cargoLock = {
              lockFile = ./Cargo.lock;
               outputHashes = {
                  "glue-0.1.0" = "sha256-3y5lyWRoW0NpgWhClm4OtuhVlD3lub1xMyZfcMCrxbo=";
                  "token-0.1.0" = "sha256-cdt5dwpksGgWbOz2UZsguSTqso0NtjXW2k5uHpWEJB4=";
               };
            };
            nativeBuildInputs = [ rust ];

            checkPhase = "echo 'skip checkPhase'";

            buildPhase = ''
              mkdir -p $out;
              cargo build --release --target wasm32-wasi;
              cp target/wasm32-wasi/release/*.wasm $out;
            '';

            installPhase = "echo 'skip installPhase'";
          };
        };
        defaultPackage = self.packages.${system}.rust-wasm32-wasi;
      });
}
```

2.create file `rust-toolchain.toml` in the Rust project

```toml
[toolchain]
channel = "1.79.0"
components = []
targets = [ "wasm32-wasi" ]
profile = "default"
```

3. Build the Rust project to wasm

```sh
nix build
```

Or use follow If you don't config nix experimental-features

```sh
nix build --extra-experimental-features "nix-command flakes"
```

The wasm file will in the `result` directory.
