[[source]]
name = "pypi"
url = "https://pypi.org/simple"
verify_ssl = true

[dev-packages]
pytest = "*"
pytest-benchmark = "*"
pytest-asyncio = "*"
ruff = "*"

[packages]
kivy = "==2.2.1"
cython = "*"
#buildozer = "==1.3.0"
wasmtime = "*"
#oscpy = "*"
pyserde = "==0.15.0"
orjson = "*"
aiorpcx = "*"
pyyaml = "*"
lmdb = "*"
pycryptodome = "*"
ntplib = "*"
base58 = "*"
attrs = "*"
rlp = "*"
rusty-rlp = { git = "git+https://github.com/zshimonz/rusty-rlp.git" }
trie = "*"
readerwriterlock = "*"
cachetools = "*"
python-multipart = "*"
fastapi = "*"
uvicorn = "*"
uvloop = "*"
websockets = "*"

# for vgraph watcher
ruamel-yaml = "*"

[requires]
python_version = "3.8"
