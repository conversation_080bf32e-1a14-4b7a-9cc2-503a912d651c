# Copyright © 2021 Primecoin Project

# docker buildx build --platform linux/arm64,linux/amd64 --load -t vgraph .

FROM python:3.8-buster AS builder

# Setup env
ENV LANG=C.UTF-8 LC_ALL=C.UTF-8 PYTHONDONTWRITEBYTECODE=1 PYTHONFAULTHANDLER=1

# Install Rust and Cargo
RUN apt-get update \
    && apt-get install -y --no-install-recommends curl gcc g++ make \
    && curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y \
    && export PATH="$HOME/.cargo/bin:$PATH" \
    && . $HOME/.cargo/env \
    && rustup default stable \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Add Rust binaries to PATH (for all subsequent RUN commands)
ENV PATH="/root/.cargo/bin:${PATH}"
RUN rustc --version && cargo --version

# install pipenv
COPY ./Pipfile ./Pipfile.lock ./
RUN pip install pipenv \
    && pipenv install --deploy --ignore-pipfile --system


FROM python:3.8-slim-buster AS runtime

# Create and switch to a new user
RUN useradd --create-home vgraph
WORKDIR /home/<USER>

# Install application into container
COPY . .

# Ensure scripts are executable
RUN chmod a+x headless.sh

# copy dependencies
COPY --from=builder /usr/local/bin /usr/local/bin
COPY --from=builder /usr/local/lib/python3.8/site-packages /usr/local/lib/python3.8/site-packages


# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends xvfb libmtdev1 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*


# Run specification
ENV DISPLAY :99
# EXPOSE 8000/tcp
ENTRYPOINT ["/home/<USER>/headless.sh"]
