{"nodes": {"devenv": {"locked": {"dir": "src/modules", "lastModified": 1729681848, "owner": "cachix", "repo": "devenv", "rev": "2634c4c9e9226a3fb54550ad4115df1992d502c5", "type": "github"}, "original": {"dir": "src/modules", "owner": "cachix", "repo": "devenv", "type": "github"}}, "fenix": {"inputs": {"nixpkgs": ["nixpkgs"], "rust-analyzer-src": "rust-analyzer-src"}, "locked": {"lastModified": 1729751566, "owner": "nix-community", "repo": "fenix", "rev": "f32a2d484091a6dc98220b1f4a2c2d60b7c97c64", "type": "github"}, "original": {"owner": "nix-community", "repo": "fenix", "type": "github"}}, "flake-compat": {"flake": false, "locked": {"lastModified": 1696426674, "owner": "edols<PERSON>", "repo": "flake-compat", "rev": "0f9255e01c2351cc7d116c072cb317785dd33b33", "type": "github"}, "original": {"owner": "edols<PERSON>", "repo": "flake-compat", "type": "github"}}, "flake-compat_2": {"flake": false, "locked": {"lastModified": 1696426674, "owner": "edols<PERSON>", "repo": "flake-compat", "rev": "0f9255e01c2351cc7d116c072cb317785dd33b33", "type": "github"}, "original": {"owner": "edols<PERSON>", "repo": "flake-compat", "type": "github"}}, "gitignore": {"inputs": {"nixpkgs": ["pre-commit-hooks", "nixpkgs"]}, "locked": {"lastModified": 1709087332, "owner": "hercules-ci", "repo": "gitignore.nix", "rev": "637db329424fd7e46cf4185293b9cc8c88c95394", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "gitignore.nix", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1716977621, "owner": "cachix", "repo": "devenv-nixpkgs", "rev": "4267e705586473d3e5c8d50299e71503f16a6fb6", "type": "github"}, "original": {"owner": "cachix", "ref": "rolling", "repo": "devenv-nixpkgs", "type": "github"}}, "nixpkgs-python": {"inputs": {"flake-compat": "flake-compat", "nixpkgs": "nixpkgs_2"}, "locked": {"lastModified": 1729194050, "owner": "cachix", "repo": "nixpkgs-python", "rev": "5e4f20785932a23847191607d84c8806520454f2", "type": "github"}, "original": {"owner": "cachix", "repo": "nixpkgs-python", "type": "github"}}, "nixpkgs-stable": {"locked": {"lastModified": 1729449015, "owner": "NixOS", "repo": "nixpkgs", "rev": "89172919243df199fe237ba0f776c3e3e3d72367", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-24.05", "repo": "nixpkgs", "type": "github"}}, "nixpkgs_2": {"locked": {"lastModified": 1729449015, "owner": "NixOS", "repo": "nixpkgs", "rev": "89172919243df199fe237ba0f776c3e3e3d72367", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-24.05", "repo": "nixpkgs", "type": "github"}}, "pre-commit-hooks": {"inputs": {"flake-compat": "flake-compat_2", "gitignore": "gitignore", "nixpkgs": ["nixpkgs"], "nixpkgs-stable": "nixpkgs-stable"}, "locked": {"lastModified": 1729104314, "owner": "cachix", "repo": "pre-commit-hooks.nix", "rev": "3c3e88f0f544d6bb54329832616af7eb971b6be6", "type": "github"}, "original": {"owner": "cachix", "repo": "pre-commit-hooks.nix", "type": "github"}}, "root": {"inputs": {"devenv": "devenv", "fenix": "fenix", "nixpkgs": "nixpkgs", "nixpkgs-python": "nixpkgs-python", "pre-commit-hooks": "pre-commit-hooks"}}, "rust-analyzer-src": {"flake": false, "locked": {"lastModified": 1729715509, "owner": "rust-lang", "repo": "rust-analyzer", "rev": "40492e15d49b89cf409e2c5536444131fac49429", "type": "github"}, "original": {"owner": "rust-lang", "ref": "nightly", "repo": "rust-analyzer", "type": "github"}}}, "root": "root", "version": 7}