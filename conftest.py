from logging import CRITICAL

import pytest
from kivy import Logger


# Fixture to disable <PERSON><PERSON> logging during pytest
@pytest.fixture(scope="session", autouse=True)
def disable_kivy_logging():
    """
    This fixture automatically disables <PERSON><PERSON> logging during pytest runs.
    It sets the Kivy logger's level to 'critical' to prevent log output.

    - scope="session": Ensures that this fixture runs once per test session, optimizing performance.
    - autouse=True: Makes this fixture automatically applied without needing to specify it in tests.
    """
    original_level = Logger.level  # Save original log level to restore later

    try:
        # Set Kivy Logger to CRITICAL to suppress output, in case that setting disabled is not working
        Logger.setLevel(CRITICAL)

        # Disable <PERSON>vy Logger to prevent any output
        Logger.disabled = True
        yield
    finally:
        # Restore the original log level after tests
        Logger.setLevel(original_level)
