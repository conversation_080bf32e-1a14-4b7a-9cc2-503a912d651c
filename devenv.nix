{ pkgs, lib, config, ... }:

{
  languages.rust = {
    enable = true;
    channel = "stable";

    targets = [ "wasm32-wasi" ];

    components = [ "rustc" "cargo" "clippy" "rustfmt" "rust-analyzer" "rust-std" ];
  };

  languages.python = {
    enable = true;
    poetry = {
      enable = true;
      install = {
        enable = true;
        installRootPackage = false;
        onlyInstallRootPackage = false;
        compile = false;
        quiet = false;
        groups = [ ];
        ignoredGroups = [ ];
        onlyGroups = [ ];
        extras = [ ];
        allExtras = false;
        verbosity = "no";
      };
      activate.enable = true;
      package = pkgs.poetry;
    };
  };

  pre-commit.hooks = {
    clippy = {
      enable = true;
      settings.offline = false;
      extraPackages = [ pkgs.openssl ];
    };
    rustfmt.enable = true;
  };

  packages = [
    config.languages.python.package.pkgs.pjsua2
  ] ++ lib.optionals pkgs.stdenv.isDarwin (with pkgs.darwin.apple_sdk; [
    frameworks.Security
  ]) ++ lib.optionals (!pkgs.stdenv.isDarwin) [ # Macos doesn't need these
    pkgs.mtdev
    pkgs.lmdb
  ];

  processes = {
    build.exec = ''
    echo "Building contracts... (this may take a while)"
    if [ -e .build-finished ]; then
      rm -f .build-finished
    fi
    poetry lock && LMDB_FORCE_SYSTEM=1 poetry install --with dev && make
    chmod +x ./scripts/devenvup_main.sh
    '';

    node1.exec = ''
    sleep 1
    echo "Waiting for the build to finish..."
    while [ ! -e .build-finished ]; do
      sleep 1
    done

    echo "Build finished, starting vgraph..."
    ./scripts/devenvup_main.sh
    '';

    node2.exec = ''
    sleep 1
    echo "Waiting for the build to finish..."
    while [ ! -e .build-finished ]; do
      sleep 1
    done

    echo "Build finished, starting vgraph..."
    CONFIG=config-1.yaml ./scripts/devenvup_main.sh
    '';

    node3.exec = ''
    sleep 1
    echo "Waiting for the build to finish..."
    while [ ! -e .build-finished ]; do
      sleep 1
    done

    echo "Build finished, starting vgraph..."
    CONFIG=config-2.yaml ./scripts/devenvup_main.sh
    '';
  };
}
