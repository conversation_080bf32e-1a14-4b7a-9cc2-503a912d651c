name: Lint & Unit Test

on: [ pull_request, workflow_dispatch ]

jobs:
  python-lint-and-test:
    name: Python Lint & Unit Test
    runs-on: ubuntu-latest
    steps:
      # 1. Checkout the code
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      # 2. Set up Python
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.8'
          cache: 'pipenv'

      # 3. Install pipenv
      - name: Install pipenv
        run: pip install pipenv

      # 4. Install dependencies
      - name: Install dependencies
        run: pipenv install --dev --deploy

      # 5. Lint the code
      - name: Run ruff lint
        run: |
          pipenv run ruff check
          pipenv run ruff format --check

      # 6. Run Python unit tests
      - name: Run Python unit tests
        run: pipenv run pytest
