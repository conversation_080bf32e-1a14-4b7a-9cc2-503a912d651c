#:kivy 2.0.0


<VGraphAppGUI>:
    scrollStack: scrollStack
    nodeNum: 0
    currentKeepAlive: 0
    nodeLabel: nodeLabel
    orientation: 'vertical'
    BoxLayout:
        orientation:'horizontal'
        size_hint: 1, 0.1
        Label:
            canvas.before:
                Color:
                    rgba: 0.5, 0.8, 0.8, 1
                Rectangle:
                    pos: self.pos
                    size: self.size
            id: nodeLabel
            size_hint: 0.8, 1
            text: f"{root.currentKeepAlive} node connected"
            font_size: 0.05 * root.width
        Button:
            size_hint: 0.2, 1
            text: 'Refresh'
            font_size: 0.025 * root.width
            on_press: app.gui.refreshList()
    ScrollView:
        size_hint: 1, 0.9
        do_scroll_x: False
        do_scroll_y: True

        StackLayout:
            canvas.before:
                Color:
                    rgba: 0.875, 0.875, 0.875, 1
                Rectangle:
                    pos: self.pos
                    size: self.size
            orientation: "lr-tb"
            padding: 0.01 * self.width,  0.01 * self.width,  0.01 * self.width, 0
            spacing: 0.01 * self.width
            size_hint_y: None
            height: self.minimum_height
            id: scrollStack


<NodeLabel@Label>:
    canvas.before:
        Color:
            rgba: 0.2, 0.2, 0.2, 1
        Rectangle:
            pos: self.pos
            size: self.size
    size_hint: 1, None
    height: 0.30 * self.width
    text: ""
    markup: True
    text_size: self.size
    halign: "left"
    valign: "middle"
    font_size: 0.05 * self.width
